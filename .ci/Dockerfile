FROM jfrog.fkinternal.com/flow-step-template/build-jdk8-maven3.8:v1 AS builder
WORKDIR /src

RUN mkdir -p /root/.m2 && \
    echo '<settings><mirrors><mirror><id>Jfrog</id><name>J<PERSON><PERSON></name><url>https://jfrog.fkinternal.com/artifactory/maven_virtual</url><mirrorOf>central</mirrorOf></mirror></mirrors></settings>' \
    > /root/.m2/settings.xml

RUN mvn -Dorg.slf4j.simpleLogger.log.org.apache.maven.cli.transfer.Slf4jMavenTransferListener=warn org.jacoco:jacoco-maven-plugin:prepare-agent clean install -B -T4


FROM jfrog.fkinternal.com/supermoney-engg/openjdk8-builder:5365853

ARG appId=sm-pandora
ARG appDirectory=pandora-service
WORKDIR /src

RUN apt-get update && apt-get install -y --force-yes adduser vim sudo git wget ftp curl unzip iputils-ping procps net-tools telnet tcptraceroute

ARG user=lending
ARG group=fpg
ARG uid=108
ARG gid=112

WORKDIR /src

COPY --from=builder /src/${appDirectory}/target/${appDirectory}-*.jar /var/lib/${appId}/${appId}.jar
COPY --from=builder /src/${appDirectory}/config/gibraltar/* /etc/gibraltar/
COPY --from=builder /src/${appDirectory}/config/axis/* /etc/axis/
COPY --from=builder /src/${appDirectory}/config/fkagent/* /etc/fkagent/

RUN groupadd -g ${gid} ${group} && \
    useradd -u ${uid} -g ${gid} -m -s /bin/bash ${user} && \
    mkdir -p /var/lib/${appId} && chown -R ${user}:${group} /var/lib/${appId} && chmod -R u+rwx /var/lib/${appId} && \
    mkdir -p /var/lib/fkagent && chown -R ${user}:${group} /var/lib/fkagent && chmod -R u+rwx /var/lib/fkagent && \
    chown -R ${user}:${group} /entrypoint && chmod -R u+rwx /entrypoint && \
    sed -i "s/__PACKAGE__/${appId}/g" "/entrypoint"

RUN curl -L -X GET 'http://jfrog.fkinternal.com/artifactory/maven_internal/com/flipkart/instrumentation/fk-agent/2.0/fk-agent-2.0.jar' --output /var/lib/fkagent/fk-agent.jar

RUN sed -i "s/__INCLUDE_FK_AGENT__/TRUE/" "/entrypoint"
RUN sed -i "s|__FK_AGENT_OPTS__|-javaagent:/var/lib/fkagent/fk-agent.jar=agent-config:/etc/fkagent/config.yaml|" "/entrypoint"

ENTRYPOINT [ "/entrypoint" ]
CMD ["com.flipkart.fintech.pandora.service.application.PandoraApplication", "server", "/etc/config/config.yml"]

