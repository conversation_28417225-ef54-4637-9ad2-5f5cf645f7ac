server:
  type: default
  applicationContextPath: /pandora
  adminContextPath: /admin
  applicationConnectors:
    - type: http
      port: 8214
  adminConnectors:
    - type: http
      port: 8215
  requestLog:
    # Note:
    # 1. We do not specify a logFormat because the default log format is Common Log Format (CLF), which is good
    # 2. If we specify a logFormat, note that the format specifiers for access logs are different
    # 3. Access log accepts ONLY ONE appender - we cannot get access logs on both console and file simultaneously
    appenders:
      - type: file
        currentLogFilename: /var/log/pandora/access.log
        threshold: ALL
        archive: true
        archivedLogFilenamePattern: /var/log/pandora/access-%i.log.gz
        archivedFileCount: 5
        maxFileSize: 20MB
        timeZone: IST

jerseyClientConfig:
  maxThreads: 200
  cookiesEnabled: true
  gzipEnabledForRequests: false

environmentConfig:
  env: preprod

logging:
  level: INFO
  appenders:
    - type: file
      threshold: ALL
      logFormat: "[%d{dd-MM-yyyy HH:mm:ss.SSS}] %highlight([%level]) [%thread] [%X{trace_id}] [%X{X-Request-ID}] [%cyan(%logger{36})]: %message%n"
      currentLogFilename: /var/log/pandora/pandora.log
      archive: true
      archivedLogFilenamePattern: /var/log/pandora/pandora-%i.log.gz
      archivedFileCount: 5
      maxFileSize: 20MB
      timeZone: IST
    - type: file
      threshold: ERROR
      logFormat: "[%d{dd-MM-yyyy HH:mm:ss.SSS}] %highlight([%level]) [%thread] [%X{trace_id}] [%X{X-Request-ID}] [%cyan(%logger{36})]: %message%n"
      currentLogFilename: /var/log/pandora/pandora-error.log
      archive: true
      archivedLogFilenamePattern: /var/log/pandora/pandora-error-%i.log.gz
      archivedFileCount: 5
      maxFileSize: 20MB
      timeZone: IST
    - type: console
      threshold: INFO
      logFormat: "[%d{dd-MM-yyyy HH:mm:ss.SSS}] %highlight([%level]) [%thread] [%X{trace_id}] [%X{X-Request-ID}] [%cyan(%logger{36})]: %message%n"
      timeZone: IST
      target: stdout
  loggers:
    "com.flipkart.affordability.clients.oauth.MultiTenantOAuthClientImpl":
      level: ERROR

healthCheckName: PandoraHealth

esClientConfig:
  hostName: "***********"
  port: 80
  connectionTimeout : 3000

deployEnv: PREPROD

swagger:
  title: Pandora Service
  description: APIs for Pandora Service
  contact: <EMAIL>
  resourcePackage: com.flipkart.fintech.pandora.service.resources.PersonalLoan

kisshtServiceClientConfigEfa:
  url: http://**********/efa
  client: kisshtNbfc
  siteKey: EHcUsfpBHdhPxYYUdrUH3VDlRoHcmJiH8QYJZnO1
  secretKey: LZO1RAPBXH3X1ASYB6LLFNYSSSGPJZS7
  reverseProxyKey: ani_pandora


rateLimitingConfig:
  [
    { "limiterKey": "IMPS_PENNY_DROP", "timeoutInMs":5 },
    { "limiterKey": "PAN_SUBMIT", "timeoutInMs":5 },
    { "limiterKey": "UPI_PENNY_DROP", "timeoutInMs":5 },
    { "limiterKey": "GENERATE_OTP", "timeoutInMs":5 },
    { "limiterKey": "CKYC_SEARCH", "timeoutInMs":5 },
    { "limiterKey": "CKYC_DOWNLOAD", "timeoutInMs":5 },
    { "limiterKey": "SEND_DOCUMENT", "timeoutInMs":5 },
    { "limiterKey": "CHECK_LOAN_STATUS", "timeoutInMs":5 },
    { "limiterKey": "CHECK_ELIGIBILITY", "timeoutInMs": 5 },
    { "limiterKey": "CREATE_LOAN", "timeoutInMs": 5 },
    { "limiterKey": "UPDATE_LOAN", "timeoutInMs": 5 },
    { "limiterKey": "IDFC_RESUME_FICO", "timeoutInMs":5 },
    { "limiterKey": "VERIFY_OTP", "timeoutInMs": 5 },
    { "limiterKey": "GEO_LOCATION", "timeoutInMs": 5 }
  ]

citiEfaConfiguration:
  url:  https://in.sandbox.apib2b.citi.com
  client: 451e41a2-c8e5-4895-83c2-336e6ffc0254
  secretKey: tH7gC8iX3vE7sA1bY4iK0vU2hC2bW6mH4oP4mU3mL1kF1hL0gT
  enabled: false
  cachedAccessTokenTTL: 30
  trustStorePath: "/etc/citi/prod/client-truststore.jks"
  trustStorePass: password
  keyStorePath: "/etc/citi/prod/client-keystore.jks"
  keyStorePass: password
  proxyUri: ************:443
  gstRate: 18
  apiPath:
    clientAccessTokenPath: "/gcbin/api/clientCredentials/oauth2/token/in/fkt"
    accessTokenPath: "/gcbin/api/whiteLabel/oauth2/token/in/fkt"
    controlFlowTokenPath: "/gcbin/api/partner/v1/apac/onboarding/products/unsecured/applications/%s/mfa/statuses"
    accountSummaryPath: "/gcbin/api/v1/accounts/%s"
    billingTransactionsPath: "/gcbin/api/v1/accounts/%s/transactions"
    createApplicationPath: "/gcbin/api/v1/apac/onboarding/products/unsecured/applications"
    updateApplicationPath: "/gcbin/api/v1/apac/onboarding/products/unsecured/applications/%s"
    checkEligibilityPath: "/gcbin/api/v1/apac/onboarding/products/unsecured/applications/%s/inPrincipleApprovals"
    activateAccountPath: "/gcbin/api/v1/apac/onboarding/products/unsecured/applications/%s/submission"
    applicationEnquiryPath: "/gcbin/api/v1/apac/onboarding/products/unsecured/applications/%s/status"
    getCustomerAccountsPath: "/gcbin/api/v1/accounts"
    fetchAccountAccessCodePath: "/gcbin/api/partner/v1/accounts/%s/accessCodes"
    consentEnquiryPath: "/gcbin/api/v1/accounts/%s/consents"
    sendOtpPath: "/gcbin/api/v1/in/customers/identity/otp"
    verifyOtpPath: "/gcbin/api/v1/in/customers/identity/kyc"
    updateMfaPath: "/gcbin/api/partner/v1/mfa/statuses"

axisCbcConfiguration:
  hmacKey: hmacKey
  hmacAlgorithm: HmacSHA256
  isNfrApiEnabled: false
  url: https://sakshamuat.axisbank.co.in
  client: 0c337359-3b23-44a6-a013-4d431462c79f
  secretKey: V2sO2jF3vL0hH6lA5yA0lU5hA7eE0lK6kD5pF7bW5kP0jO3aT8
  trustStorePath: "/etc/axis/preprod-client-truststore.jks"
  trustStorePass: password
  keyStorePath: "/etc/axis/preprod-client-keystore.jks"
  keyStorePass: supermoney
  proxyUri: https://sakshamuat.axisbank.co.in:443
  encryptionKey: 53EACE72CD83D6B60754C2F3959168EA
  channelId: FK
  encryptAxisLogs: true
  axisCbcApiConfigurationsMap:
    updatePlusMembership:
      apiPath: "/gateway/api/insta-credit-card/sc/v1/update-reward-profile"
      serviceRequestId: "API.API.GET.REWARD"
      serviceRequestVersion: "1.0"
    getSupercoinUpdateRecord:
      apiPath: "/gateway/api/insta-credit-card/sc/v1/post-ack"
      serviceRequestId: "API.API.UPDATE.RECORD"
      serviceRequestVersion: "1.0"
    checkCohortEligibility:
      apiPath: "/gateway/api/v1/insta-credit-card/check-cohort-eligibility"
      serviceRequestId: "CheckCardEligibility"
      serviceRequestVersion: "1.0"
    demoCardConsent:
      apiPath: "/gateway/api/v1/insta-credit-card/cust-dtl-consent-otp"
      serviceRequestId: "OTPGen"
      serviceRequestVersion: "1.0"
    demoCardConsentForEtbCc:
      apiPath: "/gateway/api/v1/insta-credit-card-upgrade/cust-dtl-consent-otp"
      serviceRequestId: "OTPGen"
      serviceRequestVersion: "1.0"
    demoCardConsentForEtbNpa:
      apiPath: "/gateway/api/v1/insta-credit-card-npa/cust-dtl-consent-otp"
      serviceRequestId: "OTPGen"
      serviceRequestVersion: "1.0"
    fetchCustomerDemographics:
      apiPath: "/gateway/api/v1/insta-credit-card/fetch-cust-details"
      serviceRequestId: "FetchDemog"
      serviceRequestVersion: "1.0"
    fetchCustomerDemographicsForEtbCc:
      apiPath: "/gateway/api/v1/insta-credit-card-upgrade/fetch-cust-details"
      serviceRequestId: "FetchDemog"
      serviceRequestVersion: "1.0"
    fetchCustomerDemographicsForEtbNpa:
      apiPath: "/gateway/api/v1/insta-credit-card-npa/fetch-cust-details"
      serviceRequestId: "FetchDemog"
      serviceRequestVersion: "1.0"
    applyCardConsent :
      apiPath: "/gateway/api/v1/insta-credit-card/apply-consent-otp"
      serviceRequestId: "OTPGen"
      serviceRequestVersion: "1.0"
    applyCardConsentForEtbCc:
      apiPath: "/gateway/api/v1/insta-credit-card-upgrade/apply-consent-otp"
      serviceRequestId: "OTPGen"
      serviceRequestVersion: "1.0"
    applyCardConsentForEtbNpa:
      apiPath: "/gateway/api/v1/insta-credit-card-npa/apply-consent-otp"
      serviceRequestId: "OTPGen"
      serviceRequestVersion: "1.0"
    applyCardConsentForNtb:
      apiPath: "/gateway/api/v1/insta-credit-card-ntb/apply-consent-otp"
      serviceRequestId: "OTPGen"
      serviceRequestVersion: "1.0"
    processNewCardCase:
      apiPath: "/gateway/api/v1/insta-credit-card/new-card-application"
      serviceRequestId: "ProcessNewCard"
      serviceRequestVersion: "1.0"
    processUpgradeCard:
      apiPath: "/gateway/api/v1/insta-credit-card-upgrade/new-card-application"
      serviceRequestId: "ProcessNewCard"
      serviceRequestVersion: "1.0"
    processUpgradeCardV2:
      apiPath: "/gateway/api/v2/insta-credit-card-upgrade/new-card-application"
      serviceRequestId: "ProcessNewCardCase"
      serviceRequestVersion: "1.0"
    processNewCardCaseForEtbNpa:
      apiPath: "/gateway/api/v1/insta-credit-card-npa/new-card-application"
      serviceRequestId: "ProcessNewCardCase"
      serviceRequestVersion: "1.0"
    processNewCardCaseForNtb:
      apiPath: "/gateway/api/v1/insta-credit-card-ntb/new-card-application"
      serviceRequestId: "ProcessNewCardCase"
      serviceRequestVersion: "1.0"
    checkCardUpgradeStatus:
      apiPath: "/gateway/api/v1/insta-credit-card-upgrade/check-card-upgrade-status"
      serviceRequestId: "FetchCardDetails"
      serviceRequestVersion: "1.0"
    checkCaseStatus:
      apiPath: "/gateway/api/v2/insta-credit-card/check-case-status"
      serviceRequestId: "FetchCardDetails"
      serviceRequestVersion: "1.0"
    checkCaseStatusV3:
      apiPath: "/gateway/api/v3/insta-credit-card/check-case-status"
      serviceRequestId: "FetchCardDetails"
      serviceRequestVersion: "1.0"
    lcmGenerateAuthOtp:
      apiPath: "/gateway/api/v1/insta-credit-card/lcm/gen-auth-otp"
      serviceRequestId: "AX.ICC.LCM.AUTH"
      serviceRequestVersion: "1.0"
    lcmValidateAuthOtp:
      apiPath: "/gateway/api/v1/insta-credit-card/lcm/validate-auth-otp"
      serviceRequestId: "AX.ICC.LCM.AUTH.VAL"
      serviceRequestVersion: "1.0"
    checkCaseStatusMobile:
      apiPath: "/gateway/api/v1/insta-credit-card/check-case-status-mobile"
      serviceRequestId: "CheckCaseStatus"
      serviceRequestVersion: "1.0"
    checkCaseStatusMobileForEtbNpa:
      apiPath: "/gateway/api/v1/insta-credit-card-npa/check-case-status-mobile"
      serviceRequestId: "CheckCaseStatus"
      serviceRequestVersion: "1.0"
    checkCaseStatusMobileForNtb:
      apiPath: "/gateway/api/v1/insta-credit-card-ntb/check-case-status-mobile"
      serviceRequestId: "CheckCaseStatus"
      serviceRequestVersion: "1.0"
    kycSchedule:
      apiPath: "/gateway/api/v1/insta-credit-card/kyc-scheduler"
      serviceRequestId: "ProcessNewCardCase"
      serviceRequestVersion: "1.0"
    getStatementGenerationDate:
      apiPath: "/gateway/api/v2/insta-credit-card/lcm/gen-statement-dates"
      serviceRequestId: "AX.ICC.LCM.CARDDET"
      serviceRequestVersion: "1.0"
    getCashBackSummary:
      apiPath: "/gateway/api/v2/insta-credit-card/lcm/fetch-all-cashback"
      serviceRequestId: "AX.ICC.LCM.CARDDET"
      serviceRequestVersion: "1.0"
    getStatementSummary:
      apiPath: "/gateway/api/v2/insta-credit-card/lcm/fetch-last-stmt-concise"
      serviceRequestId: "AX.ICC.LCM.CARDDET"
      serviceRequestVersion: "1.0"
    getAccountSummary:
      apiPath: "/gateway/api/v1/insta-credit-card/lcm/account-summary-details"
      serviceRequestId: "AccountSummary"
      serviceRequestVersion: "1.0"
    lcmSendEmail:
      apiPath: "/gateway/api/v1/insta-credit-card/lcm/send-email"
      serviceRequestId: "AX.ICC.LCM.SENDEML"
      serviceRequestVersion: "1.0"
    handShakeVideoKyc:
      apiPath: "/gateway/api/v1/insta-credit-card/vkyc/handshake"
      serviceRequestId: "AX.ICC.VKYC.AUTH"
      serviceRequestVersion: "1.0"
    blockCard:
      apiPath: "/gateway/api/v1/insta-credit-card/lcm/block-card"
      serviceRequestId: "AX.ICC.LCM.CARDDET"
      serviceRequestVersion: "1.0"
    getCreditCardSettings:
      apiPath: "/gateway/api/v2/insta-credit-card/lcm/manage-usage-limit/inquiry"
      serviceRequestId: "ACPInquiry"
      serviceRequestVersion: "1.0"
    updateCreditCardSettings:
      apiPath: "/gateway/api/v1/insta-credit-card/lcm/manage-usage-limit/update"
      serviceRequestId: "ACPUpdate"
      serviceRequestVersion: "1.0"
    checkCreditLimitEligibility:
      apiPath: "/gateway/api/v2/insta-credit-card/lcm/check-credit-limit-eligibility"
      serviceRequestId: "checkCreditLimitEligibility"
      serviceRequestVersion: "1.0"
    getCreditLimitIncrementSettings:
      apiPath: "/gateway/api/v1/insta-credit-card/lcm/credit-limit-increment"
      serviceRequestId: "creditLimitInc"
      serviceRequestVersion: "1.0"
    getTransactionSummary:
      apiPath: "/gateway/api/v2/insta-credit-card/lcm/transaction-summary"
      serviceRequestId: "TransactionSummary"
      serviceRequestVersion: "1.0"
    convertTxnToInstalment:
      apiPath: "/gateway/api/v1/insta-credit-card/lcm/convert-txn-to-instalment"
      serviceRequestId: "convertTxn"
      serviceRequestVersion: "1.0"
    validateDeliveryCode:
      apiPath: "/gateway/api/v2/insta-credit-card/lcm/delivery-code-validate"
      serviceRequestId: "ABC.PQR.XYZ.007"
      serviceRequestVersion: "1.0"
    initiateApplication:
      apiPath: "/gateway/api/income-assessment/v1/initiate-application"
      serviceRequestId: "Init-App"
      serviceRequestVersion: "1.0"
    generateToken:
      apiPath: "/gateway/api/income-assessment/v1/generate-auth-code"
      serviceRequestId: "Generate-Auth-Code"
      serviceRequestVersion: "1.0"
    getStatus:
      apiPath: "/gateway/api/income-assessment/v1/get-status"
      serviceRequestId: "Status"
      serviceRequestVersion: "1.0"
  testConfigsMap:
    mockAxisResponses: N
    replacePhoneNumber: Y
    mockPhoneNumber: **********:**********,**********:**********,**********:91**********,**********:**********,**********:************,**********:************,**********:************,**********:**********
    mockNudgeResponse: true
    mockCremoScore: 500

smAxisCbcConfiguration:
  hmacKey: hmacKey
  hmacAlgorithm: HmacSHA256
  url: https://sakshamuat.axisbank.co.in
  trustStorePath: "/etc/axis/preprod-client-truststore.jks"
  trustStorePass: password
  keyStorePath: "/etc/axis/preprod-client-keystore.jks"
  keyStorePass: supermoney
  proxyUri: https://sakshamuat.axisbank.co.in:443
  connectionTimeout: 60000
  readTimeout: 60000
  channelId: FLIPKARTSM
  mockTestingON: true
  kycRedirectionBaseUrl: https://maxuat.axisbank.com/gpns/external/partner/login
  smKycCallbackUrl: https://super.money/homepage
  displayNameMatchThreshold: 50
  productId: 911
  promotionCodes:
    - pnn

featureFlagBucketName: "sm-feature-flag-preprod"

kisshtServiceClientConfigBnpl:
  url: http://***********
  client: kisshtNbfc
  siteKey: bb5CKNVso77Y7P7Y9VdQNnG236DEHD6p8CcmTeWQ
  secretKey: RSFKTKIL6POCB5L1C3UIE1O0BDYNA26Q

epaylaterClientConfig:
  url: http://***********
  apiKey: secret_18e54ebc-bcc3-11e8-8be2-4fc0f616d9e6

gibraltarConfig:
  maxPublicKeyCount: 100
  keySpaceName: onboarding
  clientName: efa-onboarding-client
  trustStorePath: "/etc/gibraltar/prod-client-truststore.jks"
  trustStorePass: password
  keyStorePath: "/etc/gibraltar/prod-client-keystore.jks"
  keyStorePass: password
  certificateAlias: gibraltarSelfSigned
  url: https://*************:8443,https://***********:8443
  generateKeysOnStartUp: false
  connectionTimeoutInMs: 150
  socketTimeoutInMs: 150
  httpRequestRetryCount: 5

userServiceClientConfig:
  usUrl: http://***********:35200
  usClientId: affordability

loginServiceClientConfig:
  loginServiceUrl: http://***********:35100
  loginServiceClientId: affordability

khaataClientConfig:
  url: http://*************:8980
  clientName: pandora

chowkidaarClientConfig:
  host: http://sm-chowkidaar-kaas-v1.sm-chowkidaar-prod.fkcloud.in

bucketingConfig:
  enabled: true
  accountVintage: [90,180,360]
  daysSinceOrder: [215]
  ordersFulfilled: [3,5]
  gmvTillDate: [10000,20000]
  percentageReturned: [25,33]
  ordersAtCommonAddress: [2,3]

ubonaServiceClientConfig:
  url: http://***********

redis:
  serverAddress: localhost:6379
  maxTotal: 100
  maxIdle: 10
  minIdle: 1
  password: dummy

ardourClientConfig:
  url: http://************:9080
  client: xxxxxxxxxxxxxxxxxxx

pinakaClientConfig:
  url: http://pinaka-service-next.sm-pinaka-prod.fkcloud.in
  client: pandora-client
  merchant: mp_flipkart

cardGatewayClientConfig:
  url: http://card-gateway-service-next.sm-card-gateway-gcp-prod.fkcloud.in
  client: pandora-client
  merchant: mp_flipkart

ubonaConfiguration:
  reverseProxyKey: ubona
  aesKey: B7F13DE0F479ACEA259B3A2FF53CB94C
  aesIv: 712EE5763C687550
  aesSalt: 3D08463B8B40DF1D
  shouldEncrypt: true
  blockSize: 16

collectionsClientConfiguration:
  scheme: http
  host: ************
  port: 80
  exchangeName: fintech-collections

upswingConfig:
  mockTestingON: true
  idpUrl: "https://idp.uat-upswing.one"
  partnerApiUrl: "https://partner.api.uat-upswing.one"
  jwtTokenGenerationPath: "realms/%s/protocol/openid-connect/token"
  customerRegistrationPath: "v1/term-deposit/customer/register"
  applicationStatusPath: "v1/term-deposit/lending-account/application/status/%s"
  accountDetailsPath: "v1/term-deposit/lending-account/account/details/%s"
  netWorthPath: "v1/term-deposit/deposits/netWorth/%s"
  netWorthV2Path:   "v2/term-deposit/deposits/netWorth/%s"
  pendingJourneyPath: "v1/term-deposit/customer/pendingJourney"
  rewardRedemptionPath: /v1/credit-card/rewards/redeem
  upswingConnectionConfig:
    readTimeout: 30000
    connectTimeout: 60000
    totalRetryCount: 0
    retryDelays: 5000
  upswingCacheConfig:
    inMemoryCacheSize: 10
    inMemoryCacheExpiryInSecs: 270
    inMemoryCacheRefreshInSecs: 250

ibConfiguration:
  userSignupUrl: https://esbpr.ivlfinance.com/esb/ivl/0/process/ivlfin/a_user/v1
  uploadDocumentsUrl: https://esbpr.ivlfinance.com/esb/ivl/0/process/ivlfin/a_cardputresultdocumenttocrm/v1
  creditAssessmentUrl: https://esbpr.ivlfinance.com/esb/ivl/0/process/ivlfin/a_carddemographic/v1
  cardCaptureValidationUrl: https://esbpr.ivlfinance.com/esb/ivl/0/process/ivlfin/a_cardcapturevalidatedata/v1
  cardDetailsUrl: https://esbpr.ivlfinance.com/esb/ivl/0/process/ivlfin/a_cardgetdetails/v1
  panNameServiceUrl: https://esbpr.ivlfinance.com/esb/ivl/0/process/ivlfin/a_cardpanname/v1
  cardMandateUrl: https://esbpr.ivlfinance.com/esb/ivl/0/process/ivlfin/a_cardemandateresult/v1
  source: Flipkart
  enableWizard: false
  testUsers: ["A1","A2","ACC68257761FC00450C8AAEFDFD8361E6A0X","ACC2A5AA7EDAEC4414EA6B2EDE8E2C7A781I","ACCFE741BBF1F944C9CB376E1BF87EAF340W","ACC9778B47182D2434BB64FBDAA95F87411C","ACC07A17819EC1D44C981193DEBBF043400R","ACCB6FF2FF2D325482C9F5341CA50841626O","ACC14052326530718101","ACC14057582966055708","ACC14017297826421172","ACC5846BC71224D416DA690D8090E99F390N","ACC432BDCE121DC4D30B4DA7CAFC7EBB9C9S","ACCE60889472DC44267B9BFE9484054657BI","ACC648D1DD78C3040A686B0160F9909EFAFO","ACLWBSGA95GFWF51CLBS21W5EPYGWZBR","ACPLAJLVWTU75OMLR3QGV2XZA4761Y9U","CCABD2D37CCCC84F37B1F870993ADA6FE6X","ACC2A5AA7EDAEC4414EA6B2EDE8E2C7A781I","ACC14208208785381226","ACC7495ACE0B9F84BB9AC63D0DA60AD679AC","ACQH6JT64FW13ZSZAW6YD9QMXVXEQBNQ","ACC8D9F9B36546043F79E60EAB4ADFCE9D3Z","ACC13553664458368763","ACC05D577873443449FA9E165A662D03B961","ACCABD2D37CCCC84F37B1F870993ADA6FE6X","ACC1E6E824DC8A9420CA54AB3BC4A2586842","ACT27W3Q1XZN7MIO9YVPMHVOOZKHGUHK","AC9W4EAP7LBS7PIJYJQFRWETMVDB19NM","ACC83381E9EE2104E18B00FE41B5366F1AFK","AC2QKHBR41R08QH9T4D4VYQWG4RZZSSB","ACC4C5070D22EBD4F26A017B71167444CFCW","ACC62A1D1BCC03648AE93B7A62A4D8CA284J","ACCD134CC390E5B47EABAADA4F19EFFA6C8V","ACC4304E3E4E26B4878AF931EF89E9B8DAEC","ACC798D3CC5AC4447869C1B1DD645A7128AZ","ACCA17D4EA4D70F43388C6EC80E73A69180R","ACC13832240149214783","ACCEE79114ED745438BB1AD7D813193798FD"]

idfcConfiguration:
  kid: 35e45bfa-3222-477d-9651-75611505617d
  secretKey: NEM0RTZDNEQ2Njc5NzA3NzQzNkQ3ODM0NTM1NjY4NTA2RjQyNkY1ODQzNDEzMjMyMzc2ODY0MzMzNDc4Njg3Ng==
  tokenServiceURL: https://apiext.uat.idfcfirstbank.com/authorization/oauth2/token
  plClientId: af93d344-23d7-419e-9d18-f21f01b1de1d
  privateKeyPath: idfc_rsa_private_preprod.pem
  proxyUri: https://muleuat.capitalfirst.com:1447
  pennyDropUri: https://muleuat.capitalfirst.com:1447
  clientId: flexa42b-68c2-4daa-b570-76b4c2aaa5e5
  clientSecret: flex585a-oaf7-4589-b6d6-f4591d78a57f
  idfcEncryptionKey: Idfc4321Uidai987
  grantType: "client_credentials"
  idfcHashingSalt: 1DFcHasH1nGs@1t-prEpr0d
  accessTokenTimeoutInMinutes: 10
  bnplScheme: 53521
  emiScheme: 73010
  fsupScheme: 73012
  convFeeChargeId: 702283
  usageFeeChargeId: 702311
  emiPfChargeId: 600475
  lateFeeChargeId: 600516
  documentUploadConfiguration:
    documentUploadUrl: https://muleuat.capitalfirst.com:8443/generic_api_gateway/v2/bpm_uploadDocuments
    userId: L000101
    environment: PREPROD
    password: ABCDEFGH1234567987120QPRODWERG123
  impsPennyDropUrl: /ext/api/1.0/sys/impsFT
  upiPennyDropUrl: /api/v1/idfc/upi/preAuth
  validateUpiUrl: /api/v1/idfc/upi/validateVA
  checkLoanStatusUrl: https://muleuat.capitalfirst.com:8443
  tokenEndPoint: https://apiext.uat.idfcfirstbank.com/authorization/oauth2/token
  idfcPaybackReversalEnabledTenants: [ "FFB","FK_CONSUMER_CREDIT" ]
  clientIdV2: eyJkYXRhIjoidmF1bHQ6djE6WDVGcWpST1BpU1daU3dkRzJ0WGxUKzhtYUFRVUc5Q3pzUy9YYnZrY25CYU5NK1FGRXFPUDU1ZjRDb0hDTVVTcHQrRVE5ZWI2WSttKzRGUVpiZ3hka1E9PSIsImtleUlkIjoiY2ZnLXN2Yzo6aW4taHlkZXJhYmFkLTEtcHJlcHJvZDpwYW5kb3JhLXByZXByb2QifQ==
  clientAssertionType: urn:ietf:params:oauth:client-assertion-type:jwt-bearer
  privateKey: eyJkYXRhIjoidmF1bHQ6djE6OHR3RHlKL2dxTitDejM1NGdEcVhzTVhCZjVSbFo0eFdRSXN6N3VTUnI3Y2xDZG1TTWxsRE5CM0NCQ00zeG1Dc3Z2bjNSK1QvT01PVFNKcXFJRk5RTXFOY1FjYVFvMmhCdVNNRWdRODBGUGI3a01BNU93dWZ0MC9meXNpUnZhVjIyelZ1TzNISUNlMXRyeWNSS1d1UTF4bjN5amZWaG8xTkNjUXppVk9kZzRsZ2F0bjRyTWw3WEwrR2lKZzZjM2N3OGR4MzZRN3IydElIaXcwWDB1OEFkQWp4YXgweHF4aFlBbk1iMUFXWWNiaHE4WHYyUGN4T3ZjUzdheEEzZE01bUFTa1BJTmgvbEtaYUsrQmZoWFROalZEZ0k0V1RyT3cvZ2lVSGdCbnMxVEhjdnZaNmc3RTVJVDJzMmxKOEI3SXhmMzRKWGhqMHlqMk14VzJ2M20zbVl1anpUUzF0cC9oZHhRZHc2L01hTFdCcGJyLzF5eEFaaEJlTHB3d3Z5dmxxelR2VTcvZnNBQmJwdVRxU2hNejZPY1R5cFVGN1NkdmpUWThvUlRjanM0MERKSkwvVWUrNEExbERxamRuWVl1dWVzcVMrZEJDd2VHeW5Zc1MrbStwUWVlRkxrM1VLUW5hb1RKSERuc2F5MXdyVGpEa0srZU81Smk2dXRPWjNBTWl2T3d0bVphbTdaUnNEcEs2ek1pRDNVSWkwRUFMQ3ZNblpXR1UycEdacGM0VlNhMVlpWWRXWlNpWGZtcWNMWGFqVHhOcjdwZ1FOWHdtbE96OFMrT0hWcmxkbk5mcUlQNU1JdVI4NHBvdHQ1NnA2YmJUSzk3Y1pBeVJmMlBvSkE1VlFNaGg0Z040RW5UZjZ2QTJDUm5aZDJFWFAzUkNOV1pFQ3pPZkZMZTlORUFsdjBmb1pQOWZqVGhEY2ovbUhaVkNHZzhScUpGdnJYWFhOdDRVcFFrZnN0d3ZtZzZ1NUhQQUhjTGlKQWdGRXpqb3NBZ1BUa0FqMWF2N25zTXRBUWtPV241aWZaWlFxZ3dtTlpFM1BjRUxzeC9ZOVNuNlpveldjT0JJVjZQTXZhQkQ4MDU4Nzh4Z2t4Qm13cGZyQnQ0UDJ4cEtqWVlpRCtOOGhyVy81L2tpeWpZQ0krR0x3aW5XRStMQVcyWlBwYXpKVjZaRW9GZFA2Q0dNekRYb3F2MGNPRlNvRXJYZUcrRUd3M1VaWEpTd2FGalZxbjM0S3UrVytlQ09pNnRId1I5a01HVUZ1aDJWcXErMjk0bjhseGlucTJ4aWpnZE93TnJhVkcvT01laXZEU3k0NFFZUjJRQXB0SllldmMveGJDemIwbWVmWTR3ZXRYOVhtdFpLSy9xVVJlNGpvTWp0cnBIMS9TSERPQ2ovUW16NlhsbExwSDQydzlQN2t1WFQwcTdCZUl1alhxMkU3VXdIby9IZFBUNHY0ZkQ4YnRzL3NyNnNNcUdYUWpCc0tDcFByNW1OdXpIekdZdmNUSXZicEpaUFdhbFBxcWNmY25ZR256Vm5BMldQcDg0YWd3TmE1ZjZ2Qk9tR1lSK1VCTnRuaWZVb2JkRWx4TWpuMXNBOHdmZmtpWG5qZks1TDc0YjNMRU5lTGErNWJrck15TG1ENGR6MWFBMFFoUmZrN3FCSjB1WTBuSjUxNDhZRExvbVdnc2xuUDgwUWlSTnNZZEhwaGdrNjhLTGRXSmU5ak14d0ZuVU41V3JPY29OMUI4S0NLekE4amc1aThNQ1F1QU9zNnpQZUJYU2w1VW5zU1AxRmUySmhsSEp2a1RuamFkMUxGa2NUK3FSbUx6dXBXblJMc3ZBbHBlRW5jSTZVczQ3bmc5Q2J4MVRZZXljaHc2MnRLNmQ3ZXhYRnZscjNKR2JtWG9hRWQ0UmNPaEN2ODNBV3hqVXFmbE5UTlRJZlVvclh2WGxHMFBKbTB1b2dYQ1hkV3lDK0w4aU40bm5iUE9vdEpTYW9TbkllZ2ZUdW9rMkdNUmNSQi9NdXBsdXpxdXovS0YvUTJTZEppUjdybzdoNFlQZ1VuclZmSUE0UGRXZHpSVzAvYVNEamFPWlFRa08xYi9qNkVmRElZR2JCMUpnck5QRWlsMFFKTWxrZDVKOUhyTzB0Yzg5RUJ0akRad0FNR1hSby9mbnNpUk1Lb0pybmdvK3JNNnp6MkE2MDI1aWtFd2UvRk1uMEFyRDJFaldtcnZuTm82U3NFMHluYlZMU0FCT0l0QlEzVFBaUll2UnAwSW1aZ3pjb0ozbFBWTk5peW1kSEk0WFh5bHpEWG55RkJKd29tamhYWnA4Q29SdXdLdnZQYVpyQzRBQ3k1SXVYd0xkOWdwVWhtRGU4WEpVOHBPc245Ym9LTTJPQ1V5a3h5R2xUcHFORjJIenRrRVRHREpDbHluQlY2T0JiUVRDNWFHMk1jLytIa3loclN0R1ZoNm1ZMnpiZDFNaXNYUk15SHVmcGVZRzBYN2cyd0IxcjJIZHlsQWFJMVF2VWkvaU9QOFBDc3hSQ2Zldkk5dUhtbVlZMGxVYlpOOEphVThBWmhjK1dGc05TSitpOGpDaDB2UWVaUWpiUGFzNG42d1lzNW1heDdyeHdwSVJFWTlWcElsS1BkTW5VOWhqQzlNb2pKZGdicy8wME9VVmNKR0dCZnkxNEUrQW9hUmE4Y1dHa0hnMDcrUFRYQ3NQMFR5UlJ2Kzd2b1NLZEtBQjZOdzUvbS9pdis4a1FwT1ZYZDBtemp3ZWtYYTlBemMvZ0RTNmU0L0dyVGdZZlZDdFR6aTU4bENDd1A5L1NrZmh4UFVra3BuSkV4ajhYNHFYRXErbHMxTUNvTy8rKzNVTHlYRnlJQnozTVl0WjNFeHJjQkZDSlVPVE04emJSbHkzOFhPZ0N5MC9iN3dpb2lvdzM1RTdkZ3JRPSIsImtleUlkIjoiY2ZnLXN2Yzo6aW4taHlkZXJhYmFkLTEtcHJlcHJvZDpwYW5kb3JhLXByZXByb2QifQ==
  keyIdentifier: flpkrt-nonprod-c5b66fa4c56a111ec9d64-0242ac
  initializationVector: Kl4lNVAqKzFuNUAxXjF+Kw==
  proxyUriV2: https://apiext.uat.idfcfirstbank.com
  idfcEncryptionKeyV2: FLPKRT9E40A858FKTB0AD4DTGEEB5DF1
  jwtTokenAudience: https://app.uat-opt.idfcfirstbank.com/platform/oauth/oauth2/token
  isMockEnabled: false
  emandateSecretKey: VEN@#SMONYAES@08_NI%$hghtj=SMONY
  emandateInitializationVector: VEN@#SMONYAIV@01
  mockBucketConfig:
    bucketName: sm-pandora-mock-next
    enableLocalDynamicBucket: false
  vkycResumeUrl: https://uat.fmreporting.idfcfirstbank.com/KYCServiceApp/KYCMode/VKYC
  callerId: JUSPAY_UAT
  emandateRedirectionUrl: https://uat.fmreporting.idfcfirstbank.com/IDFCEMandate/EMandateB2BPaynimmo.aspx

indiaBullsClientConfig:
  protocol: http
  hostName: esbqa.indiabulls.com/esb/ivl/0/process/ivlfin
  portNo:
  balanceEnquiryPath:
  dashboardApiPath: /a_carddashboard/v1
  agreementApiPath: /a_onlinesoa/v1
  tijoriUrl: http://***********:80

pandoraHystrixProperties:
  commandExecutionProperties:
    hystrix.command.AxisIncomeInitiate.execution.isolation.thread.timeoutInMilliseconds: 14000
    hystrix.command.AxisIncomeGenerateToken.execution.isolation.thread.timeoutInMilliseconds: 14000
    hystrix.command.AxisIncomeGetStatus.execution.isolation.thread.timeoutInMilliseconds: 14000
    hystrix.command.IndiaBullsCardMandate.execution.isolation.thread.timeoutInMilliseconds: 10000
    hystrix.command.IndiaBullsDashboard.execution.isolation.thread.timeoutInMilliseconds: 10000
    hystrix.command.IndiaBullsUserSignup.execution.isolation.thread.timeoutInMilliseconds: 10000
    hystrix.command.IndiaBullsUploadDocuments.execution.isolation.thread.timeoutInMilliseconds: 10000
    hystrix.command.IndiaBullsCreditAssessment.execution.isolation.thread.timeoutInMilliseconds: 30000
    hystrix.command.IndiaBullsCardCaptureValidation.execution.isolation.thread.timeoutInMilliseconds: 10000
    hystrix.command.IndiaBullsCardDetails.execution.isolation.thread.timeoutInMilliseconds: 10000
    hystrix.command.IndiaBullsPanNameService.execution.isolation.thread.timeoutInMilliseconds: 10000
    hystrix.command.AxisCbcCheckCohortEligibility.execution.isolation.thread.timeoutInMilliseconds: 14000
    hystrix.command.AxisCbcDemoCardConsent.execution.isolation.thread.timeoutInMilliseconds: 14000
    hystrix.command.AxisCbcDemoCardConsentEtbNpa.execution.isolation.thread.timeoutInMilliseconds: 14000
    hystrix.command.AxisCbcDemoCardConsentEtbCc.execution.isolation.thread.timeoutInMilliseconds: 14000
    hystrix.command.AxisCbcApplyCardConsent.execution.isolation.thread.timeoutInMilliseconds: 14000
    hystrix.command.AxisCbcApplyCardConsentEtbCc.execution.isolation.thread.timeoutInMilliseconds: 14000
    hystrix.command.AxisCbcApplyCardConsentNtb.execution.isolation.thread.timeoutInMilliseconds: 14000
    hystrix.command.AxisCbcApplyCardConsentEtbNpa.execution.isolation.thread.timeoutInMilliseconds: 14000
    hystrix.command.AxisCbcFetchCustDemogs.execution.isolation.thread.timeoutInMilliseconds: 14000
    hystrix.command.AxisCbcFetchCustDemogsEtbNpa.execution.isolation.thread.timeoutInMilliseconds: 14000
    hystrix.command.AxisCbcFetchCustDemogsEtbCc.execution.isolation.thread.timeoutInMilliseconds: 14000
    hystrix.command.AxisCbcProcessNewCard.execution.isolation.thread.timeoutInMilliseconds: 20000
    hystrix.command.AxisCbcProcessNewCardNtb.execution.isolation.thread.timeoutInMilliseconds: 20000
    hystrix.command.AxisCbcProcessNewCardEtbNpa.execution.isolation.thread.timeoutInMilliseconds: 20000
    hystrix.command.AxisCbcProcessUpgradeCard.execution.isolation.thread.timeoutInMilliseconds: 20000
    hystrix.command.AxisCbcCheckCaseStatus.execution.isolation.thread.timeoutInMilliseconds: 20000
    hystrix.command.AxisCbcCheckCardUpgradeStatus.execution.isolation.thread.timeoutInMilliseconds: 60000
    hystrix.command.AxisCbcLcmAuthOtpGen.execution.isolation.thread.timeoutInMilliseconds: 14000
    hystrix.command.AxisCbcLcmAuthOtpVal.execution.isolation.thread.timeoutInMilliseconds: 14000
    hystrix.command.AxisCbcLcmSendEmail.execution.isolation.thread.timeoutInMilliseconds: 14000
    hystrix.command.AxisVKYC.execution.isolation.thread.timeoutInMilliseconds: 14000
    hystrix.command.AxisCbcAccountSummary.execution.isolation.thread.timeoutInMilliseconds: 14000
    hystrix.command.AxisCbcFetchStatement.execution.isolation.thread.timeoutInMilliseconds: 14000
    hystrix.command.AxisCbcStatementDates.execution.isolation.thread.timeoutInMilliseconds: 14000
    hystrix.command.AxisCbcLcmBlockCard.execution.isolation.thread.timeoutInMilliseconds: 14000
    hystrix.command.AxisCbcLifeTimeCashBack.execution.isolation.thread.timeoutInMilliseconds: 14000
    hystrix.command.AxisCbcLcmGetCreditCardSettings.execution.isolation.thread.timeoutInMilliseconds: 14000
    hystrix.command.AxisCbcLcmUpdateCreditCardSettings.execution.isolation.thread.timeoutInMilliseconds: 14000
    hystrix.command.AxisCbcCheckCaseStatusMobile.execution.isolation.thread.timeoutInMilliseconds: 2000
    hystrix.command.AxisCbcCheckCaseStatusMobileEtbNpa.execution.isolation.thread.timeoutInMilliseconds: 2000
    hystrix.command.AxisCbcCheckCaseStatusMobileNtb.execution.isolation.thread.timeoutInMilliseconds: 2000
    hystrix.command.IdfcPanVerification.execution.isolation.thread.timeoutInMilliseconds: 15000
    hystrix.command.IdfcAccessTokenKey.execution.isolation.thread.timeoutInMilliseconds: 5000
    hystrix.command.IdfcGenerateOtpKey.execution.isolation.thread.timeoutInMilliseconds: 30000
    hystrix.command.IdfcVerifyOtpKey.execution.isolation.thread.timeoutInMilliseconds: 30000
    hystrix.command.IdfcImpsPennyDropKey.execution.isolation.thread.timeoutInMilliseconds: 10000
    hystrix.command.IdfcUpiPennyDropKey.execution.isolation.thread.timeoutInMilliseconds: 15000
    hystrix.command.IdfcValidateUpiPennyDropKey.execution.isolation.thread.timeoutInMilliseconds: 15000
    hystrix.command.IdfcCheckEligibilityKey.execution.isolation.thread.timeoutInMilliseconds: 30000
    hystrix.command.IdfcCreateLoanKey.execution.isolation.thread.timeoutInMilliseconds: 20000
    hystrix.command.IdfcUpdateLoanKey.execution.isolation.thread.timeoutInMilliseconds: 20000
    hystrix.command.IdfcCheckLoanStatusKey.execution.isolation.thread.timeoutInMilliseconds: 2000
    hystrix.command.IdfcLoanClosure.execution.isolation.thread.timeoutInMilliseconds: 30000
    hystrix.command.IdfcSaleForwardKey.execution.isolation.thread.timeoutInMilliseconds: 20000
    hystrix.command.IdfcSaleReverseKey.execution.isolation.thread.timeoutInMilliseconds: 20000
    hystrix.command.IdfcPaybackForwardKey.execution.isolation.thread.timeoutInMilliseconds: 20000
    hystrix.command.IdfcSearchCkycKey.execution.isolation.thread.timeoutInMilliseconds: 10000
    hystrix.command.IdfcDownloadCkycKey.execution.isolation.thread.timeoutInMilliseconds: 10000
    hystrix.command.TeleSales.execution.isolation.thread.timeoutInMilliseconds: 14000
    hystrix.command.IdfcFfbUploadDocumentKey.execution.isolation.thread.timeoutInMilliseconds: 15000
    hystrix.command.IdfcSendKycDocument.execution.isolation.thread.timeoutInMilliseconds: 5000
    hystrix.command.IdfcEbcAuthTokenKey.execution.isolation.thread.timeoutInMilliseconds: 10000
    hystrix.command.IdfcResumeFicoKey.execution.isolation.thread.timeoutInMilliseconds: 10000
    hystrix.command.AxisPl_CI_command.execution.isolation.thread.timeoutInMilliseconds: 8000
    hystrix.command.AxisPl_BP_command.execution.isolation.thread.timeoutInMilliseconds: 200
    hystrix.command.AxisPl_GetOffer_command.execution.isolation.thread.timeoutInMilliseconds: 27000
    hystrix.command.AxisPl_SO_command.execution.isolation.thread.timeoutInMilliseconds: 4000
    hystrix.command.AxisPl_GS_command.execution.isolation.thread.timeoutInMilliseconds: 4000
    hystrix.command.AxisPl_FK_command.execution.isolation.thread.timeoutInMilliseconds: 4000
    hystrix.command.KotakPayLaterSaleForwardKey.execution.isolation.thread.timeoutInMilliseconds: 20000
    hystrix.command.KotakEMISaleForwardKey.execution.isolation.thread.timeoutInMilliseconds: 20000
    hystrix.command.KotakSaleReverseKey.execution.isolation.thread.timeoutInMilliseconds: 20000
    hystrix.command.KotakPaybackForwardKey.execution.isolation.thread.timeoutInMilliseconds: 20000
    hystrix.command.KotakPaybackReverseKey.execution.isolation.thread.timeoutInMilliseconds: 20000
    hystrix.command.KotakFreezeKey.execution.isolation.thread.timeoutInMilliseconds: 20000
    hystrix.command.KotakUnFreezeKey.execution.isolation.thread.timeoutInMilliseconds: 20000
    hystrix.command.KotakBillGenerationKey.execution.isolation.thread.timeoutInMilliseconds: 20000
    hystrix.command.KotakForwardFeeKey.execution.isolation.thread.timeoutInMilliseconds: 20000
    hystrix.command.KotakReverseFeeKey.execution.isolation.thread.timeoutInMilliseconds: 20000

  commandCircuitBreakerProperties:
    hystrix.command.AxisIncomeInitiate.circuitBreaker.forceClosed: false
    hystrix.command.AxisIncomeGenerateToken.circuitBreaker.forceClosed: false
    hystrix.command.AxisIncomeGetStatus.circuitBreaker.forceClosed: false
    hystrix.command.IndiaBullsDashboard.circuitBreaker.forceClosed: false
    hystrix.command.IndiaBullsCardMandate.circuitBreaker.forceClosed: false
    hystrix.command.IndiaBullsUserSignup.circuitBreaker.forceClosed: false
    hystrix.command.IndiaBullsUploadDocuments.circuitBreaker.forceClosed: false
    hystrix.command.IndiaBullsCreditAssessment.circuitBreaker.forceClosed: false
    hystrix.command.IndiaBullsCardCaptureValidation.circuitBreaker.forceClosed: false
    hystrix.command.IndiaBullsCardDetails.circuitBreaker.forceClosed: false
    hystrix.command.IndiaBullsPanNameService.circuitBreaker.forceClosed: false
    hystrix.command.AxisCbcCheckCohortEligibility.circuitBreaker.forceClosed: false
    hystrix.command.AxisCbcDemoCardConsent.circuitBreaker.forceClosed: false
    hystrix.command.AxisCbcDemoCardConsentEtbNpa.circuitBreaker.forceClosed: false
    hystrix.command.AxisCbcDemoCardConsentEtbCc.circuitBreaker.forceClosed: false
    hystrix.command.AxisCbcApplyCardConsent.circuitBreaker.forceClosed: false
    hystrix.command.AxisCbcApplyCardConsentEtbCc.circuitBreaker.forceClosed: false
    hystrix.command.AxisCbcApplyCardConsentNtb.circuitBreaker.forceClosed: false
    hystrix.command.AxisCbcApplyCardConsentEtbNpa.circuitBreaker.forceClosed: false
    hystrix.command.AxisCbcFetchCustDemogs.circuitBreaker.forceClosed: false
    hystrix.command.AxisCbcFetchCustDemogsEtbNpa.circuitBreaker.forceClosed: false
    hystrix.command.AxisCbcFetchCustDemogsEtbCc.circuitBreaker.forceClosed: false
    hystrix.command.AxisCbcProcessNewCard.circuitBreaker.forceClosed: false
    hystrix.command.AxisCbcProcessNewCardEtbNpa.circuitBreaker.forceClosed: false
    hystrix.command.AxisCbcProcessNewCardNtb.circuitBreaker.forceClosed: false
    hystrix.command.AxisCbcProcessUpgradeCard.circuitBreaker.forceClosed: false
    hystrix.command.AxisCbcCheckCaseStatus.circuitBreaker.forceClosed: false
    hystrix.command.AxisCbcCheckCardUpgradeStatus.circuitBreaker.forceClosed: false
    hystrix.command.AxisCbcLcmAuthOtpGen.circuitBreaker.forceClosed: false
    hystrix.command.AxisCbcLcmAuthOtpVal.circuitBreaker.forceClosed: false
    hystrix.command.AxisCbcLcmSendEmail.circuitBreaker.forceClosed: false
    hystrix.command.AxisCbcAccountSummary.circuitBreaker.forceClosed: false
    hystrix.command.AxisCbcLcmBlockCard.circuitBreaker.forceClosed: false
    hystrix.command.AxisCbcLcmGetCreditCardSettings.circuitBreaker.forceClosed: false
    hystrix.command.AxisCbcLcmUpdateCreditCardSettings.circuitBreaker.forceClosed: false
    hystrix.command.AxisCbcCheckCaseStatusMobile.circuitBreaker.forceClosed: false
    hystrix.command.AxisCbcCheckCaseStatusMobileEtbNpa.circuitBreaker.forceClosed: false
    hystrix.command.AxisCbcCheckCaseStatusMobileNtb.circuitBreaker.forceClosed: false
    hystrix.command.AxisCbcFetchStatement.circuitBreaker.forceClosed: false
    hystrix.command.AxisCbcStatementDates.circuitBreaker.forceClosed: false
    hystrix.command.AxisCbcLifeTimeCashBack.circuitBreaker.forceClosed: false
    hystrix.command.AxisVKYC.circuitBreaker.forceClosed: false
    hystrix.command.IdfcPanVerification.circuitBreaker.forceClosed: false
    hystrix.command.IdfcAccessTokenKey.circuitBreaker.forceClosed: false
    hystrix.command.IdfcGenerateOtpKey.circuitBreaker.forceClosed: false
    hystrix.command.IdfcVerifyOtpKey.circuitBreaker.forceClosed: false
    hystrix.command.IdfcImpsPennyDropKey.circuitBreaker.forceClosed: false
    hystrix.command.IdfcValidateUpiPennyDropKey.circuitBreaker.forceClosed: false
    hystrix.command.IdfcUpiPennyDropKey.circuitBreaker.forceClosed: false
    hystrix.command.IdfcCheckEligibilityKey.circuitBreaker.forceClosed: false
    hystrix.command.IdfcCreateLoanKey.circuitBreaker.forceClosed: false
    hystrix.command.IdfcUpdateLoanKey.circuitBreaker.forceClosed: false
    hystrix.command.IdfcCheckLoanStatusKey.circuitBreaker.forceClosed: false
    hystrix.command.IdfcLoanClosure.circuitBreaker.forceClosed: false
    hystrix.command.IdfcSearchCkycKey.circuitBreaker.forceClosed: false
    hystrix.command.IdfcDownloadCkycKey.circuitBreaker.forceClosed: false
    hystrix.command.AxisPl_CI_command.circuitBreaker.forceClosed: false
    hystrix.command.AxisPl_BP_command.circuitBreaker.forceClosed: true
    hystrix.command.AxisPl_GetOffer_command.circuitBreaker.forceClosed: false
    hystrix.command.AxisPl_SO_command.circuitBreaker.forceClosed: false
    hystrix.command.AxisPl_GS_command.circuitBreaker.forceClosed: false
    hystrix.command.AxisPl_FK_command.circuitBreaker.forceClosed: false
    hystrix.command.IdfcSaleForwardKey.circuitBreaker.forceClosed: false
    hystrix.command.IdfcSaleReverseKey.circuitBreaker.forceClosed: false
    hystrix.command.IdfcPaybackForwardKey.circuitBreaker.forceClosed: false
    hystrix.command.TeleSales.circuitBreaker.forceClosed: false
    hystrix.command:IdfcFfbUploadDocumentKey.circuitBreaker.forceClosed: false
    hystrix.command:IdfcSendKycDocument.circuitBreaker.forceClosed: false
    hystrix.command:IdfcEbcAuthTokenKey.circuitBreaker.forceClosed: false
    hystrix.command.ResumeFico.circuitBreaker.forceClosed: false
    hystrix.command.KotakPayLaterSaleForwardKey.circuitBreaker.forceClosed: false
    hystrix.command.KotakEMISaleForwardKey.circuitBreaker.forceClosed: false
    hystrix.command.KotakSaleReverseKey.circuitBreaker.forceClosed: false
    hystrix.command.KotakPaybackForwardKey.circuitBreaker.forceClosed: false
    hystrix.command.KotakPaybackReverseKey.circuitBreaker.forceClosed: false
    hystrix.command.KotakFreezeKey.circuitBreaker.forceClosed: false
    hystrix.command.KotakUnFreezeKey.circuitBreaker.forceClosed: false
    hystrix.command.KotakBillGenerationKey.circuitBreaker.forceClosed: false
    hystrix.command.KotakForwardFeeKey.circuitBreaker.forceClosed: false
    hystrix.command.KotakReverseFeeKey.circuitBreaker.forceClosed: false

  threadPoolProperties:
    hystrix.threadpool.AxisIncomeInitiate.coreSize: 10
    hystrix.threadpool.AxisIncomeInitiate.maximumSize: 10
    hystrix.threadpool.AxisIncomeInitiate.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.AxisIncomeGenerateToken.coreSize: 10
    hystrix.threadpool.AxisIncomeGenerateToken.maximumSize: 10
    hystrix.threadpool.AxisIncomeGenerateToken.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.AxisIncomeGetStatus.coreSize: 10
    hystrix.threadpool.AxisIncomeGetStatus.maximumSize: 10
    hystrix.threadpool.AxisIncomeGetStatus.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.IndiaBullsCardMandate.coreSize: 35
    hystrix.threadpool.IndiaBullsCardMandate.maximumSize: 35
    hystrix.threadpool.IndiaBullsCardMandate.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.IndiaBullsDashboard.coreSize: 20
    hystrix.threadpool.IndiaBullsDashboard.maximumSize: 20
    hystrix.threadpool.IndiaBullsDashboard.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.IndiaBullsUserSignup.coreSize: 80
    hystrix.threadpool.IndiaBullsUserSignup.maximumSize: 80
    hystrix.threadpool.IndiaBullsUserSignup.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.IndiaBullsUploadDocuments.coreSize: 150
    hystrix.threadpool.IndiaBullsUploadDocuments.maximumSize: 150
    hystrix.threadpool.IndiaBullsUploadDocuments.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.IndiaBullsCreditAssessment.coreSize: 100
    hystrix.threadpool.IndiaBullsCreditAssessment.maximumSize: 100
    hystrix.threadpool.IndiaBullsCreditAssessment.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.IndiaBullsCardCaptureValidation.coreSize: 50
    hystrix.threadpool.IndiaBullsCardCaptureValidation.maximumSize: 50
    hystrix.threadpool.IndiaBullsCardCaptureValidation.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.IndiaBullsCardDetails.coreSize: 35
    hystrix.threadpool.IndiaBullsCardDetails.maximumSize: 35
    hystrix.threadpool.IndiaBullsCardDetails.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.IndiaBullsPanNameService.coreSize: 35
    hystrix.threadpool.IndiaBullsPanNameService.maximumSize: 35
    hystrix.threadpool.IndiaBullsPanNameService.allowMaximumSizeToDivergeFromCoreSize:   false
    hystrix.threadpool.AxisCbcCheckCohortEligibility.coreSize: 100
    hystrix.threadpool.AxisCbcCheckCohortEligibility.maximumSize: 100
    hystrix.threadpool.AxisCbcCheckCohortEligibility.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.AxisCbcDemoCardConsent.coreSize: 100
    hystrix.threadpool.AxisCbcDemoCardConsent.maximumSize: 100
    hystrix.threadpool.AxisCbcDemoCardConsent.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.AxisCbcDemoCardConsentEtbNpa.coreSize: 100
    hystrix.threadpool.AxisCbcDemoCardConsentEtbNpa.maximumSize: 100
    hystrix.threadpool.AxisCbcDemoCardConsentEtbNpa.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.AxisCbcDemoCardConsentEtbCc.coreSize: 100
    hystrix.threadpool.AxisCbcDemoCardConsentEtbCc.maximumSize: 100
    hystrix.threadpool.AxisCbcDemoCardConsentEtbCc.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.AxisCbcApplyCardConsent.coreSize: 50
    hystrix.threadpool.AxisCbcApplyCardConsent.maximumSize: 50
    hystrix.threadpool.AxisCbcApplyCardConsent.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.AxisCbcApplyCardConsentNtb.coreSize: 50
    hystrix.threadpool.AxisCbcApplyCardConsentNtb.maximumSize: 50
    hystrix.threadpool.AxisCbcApplyCardConsentNtb.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.AxisCbcApplyCardConsentEtbNpa.coreSize: 50
    hystrix.threadpool.AxisCbcApplyCardConsentEtbNpa.maximumSize: 50
    hystrix.threadpool.AxisCbcApplyCardConsentEtbNpa.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.AxisCbcApplyCardConsentEtbCc.coreSize: 50
    hystrix.threadpool.AxisCbcApplyCardConsentEtbCc.maximumSize: 50
    hystrix.threadpool.AxisCbcApplyCardConsentEtbCc.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.AxisCbcFetchCustDemogs.coreSize: 100
    hystrix.threadpool.AxisCbcFetchCustDemogs.maximumSize: 100
    hystrix.threadpool.AxisCbcFetchCustDemogs.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.AxisCbcFetchCustDemogsEtbCc.coreSize: 100
    hystrix.threadpool.AxisCbcFetchCustDemogsEtbCc.maximumSize: 100
    hystrix.threadpool.AxisCbcFetchCustDemogsEtbCc.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.AxisCbcProcessNewCard.coreSize: 50
    hystrix.threadpool.AxisCbcProcessNewCard.maximumSize: 50
    hystrix.threadpool.AxisCbcProcessNewCard.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.AxisCbcProcessNewCardNtb.coreSize: 50
    hystrix.threadpool.AxisCbcProcessNewCardNtb.maximumSize: 50
    hystrix.threadpool.AxisCbcProcessNewCardNtb.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.AxisCbcProcessNewCardEtbNpa.coreSize: 50
    hystrix.threadpool.AxisCbcProcessNewCardEtbNpa.maximumSize: 50
    hystrix.threadpool.AxisCbcProcessNewCardEtbNpa.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.AxisCbcProcessUpgradeCard.coreSize: 50
    hystrix.threadpool.AxisCbcProcessUpgradeCard.maximumSize: 50
    hystrix.threadpool.AxisCbcProcessUpgradeCard.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.AxisCbcCheckCaseStatus.coreSize: 50
    hystrix.threadpool.AxisCbcCheckCaseStatus.maximumSize: 50
    hystrix.threadpool.AxisCbcCheckCaseStatus.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.AxisCbcCheckCardUpgradeStatus.coreSize: 50
    hystrix.threadpool.AxisCbcCheckCardUpgradeStatus.maximumSize: 50
    hystrix.threadpool.AxisCbcCheckCardUpgradeStatus.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.AxisCbcLcmAuthOtpGen.coreSize: 50
    hystrix.threadpool.AxisCbcLcmAuthOtpGen.maximumSize: 50
    hystrix.threadpool.AxisCbcLcmAuthOtpGen.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.AxisCbcLcmAuthOtpVal.coreSize: 50
    hystrix.threadpool.AxisCbcLcmAuthOtpVal.maximumSize: 50
    hystrix.threadpool.AxisCbcLcmAuthOtpVal.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.AxisCbcCheckCaseStatusMobile.coreSize: 100
    hystrix.threadpool.AxisCbcCheckCaseStatusMobile.maximumSize: 100
    hystrix.threadpool.AxisCbcCheckCaseStatusMobile.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.AxisCbcCheckCaseStatusMobileEtbNpa.coreSize: 100
    hystrix.threadpool.AxisCbcCheckCaseStatusMobileEtbNpa.maximumSize: 100
    hystrix.threadpool.AxisCbcCheckCaseStatusMobileEtbNpa.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.AxisCbcCheckCaseStatusMobileNtb.coreSize: 100
    hystrix.threadpool.AxisCbcCheckCaseStatusMobileNtb.maximumSize: 100
    hystrix.threadpool.AxisCbcCheckCaseStatusMobileNtb.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.IdfcPanVerification.coreSize: 100
    hystrix.threadpool.IdfcPanVerification.maximumSize: 100
    hystrix.threadpool.IdfcPanVerification.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.IdfcAccessTokenKey.coreSize: 50
    hystrix.threadpool.IdfcAccessTokenKey.maximumSize: 50
    hystrix.threadpool.IdfcAccessTokenKey.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.IdfcGenerateOtpKey.coreSize: 100
    hystrix.threadpool.IdfcGenerateOtpKey.maximumSize: 100
    hystrix.threadpool.IdfcGenerateOtpKey.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.IdfcVerifyOtpKey.coreSize: 100
    hystrix.threadpool.IdfcVerifyOtpKey.maximumSize: 100
    hystrix.threadpool.IdfcVerifyOtpKey.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.IdfcImpsPennyDropKey.coreSize: 100
    hystrix.threadpool.IdfcImpsPennyDropKey.maximumSize: 100
    hystrix.threadpool.IdfcImpsPennyDropKey.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.IdfcValidateUpiPennyDropKey.coreSize: 100
    hystrix.threadpool.IdfcValidateUpiPennyDropKey.maximumSize: 100
    hystrix.threadpool.IdfcValidateUpiPennyDropKey.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.IdfcUpiPennyDropKey.coreSize: 100
    hystrix.threadpool.IdfcUpiPennyDropKey.maximumSize: 100
    hystrix.threadpool.IdfcUpiPennyDropKey.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.IdfcCheckEligibilityKey.coreSize: 50
    hystrix.threadpool.IdfcCheckEligibilityKey.maximumSize: 50
    hystrix.threadpool.IdfcCheckEligibilityKey.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.IdfcCreateLoanKey.coreSize: 50
    hystrix.threadpool.IdfcCreateLoanKey.maximumSize: 50
    hystrix.threadpool.IdfcCreateLoanKey.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.IdfcUpdateLoanKey.coreSize: 50
    hystrix.threadpool.IdfcUpdateLoanKey.maximumSize: 50
    hystrix.threadpool.IdfcUpdateLoanKey.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.IdfcCheckLoanStatusKey.coreSize: 50
    hystrix.threadpool.IdfcCheckLoanStatusKey.maximumSize: 50
    hystrix.threadpool.IdfcCheckLoanStatusKey.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.IdfcLoanClosure.coreSize: 50
    hystrix.threadpool.IdfcLoanClosure.maximumSize: 50
    hystrix.threadpool.IdfcLoanClosure.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.IdfcSearchCkycKey.coreSize: 50
    hystrix.threadpool.IdfcSearchCkycKey.maximumSize: 50
    hystrix.threadpool.IdfcSearchCkycKey.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.IdfcDownloadCkycKey.coreSize: 50
    hystrix.threadpool.IdfcDownloadCkycKey.maximumSize: 50
    hystrix.threadpool.IdfcDownloadCkycKey.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.IdfcSaleForwardKey.coreSize: 50
    hystrix.threadpool.IdfcSaleForwardKey.maximumSize: 50
    hystrix.threadpool.IdfcSaleForwardKey.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.IdfcSaleReverseKey.coreSize: 50
    hystrix.threadpool.IdfcSaleReverseKey.maximumSize: 50
    hystrix.threadpool.IdfcSaleReverseKey.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.IdfcPaybackForwardKey.coreSize: 50
    hystrix.threadpool.IdfcPaybackForwardKey.maximumSize: 50
    hystrix.threadpool.IdfcPaybackForwardKey.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.TeleSales.coreSize: 50
    hystrix.threadpool.TeleSales.maximumSize: 50
    hystrix.threadpool.TeleSales.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.AxisCbcFetchStatement.coreSize: 50
    hystrix.threadpool.AxisCbcFetchStatement.maximumSize: 50
    hystrix.threadpool.AxisCbcFetchStatement.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.AxisCbcStatementDates.coreSize: 50
    hystrix.threadpool.AxisCbcStatementDates.maximumSize: 50
    hystrix.threadpool.AxisCbcStatementDates.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.AxisCbcLifeTimeCashBack.coreSize: 50
    hystrix.threadpool.AxisCbcLifeTimeCashBack.maximumSize: 50
    hystrix.threadpool.AxisCbcLifeTimeCashBack.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.AxisVKYC.coreSize: 50
    hystrix.threadpool.AxisVKYC.maximumSize: 50
    hystrix.threadpool.AxisVKYC.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.IdfcFfbUploadDocumentKey.coreSize: 50
    hystrix.threadpool.IdfcFfbUploadDocumentKey.maximumSize: 50
    hystrix.threadpool.IdfcFfbUploadDocumentKey.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.IdfcSendKycDocument.coreSize: 10
    hystrix.threadpool.IdfcSendKycDocument.maximumSize: 10
    hystrix.threadpool.IdfcSendKycDocument.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.IdfcEbcAuthTokenKey.coreSize: 10
    hystrix.threadpool.IdfcEbcAuthTokenKey.maximumSize: 10
    hystrix.threadpool.IdfcEbcAuthTokenKey.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.IdfcResumeFicoKey.coreSize: 50
    hystrix.threadpool.IdfcResumeFicoKey.maximumSize: 50
    hystrix.threadpool.IdfcResumeFicoKey.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.AxisPl_CI_threadPool.coreSize: 10
    hystrix.threadpool.AxisPl_CI_threadPool.maximumSize: 10
    hystrix.threadpool.AxisPl_CI_threadPool.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.AxisPl_BP_threadPool.coreSize: 20
    hystrix.threadpool.AxisPl_BP_threadPool.maximumSize: 20
    hystrix.threadpool.AxisPl_BP_threadPool.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.AxisPl_GetOffer_threadPool.coreSize: 10
    hystrix.threadpool.AxisPl_GetOffer_threadPool.maximumSize: 10
    hystrix.threadpool.AxisPl_GetOffer_threadPool.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.AxisPl_SO_threadPool.coreSize: 10
    hystrix.threadpool.AxisPl_SO_threadPool.maximumSize: 10
    hystrix.threadpool.AxisPl_SO_threadPool.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.AxisPl_GS_threadPool.coreSize: 10
    hystrix.threadpool.AxisPl_GS_threadPool.maximumSize: 10
    hystrix.threadpool.AxisPl_GS_threadPool.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.AxisPl_FK_threadPool.coreSize: 10
    hystrix.threadpool.AxisPl_FK_threadPool.maximumSize: 10
    hystrix.threadpool.AxisPl_FK_threadPool.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.KotakPayLaterSaleForwardKey.coreSize: 50
    hystrix.threadpool.KotakPayLaterSaleForwardKey.maximumSize: 50
    hystrix.threadpool.KotakPayLaterSaleForwardKey.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.KotakEMISaleForwardKey.coreSize: 50
    hystrix.threadpool.KotakEMISaleForwardKey.maximumSize: 50
    hystrix.threadpool.KotakEMISaleForwardKey.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.KotakSaleReverseKey.coreSize: 50
    hystrix.threadpool.KotakSaleReverseKey.maximumSize: 50
    hystrix.threadpool.KotakSaleReverseKey.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.KotakPaybackForwardKey.coreSize: 50
    hystrix.threadpool.KotakPaybackForwardKey.maximumSize: 50
    hystrix.threadpool.KotakPaybackForwardKey.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.KotakPaybackReverseKey.coreSize: 50
    hystrix.threadpool.KotakPaybackReverseKey.maximumSize: 50
    hystrix.threadpool.KotakPaybackReverseKey.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.KotakFreezeKey.coreSize: 50
    hystrix.threadpool.KotakFreezeKey.maximumSize: 50
    hystrix.threadpool.KotakFreezeKey.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.KotakUnFreezeKey.coreSize: 50
    hystrix.threadpool.KotakUnFreezeKey.maximumSize: 50
    hystrix.threadpool.KotakUnFreezeKey.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.KotakBillGenerationKey.coreSize: 50
    hystrix.threadpool.KotakBillGenerationKey.maximumSize: 50
    hystrix.threadpool.KotakBillGenerationKey.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.KotakForwardFeeKey.coreSize: 50
    hystrix.threadpool.KotakForwardFeeKey.maximumSize: 50
    hystrix.threadpool.KotakForwardFeeKey.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.KotakReverseFeeKey.coreSize: 50
    hystrix.threadpool.KotakReverseFeeKey.maximumSize: 50
    hystrix.threadpool.KotakReverseFeeKey.allowMaximumSizeToDivergeFromCoreSize: false

lenderErrorConfig:
  errorMapping:
    INDIA_BULLS : ["Failure:FAILURE","E:VALID_PAN","N:INVALID_PAN","PAN_FETCH_EXCEPTION:FAILED_PAN_RESPONSE","APPLY_NOW_FETCH_EXCEPTION:FAILURE"]


heliosProxyConfig:
  maxConnections: 1024
  maxConnectionsPerHost: 1024
  pooledConnectionIdleTimeoutInMS: 6000
  executorThreadsCount: 32
  executionIsolationSemaphoreMaxConcurrentRequests: 1024
  executionTimeoutInMS: 10000
  hystrixCommandKey: PandoraServiceHystrixCommand
  hystrixGroupKey: PandoraServiceHystrixGroup
  rewriteProtocal: http
  rewriteHostName: 127.0.0.1
  rewritePortNumber: 8214
  enableProxy: false


teleSalesConfiguration:
  createUrl: https://tps.intelenetglobal.com/FlipKart_Hulk/RestServiceImpl.svc/pushData
  updateUrl: https://tps.intelenetglobal.com/FlipKart_Hulk/RestServiceImpl.svc/UpdateData
  aesKey: 31A5A90F993901F784C4563D3E59EA4F
  aesIv: 528AE4EC7AAD236C
  aesSalt: 3289138030CBC0BD
  authKey: FlipKartAuthkey
  authValue: ZiPfoxlkowlQ4Ek

authConfig:
  authEnabled: true
  authnUrl: "https://service.authn-prod.fkcloud.in/v3"
  clientId: "sm-pandora-preprod"
  clientSecret: "8fuPeGmzCVmDvyAXLyVgaamBB3i4WJfVm8vBij9KNTrYITFb"
  authIgnoreUrls: "(?!(/1/ubona/cancelCall|/1/ubona/makeCall).*$).*$"
  saveRequest: false
  loginUrl: null

cryptexBundleConfiguration:
  cryptexBundleEnabled: true
  authNClientConfig:
    url: https://service.authn-prod.fkcloud.in/v3
    clientId: sm-pandora-preprod
    clientSecret: 8fuPeGmzCVmDvyAXLyVgaamBB3i4WJfVm8vBij9KNTrYITFb

  cryptexClientConfig:
    endpoint: https://service.cryptex-prod.fkcloud.in
    maxConnections: 5
    connectTimeOut: 1500
    readTimeOut: 1500

  dynamicBucketConfig:
    bucketName: sm-pandora-preprod
    enableLocalDynamicBucket: false

documentPropertyTypeList:
  - name: CF_DocumentName
    type: DOCUMENT_NAME
  - name: DocumentTitle
    type: DOCUMENT_TITLE
  - name: CF_DocumentTypeName
    type: DOCUMENT_TYPE
  - name: CF_FinnOneLoanNumber
    type: LOAN_NUMBER

idfcFfbAuthConfiguration:
  proxyUri: https://muleuat.capitalfirst.com:1447
  pennyDropUri: https://muleuat.capitalfirst.com:1447
  grantType: "client_credentials"
  clientId: kflpkrt-1a35-46fb-96b5-5f3929ba7381
  clientSecret: kflpkrt-85a9-40a1-b132-4faf2c70baa6

multiTenantOAuthClientConfig:
  cachedAccessTokenTTL : 120
  oAuthUrl: http://************:80
  oAuthServiceClientConfigMap:
    FK_CONSUMER_CREDIT:
      oAuthClientID: c2c95e0075c04b2e9b83e1bc8a09f57e
      oAuthClientSecret: FVMWGlJ22AoWfQ+ithjYn+M0tjr8xgakuHl06J5A+DELtVtc
      cachedAccessTokenTTL : 120
      oAuthUrl: http://************:80
    FK_INSURETECH:
      oAuthClientID: 25c914543d954b40919162e5b662058d
      oAuthClientSecret: IiNlp7tEJck+R/TsY4UnjmZ+3x/sIG9XVGy/t6a1Hy731DKW
      cachedAccessTokenTTL: 120
      oAuthUrl: http://************:80

fintechUserServiceTenantMap:
  FK_CONSUMER_CREDIT: FLIPKART
  FFB: FFB

businessCategorySchemeIdMap:
  grocery: 58901
  fashion: 58829

quessConfiguration:
  channelId: "FK"
  encryptionKey: xxxxxxxxxxxxxxxxxxx
  url: https://sakshamuat.axisbank.co.in
  mockEnabled: false
  client: xxxxxxxxxxxxxxxxxxx
  secretKey: xxxxxxxxxxxxxxxxxxx
  username: xxxxxxxxxxxxxxxxxxx
  password: xxxxxxxxxxxxxxxxxxx
  contextMap:
    login_token:
      maximumSize: 10000
      duration: 3600
      durationUnit: SECONDS
  axisCbcApiConfigurationsMap:
    userLogin:
      apiPath: "gateway/api/kyc/v1/user-login"
      serviceRequestId: "QUESS"
      serviceRequestVersion: "1.0"
    slotCapacity:
      apiPath: "gateway/api/kyc/v1/slot-capacity"
      serviceRequestId: "QUESS"
      serviceRequestVersion: "1.0"
    scheduleCapacity:
      apiPath: "gateway/api/kyc/v1/reserve-slot"
      serviceRequestId: "QUESS"
      serviceRequestVersion: "1.0"
    getSchedule:
      apiPath: "gateway/api/kyc/v1/get-schedule-details"
      serviceRequestId: "QUESS"
      serviceRequestVersion: "1.0"
    updateSchedule:
      apiPath: "gateway/api/kyc/v1/update-slot"
      serviceRequestId: "QUESS"
      serviceRequestVersion: "1.0"
    cancelSchedule:
      apiPath: "gateway/api/kyc/v1/cancel-slot"
      serviceRequestId: "QUESS"
      serviceRequestVersion: "1.0"

encryptionKeys:
  kycKey: 03A1CE01ED4D5FA1ABBDCD728C0BE2A8
  phoenixEncryptionKey: eyJkYXRhIjoidmF1bHQ6djE6aDFqNG1JNC9pWElQL3dFb042QjNzc3ZPd2owaVpSSFZiR25PZ3RFM2phTTNHd0FyWEx3YlJ1emJsWXJpczJieFJrV1VaUHV3ZGJpQXd3Y0QiLCJrZXlJZCI6ImNmZy1zdmM6OmluLWh5ZGVyYWJhZC0xLXByZXByb2Q6cGFuZG9yYS1wcmVwcm9kIn0=
  cbcKey: 59AC6C2B95DEFC3EC76C56CF232AF829

ebcClientConfig:
  encryptionSecretKey: eyJkYXRhIjoidmF1bHQ6djE6NEd2U0puOUJhaG05UGdaYkRwNXZ6aEtMWDN0ZHloV0JXNmpNWmVMOXJmdzZ2aEc2aTFUcDhpRnYxN1U9Iiwia2V5SWQiOiJjZmctc3ZjOjppbi1oeWRlcmFiYWQtMS1wcmVwcm9kOnBhbmRvcmEtcHJlcHJvZCJ9
  fkEncryptionKey: eyJkYXRhIjoidmF1bHQ6djE6ZXdxdU5uWVF6ODFNaGpuYnc5U2tENTBzZjFNWE5TQjhwNE5QdVZESmlsTld6TG9wOEVIcnhqOWZkdGNvVlAyUE5KMDVhVGxsU0hUcC9qazUiLCJrZXlJZCI6ImNmZy1zdmM6OmluLWh5ZGVyYWJhZC0xLXByZXByb2Q6cGFuZG9yYS1wcmVwcm9kIn0=
  ebcTokenUriHost: https://muleuat.capitalfirst.com:8444
  ebcAuthPassword: eyJkYXRhIjoidmF1bHQ6djE6OUJFeG1zWGc4NkhDVE83UE9WTXpIYm81WjZ1cVVqVzBsL3JUb0NTSVdBWk9TLzUxckt3PSIsImtleUlkIjoiY2ZnLXN2Yzo6aW4taHlkZXJhYmFkLTEtcHJlcHJvZDpwYW5kb3JhLXByZXByb2QifQ==
  ebcAuthUsername: eyJkYXRhIjoidmF1bHQ6djE6d2RTUG1lMUNucXdrV1RiazMyTGFtbEVnY21vSzVDN3UzbFl3NU01eW9LQm1sQnJ1ZUxtcFl3PT0iLCJrZXlJZCI6ImNmZy1zdmM6OmluLWh5ZGVyYWJhZC0xLXByZXByb2Q6cGFuZG9yYS1wcmVwcm9kIn0=
  environment: QA

datafeedConfiguration:
  url: http://127.0.0.1
  clientId: "fintech-datafeed-service"

phoenixClientConfig:
  url: http://**********
  clientId: pandora

experianConfig:
  authenticationUrl: "https://uat-in-api.experian.com/oauth2/v1/token"
  enhanceMatchUrl: "https://uat-in-api.experian.com/ecs/ecv-api/v1/enhanced-match"
  credentials:
    username: "<EMAIL>"
    password: "SupEngg@123"
    clientId: "pIgdZFk9Wnzb7vG57XsZG7L6zk9UNmjG"
    clientSecret: "FXVrgFVuoPoLKBB1"
    voucherCode: "Super_MoneyiyJ48"


rotation:
  enableRotator: true
  defaultRotationStatus: true

hystrixModuleConfig:
  hystrixPropertiesFileName: hystrix.properties
  resourcesFilePath: /etc/config/

idfcFetchTokenDelayInMinutes: 2

fsupTransactionFlag: true

gibraltarClientConfig:
  host: https://**********
  keySpaceName: onboarding
  clientName: efa-onboarding-client
  targetClientId: gibraltar-preprod
  maxPublicKeyCount: 100

tijoriClientConfig:
  endpoint: http://************:80
  clientId: pandora
  authClientId: tijori-service
  connectTimeout: 10000
  readTimeout: 10000

razorpayConfiguration:
  url: https://api.razorpay.com
  username: eyJkYXRhIjoidmF1bHQ6djE6cWswdVg4QjJ6ME5GdElPSjM3VDVoRnE2Y2dUZEk1K21LTWRvL2Z2Ny8rbjV6UDNZTDdGQmQ5ZGE1bmZjY0FmYUFmWjkiLCJrZXlJZCI6ImNmZy1zdmM6OmluLWh5ZGVyYWJhZC0xLXByZXByb2Q6cGFuZG9yYS1wcmVwcm9kIn0=
  password: eyJkYXRhIjoidmF1bHQ6djE6M1JsQXE2OS9XdnhqdXdReW9wR29kM3VtUGtJTWFTckpvRXVXbHFQZ2hoaXRmMmo1OEk1eWFmTkdjZVJKZmIzL1VVWXVDZz09Iiwia2V5SWQiOiJjZmctc3ZjOjppbi1oeWRlcmFiYWQtMS1wcmVwcm9kOnBhbmRvcmEtcHJlcHJvZCJ9
  accountNumber: eyJkYXRhIjoidmF1bHQ6djE6dW95L0tNRnhKUm9jRjJXUkZuN09TeThSMUdoMVJiQTRURkFZUnBOQ05nUGpTbGJ5c0hibzZzZnlLajA9Iiwia2V5SWQiOiJjZmctc3ZjOjppbi1oeWRlcmFiYWQtMS1wcmVwcm9kOnBhbmRvcmEtcHJlcHJvZCJ9

collectionsConfiguration:
  url: http://***********:7980/bnpl-collections

winterfellClientConfig:
  host: http://winterfell-service-next.sm-winterfell-prod.fkcloud.in/fintech-winterfell
  clientId: stratum
  connectionTimeout: 30000
  readTimeout: 10000

abConfiguration:
  endpoint: ***********:80
  layerList: "Affordability"
  clientId: "Mapi.affordability"
  testAccounts: [ ]
  unlayeredAbEnabled: true
  clientSecretKey: fintech-pandora-prod-1e51dca49a4943b4b344fb90eeed5f5b

eventPublishEnvironment: "PREPROD"

bigfootEntityConfig:
  plOfferConfig:
    name: sm_pl_offer_detail
    schemaVersion: 2.0

bigfootConfiguration:
  url: http://************:28223
  poolName: pool
  company: fkint
  org: cp
  namespace: affordability
  exchangeType: queue
  batchIngestionSize: 22
  exchangeName: advanz_ingestion_queue


shylockClientConfiguration:
  endPoint: http://*********:8180

coreLogisticsClientConfig:
  url: http://**********
  referer: "http://www.fintech.com"
  key: "79a271ce-b870-c03b-cdbb-a52861a90ea9"

creditSaisonConfig:
  initialOfferUrl: "https://api.int.creditsaison.in/api/v2/realtime/underwriting"

boomerangClientConfig:
  host: "http://boomerang-service.sumo-boomerang-prod.fkcloud.in/boomerang"

axisPlConfig:
  keyStorePath: "/etc/axis/preprod-client-keystore.jks"
  keyStorePass: supermoney
  trustStorePath: "/etc/axis/preprod-keystore-2412.jks"
  trustStorePass: password
  proxyUri: https://sakshamuat.axisbank.co.in:443

smKotakClientConfiguration:
  host: https://811appumami.uat.kotak811.com/apigw2/leads
  encryptionKey: "QnTG3QxGIYvzwHg4hb3e5SvGL50i2cTT"
  mockTestingON: true
  partnerId: SuperMoney
  apiKey: sXJxrZvXfe87NliUJGGwC3pi60qSgOep1xAzQASf

kotakClientConfiguration:
  host: https://apigwuat.kotak.com:8443
  clientId: l78dae4a3b52224584bc0382fd1a8d3ebe
  clientSecret: 56bca9ec1e48433fbcb21f3106ec1480
  grantType: client_credentials
  encryptionKey: 56bca9ec1e48433fbcb21f3106ec1480

kotakConfiguration:
  proxyUri: https://kotak-bnpl.finfluxdemo.io
  accessTokenPath: /fineract-provider/api/oauth/token
  clientId: api-app
  grantType: password
  username: post
  password: Fin@123
  isPasswordEncrypted: False
  payLaterSalePath: "/fineract-provider/api/v1/revolving-credit-lines/%s/transactions/payout"
  emiSalePath: "/fineract-provider/api/v2/revolving-credit-lines/%s/transactions/flexi-pay"
  saleReversePath: "/fineract-provider/api/v2/revolving-credit-lines/%s/transactions/%s/paylater-return"
  forwardPaybackPath: "/fineract-provider/api/v2/revolving-credit-lines/%s/transactions/payin"
  reversePaybackPath: "/fineract-provider/api/v1/revolving-credit-lines/%s/transactions/excess-refund"
  freezeCreditLinePath: "/fineract-provider/api/v1/revolving-credit-lines/%s/freeze"
  unFreezeCreditLinePath: "/fineract-provider/api/v1/revolving-credit-lines/%s/unfreeze"
  billGenerationPath: "/fineract-provider/api/v2/revolving-credit-lines/bill"
  forwardFeePath: "/fineract-provider/api/v2/revolving-credit-lines/%s/charges"
  reverseFeePath: "/fineract-provider/api/v1/revolving-credit-lines/%s/account-charges/%s/waive"
hyperVergeConfiguration:
  appId: sv1tsh
  appKey: gyc59wie516z8e9scicm
  authUrl: https://auth.hyperverge.co
  apiUrl: https://ind.idv.hyperverge.co

documentStoreConfiguration:
  accessKey: GOOG1E42KDDZR4BDJO4RXCEMLX2RMZTTE7EUXMDNW4ENC2VJSLNFXPYKBLZU6
  secretKey: 9qDg6wG5wW0s/BuznjennwsoxUL0vnpi2qg0ZYow
  endPoint: https://storage.googleapis.com
  maxConnections: 10
  connectionTimeout: 2000
  kycBucketName: fk-p-calm-kyc_image-docs
  selfieBucketName: fk-p-calm-selfie_image-docs

sandboxLendersConfiguration:
  moneyViewConfiguration:
    scope: LENDING_MONEYVIEW
    host: https://growth-01.stg.whizdm.com/atlas
    tokenEndpoint: /lender/1/token
    username: supermoneypa
    password: $X"Hi3uAW1
    clientId: 194
    authCode: super123
    lenderBase64PublicKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAoRKasnuPfAsNP9Wrq8TloamCI/jP5mkgX/zXK/w3qiZLxibz37wAaxeFxpuhnfKH+B5s8ujUNVHGBFgWS/LAC6xEsQKH29ohYOtgYbnC/LowKTCx97hpP5edAomNqtGdNkGpyvfPjaIECmV3mWhekSsZt0VAJKWOM3U8TAJaoh3dnr7udhJ5PDSXG6y5uqApL49Op276wriijAmQXaaNk6ZAxZwXg1FCWfOAFIVok1pQh6Ho2su4UvSqozhatsKyEzKFem8O9JWDCs2a7MtbbUpVWuTb7NxAoy7PpWWGkvt8uRAPKi4BDIHwhn0D+9MV54+Ftkg8hDZ6ogWinBM/CwIDAQAB
    lspBase64PrivateKey: MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQDh3CHD7n/QENLd+4MyMtqLcqLHpsoTwIqHO2dAHV1kLV5eAognJXOWZRNZFvRs7Er5X4VxKVGw/cQj3+zSJF02d0wn5j/Wy4mJlnqr1POgqcxfjDMQV+BCPUj1UpNxRPOD8qnRAaO9g5NzPfkWo6oVAerOk19Gf21/u4D2odZDLxME68380eugKJ631QxoS3RjLbdoMWh/R9uHhb3xpIM6QEfsV2WSMnQpNgYD26LEWKk7gA/c0+WDLGJnA7d/9bchpAcLM69Ej8G6HG6y4l5IoWRbA57AbY1SOBpi74NTr2lj/lJURlEj8WUVfdUdGgyHagV6UEIEHcGKDmz1kkeXAgMBAAECggEABKcKMwUIsPqHvXbe3vvZMKIt5Dr1Uq7Wq653lASG1fdxSDJ5J4VH+kMewOkOI+hCKxzinNAjFKhFkA+eyz1ghiyy7ud5TS1oKeNlp4B9fEPDJ/y1WJLW1cZ5aERJMT4gXglzohCMlOzO7sLKDMNWT5R4h8TVY5hgYw8zuuDejwexS9Rmnh32FAOlDXoIAHBHQo2kZrCm385f5qNPVIo07061+Err/FkFi/ilGKQwuLsO3it4UY+m+JcL02GKoi9UgS3/sh1kdcSty7kMx5A1NKyUffGPilz26d9weypLBHt36ZIoULtQN6zzk0El7oTmYppDDGRAMaZGyVRLqnvhoQKBgQD7y8M9biQM8fkCk8ACbANiHUhlQWyuyFImhvUBQzt0SMf9gOjQQ80n5XlMcGSaRC8KnNxSwbsd0vGh1gz+eHA8l7lGjL09Aj90BPjqVfs3YH7ueOZ/6yKM2X5Y3mqwhW0yS8nu3VWZHTJqB5c6ZEJAgkFspcJvPNwuDN0Hy4XOCQKBgQDloYMfcJug3aVbdEeUU3VCqRczv2kG/ylXgy4iuwOEOfnBURgPL90p8WbPVEOTIob+VuVCB8KWVriWYzGBVISW4vKM5KgFMQfym/J/UPVrAZXp1ljDER7EArWEV56UFLVvaDcQnhMNLBIuNxpWhLoLEk9Y/aWbC9AYTfWpqZDQnwKBgC4sdYckVXQSQ2mXkpRfHle1qvr2fmmmznnGMdOjcpmmZxYzt33JkSuJxy6fRbOMYVv+2+BJKnkTn7/fss5PY7WY1R783A7+BIjQCJ1gWwsSpPQyM4Ktqc5Oa/77+nCYZyfcAhHNgM7tLbuH3NGWZRfTSxATbY5ciLvhG/878TORAoGAWJ54SA3hQbfINP8vEKMUweEXvzTDh5u5jLhneafKt6DYLi7pphLGu4UaNDFE0uekSUuntk/gXLyjCAbH+C1x7fkvuggUf8xoUMy2TUQlMmKuREwgfUeCJAsXmjdmT6eeTZxeg989Yvv7GDLTy0f1tIggbyuUWb25VWfgwtVZuekCgYBX3BsG66HhkFvvVGguzajwJZwobsH07gEeRyvj+aWxTmU9V1QjCVi+gDWYeypXogiCKoneJ1cxIT5K5B5EQHpxRgYs8jsaPIZAe5wbxTJRLuPUryYJr27+CmhO/WWGDexmIWGkCKzfPECaOsWcwqDbKHKiQE87A5G2pLXlFQME6g==
    apiModel:
      GET_APPLICATION:
        endpoint: /lender/1/getApplication
        httpTimeout: 2500
        httpRetryCount: 0
      CHECK_DEDUPE_V2:
        endpoint: /v1/lead/hash-dedupe
        httpTimeout: 2500
        httpRetryCount: 0
      CREATE_APPLICATION_V2:
        endpoint: /lender/2.0/createApplication
        httpTimeout: 3000
        httpRetryCount: 0
      GENERATE_OFFER:
        endpoint: /lender/2.0/generateOffer
        httpTimeout: 2500
        httpRetryCount: 0
      SUBMIT_OFFER:
        endpoint: /lender/2.0/submitOffer
        httpTimeout: 15000
        httpRetryCount: 0
      INIT_AA:
        endpoint: /lender/2.0/initAccountAggregator
        httpTimeout: 2500
        httpRetryCount: 0
      GET_OFFER:
        endpoint: /lender/2.0/getOffer
        httpTimeout: 2500
        httpRetryCount: 0
      GET_APPLICATION_STATUS_V2:
        endpoint: /lender/2.0/getApplicationStatus
        httpTimeout: 1500
        httpRetryCount: 0
  moneyViewMfiConfiguration:
    scope: LENDING_MONEYVIEWMFI
    host: https://growth-01.stg.whizdm.com/atlas
    tokenEndpoint: /lender/1/token
    username: supermoneypa
    password: $X"Hi3uAW1
    clientId: 194
    authCode: super123
    lenderBase64PublicKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAoRKasnuPfAsNP9Wrq8TloamCI/jP5mkgX/zXK/w3qiZLxibz37wAaxeFxpuhnfKH+B5s8ujUNVHGBFgWS/LAC6xEsQKH29ohYOtgYbnC/LowKTCx97hpP5edAomNqtGdNkGpyvfPjaIECmV3mWhekSsZt0VAJKWOM3U8TAJaoh3dnr7udhJ5PDSXG6y5uqApL49Op276wriijAmQXaaNk6ZAxZwXg1FCWfOAFIVok1pQh6Ho2su4UvSqozhatsKyEzKFem8O9JWDCs2a7MtbbUpVWuTb7NxAoy7PpWWGkvt8uRAPKi4BDIHwhn0D+9MV54+Ftkg8hDZ6ogWinBM/CwIDAQAB
    lspBase64PrivateKey: MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQDh3CHD7n/QENLd+4MyMtqLcqLHpsoTwIqHO2dAHV1kLV5eAognJXOWZRNZFvRs7Er5X4VxKVGw/cQj3+zSJF02d0wn5j/Wy4mJlnqr1POgqcxfjDMQV+BCPUj1UpNxRPOD8qnRAaO9g5NzPfkWo6oVAerOk19Gf21/u4D2odZDLxME68380eugKJ631QxoS3RjLbdoMWh/R9uHhb3xpIM6QEfsV2WSMnQpNgYD26LEWKk7gA/c0+WDLGJnA7d/9bchpAcLM69Ej8G6HG6y4l5IoWRbA57AbY1SOBpi74NTr2lj/lJURlEj8WUVfdUdGgyHagV6UEIEHcGKDmz1kkeXAgMBAAECggEABKcKMwUIsPqHvXbe3vvZMKIt5Dr1Uq7Wq653lASG1fdxSDJ5J4VH+kMewOkOI+hCKxzinNAjFKhFkA+eyz1ghiyy7ud5TS1oKeNlp4B9fEPDJ/y1WJLW1cZ5aERJMT4gXglzohCMlOzO7sLKDMNWT5R4h8TVY5hgYw8zuuDejwexS9Rmnh32FAOlDXoIAHBHQo2kZrCm385f5qNPVIo07061+Err/FkFi/ilGKQwuLsO3it4UY+m+JcL02GKoi9UgS3/sh1kdcSty7kMx5A1NKyUffGPilz26d9weypLBHt36ZIoULtQN6zzk0El7oTmYppDDGRAMaZGyVRLqnvhoQKBgQD7y8M9biQM8fkCk8ACbANiHUhlQWyuyFImhvUBQzt0SMf9gOjQQ80n5XlMcGSaRC8KnNxSwbsd0vGh1gz+eHA8l7lGjL09Aj90BPjqVfs3YH7ueOZ/6yKM2X5Y3mqwhW0yS8nu3VWZHTJqB5c6ZEJAgkFspcJvPNwuDN0Hy4XOCQKBgQDloYMfcJug3aVbdEeUU3VCqRczv2kG/ylXgy4iuwOEOfnBURgPL90p8WbPVEOTIob+VuVCB8KWVriWYzGBVISW4vKM5KgFMQfym/J/UPVrAZXp1ljDER7EArWEV56UFLVvaDcQnhMNLBIuNxpWhLoLEk9Y/aWbC9AYTfWpqZDQnwKBgC4sdYckVXQSQ2mXkpRfHle1qvr2fmmmznnGMdOjcpmmZxYzt33JkSuJxy6fRbOMYVv+2+BJKnkTn7/fss5PY7WY1R783A7+BIjQCJ1gWwsSpPQyM4Ktqc5Oa/77+nCYZyfcAhHNgM7tLbuH3NGWZRfTSxATbY5ciLvhG/878TORAoGAWJ54SA3hQbfINP8vEKMUweEXvzTDh5u5jLhneafKt6DYLi7pphLGu4UaNDFE0uekSUuntk/gXLyjCAbH+C1x7fkvuggUf8xoUMy2TUQlMmKuREwgfUeCJAsXmjdmT6eeTZxeg989Yvv7GDLTy0f1tIggbyuUWb25VWfgwtVZuekCgYBX3BsG66HhkFvvVGguzajwJZwobsH07gEeRyvj+aWxTmU9V1QjCVi+gDWYeypXogiCKoneJ1cxIT5K5B5EQHpxRgYs8jsaPIZAe5wbxTJRLuPUryYJr27+CmhO/WWGDexmIWGkCKzfPECaOsWcwqDbKHKiQE87A5G2pLXlFQME6g==
    apiModel:
      GET_APPLICATION:
        endpoint: /lender/1/getApplication
        httpTimeout: 2500
        httpRetryCount: 0
      CHECK_DEDUPE_V2:
        endpoint: /v1/lead/hash-dedupe
        httpTimeout: 2500
        httpRetryCount: 0
      CREATE_APPLICATION_V2:
        endpoint: /lender/2.0/createApplication
        httpTimeout: 3000
        httpRetryCount: 0
      GENERATE_OFFER:
        endpoint: /lender/2.0/generateOffer
        httpTimeout: 2500
        httpRetryCount: 0
      SUBMIT_OFFER:
        endpoint: /lender/2.0/submitOffer
        httpTimeout: 15000
        httpRetryCount: 0
      INIT_AA:
        endpoint: /lender/2.0/initAccountAggregator
        httpTimeout: 2500
        httpRetryCount: 0
      GET_OFFER:
        endpoint: /lender/2.0/getOffer
        httpTimeout: 2500
        httpRetryCount: 0
      GET_APPLICATION_STATUS_V2:
        endpoint: /lender/2.0/getApplicationStatus
        httpTimeout: 1500
        httpRetryCount: 0
  moneyViewv2Configuration:
    scope: LENDING_MONEYVIEWV2
    host: https://growth-01.stg.whizdm.com/atlas
    tokenEndpoint: /lender/1/token
    username: supermoneypa
    password: $X"Hi3uAW1
    clientId: 194
    authCode: super123
    lenderBase64PublicKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAoRKasnuPfAsNP9Wrq8TloamCI/jP5mkgX/zXK/w3qiZLxibz37wAaxeFxpuhnfKH+B5s8ujUNVHGBFgWS/LAC6xEsQKH29ohYOtgYbnC/LowKTCx97hpP5edAomNqtGdNkGpyvfPjaIECmV3mWhekSsZt0VAJKWOM3U8TAJaoh3dnr7udhJ5PDSXG6y5uqApL49Op276wriijAmQXaaNk6ZAxZwXg1FCWfOAFIVok1pQh6Ho2su4UvSqozhatsKyEzKFem8O9JWDCs2a7MtbbUpVWuTb7NxAoy7PpWWGkvt8uRAPKi4BDIHwhn0D+9MV54+Ftkg8hDZ6ogWinBM/CwIDAQAB
    lspBase64PrivateKey: MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQDh3CHD7n/QENLd+4MyMtqLcqLHpsoTwIqHO2dAHV1kLV5eAognJXOWZRNZFvRs7Er5X4VxKVGw/cQj3+zSJF02d0wn5j/Wy4mJlnqr1POgqcxfjDMQV+BCPUj1UpNxRPOD8qnRAaO9g5NzPfkWo6oVAerOk19Gf21/u4D2odZDLxME68380eugKJ631QxoS3RjLbdoMWh/R9uHhb3xpIM6QEfsV2WSMnQpNgYD26LEWKk7gA/c0+WDLGJnA7d/9bchpAcLM69Ej8G6HG6y4l5IoWRbA57AbY1SOBpi74NTr2lj/lJURlEj8WUVfdUdGgyHagV6UEIEHcGKDmz1kkeXAgMBAAECggEABKcKMwUIsPqHvXbe3vvZMKIt5Dr1Uq7Wq653lASG1fdxSDJ5J4VH+kMewOkOI+hCKxzinNAjFKhFkA+eyz1ghiyy7ud5TS1oKeNlp4B9fEPDJ/y1WJLW1cZ5aERJMT4gXglzohCMlOzO7sLKDMNWT5R4h8TVY5hgYw8zuuDejwexS9Rmnh32FAOlDXoIAHBHQo2kZrCm385f5qNPVIo07061+Err/FkFi/ilGKQwuLsO3it4UY+m+JcL02GKoi9UgS3/sh1kdcSty7kMx5A1NKyUffGPilz26d9weypLBHt36ZIoULtQN6zzk0El7oTmYppDDGRAMaZGyVRLqnvhoQKBgQD7y8M9biQM8fkCk8ACbANiHUhlQWyuyFImhvUBQzt0SMf9gOjQQ80n5XlMcGSaRC8KnNxSwbsd0vGh1gz+eHA8l7lGjL09Aj90BPjqVfs3YH7ueOZ/6yKM2X5Y3mqwhW0yS8nu3VWZHTJqB5c6ZEJAgkFspcJvPNwuDN0Hy4XOCQKBgQDloYMfcJug3aVbdEeUU3VCqRczv2kG/ylXgy4iuwOEOfnBURgPL90p8WbPVEOTIob+VuVCB8KWVriWYzGBVISW4vKM5KgFMQfym/J/UPVrAZXp1ljDER7EArWEV56UFLVvaDcQnhMNLBIuNxpWhLoLEk9Y/aWbC9AYTfWpqZDQnwKBgC4sdYckVXQSQ2mXkpRfHle1qvr2fmmmznnGMdOjcpmmZxYzt33JkSuJxy6fRbOMYVv+2+BJKnkTn7/fss5PY7WY1R783A7+BIjQCJ1gWwsSpPQyM4Ktqc5Oa/77+nCYZyfcAhHNgM7tLbuH3NGWZRfTSxATbY5ciLvhG/878TORAoGAWJ54SA3hQbfINP8vEKMUweEXvzTDh5u5jLhneafKt6DYLi7pphLGu4UaNDFE0uekSUuntk/gXLyjCAbH+C1x7fkvuggUf8xoUMy2TUQlMmKuREwgfUeCJAsXmjdmT6eeTZxeg989Yvv7GDLTy0f1tIggbyuUWb25VWfgwtVZuekCgYBX3BsG66HhkFvvVGguzajwJZwobsH07gEeRyvj+aWxTmU9V1QjCVi+gDWYeypXogiCKoneJ1cxIT5K5B5EQHpxRgYs8jsaPIZAe5wbxTJRLuPUryYJr27+CmhO/WWGDexmIWGkCKzfPECaOsWcwqDbKHKiQE87A5G2pLXlFQME6g==
    apiModel:
      GET_APPLICATION:
        endpoint: /lender/1/getApplication
        httpTimeout: 2500
        httpRetryCount: 0
      CHECK_DEDUPE_V2:
        endpoint: /v1/lead/hash-dedupe
        httpTimeout: 2500
        httpRetryCount: 0
      CREATE_APPLICATION_V2:
        endpoint: /lender/2.0/createApplication
        httpTimeout: 15000
        httpRetryCount: 0
      GENERATE_OFFER:
        endpoint: /lender/2.0/generateOffer
        httpTimeout: 2500
        httpRetryCount: 0
      SUBMIT_OFFER:
        endpoint: /lender/2.0/submitOffer
        httpTimeout: 3000
        httpRetryCount: 0
      INIT_AA:
        endpoint: /lender/2.0/initAccountAggregator
        httpTimeout: 2500
        httpRetryCount: 0
      GET_OFFER:
        endpoint: /lender/2.0/getOffer
        httpTimeout: 2500
        httpRetryCount: 0
      GET_APPLICATION_STATUS_V2:
        endpoint: /lender/2.0/getApplicationStatus
        httpTimeout: 1500
        httpRetryCount: 0
  fibeConfiguration:
    scope: LENDING_FIBE
    host: https://uatapi.earlysalary.com/aggregatorexternal
    tokenEndpoint: /lender/1/token
    username: sUperMoneyUat
    password: sUperMoneyUat#p1a@261123
    clientId: 111
    xApiKey: GocZL0S7TE4mgjMpkvqCBav2zzP7NQz66stDs9xJ
    authCode: super123
    cremoEnabled: true
    lenderBase64PublicKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAotnstFqI1d2eeAMwdz4lFMd8OpE/QK65fmt/2AtZljhOfEgFJ3TONPmMMrzHMxOrgUbaVXfPLw5e2HhYJxZMVSO6TFOJlIHDF5ILXCH+tz7Aozb5H2LawhUYgtLfxJdhT7en8lZE++9PBbPQiiN2ZebYe6IjWZ5XYg+shM/pCA2/jz/7VYYhSEPh7Opg1USFppEFOZqEP9P1997hm1RKMl/x2ZqI5X9SJr7ReesIQn1l3xkGTjoaLTeu54X+vHBhoFFe3DLdBtigGNIb59XrqAbJBiLsm9RZ6dDY4cY/oyJrZzpxgwGUrePJftsBlSxH/TzgrfDvA+Fe7pGB6SVDBQIDAQAB
    lspBase64PrivateKey: MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQDh3CHD7n/QENLd+4MyMtqLcqLHpsoTwIqHO2dAHV1kLV5eAognJXOWZRNZFvRs7Er5X4VxKVGw/cQj3+zSJF02d0wn5j/Wy4mJlnqr1POgqcxfjDMQV+BCPUj1UpNxRPOD8qnRAaO9g5NzPfkWo6oVAerOk19Gf21/u4D2odZDLxME68380eugKJ631QxoS3RjLbdoMWh/R9uHhb3xpIM6QEfsV2WSMnQpNgYD26LEWKk7gA/c0+WDLGJnA7d/9bchpAcLM69Ej8G6HG6y4l5IoWRbA57AbY1SOBpi74NTr2lj/lJURlEj8WUVfdUdGgyHagV6UEIEHcGKDmz1kkeXAgMBAAECggEABKcKMwUIsPqHvXbe3vvZMKIt5Dr1Uq7Wq653lASG1fdxSDJ5J4VH+kMewOkOI+hCKxzinNAjFKhFkA+eyz1ghiyy7ud5TS1oKeNlp4B9fEPDJ/y1WJLW1cZ5aERJMT4gXglzohCMlOzO7sLKDMNWT5R4h8TVY5hgYw8zuuDejwexS9Rmnh32FAOlDXoIAHBHQo2kZrCm385f5qNPVIo07061+Err/FkFi/ilGKQwuLsO3it4UY+m+JcL02GKoi9UgS3/sh1kdcSty7kMx5A1NKyUffGPilz26d9weypLBHt36ZIoULtQN6zzk0El7oTmYppDDGRAMaZGyVRLqnvhoQKBgQD7y8M9biQM8fkCk8ACbANiHUhlQWyuyFImhvUBQzt0SMf9gOjQQ80n5XlMcGSaRC8KnNxSwbsd0vGh1gz+eHA8l7lGjL09Aj90BPjqVfs3YH7ueOZ/6yKM2X5Y3mqwhW0yS8nu3VWZHTJqB5c6ZEJAgkFspcJvPNwuDN0Hy4XOCQKBgQDloYMfcJug3aVbdEeUU3VCqRczv2kG/ylXgy4iuwOEOfnBURgPL90p8WbPVEOTIob+VuVCB8KWVriWYzGBVISW4vKM5KgFMQfym/J/UPVrAZXp1ljDER7EArWEV56UFLVvaDcQnhMNLBIuNxpWhLoLEk9Y/aWbC9AYTfWpqZDQnwKBgC4sdYckVXQSQ2mXkpRfHle1qvr2fmmmznnGMdOjcpmmZxYzt33JkSuJxy6fRbOMYVv+2+BJKnkTn7/fss5PY7WY1R783A7+BIjQCJ1gWwsSpPQyM4Ktqc5Oa/77+nCYZyfcAhHNgM7tLbuH3NGWZRfTSxATbY5ciLvhG/878TORAoGAWJ54SA3hQbfINP8vEKMUweEXvzTDh5u5jLhneafKt6DYLi7pphLGu4UaNDFE0uekSUuntk/gXLyjCAbH+C1x7fkvuggUf8xoUMy2TUQlMmKuREwgfUeCJAsXmjdmT6eeTZxeg989Yvv7GDLTy0f1tIggbyuUWb25VWfgwtVZuekCgYBX3BsG66HhkFvvVGguzajwJZwobsH07gEeRyvj+aWxTmU9V1QjCVi+gDWYeypXogiCKoneJ1cxIT5K5B5EQHpxRgYs8jsaPIZAe5wbxTJRLuPUryYJr27+CmhO/WWGDexmIWGkCKzfPECaOsWcwqDbKHKiQE87A5G2pLXlFQME6g==
    apiModel:
      CREATE_APPLICATION:
        endpoint: /lender/1/createApplication
        httpTimeout: 15000
        httpRetryCount: 0
      CREATE_APPLICATION_V2:
        endpoint: /lender/2.0/createApplication
        httpTimeout: 10000
        httpRetryCount: 0
      GET_APPLICATION:
        endpoint: /lender/2.0/getApplication
        httpTimeout: 2500
        httpRetryCount: 0
      GENERATE_OFFER:
        endpoint: /lender/2.0/generateOffer
        httpTimeout: 2500
        httpRetryCount: 0
      GET_OFFER:
        endpoint: /lender/2.0/getOffer
        httpTimeout: 2500
        httpRetryCount: 0
      INIT_AA:
        endpoint: /lender/2.0/initAccountAggregator
        httpTimeout: 2500
        httpRetryCount: 0
      GET_APPLICATION_STATUS:
        endpoint: /lender/1/getApplicationStatus
        httpTimeout: 2500
        httpRetryCount: 3
      GET_APPLICATION_STATUS_V2:
        endpoint: /lender/2.0/getApplicationStatus
        httpTimeout: 1500
        httpRetryCount: 0
      CHECK_DEDUPE:
        endpoint: /masked-dedupe
        httpTimeout: 2500
        httpRetryCount: 3
      SUBMIT_OFFER:
        endpoint: /lender/2.0/submitOffer
        httpTimeout: 4000
  fibev2Configuration:
    scope: LENDING_FIBEV2
    host: https://api.socialworth.in/aggext-prod-enc
    tokenEndpoint: /lender/1/token
    username: SuPerMOnEY#Pr0d
    password: SuPerMOnEY#Pr0d@#********
    clientId: 111
    clientSecret: supermoney
    authCode: super123
    xApiKey: sRx7VBezXD3srYiyrXCsr5oAWypvaJPN5RE3KnRd
    cremoEnabled: true
    lenderBase64PublicKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEApyb2NXJIMrSLn7KGmslAm2dk72B/9gzWx1FMoKG2RZd/yNGCSDpdwbdoAJkEcKexN5trnhfGIJmNPj3V5FFI9ffPTAXMJ74ZiZnq/x0mMaa51ad6/bDSlyQec5pafUEsWfTeLl1zoFwx7H5x+7jZWe3WMD6j13ou7k/4BFDlvxVmy3i7SwAJNpeOvDZkgn7Rr1VdpsyHRvDlsxXWhqBtGJia/F3yBqh27yCkQWlBet2qdC1OVpFd4JXP/fBXFbtaH70ho+Jx/IbzuG0TpQNi0LSjZBomKk5ulFGE/qyVVphP+YvIvIzWQbMD9ky3664l/2qhZ45pmmx+d6YVu6balwIDAQAB
    lspBase64PrivateKey: MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDp1tI626IPVzaPvyqsJ2r6+ezSrhp7tfP9lPKG5WD03oDt9Qwx8Dtsv35ZmOY2mgQClBDPLKSpfQScLKveBeZ+QhZDmHNh2G5KD1JOA79Jk+NvunO5sqp2asWsc941wmp7OMYRu1ilXPvAB6YGK/BhaYW3izFKg961UVSKOC7xfwCDsmkZ1O3OsfNHOiGbvqlKkKiJH3PSy0fS/7t9lEhqPPgCm1JmrtRyXOV5vDN2VO59eBts7gYpfoDAn0wAgEV5q571HqqPTFLN8sKZ/tvnIkHIzxXeVFCqGjNThho/O1PFhelue6v4v0x12CrFslKIbTC86NbGKB0sG+1xQSt5AgMBAAECggEAATgG/ucvVetMPrswO/ndxl7lgbhtMuxYuds+LJ8JKlusASne12AUGFkAKEHCSU3unUcQuStK+Q+EMPS0wwBE+FuPwHv6tdrWSknDO5dK/D8oZIgdXCqiUxQ+foSvKOaWR+ycr+gSYow674zEvkF1pc6DCf/7kYN3hze8Mbh7t5eToBl6h1FvA7oryEKbc+SUt49sRmwdM5i9V5wL93Otyq9ZwklCqck/NTjBJlBBtPbWJX+Tv9cegKfrFN3h949EJNvf32sdc/2MPI6HA82/GSQHb9gSrJCXQtWbiLKTFmzObpV2w9IyNBpTulhlxHUBK8w2Wt514vUkwEvslAajEQKBgQD6e4NuU41dysBKog8bU9TPC83S5ePv/4qlVQRm5fnibZPlC3f8OhttBD/l5aNhPZgwSPMTwNgtnXqBdk87MEeKH70iqqQQ6AaHNbjwh/TSG8tSuUTzLvr4WtVsI2TpFCxCrkzBGbhHLV1bL7R/qU7VALEZVvwkRUzaEd3n/3ZMKQKBgQDu/XR8GyASOf+a9HOq35xWOSp7Zq/WO4FCidf8bYsnhH5qjC+2FGbFF8neq/cEdCkTXs/LD4JD5aQuJIoQ0NyFL6pzKPTGzsARWYJyfR9mkKXcWfSo8hFRBKiVpSSqEDqYQfsZPDqf7rVECB4NkDRYNkuo2TcNK3I/7xKrecTO0QKBgB7waLCU8241YAEX+WO3vB6C5754FOqNFvQLB0sqdu4n+M+e1gg6qS6TCs17yu107TpxmEzFUi54OCyLj4fRE9UUzIjeqPtJnZzP3vMe5/eX5UpnhgO/ttI8bgY4bPt5OWpXGzXy1lc8XD6qKtJl+E3JExb9LpHQWYjKEi3Y2STZAoGBAMLHNTm0zdWtCWKcGu1H493uJ259iY/b3YSCXdLw4LnrZTCnYndOd1fpBHEDzIYHujZ8TnScBjoaHk4xH4mHAENYTt4eoiip9qXtQsDWs1HfUB6f/iP3bh+O4WtdNd2Y7niLRgASM2GHppp01zGMtuyyC8XoitKj9zCeQ6QX0UwRAoGAdE2PkUqSe3vhu1/XATAiFJs8h6FOadno8DhlIe27p44OtTanPMGRM6TIgjZSJ3qGn5n3Iajf8es0HRPM1KyABKekPJDFCwwgGrf6b+41T5G0ZNWv3L/ftu4MgxJadvqXKM3eWnJk3Swz6T2bqHt7peUVR6vqDPwe4WyfpFRqpLo=
    apiModel:
      CREATE_APPLICATION_V2:
        endpoint: /lender/2.0/createApplication
        httpTimeout: 10000
        httpRetryCount: 0
      GET_APPLICATION:
        endpoint: /lender/2.0/getApplication
        httpTimeout: 2500
        httpRetryCount: 0
      GENERATE_OFFER:
        endpoint: /lender/2.0/generateOffer
        httpTimeout: 2500
        httpRetryCount: 0
      GET_OFFER:
        endpoint: /lender/2.0/getOffer
        httpTimeout: 2500
        httpRetryCount: 0
      SUBMIT_OFFER:
        endpoint: /lender/2.0/submitOffer
        httpTimeout: 3000
        httpRetryCount: 0
      INIT_AA:
        endpoint: /lender/2.0/initAccountAggregator
        httpTimeout: 2500
        httpRetryCount: 0
      GET_APPLICATION_STATUS_V2:
        endpoint: /lender/2.0/getApplicationStatus
        httpTimeout: 2500
        httpRetryCount: 0
      CHECK_DEDUPE:
        endpoint: /v1/users/dedupe
        httpTimeout: 2500
        httpRetryCount: 0
  moneyViewOpenMktConfiguration:
    scope: LENDING_MONEYVIEWOPENMKT
    host: https://growth-01.stg.whizdm.com/atlas
    tokenEndpoint: /lender/1/token
    username: supermoney
    password: ",CMW39rm)O"
    clientId: 213
    xApiKey:
    authCode: super123
    lenderBase64PublicKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAoRKasnuPfAsNP9Wrq8TloamCI/jP5mkgX/zXK/w3qiZLxibz37wAaxeFxpuhnfKH+B5s8ujUNVHGBFgWS/LAC6xEsQKH29ohYOtgYbnC/LowKTCx97hpP5edAomNqtGdNkGpyvfPjaIECmV3mWhekSsZt0VAJKWOM3U8TAJaoh3dnr7udhJ5PDSXG6y5uqApL49Op276wriijAmQXaaNk6ZAxZwXg1FCWfOAFIVok1pQh6Ho2su4UvSqozhatsKyEzKFem8O9JWDCs2a7MtbbUpVWuTb7NxAoy7PpWWGkvt8uRAPKi4BDIHwhn0D+9MV54+Ftkg8hDZ6ogWinBM/CwIDAQAB
    lspBase64PrivateKey: MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQDh3CHD7n/QENLd+4MyMtqLcqLHpsoTwIqHO2dAHV1kLV5eAognJXOWZRNZFvRs7Er5X4VxKVGw/cQj3+zSJF02d0wn5j/Wy4mJlnqr1POgqcxfjDMQV+BCPUj1UpNxRPOD8qnRAaO9g5NzPfkWo6oVAerOk19Gf21/u4D2odZDLxME68380eugKJ631QxoS3RjLbdoMWh/R9uHhb3xpIM6QEfsV2WSMnQpNgYD26LEWKk7gA/c0+WDLGJnA7d/9bchpAcLM69Ej8G6HG6y4l5IoWRbA57AbY1SOBpi74NTr2lj/lJURlEj8WUVfdUdGgyHagV6UEIEHcGKDmz1kkeXAgMBAAECggEABKcKMwUIsPqHvXbe3vvZMKIt5Dr1Uq7Wq653lASG1fdxSDJ5J4VH+kMewOkOI+hCKxzinNAjFKhFkA+eyz1ghiyy7ud5TS1oKeNlp4B9fEPDJ/y1WJLW1cZ5aERJMT4gXglzohCMlOzO7sLKDMNWT5R4h8TVY5hgYw8zuuDejwexS9Rmnh32FAOlDXoIAHBHQo2kZrCm385f5qNPVIo07061+Err/FkFi/ilGKQwuLsO3it4UY+m+JcL02GKoi9UgS3/sh1kdcSty7kMx5A1NKyUffGPilz26d9weypLBHt36ZIoULtQN6zzk0El7oTmYppDDGRAMaZGyVRLqnvhoQKBgQD7y8M9biQM8fkCk8ACbANiHUhlQWyuyFImhvUBQzt0SMf9gOjQQ80n5XlMcGSaRC8KnNxSwbsd0vGh1gz+eHA8l7lGjL09Aj90BPjqVfs3YH7ueOZ/6yKM2X5Y3mqwhW0yS8nu3VWZHTJqB5c6ZEJAgkFspcJvPNwuDN0Hy4XOCQKBgQDloYMfcJug3aVbdEeUU3VCqRczv2kG/ylXgy4iuwOEOfnBURgPL90p8WbPVEOTIob+VuVCB8KWVriWYzGBVISW4vKM5KgFMQfym/J/UPVrAZXp1ljDER7EArWEV56UFLVvaDcQnhMNLBIuNxpWhLoLEk9Y/aWbC9AYTfWpqZDQnwKBgC4sdYckVXQSQ2mXkpRfHle1qvr2fmmmznnGMdOjcpmmZxYzt33JkSuJxy6fRbOMYVv+2+BJKnkTn7/fss5PY7WY1R783A7+BIjQCJ1gWwsSpPQyM4Ktqc5Oa/77+nCYZyfcAhHNgM7tLbuH3NGWZRfTSxATbY5ciLvhG/878TORAoGAWJ54SA3hQbfINP8vEKMUweEXvzTDh5u5jLhneafKt6DYLi7pphLGu4UaNDFE0uekSUuntk/gXLyjCAbH+C1x7fkvuggUf8xoUMy2TUQlMmKuREwgfUeCJAsXmjdmT6eeTZxeg989Yvv7GDLTy0f1tIggbyuUWb25VWfgwtVZuekCgYBX3BsG66HhkFvvVGguzajwJZwobsH07gEeRyvj+aWxTmU9V1QjCVi+gDWYeypXogiCKoneJ1cxIT5K5B5EQHpxRgYs8jsaPIZAe5wbxTJRLuPUryYJr27+CmhO/WWGDexmIWGkCKzfPECaOsWcwqDbKHKiQE87A5G2pLXlFQME6g==
    apiModel:
      CREATE_APPLICATION:
        endpoint: /lender/1/createApplication
        httpTimeout: 15000
        httpRetryCount: 0
      CREATE_APPLICATION_V2:
        endpoint: /lender/2.0/createApplication
        httpTimeout: 15000
        httpRetryCount: 0
      GENERATE_OFFER:
        endpoint: /lender/2.0/generateOffer
        httpTimeout: 2500
        httpRetryCount: 0
      GET_OFFER:
        endpoint: /lender/2.0/getOffer
        httpTimeout: 2500
        httpRetryCount: 0
      GET_APPLICATION_STATUS_V2:
        endpoint: /lender/2.0/getApplicationStatus
        httpTimeout: 1500
        httpRetryCount: 0
      GET_APPLICATION:
        endpoint: /lender/1/getApplication
        httpTimeout: 2500
        httpRetryCount: 0
      GET_APPLICATION_STATUS:
        endpoint: /lender/1/getApplicationStatus
        httpTimeout: 2500
        httpRetryCount: 0
      CHECK_DEDUPE:
        endpoint: /v1/lead/dedupe
        httpTimeout: 2500
        httpRetryCount: 0
      SUBMIT_OFFER:
        endpoint: /lender/2.0/submitOffer
        httpTimeout: 15000
        httpRetryCount: 0
      INIT_AA:
        endpoint: /lender/2.0/initAccountAggregator
        httpTimeout: 2500
        httpRetryCount: 0
  omniv2Configuration:
    scope: LENDING_OMNI
    host: https://dev.saisonomni.com
    tokenEndpoint: /lender/1/token
    username: supermoney
    password: supermoney
    clientId: smTestClientId
    xApiKey:
    authCode: super123
    lenderBase64PublicKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA0CDvDz7v82xLMBWowEUmCty1c6IwXGcSUzG46fTAPrXcy6Qe1/OCzcrvs+D4PHn6ajxesxBQf259zUPz39gUScBAXooFUPP2FXha6XCMzQXfI4KrYDLBW5Lsw6x4TFeH3nhAJ2vJN6ptI7cZzsU87fsctre1A1Oryc3PIJmSgVAMJQwwRE/uovnFby7qmczAPOfpp97cNfC0y+iDA3WaiJnjB17JFSFop91lZXgVu4BcufycwFaEk3Cw47KrKZ7tC4RsobRKz/fbVOLUMFN3D0UbLtNZXMIPjSyiVr7aoDZsZ+KNkLDIGunOZNkymxjgbmuT6DOBxbIOxlel1ue13QIDAQAB
    lspBase64PrivateKey: MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQDh3CHD7n/QENLd+4MyMtqLcqLHpsoTwIqHO2dAHV1kLV5eAognJXOWZRNZFvRs7Er5X4VxKVGw/cQj3+zSJF02d0wn5j/Wy4mJlnqr1POgqcxfjDMQV+BCPUj1UpNxRPOD8qnRAaO9g5NzPfkWo6oVAerOk19Gf21/u4D2odZDLxME68380eugKJ631QxoS3RjLbdoMWh/R9uHhb3xpIM6QEfsV2WSMnQpNgYD26LEWKk7gA/c0+WDLGJnA7d/9bchpAcLM69Ej8G6HG6y4l5IoWRbA57AbY1SOBpi74NTr2lj/lJURlEj8WUVfdUdGgyHagV6UEIEHcGKDmz1kkeXAgMBAAECggEABKcKMwUIsPqHvXbe3vvZMKIt5Dr1Uq7Wq653lASG1fdxSDJ5J4VH+kMewOkOI+hCKxzinNAjFKhFkA+eyz1ghiyy7ud5TS1oKeNlp4B9fEPDJ/y1WJLW1cZ5aERJMT4gXglzohCMlOzO7sLKDMNWT5R4h8TVY5hgYw8zuuDejwexS9Rmnh32FAOlDXoIAHBHQo2kZrCm385f5qNPVIo07061+Err/FkFi/ilGKQwuLsO3it4UY+m+JcL02GKoi9UgS3/sh1kdcSty7kMx5A1NKyUffGPilz26d9weypLBHt36ZIoULtQN6zzk0El7oTmYppDDGRAMaZGyVRLqnvhoQKBgQD7y8M9biQM8fkCk8ACbANiHUhlQWyuyFImhvUBQzt0SMf9gOjQQ80n5XlMcGSaRC8KnNxSwbsd0vGh1gz+eHA8l7lGjL09Aj90BPjqVfs3YH7ueOZ/6yKM2X5Y3mqwhW0yS8nu3VWZHTJqB5c6ZEJAgkFspcJvPNwuDN0Hy4XOCQKBgQDloYMfcJug3aVbdEeUU3VCqRczv2kG/ylXgy4iuwOEOfnBURgPL90p8WbPVEOTIob+VuVCB8KWVriWYzGBVISW4vKM5KgFMQfym/J/UPVrAZXp1ljDER7EArWEV56UFLVvaDcQnhMNLBIuNxpWhLoLEk9Y/aWbC9AYTfWpqZDQnwKBgC4sdYckVXQSQ2mXkpRfHle1qvr2fmmmznnGMdOjcpmmZxYzt33JkSuJxy6fRbOMYVv+2+BJKnkTn7/fss5PY7WY1R783A7+BIjQCJ1gWwsSpPQyM4Ktqc5Oa/77+nCYZyfcAhHNgM7tLbuH3NGWZRfTSxATbY5ciLvhG/878TORAoGAWJ54SA3hQbfINP8vEKMUweEXvzTDh5u5jLhneafKt6DYLi7pphLGu4UaNDFE0uekSUuntk/gXLyjCAbH+C1x7fkvuggUf8xoUMy2TUQlMmKuREwgfUeCJAsXmjdmT6eeTZxeg989Yvv7GDLTy0f1tIggbyuUWb25VWfgwtVZuekCgYBX3BsG66HhkFvvVGguzajwJZwobsH07gEeRyvj+aWxTmU9V1QjCVi+gDWYeypXogiCKoneJ1cxIT5K5B5EQHpxRgYs8jsaPIZAe5wbxTJRLuPUryYJr27+CmhO/WWGDexmIWGkCKzfPECaOsWcwqDbKHKiQE87A5G2pLXlFQME6g==
    apiModel:
      CREATE_APPLICATION:
        endpoint: /lender/1/createApplication
        httpTimeout: 10000
        httpRetryCount: 0
      GET_APPLICATION_STATUS:
        endpoint: /lender/1/getApplicationStatus
        httpTimeout: 1500
        httpRetryCount: 0
      CREATE_APPLICATION_V2:
        endpoint: /api/v2/partner/flipkart/application
        httpTimeout: 15000
        httpRetryCount: 0
      GET_APPLICATION:
        endpoint: /lender/2.0/getApplication
        httpTimeout: 2500
        httpRetryCount: 0
      GENERATE_OFFER:
        endpoint: /api/v2/partner/flipkart/generate-offer
        httpTimeout: 2500
        httpRetryCount: 0
      GET_OFFER:
        endpoint: /api/v2/partner/flipkart/get-offer
        httpTimeout: 2500
        httpRetryCount: 0
      SUBMIT_OFFER:
        endpoint: /api/v2/partner/flipkart/submit-offer
        httpTimeout: 4000
        httpRetryCount: 0
      INIT_AA:
        endpoint: /lender/2.0/initAccountAggregator
        httpTimeout: 2500
        httpRetryCount: 0
      GET_APPLICATION_STATUS_V2:
        endpoint: /api/v2/partner/flipkart/application/status
        httpTimeout: 1500
        httpRetryCount: 0
  omniConfiguration:
    scope: LENDING_OMNIV2
    host: https://dev.saisonomni.com
    tokenEndpoint: /lender/1/token
    username: supermoney
    password: supermoney
    clientId: smTestClientId
    xApiKey:
    authCode: super123
    lenderBase64PublicKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA0CDvDz7v82xLMBWowEUmCty1c6IwXGcSUzG46fTAPrXcy6Qe1/OCzcrvs+D4PHn6ajxesxBQf259zUPz39gUScBAXooFUPP2FXha6XCMzQXfI4KrYDLBW5Lsw6x4TFeH3nhAJ2vJN6ptI7cZzsU87fsctre1A1Oryc3PIJmSgVAMJQwwRE/uovnFby7qmczAPOfpp97cNfC0y+iDA3WaiJnjB17JFSFop91lZXgVu4BcufycwFaEk3Cw47KrKZ7tC4RsobRKz/fbVOLUMFN3D0UbLtNZXMIPjSyiVr7aoDZsZ+KNkLDIGunOZNkymxjgbmuT6DOBxbIOxlel1ue13QIDAQAB
    lspBase64PrivateKey: MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQDh3CHD7n/QENLd+4MyMtqLcqLHpsoTwIqHO2dAHV1kLV5eAognJXOWZRNZFvRs7Er5X4VxKVGw/cQj3+zSJF02d0wn5j/Wy4mJlnqr1POgqcxfjDMQV+BCPUj1UpNxRPOD8qnRAaO9g5NzPfkWo6oVAerOk19Gf21/u4D2odZDLxME68380eugKJ631QxoS3RjLbdoMWh/R9uHhb3xpIM6QEfsV2WSMnQpNgYD26LEWKk7gA/c0+WDLGJnA7d/9bchpAcLM69Ej8G6HG6y4l5IoWRbA57AbY1SOBpi74NTr2lj/lJURlEj8WUVfdUdGgyHagV6UEIEHcGKDmz1kkeXAgMBAAECggEABKcKMwUIsPqHvXbe3vvZMKIt5Dr1Uq7Wq653lASG1fdxSDJ5J4VH+kMewOkOI+hCKxzinNAjFKhFkA+eyz1ghiyy7ud5TS1oKeNlp4B9fEPDJ/y1WJLW1cZ5aERJMT4gXglzohCMlOzO7sLKDMNWT5R4h8TVY5hgYw8zuuDejwexS9Rmnh32FAOlDXoIAHBHQo2kZrCm385f5qNPVIo07061+Err/FkFi/ilGKQwuLsO3it4UY+m+JcL02GKoi9UgS3/sh1kdcSty7kMx5A1NKyUffGPilz26d9weypLBHt36ZIoULtQN6zzk0El7oTmYppDDGRAMaZGyVRLqnvhoQKBgQD7y8M9biQM8fkCk8ACbANiHUhlQWyuyFImhvUBQzt0SMf9gOjQQ80n5XlMcGSaRC8KnNxSwbsd0vGh1gz+eHA8l7lGjL09Aj90BPjqVfs3YH7ueOZ/6yKM2X5Y3mqwhW0yS8nu3VWZHTJqB5c6ZEJAgkFspcJvPNwuDN0Hy4XOCQKBgQDloYMfcJug3aVbdEeUU3VCqRczv2kG/ylXgy4iuwOEOfnBURgPL90p8WbPVEOTIob+VuVCB8KWVriWYzGBVISW4vKM5KgFMQfym/J/UPVrAZXp1ljDER7EArWEV56UFLVvaDcQnhMNLBIuNxpWhLoLEk9Y/aWbC9AYTfWpqZDQnwKBgC4sdYckVXQSQ2mXkpRfHle1qvr2fmmmznnGMdOjcpmmZxYzt33JkSuJxy6fRbOMYVv+2+BJKnkTn7/fss5PY7WY1R783A7+BIjQCJ1gWwsSpPQyM4Ktqc5Oa/77+nCYZyfcAhHNgM7tLbuH3NGWZRfTSxATbY5ciLvhG/878TORAoGAWJ54SA3hQbfINP8vEKMUweEXvzTDh5u5jLhneafKt6DYLi7pphLGu4UaNDFE0uekSUuntk/gXLyjCAbH+C1x7fkvuggUf8xoUMy2TUQlMmKuREwgfUeCJAsXmjdmT6eeTZxeg989Yvv7GDLTy0f1tIggbyuUWb25VWfgwtVZuekCgYBX3BsG66HhkFvvVGguzajwJZwobsH07gEeRyvj+aWxTmU9V1QjCVi+gDWYeypXogiCKoneJ1cxIT5K5B5EQHpxRgYs8jsaPIZAe5wbxTJRLuPUryYJr27+CmhO/WWGDexmIWGkCKzfPECaOsWcwqDbKHKiQE87A5G2pLXlFQME6g==
    apiModel:
      CREATE_APPLICATION:
        endpoint: /lender/1/createApplication
        httpTimeout: 10000
        httpRetryCount: 0
      GET_APPLICATION_STATUS:
        endpoint: /lender/1/getApplicationStatus
        httpTimeout: 1500
        httpRetryCount: 0
      CREATE_APPLICATION_V2:
        endpoint: /api/v2/partner/flipkart/application
        httpTimeout: 15000
        httpRetryCount: 0
      GET_APPLICATION:
        endpoint: /lender/2.0/getApplication
        httpTimeout: 2500
        httpRetryCount: 0
      GENERATE_OFFER:
        endpoint: /api/v2/partner/flipkart/generate-offer
        httpTimeout: 2500
        httpRetryCount: 0
      GET_OFFER:
        endpoint: /api/v2/partner/flipkart/get-offer
        httpTimeout: 2500
        httpRetryCount: 0
      SUBMIT_OFFER:
        endpoint: /api/v2/partner/flipkart/submit-offer
        httpTimeout: 4000
        httpRetryCount: 0
      INIT_AA:
        endpoint: /lender/2.0/initAccountAggregator
        httpTimeout: 2500
        httpRetryCount: 0
      GET_APPLICATION_STATUS_V2:
        endpoint: /api/v2/partner/flipkart/application/status
        httpTimeout: 1500
        httpRetryCount: 0
  dmiConfiguration:
    scope: LENDING_DMI
    host: https://services.dmifinance.in
    tokenEndpoint: /lender/1/token
    username: <EMAIL>
    password: DMI_SM@123
    clientId: 423141fdseq13er4133ed1q
    xApiKey:
    authCode: aaffc7b9-203a-4e9a-aed0-e886db8eb0a7
    lenderBase64PublicKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAyFGR+/N38srrBmOvRJRGAEb1YDKcZQAVg7rg0wXvSBMJc2sBqrQAOOg5gi7IiCBU/hD8J6sOAP2imMjLTItmhN6k0l2Z/7lP40QSEN6mb1wPAXfSjHiCptLaEmDyFfC5RV6Nnb7MNx2wRQZhOmBW8Aw2YjJRehSXGUOOG7qHII/hEQ2yLyg8OK0fem4jU7c08gEpya6NuPYEwu/jh5Q4WCnoIxmtFPDPQYmGsCznzCEpynrWkIGYyPWIZXbpUJRylCUB0QjVRdPJhkwGBEPMuIlkn/9/PRdgq2H57g7igXPyE6lqBjMxlHGV58uDQ/nThWwNpEvzUifTXeExXR2DZQIDAQAB
    lspBase64PrivateKey: MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQDh3CHD7n/QENLd+4MyMtqLcqLHpsoTwIqHO2dAHV1kLV5eAognJXOWZRNZFvRs7Er5X4VxKVGw/cQj3+zSJF02d0wn5j/Wy4mJlnqr1POgqcxfjDMQV+BCPUj1UpNxRPOD8qnRAaO9g5NzPfkWo6oVAerOk19Gf21/u4D2odZDLxME68380eugKJ631QxoS3RjLbdoMWh/R9uHhb3xpIM6QEfsV2WSMnQpNgYD26LEWKk7gA/c0+WDLGJnA7d/9bchpAcLM69Ej8G6HG6y4l5IoWRbA57AbY1SOBpi74NTr2lj/lJURlEj8WUVfdUdGgyHagV6UEIEHcGKDmz1kkeXAgMBAAECggEABKcKMwUIsPqHvXbe3vvZMKIt5Dr1Uq7Wq653lASG1fdxSDJ5J4VH+kMewOkOI+hCKxzinNAjFKhFkA+eyz1ghiyy7ud5TS1oKeNlp4B9fEPDJ/y1WJLW1cZ5aERJMT4gXglzohCMlOzO7sLKDMNWT5R4h8TVY5hgYw8zuuDejwexS9Rmnh32FAOlDXoIAHBHQo2kZrCm385f5qNPVIo07061+Err/FkFi/ilGKQwuLsO3it4UY+m+JcL02GKoi9UgS3/sh1kdcSty7kMx5A1NKyUffGPilz26d9weypLBHt36ZIoULtQN6zzk0El7oTmYppDDGRAMaZGyVRLqnvhoQKBgQD7y8M9biQM8fkCk8ACbANiHUhlQWyuyFImhvUBQzt0SMf9gOjQQ80n5XlMcGSaRC8KnNxSwbsd0vGh1gz+eHA8l7lGjL09Aj90BPjqVfs3YH7ueOZ/6yKM2X5Y3mqwhW0yS8nu3VWZHTJqB5c6ZEJAgkFspcJvPNwuDN0Hy4XOCQKBgQDloYMfcJug3aVbdEeUU3VCqRczv2kG/ylXgy4iuwOEOfnBURgPL90p8WbPVEOTIob+VuVCB8KWVriWYzGBVISW4vKM5KgFMQfym/J/UPVrAZXp1ljDER7EArWEV56UFLVvaDcQnhMNLBIuNxpWhLoLEk9Y/aWbC9AYTfWpqZDQnwKBgC4sdYckVXQSQ2mXkpRfHle1qvr2fmmmznnGMdOjcpmmZxYzt33JkSuJxy6fRbOMYVv+2+BJKnkTn7/fss5PY7WY1R783A7+BIjQCJ1gWwsSpPQyM4Ktqc5Oa/77+nCYZyfcAhHNgM7tLbuH3NGWZRfTSxATbY5ciLvhG/878TORAoGAWJ54SA3hQbfINP8vEKMUweEXvzTDh5u5jLhneafKt6DYLi7pphLGu4UaNDFE0uekSUuntk/gXLyjCAbH+C1x7fkvuggUf8xoUMy2TUQlMmKuREwgfUeCJAsXmjdmT6eeTZxeg989Yvv7GDLTy0f1tIggbyuUWb25VWfgwtVZuekCgYBX3BsG66HhkFvvVGguzajwJZwobsH07gEeRyvj+aWxTmU9V1QjCVi+gDWYeypXogiCKoneJ1cxIT5K5B5EQHpxRgYs8jsaPIZAe5wbxTJRLuPUryYJr27+CmhO/WWGDexmIWGkCKzfPECaOsWcwqDbKHKiQE87A5G2pLXlFQME6g==
    apiModel:
      CREATE_APPLICATION:
        endpoint: /lender/1/createApplication
        httpTimeout: 10000
        httpRetryCount: 0
      GET_APPLICATION:
        endpoint: /lender/1/getApplication
        httpTimeout: 1500
        httpRetryCount: 0
      GET_APPLICATION_STATUS:
        endpoint: /lender/1/getApplicationStatus
        httpTimeout: 1500
        httpRetryCount: 0
      CHECK_DEDUPE:
        endpoint: /uat/v1/dedupeForSM
        httpTimeout: 1500
        httpRetryCount: 0
  kisshtConfiguration:
    scope: LENDING_RING
    host: https://go-los-gateway.test.paywithring.com/api
    tokenEndpoint: /lender/1/token
    username: mIzMsPw721fKwBnVH9Rkkika3HAKniVZ
    password: TPglZpexZE4368SM7kRofXtfCqaM4HMh
    clientId: AUTH17108329019240P
    authCode:
    lenderBase64PublicKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA5AQv9Vv3qbeNgMShvL3n59L+O3taJrEj9vnwbBVWyiyHXI6NQCkP2Q7ib5gHFzJtCr4thyGucRwYDdRD9544qCozSknWLqNb+tTE8Iz/kE1LY/W7KZjI4FQ/iWirdxPMBYlG/mRhZuyK3P574d3vsl4WEGpqkVtVvopQUrNANG1U1o2Nohrxnj8Ir/S9NapDt1ONiCBGDILbh00wpPPa4qzluDyBGYYRTFDXm52qfTv1DgMnGzAJEVCnPEHrmw/DRTZUSMpdSv2NrdJV8rpLrwCPS6A8iKSoE8oRtxX0KNlEjwOF8hSWeeMkiw3d4pPq7fwNrhu2cctM5dcLpb2u+wIDAQAB
    lspBase64PrivateKey: MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQDh3CHD7n/QENLd+4MyMtqLcqLHpsoTwIqHO2dAHV1kLV5eAognJXOWZRNZFvRs7Er5X4VxKVGw/cQj3+zSJF02d0wn5j/Wy4mJlnqr1POgqcxfjDMQV+BCPUj1UpNxRPOD8qnRAaO9g5NzPfkWo6oVAerOk19Gf21/u4D2odZDLxME68380eugKJ631QxoS3RjLbdoMWh/R9uHhb3xpIM6QEfsV2WSMnQpNgYD26LEWKk7gA/c0+WDLGJnA7d/9bchpAcLM69Ej8G6HG6y4l5IoWRbA57AbY1SOBpi74NTr2lj/lJURlEj8WUVfdUdGgyHagV6UEIEHcGKDmz1kkeXAgMBAAECggEABKcKMwUIsPqHvXbe3vvZMKIt5Dr1Uq7Wq653lASG1fdxSDJ5J4VH+kMewOkOI+hCKxzinNAjFKhFkA+eyz1ghiyy7ud5TS1oKeNlp4B9fEPDJ/y1WJLW1cZ5aERJMT4gXglzohCMlOzO7sLKDMNWT5R4h8TVY5hgYw8zuuDejwexS9Rmnh32FAOlDXoIAHBHQo2kZrCm385f5qNPVIo07061+Err/FkFi/ilGKQwuLsO3it4UY+m+JcL02GKoi9UgS3/sh1kdcSty7kMx5A1NKyUffGPilz26d9weypLBHt36ZIoULtQN6zzk0El7oTmYppDDGRAMaZGyVRLqnvhoQKBgQD7y8M9biQM8fkCk8ACbANiHUhlQWyuyFImhvUBQzt0SMf9gOjQQ80n5XlMcGSaRC8KnNxSwbsd0vGh1gz+eHA8l7lGjL09Aj90BPjqVfs3YH7ueOZ/6yKM2X5Y3mqwhW0yS8nu3VWZHTJqB5c6ZEJAgkFspcJvPNwuDN0Hy4XOCQKBgQDloYMfcJug3aVbdEeUU3VCqRczv2kG/ylXgy4iuwOEOfnBURgPL90p8WbPVEOTIob+VuVCB8KWVriWYzGBVISW4vKM5KgFMQfym/J/UPVrAZXp1ljDER7EArWEV56UFLVvaDcQnhMNLBIuNxpWhLoLEk9Y/aWbC9AYTfWpqZDQnwKBgC4sdYckVXQSQ2mXkpRfHle1qvr2fmmmznnGMdOjcpmmZxYzt33JkSuJxy6fRbOMYVv+2+BJKnkTn7/fss5PY7WY1R783A7+BIjQCJ1gWwsSpPQyM4Ktqc5Oa/77+nCYZyfcAhHNgM7tLbuH3NGWZRfTSxATbY5ciLvhG/878TORAoGAWJ54SA3hQbfINP8vEKMUweEXvzTDh5u5jLhneafKt6DYLi7pphLGu4UaNDFE0uekSUuntk/gXLyjCAbH+C1x7fkvuggUf8xoUMy2TUQlMmKuREwgfUeCJAsXmjdmT6eeTZxeg989Yvv7GDLTy0f1tIggbyuUWb25VWfgwtVZuekCgYBX3BsG66HhkFvvVGguzajwJZwobsH07gEeRyvj+aWxTmU9V1QjCVi+gDWYeypXogiCKoneJ1cxIT5K5B5EQHpxRgYs8jsaPIZAe5wbxTJRLuPUryYJr27+CmhO/WWGDexmIWGkCKzfPECaOsWcwqDbKHKiQE87A5G2pLXlFQME6g==
    apiModel:
      CREATE_APPLICATION_V2:
        endpoint: /lender/2.0/createApplication
        httpTimeout: 10000
        httpRetryCount: 0
      GET_APPLICATION:
        endpoint: /lender/2.0/getApplication
        httpTimeout: 2500
        httpRetryCount: 0
      GENERATE_OFFER:
        endpoint: /lender/2.0/generateOffer
        httpTimeout: 2500
        httpRetryCount: 0
      GET_OFFER:
        endpoint: /lender/2.0/getOffer
        httpTimeout: 2500
        httpRetryCount: 0
      SUBMIT_OFFER:
        endpoint: /lender/2.0/submitOffer
        httpTimeout: 3000
        httpRetryCount: 0
      INIT_AA:
        endpoint: /lender/2.0/initAccountAggregator
        httpTimeout: 2500
        httpRetryCount: 0
      GET_APPLICATION_STATUS_V2:
        endpoint: /lender/2.0/getApplicationStatus
        httpTimeout: 2500
        httpRetryCount: 0
      CHECK_DEDUPE:
        endpoint: /v1/users/dedupe
        httpTimeout: 2500
        httpRetryCount: 0

  smartcoinConfiguration:
    scope: LENDING_SMARTCOIN
    host: https://lead-admin-stage.rebase.in
    tokenEndpoint: /lender/1/token
    username: 075D7EFC38CFC10D4E5261F577F30841
    password: B7939364105A64B5AB9900CD20F8596D
    clientId: SC_SPMN_d2eH7iK8jL5gF4b_np
    xApiKey:
    authCode:
    lenderBase64PublicKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA3KSlyrKuMZpsXhydh/5yxIrc1CqQF/PO3UDji9binIBW/RZDqaYHdMIrUL3hgAJ8W3yH7i/2AbufkxDxORMYKa0+27t3JFopLmIZK6lGiQkefNALxifufNQfJcWT/9ZeXbY3bdQ6S1DWlVJpgPiyabp9WD0HYzcE0IZ2zdXXSb27ovvCxXdmYFMbL7M7Y/o2F5jQDp0TL7CRuzrhywGu9Hknaelter61G0EJi0lXSsr3jWigtS6wR2Gd8AFn7h+MH4hmnIMQaS2zKY3E/VEHzAaEMqwychsgZ9rw8yfuHHBn+5S+RzUyCopjGGuNvbTlbwgtzImxq6prD6Ez/r181QIDAQAB
    lspBase64PrivateKey: MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQDh3CHD7n/QENLd+4MyMtqLcqLHpsoTwIqHO2dAHV1kLV5eAognJXOWZRNZFvRs7Er5X4VxKVGw/cQj3+zSJF02d0wn5j/Wy4mJlnqr1POgqcxfjDMQV+BCPUj1UpNxRPOD8qnRAaO9g5NzPfkWo6oVAerOk19Gf21/u4D2odZDLxME68380eugKJ631QxoS3RjLbdoMWh/R9uHhb3xpIM6QEfsV2WSMnQpNgYD26LEWKk7gA/c0+WDLGJnA7d/9bchpAcLM69Ej8G6HG6y4l5IoWRbA57AbY1SOBpi74NTr2lj/lJURlEj8WUVfdUdGgyHagV6UEIEHcGKDmz1kkeXAgMBAAECggEABKcKMwUIsPqHvXbe3vvZMKIt5Dr1Uq7Wq653lASG1fdxSDJ5J4VH+kMewOkOI+hCKxzinNAjFKhFkA+eyz1ghiyy7ud5TS1oKeNlp4B9fEPDJ/y1WJLW1cZ5aERJMT4gXglzohCMlOzO7sLKDMNWT5R4h8TVY5hgYw8zuuDejwexS9Rmnh32FAOlDXoIAHBHQo2kZrCm385f5qNPVIo07061+Err/FkFi/ilGKQwuLsO3it4UY+m+JcL02GKoi9UgS3/sh1kdcSty7kMx5A1NKyUffGPilz26d9weypLBHt36ZIoULtQN6zzk0El7oTmYppDDGRAMaZGyVRLqnvhoQKBgQD7y8M9biQM8fkCk8ACbANiHUhlQWyuyFImhvUBQzt0SMf9gOjQQ80n5XlMcGSaRC8KnNxSwbsd0vGh1gz+eHA8l7lGjL09Aj90BPjqVfs3YH7ueOZ/6yKM2X5Y3mqwhW0yS8nu3VWZHTJqB5c6ZEJAgkFspcJvPNwuDN0Hy4XOCQKBgQDloYMfcJug3aVbdEeUU3VCqRczv2kG/ylXgy4iuwOEOfnBURgPL90p8WbPVEOTIob+VuVCB8KWVriWYzGBVISW4vKM5KgFMQfym/J/UPVrAZXp1ljDER7EArWEV56UFLVvaDcQnhMNLBIuNxpWhLoLEk9Y/aWbC9AYTfWpqZDQnwKBgC4sdYckVXQSQ2mXkpRfHle1qvr2fmmmznnGMdOjcpmmZxYzt33JkSuJxy6fRbOMYVv+2+BJKnkTn7/fss5PY7WY1R783A7+BIjQCJ1gWwsSpPQyM4Ktqc5Oa/77+nCYZyfcAhHNgM7tLbuH3NGWZRfTSxATbY5ciLvhG/878TORAoGAWJ54SA3hQbfINP8vEKMUweEXvzTDh5u5jLhneafKt6DYLi7pphLGu4UaNDFE0uekSUuntk/gXLyjCAbH+C1x7fkvuggUf8xoUMy2TUQlMmKuREwgfUeCJAsXmjdmT6eeTZxeg989Yvv7GDLTy0f1tIggbyuUWb25VWfgwtVZuekCgYBX3BsG66HhkFvvVGguzajwJZwobsH07gEeRyvj+aWxTmU9V1QjCVi+gDWYeypXogiCKoneJ1cxIT5K5B5EQHpxRgYs8jsaPIZAe5wbxTJRLuPUryYJr27+CmhO/WWGDexmIWGkCKzfPECaOsWcwqDbKHKiQE87A5G2pLXlFQME6g==
    apiModel:
      CREATE_APPLICATION_V2:
        endpoint: /lender/2.0/createApplication
        httpTimeout: 10000
        httpRetryCount: 0
      GET_APPLICATION:
        endpoint: /lender/2.0/getApplication
        httpTimeout: 2500
        httpRetryCount: 0
      GENERATE_OFFER:
        endpoint: /lender/2.0/generateOffer
        httpTimeout: 2500
        httpRetryCount: 0
      GET_OFFER:
        endpoint: /lender/2.0/getOffer
        httpTimeout: 2500
        httpRetryCount: 0
      SUBMIT_OFFER:
        endpoint: /lender/2.0/submitOffer
        httpTimeout: 3000
        httpRetryCount: 0
      INIT_AA:
        endpoint: /lender/2.0/initAccountAggregator
        httpTimeout: 2500
        httpRetryCount: 0
      GET_APPLICATION_STATUS_V2:
        endpoint: /lender/2.0/getApplicationStatus
        httpTimeout: 2500
        httpRetryCount: 0
      GET_APPLICATION_V2:
        endpoint: /lender/2.0/getApplication
        httpTimeout: 2500
        httpRetryCount: 0
      CHECK_DEDUPE:
        endpoint: /lender/v1/dedup
        httpTimeout: 2500
        httpRetryCount: 0
  finnableConfiguration:
    scope: LENDING_FINNABLE
    host: https://dlff2zjgiszet.cloudfront.net/super-money/flipkart-api/
    tokenEndpoint: /lender/1/token
    username: FLIPKART-DEV
    password: FLIPKART-DEV
    clientId: SC_SPMN_d2eH7iK8jL5gF4b_np
    xApiKey:
    authCode: FLIPKART-DEV
    lenderBase64PublicKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAnBp6sF3irBwEYxCTXoDDZ7Qj0gC20BxdJz6Qus46ScNV873GntJfwXDP7mWLITOOx0QVNZBjqqIWYlW0cr+IJ0x8AA05znRQ0w410lOEzbqeHaRraOgsHu9hFxSAdh1ET5XFBdsB/yca1nRWMtXNwW8Bt2HrbI54OTxK1Xty/r/qBoEJZknOaX8PaE7Pfsv1jYy/c3DuuF5Dso6PwMEHLctMxBmpjNBhRggw3T66OUkg/XDtA8YVb6WVp2rqxtqdx07WDLAT6Unx855yd9azqt+gGcuN0HvPk+Gp5LJUzQpVNLNEumq7wjXFJqrBXH5NfzoiK4Q5KEQUxGY7ukHrHQIDAQAB
    lspBase64PrivateKey: MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQDh3CHD7n/QENLd+4MyMtqLcqLHpsoTwIqHO2dAHV1kLV5eAognJXOWZRNZFvRs7Er5X4VxKVGw/cQj3+zSJF02d0wn5j/Wy4mJlnqr1POgqcxfjDMQV+BCPUj1UpNxRPOD8qnRAaO9g5NzPfkWo6oVAerOk19Gf21/u4D2odZDLxME68380eugKJ631QxoS3RjLbdoMWh/R9uHhb3xpIM6QEfsV2WSMnQpNgYD26LEWKk7gA/c0+WDLGJnA7d/9bchpAcLM69Ej8G6HG6y4l5IoWRbA57AbY1SOBpi74NTr2lj/lJURlEj8WUVfdUdGgyHagV6UEIEHcGKDmz1kkeXAgMBAAECggEABKcKMwUIsPqHvXbe3vvZMKIt5Dr1Uq7Wq653lASG1fdxSDJ5J4VH+kMewOkOI+hCKxzinNAjFKhFkA+eyz1ghiyy7ud5TS1oKeNlp4B9fEPDJ/y1WJLW1cZ5aERJMT4gXglzohCMlOzO7sLKDMNWT5R4h8TVY5hgYw8zuuDejwexS9Rmnh32FAOlDXoIAHBHQo2kZrCm385f5qNPVIo07061+Err/FkFi/ilGKQwuLsO3it4UY+m+JcL02GKoi9UgS3/sh1kdcSty7kMx5A1NKyUffGPilz26d9weypLBHt36ZIoULtQN6zzk0El7oTmYppDDGRAMaZGyVRLqnvhoQKBgQD7y8M9biQM8fkCk8ACbANiHUhlQWyuyFImhvUBQzt0SMf9gOjQQ80n5XlMcGSaRC8KnNxSwbsd0vGh1gz+eHA8l7lGjL09Aj90BPjqVfs3YH7ueOZ/6yKM2X5Y3mqwhW0yS8nu3VWZHTJqB5c6ZEJAgkFspcJvPNwuDN0Hy4XOCQKBgQDloYMfcJug3aVbdEeUU3VCqRczv2kG/ylXgy4iuwOEOfnBURgPL90p8WbPVEOTIob+VuVCB8KWVriWYzGBVISW4vKM5KgFMQfym/J/UPVrAZXp1ljDER7EArWEV56UFLVvaDcQnhMNLBIuNxpWhLoLEk9Y/aWbC9AYTfWpqZDQnwKBgC4sdYckVXQSQ2mXkpRfHle1qvr2fmmmznnGMdOjcpmmZxYzt33JkSuJxy6fRbOMYVv+2+BJKnkTn7/fss5PY7WY1R783A7+BIjQCJ1gWwsSpPQyM4Ktqc5Oa/77+nCYZyfcAhHNgM7tLbuH3NGWZRfTSxATbY5ciLvhG/878TORAoGAWJ54SA3hQbfINP8vEKMUweEXvzTDh5u5jLhneafKt6DYLi7pphLGu4UaNDFE0uekSUuntk/gXLyjCAbH+C1x7fkvuggUf8xoUMy2TUQlMmKuREwgfUeCJAsXmjdmT6eeTZxeg989Yvv7GDLTy0f1tIggbyuUWb25VWfgwtVZuekCgYBX3BsG66HhkFvvVGguzajwJZwobsH07gEeRyvj+aWxTmU9V1QjCVi+gDWYeypXogiCKoneJ1cxIT5K5B5EQHpxRgYs8jsaPIZAe5wbxTJRLuPUryYJr27+CmhO/WWGDexmIWGkCKzfPECaOsWcwqDbKHKiQE87A5G2pLXlFQME6g==
    apiModel:
      CREATE_APPLICATION_V2:
        endpoint: /lender/2.0/createApplication
        httpTimeout: 15000
        httpRetryCount: 0
      GET_APPLICATION:
        endpoint: /lender/2.0/getApplication
        httpTimeout: 2500
        httpRetryCount: 0
      GENERATE_OFFER:
        endpoint: /lender/2.0/generateOffer
        httpTimeout: 2500
        httpRetryCount: 0
      GET_OFFER:
        endpoint: /lender/2.0/getOffer
        httpTimeout: 2500
        httpRetryCount: 0
      SUBMIT_OFFER:
        endpoint: /lender/2.0/submitOffer
        httpTimeout: 4000
        httpRetryCount: 0
      INIT_AA:
        endpoint: /lender/2.0/initAccountAggregator
        httpTimeout: 2500
        httpRetryCount: 0
      GET_APPLICATION_STATUS_V2:
        endpoint: /lender/2.0/getApplicationStatus
        httpTimeout: 1500
        httpRetryCount: 0
      CHECK_DEDUPE:
        endpoint: /lender/2.0/dedupe
        httpTimeout: 2500
        httpRetryCount: 0
  testConfiguration:
    scope: LENDING_TEST
    host: https://services.dmifinance.in
    tokenEndpoint: /lender/1/token
    username: <EMAIL>
    password: DMI_SM@123
    clientId: 423141fdseq13er4133ed1q
    xApiKey:
    authCode: aaffc7b9-203a-4e9a-aed0-e886db8eb0a7
    lenderBase64PublicKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAyFGR+/N38srrBmOvRJRGAEb1YDKcZQAVg7rg0wXvSBMJc2sBqrQAOOg5gi7IiCBU/hD8J6sOAP2imMjLTItmhN6k0l2Z/7lP40QSEN6mb1wPAXfSjHiCptLaEmDyFfC5RV6Nnb7MNx2wRQZhOmBW8Aw2YjJRehSXGUOOG7qHII/hEQ2yLyg8OK0fem4jU7c08gEpya6NuPYEwu/jh5Q4WCnoIxmtFPDPQYmGsCznzCEpynrWkIGYyPWIZXbpUJRylCUB0QjVRdPJhkwGBEPMuIlkn/9/PRdgq2H57g7igXPyE6lqBjMxlHGV58uDQ/nThWwNpEvzUifTXeExXR2DZQIDAQAB
    lspBase64PrivateKey: MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQDh3CHD7n/QENLd+4MyMtqLcqLHpsoTwIqHO2dAHV1kLV5eAognJXOWZRNZFvRs7Er5X4VxKVGw/cQj3+zSJF02d0wn5j/Wy4mJlnqr1POgqcxfjDMQV+BCPUj1UpNxRPOD8qnRAaO9g5NzPfkWo6oVAerOk19Gf21/u4D2odZDLxME68380eugKJ631QxoS3RjLbdoMWh/R9uHhb3xpIM6QEfsV2WSMnQpNgYD26LEWKk7gA/c0+WDLGJnA7d/9bchpAcLM69Ej8G6HG6y4l5IoWRbA57AbY1SOBpi74NTr2lj/lJURlEj8WUVfdUdGgyHagV6UEIEHcGKDmz1kkeXAgMBAAECggEABKcKMwUIsPqHvXbe3vvZMKIt5Dr1Uq7Wq653lASG1fdxSDJ5J4VH+kMewOkOI+hCKxzinNAjFKhFkA+eyz1ghiyy7ud5TS1oKeNlp4B9fEPDJ/y1WJLW1cZ5aERJMT4gXglzohCMlOzO7sLKDMNWT5R4h8TVY5hgYw8zuuDejwexS9Rmnh32FAOlDXoIAHBHQo2kZrCm385f5qNPVIo07061+Err/FkFi/ilGKQwuLsO3it4UY+m+JcL02GKoi9UgS3/sh1kdcSty7kMx5A1NKyUffGPilz26d9weypLBHt36ZIoULtQN6zzk0El7oTmYppDDGRAMaZGyVRLqnvhoQKBgQD7y8M9biQM8fkCk8ACbANiHUhlQWyuyFImhvUBQzt0SMf9gOjQQ80n5XlMcGSaRC8KnNxSwbsd0vGh1gz+eHA8l7lGjL09Aj90BPjqVfs3YH7ueOZ/6yKM2X5Y3mqwhW0yS8nu3VWZHTJqB5c6ZEJAgkFspcJvPNwuDN0Hy4XOCQKBgQDloYMfcJug3aVbdEeUU3VCqRczv2kG/ylXgy4iuwOEOfnBURgPL90p8WbPVEOTIob+VuVCB8KWVriWYzGBVISW4vKM5KgFMQfym/J/UPVrAZXp1ljDER7EArWEV56UFLVvaDcQnhMNLBIuNxpWhLoLEk9Y/aWbC9AYTfWpqZDQnwKBgC4sdYckVXQSQ2mXkpRfHle1qvr2fmmmznnGMdOjcpmmZxYzt33JkSuJxy6fRbOMYVv+2+BJKnkTn7/fss5PY7WY1R783A7+BIjQCJ1gWwsSpPQyM4Ktqc5Oa/77+nCYZyfcAhHNgM7tLbuH3NGWZRfTSxATbY5ciLvhG/878TORAoGAWJ54SA3hQbfINP8vEKMUweEXvzTDh5u5jLhneafKt6DYLi7pphLGu4UaNDFE0uekSUuntk/gXLyjCAbH+C1x7fkvuggUf8xoUMy2TUQlMmKuREwgfUeCJAsXmjdmT6eeTZxeg989Yvv7GDLTy0f1tIggbyuUWb25VWfgwtVZuekCgYBX3BsG66HhkFvvVGguzajwJZwobsH07gEeRyvj+aWxTmU9V1QjCVi+gDWYeypXogiCKoneJ1cxIT5K5B5EQHpxRgYs8jsaPIZAe5wbxTJRLuPUryYJr27+CmhO/WWGDexmIWGkCKzfPECaOsWcwqDbKHKiQE87A5G2pLXlFQME6g==
    apiModel:
      CREATE_APPLICATION:
        endpoint: /lender/1/createApplication
        httpTimeout: 10000
        httpRetryCount: 0
      GET_APPLICATION:
        endpoint: /lender/1/getApplication
        httpTimeout: 1500
        httpRetryCount: 0
      GET_APPLICATION_STATUS:
        endpoint: /lender/1/getApplicationStatus
        httpTimeout: 1500
        httpRetryCount: 0
  mpockketConfiguration:
    scope: LENDING_MPOCKKET
    host: https://stg-api.mpkt.in
    apiModel:
      CHECK_DEDUPE:
        endpoint: /acquisition-affiliate/v1/dedupe/check/hashed
        httpTimeout: 2500
        httpRetryCount: 0


smUserServiceClientConfig:
  url: "http://kavach-service-pg.kavach-prod.fkcloud.in"
  clientId: "supermoney"
  fkUserProfileDisabled: false

digitapAiConfig:
  url: "https://svcdemo.digitap.work"
  clientId: "18727363"
  clientSecret: "FpTbd3JLEO1zfdt10xuDlgZKRbiqDKhh"
  smPrivateKey: MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCIQBzzwI5V5OIrNV9fZ0/ofYJf9yNY/skb24MOMQtbwFfgpwjbIC5pH5I2YacSQAWgCgzzfz8DLcI72+m3qdaAp7X4Oa6Wp8Ar5Bwx2BfnNCgOs5snVBrtXyOeXGHr8S4wyClI6PPlTCRrQ3zSUnCvzF/b1cs2nxqBUSXAw+M4M2eayf++aw/GO4y81M9C7TPDt6DNPCBjiCgqvAbvB5B7Q7FnF+oE03r+xCe31BP2Do/mvPmVS20IZlYDyViqT4xi3q/0gE8fQzOj60fFZM/lf7zsYUfMreJj523xyIlr4gq+1h5+T5GBBvyY9X/7l99rPWF4WgR3nyx0m2zEsVCHAgMBAAECggEAApqbEwuAjOKxcdr0xil+zPYtGjFqcwZ3iHlDAW/thCTiaRA4KpvsVyT70d8+5qnjkaHvim3JogFrX2J+Dn0H9PB/mU1c6VMuaIhVA5zaJBiKlXFYS0GfvB6f2yRv2KcE13jT1Slik+THI2eoSIq7zZhN0rE8z3/t7T/ERXFY89d8hRIvm9PKVKN3O1rjyhu5EAcy5kDdfAraCAXLX7r3rCzRCVTrvrxM4XoHppyYqp6v0aHrNwb3JEizXkfCYOoc3ttX+yleiGUti4POV4AFHL6BJl5oiAdEY4NbodSfadv8oZ3LF+iZw+FsXB+9FQwSNmjWMUxEUaV7qQi+Q6Y4AQKBgQC+ZUEdQzfydoUNsFf7X7UemJGLHJHi/LgMk0KfWjYl35EmK1n9SoYHif9APqvR7WxVf5/JzJdSrG4ShbDrVsGkKFE/4iwrNHRJpwtwOZ2mxhs+2WP2LjCmCGNJYevy6vKK2m4NCj20yoGtF9Y+YoYKAU+Fy7kjAVpxD8zVrHUoAQKBgQC3Mru9ylHX1TjSeh5ekUFzIx/DbrHbG7uNKB3bxc1ZNK+9yjYXT7QIGBiTiq8zlHegBaxKrCdygMrbkEKZ80mvNbScoLe9xQ4VR4H0GSSLYX0+10ue/n331ELPZP1XwlxxJ6qoJpDbFOk1b1HnMte7xEKOsYrIVu9WAyCxyik4hwKBgHdpK9h9gwZHfRVjxxp6IS8qQwX7iifRlmEUrtEglBtWx0MoJ01rwvnRe8xAQiTsgmhxHbdD4JhSWsq1HIvNIq+hNawaXtdOE6zp3oupJw4k64uUblRFDe/kDNlGV+RHpIyfiCwXgzHzXDdCXvVLd4Oo5MYmSlwBCkckwLQ2cFgBAoGAB1/FathP/w4YIXDE9C2IoufRCX7VLeqp2/63ZPdLUsHqKQuQrdOJfaHqq1Wzw7HANvLDgqX3WIaSo8/kvmmql6DjykI14y+hZ33fX0H71JhyACuOYbIAxA8JOWvpPrUWH3/nQKl8vzTSRlJay6GiSW6W90NyQKovLBkoIb/vdNUCgYAO7JeMLsb0JRvf0/vETdGMvEsGt2KBaSI5HJw8LopQdIJqNZ9gZXQhBwgF9BWdvXtAZ9Q0isHsiE65EzMdTNbtqGYOiX53vG0HddAieb+H7FPkhOGzgqcpEIZ2Ff2RxrVLoJGx6jJA75rAncRpmrMdOdy4rwv1+IDS5AyTf5B1+A==
  digitapPublicKey: MIGeMA0GCSqGSIb3DQEBAQUAA4GMADCBiAKBgGqKBgwa8Ho1Z/R354lhBTXT5NgkJt8mXzSXJFXVMO/cru3EWM6dv6JnbdnqVLNhN5qZQej13oHgdh5t5eAQhpm2tmfri0w2i23MKBPrf4W2Wkqmndh8eJeeyV+zV6r9I6ULx0AbtJmjuos5CGYuUSP7aDfpaWgVTPuWmxCZVEdtAgMBAAE=
  isMockEnabled: true

fixeraConfig:
  endpoint : "https://users-api.dev.fixerra.in"
  consolidatedViewUrl : "https://investments-api.dev.fixerra.in"

digioConfiguration:
  serverUrl: "https://ext.digio.in:444"
  clientId: "ACK241129142356221WJC1OZ21KB93D4"
  clientSecret: "U5WBA7H24GITENSB87PU8ANSLWD65AKF"
  trustStorePath: "/etc/digio/preprod-client-truststore.jks"
  trustStorePass: supermoney
  keyStorePath: "/etc/digio/preprod-client-keystore.jks"
  keyStorePass: supermoney
  upiMandateCorporateConfigId: "TSE241129143055241F1JFMPH56CHZZY"
  apiMandateCorporateConfigId: "TSE241129143758745OBEIQKGOPAVS3H"
  encryptionKey: "f7a4b3c92e8d4b7fa2391bc5d8e6a172"
  isgcp: true
  encryption:
    publicKey: "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAwvPO9FVEJWeW9jIBXQezX2g-8X-mPRR48OvHRjb05G-F2di4TdHTqwxc-XEFuKz0PjLLpqY0A11nF1bo4sMxRAKyTU2IKcmybH0yTrdx4pp-bsADS4xnV0GaoeOXg3JV1BdU7DMVHr910XY_J32BzX6C1DJ3ZWyROeywQNhSRYYEMUfrBAbfe4ghIdmnwdWgOzvmzp27VVFWRNUGLF_erx1MQAgcQlFopjDb-G8S0gnt7TVBIvt41MRdCT1BnMYpVSV1vO7sfWeEPnBRW8dzG49cl0Lz_D7CAZ0wEfhKazM6gqm3ehSlB6oKOfmb27Iu16ZyHoV_ZjWgQQ6-tPiJLQIDAQAB"
    privateKey: "MIIEvwIBADANBgkqhkiG9w0BAQEFAASCBKkwggSlAgEAAoIBAQCtUdzwJB90u1Ojht5fxCyM619077km8zZgiV7GG8EKvshWa4Y1nJ4dCStKMSjZYfs3ToPrM1k4sebJyCmsMeCcdkvW+4YHk912+ftUokdkQRE64y9BIRuNE7MpYCQEuxJVDOtgkLE3DCyDQj6PZx8PpzfVoiitQzQcDOOpyPJbrr6yv/FkCwjMjbbiIj3yZa81eT2W6OvDpuyLs4zceXKJpwm13F3HPckzwOBcZ/4EzO/6CpjRHVaIi81R6Jh5e8DXteC4oJpErcVoZr558Chscq6HYnQ26w201itnCTt1yzmOaIehiKU2VdKNiMZ8lN+bkPx8X0wDstRkM3KXJ5MTAgMBAAECggEAWsKmrv5LlqZOGzESHBYvui+7khHC5HmyPQVSou1/vm+/mzm9Uy/LwKq/h5+Mo6Fim0i7XhxKmZzXnUz7PZf05howhsi6HBe7foVTrrM4LXDBZBP59+ItekgZpw4Ybn3EENOuIqhClPAEE0xKVHAM3PaVD4NWR70Mea/qPEvlVkqkJ3jRqRjwJu9ZtiPzqto2E2y+EWK4u9JU21oJWhEjk4LdB2mOkdjLfDobMBvbxuxGKwwLlhpB0yABOKbL5PIVs0x7ZjXzyZbNo1S5XWh1rYM4aPLo9fV8PTZ/88ONOHVoWOXpjtbdUdRjzW8qf5S+qfcszkKaZlfu0/Fx0sXDmQKBgQDSuhnEUI9FWBXiyZMPUBZWVbAV3R3oBTW/VRaq5LqqQuKOb8rjE0mWqby/xEltw607CBWVwKmb4Q/VHDC045u4rQYf+1iFjaszPWh/754WRmzSEMN0Csnr5wEBy8JePTELMMoB7453h9nNS8TkwN9PcWPW43K0JGNLy2v/3nkuzQKBgQDSjmHiUYEM5NkYNjSgTVzor+eypi0ij+y1A7fMaq0oivz11Hqv6lw3dGjCYzWXcmhrrSCbipBjhnpCP0f6iFYf7BIdeEOsIx1142UW88DfX1eFJF7/3hXpXkdnArSXp4i+D9DD+c7xQzSGUjiMNJKN4mNXXvBkg7qVH0FtJ7IJXwKBgQDQH1uJh6Mo8smS1lE3Q3r8Q98IiP2bhWHDGa+skwFFXY/S69ivEwKQGg5APAXlsegqsF5ZLBKX0N3JNfFpBo9rAwBUn/Ka3QOkNNc73BX/nAioJERWHL23Dszw0CSk3LzOd55b8RR9O6BCYb4Ry+gB11SrDLLh9jpXxo5hBIIdVQKBgQDQ32gg8AOaO+dUymkYYMZ7Gg06PvllYOtLgp2xTl3qaz6Xy6nNmyrR5EwMEgjW7SrDLmK7ihDEemIMF+SalucBEdnCnExdlhQin2roAvThYh5bOQJ4xuJruBgTCy8Fhq/zA5Nw5f6xfZ68ZRItm/WKhPtRcB//RmIwjmoEE63c9wKBgQCC4DrsHNrasTMm3gYEcArFGSXJXDyBDGV13oBv42vW5ODfOw+kTYq9asHQQVkTBAaiKJ6nu0uksjCMUT21pwZCBBMC2QjsvFMdfLl3onl+IkPDy5hdcd9oICPFUA9iF11Y7H7CE//dxfJOm+XHs3MXe2n6b8csw469ieQcPf1dRQ=="


mpockketConfiguration:
  host: https://stg-api.mpkt.in
  apiKey: 83D7473E3C0049C59755FC365EAEC
  lenderPublicKey: MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEAn6DFdNwsE17o9oe/6Vy64fhjLAGpFSVB0FePylM6G1SNfkQ5NJbmWcbUSE/zM9cnbhns5pe3JXuu0pJ5tsZkH/u4dZ+OmDziaC7eL3gHX45ZM52ML4VneTnX+bBQuwrHmyiECKlSY6/X2hFTbY/y2iq3tPO1+OEwm/nxOEUxlpv7qj/HYucgMtXd0ScUbhwz0epGAk5yYwROUhsccYIWaKYGjXWx8bEFZzEpN7qXrNHo9fPQsWBB657wMbLB9NACH2Ieb1d0+5OUxLvsvfuJkusT+PTI3AeLdpGVF9ouRKWrgNCiTF1rmXt/MxL4O6RHd/kXnyYnByI3EInEijUM5kVvkKTC26p/jsz8/NEJgz6CU5sqJnJGaTsnWeNNSsQopJ/iW3e2tcV2SDSZvoeKurgYDOyW5RdlXOgwW7DE7LSDcUjAvCKBrgqd2JzAKi5SY9ADW0TJsNdwNc55PYTOFb8Qa+bBb97tcvPxd7UVb+xzCD1XdyX6VEsbikiF7hPVR8UfjpljdZzxxhJKm+jk+TLzYbgm2jQ988ihDgSLqJeUY6i0k/eGmHPKMgIdVelnNohTzcjqlmX40JtJIvt2T6ai518xvjTlgwWOwc1xZ2nDkLupMWuNomZvREUWujIy42QE1Bbroi6ncdz/alkOpi9YY5fejH140BvbdP0xKcMCAwEAAQ==
  lspPrivateKey: MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQDh3CHD7n/QENLd+4MyMtqLcqLHpsoTwIqHO2dAHV1kLV5eAognJXOWZRNZFvRs7Er5X4VxKVGw/cQj3+zSJF02d0wn5j/Wy4mJlnqr1POgqcxfjDMQV+BCPUj1UpNxRPOD8qnRAaO9g5NzPfkWo6oVAerOk19Gf21/u4D2odZDLxME68380eugKJ631QxoS3RjLbdoMWh/R9uHhb3xpIM6QEfsV2WSMnQpNgYD26LEWKk7gA/c0+WDLGJnA7d/9bchpAcLM69Ej8G6HG6y4l5IoWRbA57AbY1SOBpi74NTr2lj/lJURlEj8WUVfdUdGgyHagV6UEIEHcGKDmz1kkeXAgMBAAECggEABKcKMwUIsPqHvXbe3vvZMKIt5Dr1Uq7Wq653lASG1fdxSDJ5J4VH+kMewOkOI+hCKxzinNAjFKhFkA+eyz1ghiyy7ud5TS1oKeNlp4B9fEPDJ/y1WJLW1cZ5aERJMT4gXglzohCMlOzO7sLKDMNWT5R4h8TVY5hgYw8zuuDejwexS9Rmnh32FAOlDXoIAHBHQo2kZrCm385f5qNPVIo07061+Err/FkFi/ilGKQwuLsO3it4UY+m+JcL02GKoi9UgS3/sh1kdcSty7kMx5A1NKyUffGPilz26d9weypLBHt36ZIoULtQN6zzk0El7oTmYppDDGRAMaZGyVRLqnvhoQKBgQD7y8M9biQM8fkCk8ACbANiHUhlQWyuyFImhvUBQzt0SMf9gOjQQ80n5XlMcGSaRC8KnNxSwbsd0vGh1gz+eHA8l7lGjL09Aj90BPjqVfs3YH7ueOZ/6yKM2X5Y3mqwhW0yS8nu3VWZHTJqB5c6ZEJAgkFspcJvPNwuDN0Hy4XOCQKBgQDloYMfcJug3aVbdEeUU3VCqRczv2kG/ylXgy4iuwOEOfnBURgPL90p8WbPVEOTIob+VuVCB8KWVriWYzGBVISW4vKM5KgFMQfym/J/UPVrAZXp1ljDER7EArWEV56UFLVvaDcQnhMNLBIuNxpWhLoLEk9Y/aWbC9AYTfWpqZDQnwKBgC4sdYckVXQSQ2mXkpRfHle1qvr2fmmmznnGMdOjcpmmZxYzt33JkSuJxy6fRbOMYVv+2+BJKnkTn7/fss5PY7WY1R783A7+BIjQCJ1gWwsSpPQyM4Ktqc5Oa/77+nCYZyfcAhHNgM7tLbuH3NGWZRfTSxATbY5ciLvhG/878TORAoGAWJ54SA3hQbfINP8vEKMUweEXvzTDh5u5jLhneafKt6DYLi7pphLGu4UaNDFE0uekSUuntk/gXLyjCAbH+C1x7fkvuggUf8xoUMy2TUQlMmKuREwgfUeCJAsXmjdmT6eeTZxeg989Yvv7GDLTy0f1tIggbyuUWb25VWfgwtVZuekCgYBX3BsG66HhkFvvVGguzajwJZwobsH07gEeRyvj+aWxTmU9V1QjCVi+gDWYeypXogiCKoneJ1cxIT5K5B5EQHpxRgYs8jsaPIZAe5wbxTJRLuPUryYJr27+CmhO/WWGDexmIWGkCKzfPECaOsWcwqDbKHKiQE87A5G2pLXlFQME6g==

cardServiceClientConfig:
  baseUrl: http://card-service-next.sm-card-gateway-gcp-prod.fkcloud.in
  connectTimeoutMs: 10000
  readTimeoutMs: 10000
  callTimeoutMs: 10000

gupshupWebhookConfig:
  webhookApiKey : MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEAn6DFdNwsE17o9oe  #overwritten by cryptex as Encrypted

