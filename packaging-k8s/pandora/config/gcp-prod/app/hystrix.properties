hystrix.threadpool.USER_SERVICE.coreSize=100
hystrix.threadpool.USER_SERVICE.queueSizeRejectionThreshold=10
hystrix.threadpool.USER_SERVICE.maxQueueSize=10
hystrix.command.GET_ADDRESS_DETAILS.execution.isolation.thread.timeoutInMilliseconds=1000
hystrix.command.GET_USER_PROFILE.execution.isolation.thread.timeoutInMilliseconds=1000
hystrix.command.GET_NON_VERIFIED_USER_PROFILE.execution.isolation.thread.timeoutInMilliseconds=1000
hystrix.command.GET_ADDRESS_FROM_ACCOUNT_ID.execution.isolation.thread.timeoutInMilliseconds=1000
hystrix.command.GET_ALTERNATE_NUMBERS.execution.isolation.thread.timeoutInMilliseconds=1000
hystrix.threadpool.LOGIN_SERVICE.coreSize=100
hystrix.threadpool.LOGIN_SERVICE.queueSizeRejectionThreshold=10
hystrix.threadpool.LOGIN_SERVICE.maxQueueSize=10
hystrix.command.GENERATE_OTP_USING_ACCOUNT_ID.execution.isolation.thread.timeoutInMilliseconds=1000
hystrix.command.VERIFY_OTP_USING_ACCOUNT_ID_AND_LOGIN_ID.execution.isolation.thread.timeoutInMilliseconds=1000
hystrix.command.GET_VERIFIED_USER_RESPONSE.execution.isolation.thread.timeoutInMilliseconds=1000
hystrix.command.GET_USER_BY_LOGIN_ID.execution.isolation.thread.timeoutInMilliseconds=1000
hystrix.threadpool.TijoriLenderActionKey.coreSize: 100
hystrix.threadpool.TijoriLenderActionKey.maxQueueSize: 10
hystrix.threadpool.TijoriLenderActionKey.allowMaximumSizeToDivergeFromCoreSize: false
hystrix.command.TijoriLenderActionKey.execution.isolation.thread.timeoutInMilliseconds: 10000
hystrix.threadpool.TijoriFetchBorrowerKey.coreSize: 100
hystrix.threadpool.TijoriFetchBorrowerKey.maxQueueSize: 10
hystrix.threadpool.TijoriFetchBorrowerKey.allowMaximumSizeToDivergeFromCoreSize: false
hystrix.command.TijoriFetchBorrowerKey.execution.isolation.thread.timeoutInMilliseconds: 5000
hystrix.threadpool.GEO_LOCATION.coreSize=100
hystrix.threadpool.GEO_LOCATION.queueSizeRejectionThreshold=10
hystrix.threadpool.GEO_LOCATION.maxQueueSize=10
hystrix.command.FETCH_GEO_LOCATION.execution.isolation.thread.timeoutInMilliseconds=3500
