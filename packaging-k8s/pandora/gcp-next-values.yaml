namespace: sm-pandora-prod

fcp:
  team: "supermoney-engg"
  appId: "sm-pandora-prod"
  mail: "supermoney"
  service: "sm-pandora-next"

mtl:
  cosmos:
    statsd: enabled
    tail: disabled
    config: cosmos-config-next
  asterix: disabled
  logsvc:
    name: mtlconfig-next
    enabled: false
    envName: gcp-next
    sourceLoc: rsyslog.conf

pod:
  replicaCount: 1

hostpopulator:
  state: enabled
  cpu: 0.2
  nameservers:
    - dns.fkinternal.com

image:
  service:
    tag: "1708085-1042-flow-48873af7a9b39949f7a06167794f938114f61dc0-05-Jan-23_12-19-22"
    repository: lending-los/pandora
    runAsUser: 108
    runAsGroup: 112
    volumes:
      - app-config-next
      - app-log
      - cosmos-config-next
      - cfg-svc-metadata-next
      - host-populator-logs
      - host-d
      - host-populator-config-next
    livenessCheck:
      initialDelay: 160
      checkInterval: 15
      method: tcp
      port: 8215
    readinessCheck:
      initialDelay: 130
      checkInterval: 15
      method: http
      port: 8215
      path: /admin/healthcheck
    ports:
      - name: application
        containerPort: 8214
      - name: admin
        containerPort: 8215
      - name: jmx
        containerPort: 9311
    maxCpuLimit: 1
    maxCpuAsk: 1
    maxMemoryLimit: 3Gi
    maxMemoryAsk: 3Gi
  fluentbit:
    registry: edge.fkinternal.com/docker-external
    repository: grafana/fluent-bit-plugin-loki
    tag: main-e2ed1c0
    #    imagePullPolicy: IfNotPresent
    #    ports:
    #      - name: metrics
    #        containerPort: 2020
    #        protocol: TCP
    maxCpuLimit: 0.1
    maxCpuAsk: 0.1
    maxMemoryLimit: 500Mi
    maxMemoryAsk: 500Mi
    volumes:
      - fluent-bit-config-volume-next
      - app-log


volumes:
  - name: app-config-next
    type: configMap
    value: app-config-next
    mountPath: /etc/config/
  - name: app-log
    type: emptyDir
    value: {}
    mountPath: /var/log/
  - name: cfg-svc-metadata-next
    type: configMap
    value: cfg-svc-metadata-next
    mountPath: /etc/default/
  - name: host-populator-logs
    type: emptyDir
    value: { }
    mountPath: /var/log/hosts-populator/
  - name: host-d
    type: emptyDir
    value: { }
    mountPath: /etc/hosts.d/
  - name: fluent-bit-config-volume-next
    type: configMap
    value: fluent-bit-config-map-next
    mountPath: /fluent-bit/etc/

configMaps:
  envName: gcp-next
  configs:
    - name: app-config-next
      source : dir
      sourceLoc : app
    - name: cosmos-config-next
      source: dir
      sourceLoc: cosmos
    - name: fluent-bit-config-map-next
      source: dir
      sourceLoc : fluent-bit
    - name: cfg-svc-metadata-next
      source: data
      data:
        cfg-api: |
          host=api.aso1.cfgsvc-prod.fkcloud.in
          port=80
        fk-env: |
          prod
    - name: host-populator-config-next
      source: dir
      sourceLoc: host-populator

services:
  - name: pandora-service-next
    type: LoadBalancer
    ports:
      - port: 10010
        targetPort: 8214
        protocol: TCP
        name: pandora-next
    LoadBalancer:
      mode: http
      vip: sm-pandora-prod-next-vip-1
      healthCheckPort: 8215
      healthCheckPath: "/admin/healthcheck"
      healthCheckTimeout: 1s
      healthCheckInterval: 5s
      loadBalancingAlgorithm: leastconn
      backendService: sm-pandora-prod-next-vip-1
      backendPort: 8214
      frontendPort: 80
      doNotDeleteVip: true
      gcp:
        type: "Internal"
        globalAccess: true # Enables the IP address of the forwarding rule to be accessible by clients in any region of the VPC network or a connected network.
  - name: sm-pandora-next-global
    type: LoadBalancer
    ports:
      - port: 10010 # useless but not to be changed
        targetPort: 8214
        protocol: TCP
        name: sm-pandora-next-global
    LoadBalancer:
      mode: https
      sslCertId: www-sm-pandora-next-super-money-2
      cloudArmorSecurityPolicyId: cloud-armor-supermoney-app-regional-policy
      hostnames: sm-pandora-next.super.money
      domainName: sm-pandora-next.super.money
      vip: sm-pandora-next-vip-2
      healthCheckPort: 8215
      healthCheckPath: "/admin/healthcheck"
      healthCheckTimeout: 1s
      healthCheckInterval: 5s
      loadBalancingAlgorithm: leastconn
      backendPort: 8214
      frontendPort: 443
      scope: regional-external # should create external LB
      doNotDeleteVip: true
      globalAccess: true

servicemesh:
  enabled: false