namespace: sm-pandora-prod

fcp:
  team: "supermoney-engg"
  appId: "sm-pandora-prod"
  mail: "supermoney"
  service: "sm-pandora-prod"

mtl:
  cosmos:
    statsd: enabled
    tail: enabled
    config: cosmos-config
  asterix: disabled

pod:
  replicaCount: 3

hostpopulator:
  state: enabled
  cpu: 0.2
  nameservers:
    - dns.fkinternal.com

image:
  service:
    tag: "1708085-1042-flow-48873af7a9b39949f7a06167794f938114f61dc0-05-Jan-23_12-19-22"
    repository: lending-los/pandora
    runAsUser: 108
    runAsGroup: 112
    volumes:
      - app-config
      - app-log
      - cosmos-config
      - cfg-svc-metadata
      - host-populator-logs
      - host-d
      - host-populator-config
      - sumo-observability-volume
    livenessCheck:
      initialDelay: 160
      checkInterval: 15
      method: tcp
      port: 8215
    readinessCheck:
      initialDelay: 130
      checkInterval: 15
      method: http
      port: 8215
      path: /admin/healthcheck
    ports:
      - name: application
        containerPort: 8214
      - name: admin
        containerPort: 8215
      - name: jmx
        containerPort: 9311
    maxCpuLimit: 1.1
    maxCpuAsk: 1.1
    maxMemoryLimit: 4Gi
    maxMemoryAsk: 4Gi
    envVars:
      - name: SUMO_OBSERVABILITY_ENABLED
        source: value
        value: "TRUE"
  fluentbit:
    registry: edge.fkinternal.com/docker-external
    repository: grafana/fluent-bit-plugin-loki
    tag: main-e2ed1c0
    #    imagePullPolicy: IfNotPresent
    #    ports:
    #      - name: metrics
    #        containerPort: 2020
    #        protocol: TCP
    maxCpuLimit: 0.1
    maxCpuAsk: 0.1
    maxMemoryLimit: 500Mi
    maxMemoryAsk: 500Mi
    volumes:
      - fluent-bit-config-volume
      - app-log


volumes:
  - name: app-config
    type: configMap
    value: app-config
    mountPath: /etc/config/
  - name: app-log
    type: emptyDir
    value: {}
    mountPath: /var/log/
  - name: cfg-svc-metadata
    type: configMap
    value: cfg-svc-metadata
    mountPath: /etc/default/
  - name: host-populator-logs
    type: emptyDir
    value: { }
    mountPath: /var/log/hosts-populator/
  - name: host-d
    type: emptyDir
    value: { }
    mountPath: /etc/hosts.d/
  - name: fluent-bit-config-volume
    type: configMap
    value: fluent-bit-config-map
    mountPath: /fluent-bit/etc/
  - name: sumo-observability-volume
    type: configMap
    value: sumo-observability-config
    mountPath: /etc/sumo-observability/

configMaps:
  envName: gcp-prod
  configs:
    - name: app-config
      source : dir
      sourceLoc : app
    - name: cosmos-config
      source: dir
      sourceLoc: cosmos
    - name: fluent-bit-config-map
      source: dir
      sourceLoc : fluent-bit
    - name: sumo-observability-config
      source: dir
      sourceLoc: sumo-observability
    - name: cfg-svc-metadata
      source: data
      data:
        cfg-api: |
          host=api.aso1.cfgsvc-prod.fkcloud.in
          port=80
        fk-env: |
          prod
    - name: host-populator-config
      source: dir
      sourceLoc: host-populator

services:
  - name: pandora-service
    type: LoadBalancer
    ports:
      - port: 10010
        targetPort: 8214
        protocol: TCP
        name: pandora
    LoadBalancer:
      mode: http
      vip: sm-pandora-prod-vip-1
      healthCheckPort: 8215
      healthCheckPath: "/admin/healthcheck"
      healthCheckTimeout: 1s
      healthCheckInterval: 5s
      loadBalancingAlgorithm: leastconn
      backendService: sm-pandora-prod-vip-1
      backendPort: 8214
      frontendPort: 80
      doNotDeleteVip: true
      gcp:
        type: "Internal"
        globalAccess: true # Enables the IP address of the forwarding rule to be accessible by clients in any region of the VPC network or a connected network.
  - name: sm-pandora-vip-2
    type: LoadBalancer
    ports:
      - port: 10010
        targetPort: 8214
        protocol: TCP
        name: pandora
    LoadBalancer:
      mode: http
      vip: sm-pandora-prod-vpc-vip-2
      healthCheckPort: 8215
      healthCheckPath: "/admin/healthcheck"
      healthCheckTimeout: 1s
      healthCheckInterval: 5s
      loadBalancingAlgorithm: leastconn
      backendService: sm-pandora-prod-vpc-vip-2
      backendPort: 8214
      frontendPort: 80
      doNotDeleteVip: true
      gcp:
        type: "Internal"
        globalAccess: true # Enables the IP address of the forwarding rule to be accessible by clients in any region of the VPC network or a connected network.
  - name: sm-pandora-global
    type: ClusterIP
    ports:
      - port: 10010 # useless but not to be changed
        targetPort: 8214
        protocol: TCP
        name: pandora
    LoadBalancer:
      mode: https
      sslCertId: www.sm-pandora.super.money-1
      domainName: www.sm-pandora.super.money
      vip: sm-pandora-prod-vip-2
      name: sm-pandora-global
      healthCheckPort: 8215
      healthCheckPath: "/admin/healthcheck"
      healthCheckTimeout: 1s
      healthCheckInterval: 5s
      loadBalancingAlgorithm: leastconn
      backendService: sm-pandora-prod-vip-2
      backendPort: 8214
      frontendPort: 443
      scope: "GLOBAL"
      doNotDeleteVip: true

servicemesh:
  enabled: false


