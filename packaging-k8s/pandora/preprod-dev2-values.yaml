namespace: sm-pandora-playground

fcp:
  team: "supermoney-engg"
  appId: "sm-pandora-preprod"
  mail: "supermoney"
  service: "sm-pandora-preprod-dev2"
  env: "dev2"
mtl:
  cosmos:
    statsd: enabled
    tail: enabled
    config: cosmos-config-dev2
  asterix: disabled
  logsvc:
    enabled: true
    envName: preprod
    sourceLoc: rsyslog.conf

environment: dev


pod:
  replicaCount: 1

hostpopulator:
  state: enabled
  cpu: 0.2
  nameservers:
    - dns.fkinternal.com

image:
  service:
    tag: "1774756-1076-flow-f414ac22d5cb8adf157c30d9a6be15d858d2600d-20-Jan-23_17-15-24"
    registry: jfrog.fkinternal.com
    repository: lending-los/pandora-preprod
    runAsUser: 108
    runAsGroup: 112
    volumes:
      - app-dev2-config
      - app-log
      - cosmos-config-dev2
      - cfg-svc-metadata-dev2
      - host-populator-logs
      - host-d
      - host-populator-config-dev2
    livenessCheck:
      initialDelay: 160
      checkInterval: 15
      method: tcp
      port: 8215
    readinessCheck:
      initialDelay: 130
      checkInterval: 15
      method: http
      port: 8215
      path: /admin/healthcheck
    ports:
      - name: application
        containerPort: 8214
      - name: admin
        containerPort: 8215
      - name: jmx
        containerPort: 9311
    maxCpuLimit: 1
    maxCpuAsk: 1
    maxMemoryLimit: 4Gi
    maxMemoryAsk: 4Gi

volumes:
  - name: app-dev2-config
    type: configMap
    value: app-dev2-config
    mountPath: /etc/config/
  - name: app-log
    type: emptyDir
    value: {}
    mountPath: /var/log/
  - name: cfg-svc-metadata-dev2
    type: configMap
    value: cfg-svc-metadata-dev2
    mountPath: /etc/default/
  - name: relayer-config-dev2
    type: configMap
    value: relayer-config-dev2
    mountPath: /etc/fk-sc-mq/
  - name: host-populator-logs
    type: emptyDir
    value: { }
    mountPath: /var/log/hosts-populator/
  - name: host-d
    type: emptyDir
    value: { }
    mountPath: /etc/hosts.d/

configMaps:
  envName: dev2
  configs:
    - name: app-dev2-config
      source : dir
      sourceLoc : app
    - name: relayer-config-dev2
      source: dir
      sourceLoc: relayer
    - name: cosmos-config-dev2
      source: dir
      sourceLoc: cosmos
    - name: cfg-svc-metadata-dev2
      source: data
      data:
        cfg-api: |
          host=************
          port=80
        fk-env: |
          preprod
    - name: host-populator-config-dev2
      source: dir
      sourceLoc: host-populator

services:
  - name: sm-pandora-dev2-preprod
    type: LoadBalancer
    ports:
      - port: 10010
        targetPort: 8214
        protocol: TCP
        name: pandora-dev2
    LoadBalancer:
      mode: http
      vip: sm-pandora-preprod-vpc-dev2-vip
      healthCheckPort: 8215
      healthCheckPath: "/admin/healthcheck"
      healthCheckTimeout: 1s
      healthCheckInterval: 5s
      loadBalancingAlgorithm: leastconn
      backendService: sm-pandora-preprod-vpc-dev2-vip
      backendPort: 8214
      frontendPort: 80
  - name: sm-pandora-external-dev2
    type: LoadBalancer
    ports:
      - port: 10010 # useless but not to be changed
        targetPort: 8214
        protocol: TCP
        name: pandora-dev2
    LoadBalancer:
      mode: https
      sslCertId: www.sm-pandora-preprod.flipkart.net-1
      domainName: www.sm-pandora-preprod.flipkart.net
      vip: sm-pandora-preprod-vip-2
      name: sm-pandora-external-dev2
      healthCheckPort: 8215
      healthCheckPath: "/admin/healthcheck"
      healthCheckTimeout: 1s
      healthCheckInterval: 5s
      loadBalancingAlgorithm: leastconn
      backendService: sm-pandora-preprod-vip-2
      backendPort: 8214
      frontendPort: 443
      scope: "GLOBAL"


servicemesh:
  enabled: false