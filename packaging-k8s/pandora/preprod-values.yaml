namespace: sm-pandora-playground

fcp:
  team: "supermoney-engg"
  appId: "sm-pandora-preprod"
  mail: "supermoney"
  service: "sm-pandora-preprod"

mtl:
  cosmos:
    statsd: enabled
    tail: enabled
    config: cosmos-config
  asterix: disabled
  logsvc:
    enabled: true
    envName: preprod
    sourceLoc: rsyslog.conf

pod:
  replicaCount: 1

hostpopulator:
  state: enabled
  cpu: 0.2
  nameservers:
    - dns.fkinternal.com

image:
  service:
    tag: "1774756-1076-flow-f414ac22d5cb8adf157c30d9a6be15d858d2600d-20-Jan-23_17-15-24"
    registry: jfrog.fkinternal.com
    repository: lending-los/pandora-preprod
    runAsUser: 108
    runAsGroup: 112
    volumes:
      - app-config
      - app-log
      - cosmos-config
      - cfg-svc-metadata
      - host-populator-logs
      - host-d
      - host-populator-config
    livenessCheck:
      initialDelay: 160
      checkInterval: 15
      method: tcp
      port: 8215
    readinessCheck:
      initialDelay: 130
      checkInterval: 15
      method: http
      port: 8215
      path: /admin/healthcheck
    ports:
      - name: application
        containerPort: 8214
      - name: admin
        containerPort: 8215
      - name: jmx
        containerPort: 9311
    maxCpuLimit: 1
    maxCpuAsk: 1
    maxMemoryLimit: 4Gi
    maxMemoryAsk: 4Gi

  fluentbit:
    registry: edge.fkinternal.com/docker-external
    repository: grafana/fluent-bit-plugin-loki
    tag: main
    #    imagePullPolicy: IfNotPresent
    #    ports:
    #      - name: metrics
    #        containerPort: 2020
    #        protocol: TCP
    maxCpuLimit: 0.1
    maxCpuAsk: 0.1
    maxMemoryLimit: 100Mi
    maxMemoryAsk: 100Mi
    volumes:
      - fluent-bit-config-volume
      - app-log

volumes:
  - name: app-config
    type: configMap
    value: app-config
    mountPath: /etc/config/
  - name: app-log
    type: emptyDir
    value: {}
    mountPath: /var/log/
  - name: cfg-svc-metadata
    type: configMap
    value: cfg-svc-metadata
    mountPath: /etc/default/
  - name: relayer-config
    type: configMap
    value: relayer-config
    mountPath: /etc/fk-sc-mq/
  - name: host-populator-logs
    type: emptyDir
    value: { }
    mountPath: /var/log/hosts-populator/
  - name: host-d
    type: emptyDir
    value: { }
    mountPath: /etc/hosts.d/
  - name: fluent-bit-config-volume
    type: configMap
    value: fluent-bit-config-map
    mountPath: /fluent-bit/etc/

configMaps:
  envName: preprod
  configs:
    - name: app-config
      source : dir
      sourceLoc : app
    - name: relayer-config
      source: dir
      sourceLoc: relayer
    - name: cosmos-config
      source: dir
      sourceLoc: cosmos
    - name: fluent-bit-config-map
      source: dir
      sourceLoc : fluent-bit
    - name: cfg-svc-metadata
      source: data
      data:
        cfg-api: |
          host=************
          port=80
        fk-env: |
          preprod
    - name: host-populator-config
      source: dir
      sourceLoc: host-populator

services:
  - name: sm-pandora-preprod
    type: LoadBalancer
    ports:
      - port: 10010
        targetPort: 8214
        protocol: TCP
        name: pandora
    LoadBalancer:
      mode: http
      vip: sm-pandora-preprod-vpc-vip
      healthCheckPort: 8215
      healthCheckPath: "/admin/healthcheck"
      healthCheckTimeout: 1s
      healthCheckInterval: 5s
      loadBalancingAlgorithm: leastconn
      backendService: sm-pandora-preprod-vpc-vip
      backendPort: 8214
      frontendPort: 80
  - name: sm-pandora-external
    type: LoadBalancer
    ports:
      - port: 10010 # useless but not to be changed
        targetPort: 8214
        protocol: TCP
        name: pandora
    LoadBalancer:
      mode: https
      sslCertId: www.sm-pandora-preprod.flipkart.net-1
      domainName: www.sm-pandora-preprod.flipkart.net
      vip: sm-pandora-preprod-vip-2
      name: sm-pandora-external
      healthCheckPort: 8215
      healthCheckPath: "/admin/healthcheck"
      healthCheckTimeout: 1s
      healthCheckInterval: 5s
      loadBalancingAlgorithm: leastconn
      backendService: sm-pandora-preprod-vip-2
      backendPort: 8214
      frontendPort: 443
      scope: "GLOBAL"


servicemesh:
  enabled: false