package com.flipkart.fintech.pandora.api.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR> H Adavi
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class AccountNoPaymentInstrument extends PaymentInstrument {
	
	@JsonProperty("account_no")
	private String accountNumber;
	
	public AccountNoPaymentInstrument() {
		super(PaymentInstrumentType.ACCOUNT_NUMBER);
	}
	
	public String getAccountNumber() {
		return accountNumber;
	}
	
	public void setAccountNumber(String accountNumber) {
		this.accountNumber = accountNumber;
	}
	
	@Override
	public String toString()
	{
		return "AccountNoPaymentInstrument{" +
				"accountNumber='" + accountNumber + '\'' +
				"} " + super.toString();
	}
}
