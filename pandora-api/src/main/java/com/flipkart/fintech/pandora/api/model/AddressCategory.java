package com.flipkart.fintech.pandora.api.model;

import com.fasterxml.jackson.annotation.JsonCreator;

public enum  AddressCategory {
    PA("permanent_address"),
    CA("current_address"),
    BOTH("both");

    private String value;

    private AddressCategory(String value){
        this.value = value;
    }

    @JsonCreator
    public AddressCategory fromString(String value){
        return AddressCategory.valueOf(value);
    }
}
