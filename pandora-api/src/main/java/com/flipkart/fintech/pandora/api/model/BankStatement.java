package com.flipkart.fintech.pandora.api.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pandora.api.model.common.Timeline;

import java.math.BigDecimal;

@JsonIgnoreProperties(ignoreUnknown = true)
public class BankStatement extends IncomeDetails {

    @JsonProperty
    private String bankStatementId;

    @JsonProperty
    private BigDecimal currentBalance;

    @JsonProperty
    private BigDecimal deposits;

    @JsonProperty
    private BigDecimal withdrawls;

    @JsonProperty
    private Timeline statementTimeline;

    public String getBankStatementId() {
        return bankStatementId;
    }

    public void setBankStatementId(String bankStatementId) {
        this.bankStatementId = bankStatementId;
    }

    public BigDecimal getCurrentBalance() {
        return currentBalance;
    }

    public void setCurrentBalance(BigDecimal currentBalance) {
        this.currentBalance = currentBalance;
    }

    public BigDecimal getDeposits() {
        return deposits;
    }

    public void setDeposits(BigDecimal deposits) {
        this.deposits = deposits;
    }

    public BigDecimal getWithdrawls() {
        return withdrawls;
    }

    public void setWithdrawls(BigDecimal withdrawls) {
        this.withdrawls = withdrawls;
    }

    public Timeline getStatementTimeline() {
        return statementTimeline;
    }

    public void setStatementTimeline(Timeline statementTimeline) {
        this.statementTimeline = statementTimeline;
    }

    @Override
    public String toString() {
        return "BankStatement{" +
                "bankStatementId='" + bankStatementId + '\'' +
                ", currentBalance=" + currentBalance +
                ", deposits=" + deposits +
                ", withdrawls=" + withdrawls +
                ", statementTimeline=" + statementTimeline +
                '}';
    }
}
