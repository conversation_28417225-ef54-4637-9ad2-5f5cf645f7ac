package com.flipkart.fintech.pandora.api.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR> H Adavi
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class CardPaymentInstrument extends PaymentInstrument {
	
	@JsonProperty("card_no")
	private String cardNumber;
	
	@JsonProperty("cvv_required")
	private boolean cvvRequired;
	
	@JsonProperty("expiry_month_required")
	private boolean expiryMonthRequired;
	
	@JsonProperty("expiry_year_required")
	private boolean expiryYearRequired;
	
	public CardPaymentInstrument() {
		super(PaymentInstrumentType.CARD);
	}
	
	public String getCardNumber() {
		return cardNumber;
	}
	
	public void setCardNumber(String cardNumber) {
		this.cardNumber = cardNumber;
	}
	
	public boolean isCvvRequired() {
		return cvvRequired;
	}
	
	public void setCvvRequired(boolean cvvRequired) {
		this.cvvRequired = cvvRequired;
	}
	
	public boolean isExpiryMonthRequired() {
		return expiryMonthRequired;
	}
	
	public void setExpiryMonthRequired(boolean expiryMonthRequired) {
		this.expiryMonthRequired = expiryMonthRequired;
	}
	
	public boolean isExpiryYearRequired() {
		return expiryYearRequired;
	}
	
	public void setExpiryYearRequired(boolean expiryYearRequired) {
		this.expiryYearRequired = expiryYearRequired;
	}
	
	@Override
	public String toString() {
		return "CardPaymentInstrument{" +
				"cardNumber='" + cardNumber + '\'' +
				", cvvRequired=" + cvvRequired +
				", expiryMonthRequired=" + expiryMonthRequired +
				", expiryYearRequired=" + expiryYearRequired +
				"} " + super.toString();
	}
}
