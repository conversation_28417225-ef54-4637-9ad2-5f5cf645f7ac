package com.flipkart.fintech.pandora.api.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class EducationDetails {

    @JsonProperty
    private String highestEducationLevel;

    public String getHighestEducationLevel() {
        return highestEducationLevel;
    }

    public void setHighestEducationLevel(String highestEducationLevel) {
        this.highestEducationLevel = highestEducationLevel;
    }

    @Override
    public String toString() {
        return "EducationDetails{" +
                "highestEducationLevel='" + highestEducationLevel + '\'' +
                '}';
    }
}
