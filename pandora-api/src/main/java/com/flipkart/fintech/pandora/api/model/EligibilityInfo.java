package com.flipkart.fintech.pandora.api.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigInteger;

@JsonIgnoreProperties(ignoreUnknown = true)
public class EligibilityInfo {

    @JsonProperty
    private BigInteger creditLimit;

    @JsonProperty
    private Integer durationInMonths;

    @JsonProperty
    private float rateOfInterest;

    public BigInteger getCreditLimit() {
        return creditLimit;
    }

    public void setCreditLimit(BigInteger creditLimit) {
        this.creditLimit = creditLimit;
    }

    public Integer getDurationInMonths() {
        return durationInMonths;
    }

    public void setDurationInMonths(Integer durationInMonths) {
        this.durationInMonths = durationInMonths;
    }

    public float getRateOfInterest() {
        return rateOfInterest;
    }

    public void setRateOfInterest(float rateOfInterest) {
        this.rateOfInterest = rateOfInterest;
    }

    @Override
    public String toString() {
        return "EligibilityInfo{" +
                "creditLimit=" + creditLimit +
                ", durationInMonths=" + durationInMonths +
                ", rateOfInterest=" + rateOfInterest +
                '}';
    }
}
