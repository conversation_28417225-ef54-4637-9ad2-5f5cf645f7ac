package com.flipkart.fintech.pandora.api.model.billing;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigDecimal;

/**
 * <AUTHOR> H Adavi
 *
 * The dataModels represents the summary of the bill. It shows how the total due amount has been arrived at by aggregating
 * various components.
 *
 * Total Due Amount = Opening Balance -  Total Credits + Total Debits + Charges
 *
 * Opening Balance = Previous Bill Amount
 *
 * Total Credit = Bill Payment + Charges Waveoff + Return of Bill Line Item
 *
 * Total Debit = Pay Later Amount + Installment Amount of that month
 * where
 *    Installment Amount of that month = Principal Amount + Interest
 *
 * Charges = Late Fees
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class BillSummary {
	
	@JsonProperty("opening_balance")
	private BigDecimal openingBalance;
	
	@JsonProperty("total_credits")
	private BigDecimal totalCredits;
	
	@JsonProperty("total_debits")
	private BigDecimal totalDebits;
	
	@JsonProperty("charges")
	private BigDecimal charges;
	
	public BigDecimal getOpeningBalance() {
		return openingBalance;
	}
	
	public void setOpeningBalance(BigDecimal openingBalance) {
		this.openingBalance = openingBalance;
	}
	
	public BigDecimal getTotalCredits() {
		return totalCredits;
	}
	
	public void setTotalCredits(BigDecimal totalCredits) {
		this.totalCredits = totalCredits;
	}
	
	public BigDecimal getTotalDebits() {
		return totalDebits;
	}
	
	public void setTotalDebits(BigDecimal totalDebits) {
		this.totalDebits = totalDebits;
	}
	
	public BigDecimal getCharges() {
		return charges;
	}
	
	public void setCharges(BigDecimal charges) {
		this.charges = charges;
	}
	
	@Override
	public String toString() {
		return "BillSummary{" +
				"openingBalance=" + openingBalance +
				", totalCredits=" + totalCredits +
				", totalDebits=" + totalDebits +
				", charges=" + charges +
				'}';
	}
}
