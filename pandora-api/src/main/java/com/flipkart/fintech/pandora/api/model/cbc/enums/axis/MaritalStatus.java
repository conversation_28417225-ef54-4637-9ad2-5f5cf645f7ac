package com.flipkart.fintech.pandora.api.model.cbc.enums.axis;

import com.flipkart.fintech.pandora.api.model.cbc.enums.IEnum;

public enum MaritalStatus implements IEnum<MaritalStatus> {
    MARRIED("MARRIED"),
    UNMARRIED("UNMARRIED");

    private final String value;
    MaritalStatus(String value){
        this.value = value;
    }

    public String getValue() {
        return this.value;
    }

    @Override
    public String getId() {
        return this.value;
    }
}
