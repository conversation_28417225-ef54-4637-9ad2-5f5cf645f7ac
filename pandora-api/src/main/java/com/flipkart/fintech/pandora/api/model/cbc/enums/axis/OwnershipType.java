package com.flipkart.fintech.pandora.api.model.cbc.enums.axis;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.flipkart.fintech.pandora.api.model.cbc.enums.IEnum;
import com.flipkart.fintech.pandora.api.model.cbc.enums.deserializer.OwnershipTypeDeserializer;
import lombok.Getter;

@Getter
@JsonDeserialize(using = OwnershipTypeDeserializer.class)
public enum OwnershipType implements IEnum<OwnershipType> {
    SOLE_PROPRIETORSHIP("SOLE_PROPRIETORSHIP", "Sole Proprietorship"),
    PARTNERSHIP("PARTNERSHIP", "Partnership"),
    PRIVATE_LIMITED("PRIVATE_LIMITED", "Private Limited");

    private final String value;
    private final String id;

    OwnershipType(String id, String value){
        this.value = value;
        this.id = id;
    }
}
