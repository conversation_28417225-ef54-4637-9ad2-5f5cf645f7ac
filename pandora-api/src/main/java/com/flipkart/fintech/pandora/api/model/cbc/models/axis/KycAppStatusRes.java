package com.flipkart.fintech.pandora.api.model.cbc.models.axis;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Builder;
import lombok.Data;
import lombok.Value;
import lombok.extern.jackson.Jacksonized;

@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
@Value
@Jacksonized
public class KycAppStatusRes {
    ExpirationTimestamp eKyc;
    ExpirationTimestamp vKyc;
    ExpirationTimestamp offlineKyc;

}


