package com.flipkart.fintech.pandora.api.model.cbc.models.axis;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.flipkart.fintech.pandora.api.model.cbc.enums.axis.State;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.Map;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class StateContext {
    State state;
    String granularStatus;
    LastUpdateTimestamp lastUpdateTimestamp;
    Map<String, Object> notStartedStateContext;
    Map<String, Object> inProgressStateContext;
    Map<String, Object> approvedStateContext;
    LastUpdateTimestamp expirationTimestamp;
    Map<String, Object> rejectedStateContext;
    Map<String, Object> awaitingApprovalStateContext;
    Map<String, Object> expiredStateContext;

}
