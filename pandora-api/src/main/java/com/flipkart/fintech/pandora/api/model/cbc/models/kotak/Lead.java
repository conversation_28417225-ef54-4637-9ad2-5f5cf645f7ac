package com.flipkart.fintech.pandora.api.model.cbc.models.kotak;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.flipkart.fintech.pandora.api.model.cbc.enums.kotak.SmLeadStatusEnum;
import lombok.Builder;
import lombok.Value;
import lombok.extern.jackson.Jacksonized;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
@Value
@Jacksonized
@JsonInclude(JsonInclude.Include.NON_NULL)

public class Lead {
    String partnerId;
    String leadId;
    SmLeadStatusEnum leadStatus;
    List<SmLeadStatusEnum> nextStatus;
    PersonalDetails personalDetails;
    List<FdDetails> fdDetails;
    List<PaymentDetails> paymentDetails;
    SmLeadCardDetails cardDetails;
}
