package com.flipkart.fintech.pandora.api.model.cbc.models.kotak;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Builder;
import lombok.Value;
import lombok.extern.jackson.Jacksonized;

@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
@Value
@Jacksonized
@JsonInclude(JsonInclude.Include.NON_NULL)

public class SmAnnualIncomeOptions {
    String id;
    String order;
    String code;
    String label;

}
