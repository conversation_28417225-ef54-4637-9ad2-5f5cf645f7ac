package com.flipkart.fintech.pandora.api.model.cbc.request.axis;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.flipkart.fintech.pandora.api.model.cbc.enums.axis.BalancePaymentOption;
import com.flipkart.fintech.pandora.api.model.cbc.enums.axis.CustomerType;
import com.flipkart.fintech.pandora.api.model.cbc.enums.axis.EducationStatus;
import com.flipkart.fintech.pandora.api.model.cbc.enums.axis.MaritalStatus;
import com.flipkart.fintech.pandora.api.model.cbc.enums.axis.ResidenceType;
import com.flipkart.fintech.pandora.api.model.cbc.enums.common.SmGenderEnum;
import com.flipkart.fintech.pandora.api.model.cbc.enums.common.SmMaritalStatusEnum;
import com.flipkart.fintech.pandora.api.model.cbc.models.axis.AccountDetails;
import com.flipkart.fintech.pandora.api.model.cbc.models.axis.Address;
import com.flipkart.fintech.pandora.api.model.cbc.models.axis.FamilyDetails;
import com.flipkart.fintech.pandora.api.model.cbc.models.axis.FinancialDetails;
import com.flipkart.fintech.pandora.api.model.cbc.models.axis.NomineeDetails;
import com.flipkart.fintech.pandora.api.model.cbc.models.axis.TnCAcceptanceMetadata;
import com.flipkart.fintech.pandora.api.model.cbc.models.axis.Token;
import com.flipkart.fintech.pandora.api.model.cbc.request.CbcRequest;
import lombok.Data;

import java.util.List;
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class NTBSubmitApplicationRequest implements CbcRequest{
    String applicationId;
    String offerId = "";
    String phoneNumber ;
    Token token;
    CustomerType custType;
    String pan ;
    String displayName;
    String fullName ;
    String emailAddress ;
    String dateOfBirth ;
    SmMaritalStatusEnum maritalStatus;
    SmGenderEnum gender;
    List<Address> addresses;
    ResidenceType residenceType;
    AccountDetails accountDetails;
    NomineeDetails nomineeDetails;
    EducationStatus educationStatus;
    BalancePaymentOption balancePaymentOption;
    List<String> promotionCodes;
    List<TnCAcceptanceMetadata> tncAcceptanceMetadata;
    FinancialDetails financialDetails;
    FamilyDetails familyDetails;
    Double binScore;
}
