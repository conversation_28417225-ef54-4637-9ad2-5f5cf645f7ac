package com.flipkart.fintech.pandora.api.model.cbc.response.axis;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pandora.api.model.cbc.enums.axis.CustomerType;
import com.flipkart.fintech.pandora.api.model.cbc.response.CbcResponse;
import lombok.Builder;
import lombok.Value;
import lombok.extern.jackson.Jacksonized;

@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
@Value
@Jacksonized
public class OfferDetailsResponse implements CbcResponse {

    @JsonProperty("productId")
    String productId;

    @JsonProperty("offerId")
    String offerId;

    @JsonProperty("startTime")
    Long startTime;

    @JsonProperty("endTime")
    Long endTime;

    @JsonProperty("crLimit")
    Long crLimit ;

    @JsonProperty("custType")
    CustomerType custType;

}
