package com.flipkart.fintech.pandora.api.model.cbc.response.common;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.flipkart.fintech.pandora.api.model.cbc.request.axis.AxisDisplayNameValidationRequest;
import com.flipkart.fintech.pandora.api.model.cbc.request.common.FieldValidationRequest;
import com.flipkart.fintech.pandora.api.model.cbc.request.common.ValidateDetailsRequestType;
import com.flipkart.fintech.pandora.api.model.cbc.response.CbcResponse;
import com.flipkart.fintech.pandora.api.model.cbc.response.axis.AxisDisplayNameValidationResponse;
import lombok.Builder;
import lombok.Value;
import lombok.extern.jackson.Jacksonized;

import java.math.BigDecimal;
import java.util.List;


@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
@Value
@Jacksonized
public class ValidateDetailsResponse implements CbcResponse {
    @JsonProperty("type")
    private ValidateDetailsRequestType type;

    @JsonProperty("fieldValidationResponse")
    @JsonTypeInfo(
            property = "type",
            include = JsonTypeInfo.As.EXTERNAL_PROPERTY,
            visible = true,
            use = JsonTypeInfo.Id.NAME
    )
    @JsonSubTypes({
            @JsonSubTypes.Type(name = "AXIS_DISPLAY_NAME_VALIDATION", value = AxisDisplayNameValidationResponse.class)
    })
    private FieldValidationResponse fieldValidationResponse;

}