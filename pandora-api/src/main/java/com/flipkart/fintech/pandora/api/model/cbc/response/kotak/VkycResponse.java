package com.flipkart.fintech.pandora.api.model.cbc.response.kotak;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.flipkart.fintech.pandora.api.model.cbc.models.kotak.ErrorResp;
import com.flipkart.fintech.pandora.api.model.cbc.models.kotak.Vkyc;
import com.flipkart.fintech.pandora.api.model.cbc.response.CbcResponse;
import lombok.Builder;
import lombok.Value;
import lombok.extern.jackson.Jacksonized;

@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
@Value
@Jacksonized
@JsonInclude(JsonInclude.Include.NON_NULL)

public class VkycResponse implements CbcResponse {
    ErrorResp errorDetails;
    Vkyc response;
}
