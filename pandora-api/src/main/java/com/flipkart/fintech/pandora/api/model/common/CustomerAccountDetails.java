package com.flipkart.fintech.pandora.api.model.common;

import java.math.BigDecimal;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>.r on 19/07/18.
 */
public class CustomerAccountDetails {

    private String displayAccountNumber;

    private String accountId;

    private BigDecimal availableCredit;

    public String getDisplayAccountNumber() {
        return displayAccountNumber;
    }

    public void setDisplayAccountNumber(String displayAccountNumber) {
        this.displayAccountNumber = displayAccountNumber;
    }

    public String getAccountId() {
        return accountId;
    }

    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    public BigDecimal getAvailableCredit() {
        return availableCredit;
    }

    public void setAvailableCredit(BigDecimal availableCredit) {
        this.availableCredit = availableCredit;
    }
	
	@Override
	public String toString() {
		return "CustomerAccountDetails{" +
				"displayAccountNumber='" + displayAccountNumber + '\'' +
				", accountId='" + accountId + '\'' +
				", availableCredit=" + availableCredit +
				'}';
	}
}
