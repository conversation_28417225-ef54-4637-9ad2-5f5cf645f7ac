
package com.flipkart.fintech.pandora.api.model.pl.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Getter
@Setter
public class AadhaarValidateOtpRequest implements LoanRequest {

    @JsonProperty("aadhaarNo")
    private String aadhaarNo;
    
    @JsonProperty("applicationId")
    private String applicationId;
    
    @JsonProperty("transactionId")
    private String transactionId;
    
    @JsonProperty("otp")
    private String otp;

    @JsonProperty("amsId")
    private String amsId;

    @JsonProperty("accountId")
    private String accountId;

    @JsonProperty("smUserId")
    private String smUserId;

}
