package com.flipkart.fintech.pandora.api.model.pl.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Getter
@Setter
public class BankUpdateRequest implements LoanRequest {

    @JsonProperty("bankAccountNumber")
    private String bankAccountNumber;

    @JsonProperty("bankIfscCode")
    private String bankIfscCode;

    @JsonProperty("bankName")
    private String bankName;

    @JsonProperty("userId")
    private String userId;

    @JsonProperty("firstName")
    private String firstName;
    @JsonProperty("lastName")
    private String lastName;

    @JsonProperty("applicationId")
    private String applicationId;

    @JsonProperty("accountId")
    private String accountId;

}
