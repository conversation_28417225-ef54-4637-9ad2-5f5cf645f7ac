
package com.flipkart.fintech.pandora.api.model.pl.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FetchKfsDocumentRequest implements LoanRequest {

    private String applicationId;
    
    private String lenderApplicationId;


}
