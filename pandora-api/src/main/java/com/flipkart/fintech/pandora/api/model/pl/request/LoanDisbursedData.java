package  com.flipkart.fintech.pandora.api.model.pl.request;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;
import java.util.Date;

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class LoanDisbursedData {
    private String applicationId;

    private String userId;

    private String creditId;

    private String lender;

    private String creditLineId;

    private Integer principalAmount;

    private Date disbursalDate;

    private String status;
}