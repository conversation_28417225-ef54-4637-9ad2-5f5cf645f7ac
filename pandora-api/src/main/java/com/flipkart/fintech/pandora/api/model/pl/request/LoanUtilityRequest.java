
package com.flipkart.fintech.pandora.api.model.pl.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Getter
@Setter

public class LoanUtilityRequest implements LoanRequest {
    
    @JsonProperty("loanId")
    private String agreementId;
    
    @JsonProperty("requestCode")
    private String requestCode;

    @JsonProperty("clientApplicationId")
    private String clientApplicationId;


}
