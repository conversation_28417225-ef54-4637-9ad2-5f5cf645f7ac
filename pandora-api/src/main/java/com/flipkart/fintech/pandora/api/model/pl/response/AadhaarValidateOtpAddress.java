
package com.flipkart.fintech.pandora.api.model.pl.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.Getter;
import lombok.Setter;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Getter
@Setter
public class AadhaarValidateOtpAddress {

    @JsonProperty("co")
    private String co;
    
    @JsonProperty("country")
    private String country;
    
    @JsonProperty("dist")
    private String dist;
    
    @JsonProperty("house")
    private String house;
    
    @JsonProperty("lm")
    private String lm;
    
    @JsonProperty("loc")
    private String loc;
    
    @JsonProperty("pc")
    private String pc;
    
    @JsonProperty("po")
    private String po;
    
    @JsonProperty("state")
    private String state;
    
    @JsonProperty("street")
    private String street;
    
    @JsonProperty("subdist")
    private String subdist;
    
    @JsonProperty("vtc")
    private String vtc;
    
    @JsonProperty("pinType")
    private String pinType;

}
