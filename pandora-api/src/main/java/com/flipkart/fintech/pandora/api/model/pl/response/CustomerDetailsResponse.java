package com.flipkart.fintech.pandora.api.model.pl.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class CustomerDetailsResponse extends LoanResponse {
    private String clientCustomerId;
    private String partnershipId;
    private CustomerData customerData;

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class CustomerData {
        private List<Task> tasks;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Task {
        private String type;
        private String status;
        private String message;
        private TaskData data;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TaskData {
        private String limitId;
        private String limitValidity;
        private String limitAssessmentDate; // (expects OffsetDateTime ,eg. 2024-02-05T14:30:00+05:30)
        private Double totalApprovedLimit;
        private Double availableLimit;
    }
}
