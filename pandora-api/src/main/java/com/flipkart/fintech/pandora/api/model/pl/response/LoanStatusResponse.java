
package com.flipkart.fintech.pandora.api.model.pl.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.Getter;
import lombok.Setter;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Getter
@Setter
public class  LoanStatusResponse {

    
    @JsonProperty("crn")
    private String crn;
    
    @JsonProperty("loanApplicationNo")
    private String loanApplicationNo;
    
    @JsonProperty("referenceId")
    private String referenceId;
    
    @JsonProperty("status")
    private String status;
    
    @JsonProperty("ficoStatus")
    private String ficoStatus;
    
    @JsonProperty("rejectReason")
    private String rejectReason;

}
