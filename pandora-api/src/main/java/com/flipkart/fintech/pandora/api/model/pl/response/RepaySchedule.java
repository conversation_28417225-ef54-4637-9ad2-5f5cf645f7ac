
package com.flipkart.fintech.pandora.api.model.pl.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Getter
@Setter
public class RepaySchedule {

    @JsonProperty("installmentNumber")
    private String installmentNumber;
    
    @JsonProperty("dueDate")
    private String dueDate;
    
    @JsonProperty("outstandingPrincipal")
    private String outstandingPrincipal;
    
    @JsonProperty("principalComponent")
    private String principalComponent;
    
    @JsonProperty("interestComponent")
    private String interestComponent;
    
    @JsonProperty("installment")
    private String installment;
    
    @JsonProperty("loanId")
    private String loanId;

}
