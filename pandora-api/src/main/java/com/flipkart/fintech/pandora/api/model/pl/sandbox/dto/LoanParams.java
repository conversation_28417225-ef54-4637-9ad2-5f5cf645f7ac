package com.flipkart.fintech.pandora.api.model.pl.sandbox.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@Getter
@Builder
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class LoanParams {
    private Double loanAmount;
    private Double roi;
    private List<Charge> charges;
    private Double netDisbursalAmount;
    private Double emi;
    private Tenure tenure;
    private Insurance insuranceSelected;

}
