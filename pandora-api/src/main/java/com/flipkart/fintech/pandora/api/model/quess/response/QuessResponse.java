package com.flipkart.fintech.pandora.api.model.quess.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class QuessResponse {
    @JsonProperty("error")
    private String error;
    @JsonProperty("statusCode")
    private int statusCode;
    @JsonProperty("statusMessage")
    private String statusMessage;
    @JsonProperty("responseTime")
    private Long responseTime;
}
