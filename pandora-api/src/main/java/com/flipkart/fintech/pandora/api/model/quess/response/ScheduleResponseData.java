package com.flipkart.fintech.pandora.api.model.quess.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import com.flipkart.fintech.pandora.api.model.quess.Address;
import com.flipkart.fintech.pandora.api.model.quess.request.ScheduleRequest;
import com.flipkart.sensitive.annotation.SensitiveField;
import lombok.Data;
import lombok.ToString;

import java.util.Map;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ScheduleResponseData extends ScheduleRequest {
    @JsonProperty("emp_fullname")
    private String empFullname;
    @JsonProperty("contact_no")
    @SensitiveField(keyName = "cbcKey")
    private String contactNo;
    @JsonProperty("schedule_time")
    private String scheduleTime;
    @JsonProperty("schedule_date")
    private String scheduleDate;
    @JsonProperty("schedule_status")
    private String scheduleStatus;
    @JsonProperty("contact_number")
    @ToString.Exclude
    private String contactNumber;
    @JsonProperty("alternate_contact_number")
    @ToString.Exclude
    private String alternateContactNumber;
    @JsonProperty("email")
    @ToString.Exclude
    private String email;
    @JsonProperty("residential_address")
    @ToString.Exclude
    private Address residentialAddress;
    @JsonProperty("communication_address")
    @ToString.Exclude
    private Address communicationAddress;
    @JsonProperty("work_address")
    @ToString.Exclude
    private Address workAddress;
    @JsonProperty("pincode")
    private String pincode;
    @JsonProperty("document_type")
    private String documentType;
    @JsonProperty("extra_param")
    private Map<String, String> extraParam;
    @JsonProperty("channel_name")
    private String channelName;
    @JsonProperty("product_name")
    private String productName;
    @JsonProperty("message")
    private String message;

}
