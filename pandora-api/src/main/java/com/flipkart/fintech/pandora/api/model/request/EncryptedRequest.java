package com.flipkart.fintech.pandora.api.model.request;

import com.fasterxml.jackson.annotation.JsonProperty;

import javax.validation.constraints.NotNull;

/**
 * Created by rajat.mathur on 28/02/18.
 */
public abstract class EncryptedRequest {

    @NotNull
    @JsonProperty("enc_key_details")
    protected EncryptionKeyData encKeyData;

    public EncryptionKeyData getEncKeyData() {
        return encKeyData;
    }

    public void setEncKeyData(EncryptionKeyData encKeyData) {
        this.encKeyData = encKeyData;
    }

    @Override
    public String toString() {
        return "EncryptedRequest{" + "encKeyData=" + encKeyData +
                '}';
    }
}
