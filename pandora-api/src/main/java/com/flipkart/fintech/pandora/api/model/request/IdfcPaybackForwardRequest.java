package com.flipkart.fintech.pandora.api.model.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.flipkart.fintech.pandora.api.model.request.ivr.enums.PaymentMethod;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.joda.time.DateTime;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class IdfcPaybackForwardRequest extends PaybackForwardRequest {

    private String appId;
    private PaymentMethod paymentMethod;
    private BigDecimal minDueAmount;
    private Boolean isOverduePayment;
    public List<IdfcPaybackForwardRequest.TransactionDetail> transactionDetails;

    public Boolean getOverduePayment() {
        return isOverduePayment;
    }

    public void setOverduePayment(Boolean overduePayment) {
        isOverduePayment = overduePayment;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class TransactionDetail {

        private String txnId;
        private BigDecimal saleTxnAmt;
        private String transactionType;
        private DateTime txnDate;
        private String lenderTxnId;
        private String sublimitType;

    }
}
