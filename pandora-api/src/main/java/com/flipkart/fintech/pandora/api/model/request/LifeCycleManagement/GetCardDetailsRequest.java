package com.flipkart.fintech.pandora.api.model.request.LifeCycleManagement;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pandora.api.model.FingerprintInfo;
import com.flipkart.fintech.pandora.api.model.LeadInformation;
import com.flipkart.fintech.pandora.api.model.StatementSection;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 11/05/20.
 */
@Data
public class GetCardDetailsRequest {
    @JsonProperty
    private StatementSection statementSection;

    @JsonProperty
    private FingerprintInfo fingerprintInfo;

    @JsonProperty
    private LeadInformation leadInformation;
}
