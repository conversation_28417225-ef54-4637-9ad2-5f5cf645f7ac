package com.flipkart.fintech.pandora.api.model.request.LifeCycleManagement;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pandora.api.model.common.STATUS;
import lombok.*;
import lombok.Builder;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdateSupercoinRecordAckRequest {

    @JsonProperty("referenceId")
    private String referenceId;

    @JsonProperty("cardSerNo")
    private String cardSerNo;

    @JsonProperty("merchantTransactionId")
    private String merchantTransactionId;

    @JsonProperty("description")
    private String description;

    @JsonProperty("status")
    private STATUS status;

    @JsonProperty("addOn1")
    private String addOn1;

    @JsonProperty("addOn2")
    private String addOn2;

    @JsonProperty("addOn3")
    private String addOn3;

}