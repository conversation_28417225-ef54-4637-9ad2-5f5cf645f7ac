package com.flipkart.fintech.pandora.api.model.request.LifeCycleManagement;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pandora.api.model.FingerprintInfo;
import com.flipkart.fintech.pandora.api.model.LenderInfoSection;
import com.flipkart.fintech.pandora.api.model.OtpDetails;

public class ValidateAuthOtpRequest {
    @JsonProperty
    private LenderInfoSection lenderInfoSection;

    @JsonProperty
    private FingerprintInfo fingerprintInfo;

    @JsonProperty
    private OtpDetails otpDetails;

    public LenderInfoSection getLenderInfoSection() {
        return lenderInfoSection;
    }

    public void setLenderInfoSection(LenderInfoSection lenderInfoSection) {
        this.lenderInfoSection = lenderInfoSection;
    }

    public FingerprintInfo getFingerprintInfo() {
        return fingerprintInfo;
    }

    public void setFingerprintInfo(FingerprintInfo fingerprintInfo) {
        this.fingerprintInfo = fingerprintInfo;
    }

    public OtpDetails getOtpDetails() {
        return otpDetails;
    }

    public void setOtpDetails(OtpDetails otpDetails) {
        this.otpDetails = otpDetails;
    }

    @Override
    public String toString() {
        return "ValidateAuthOtpRequest{" +
                "lenderInfoSection=" + lenderInfoSection +
                ", fingerprintInfo=" + fingerprintInfo +
                ", otpDetails=" + otpDetails +
                '}';
    }
}
