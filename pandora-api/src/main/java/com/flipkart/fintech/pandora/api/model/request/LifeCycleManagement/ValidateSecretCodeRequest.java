package com.flipkart.fintech.pandora.api.model.request.LifeCycleManagement;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pandora.api.model.StatementSection;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 11/05/20.
 */
@Data
public class ValidateSecretCodeRequest {
    @JsonProperty
    private StatementSection statementSection;

    @JsonProperty("deliveryCode")
    private String deliveryCode;
}
