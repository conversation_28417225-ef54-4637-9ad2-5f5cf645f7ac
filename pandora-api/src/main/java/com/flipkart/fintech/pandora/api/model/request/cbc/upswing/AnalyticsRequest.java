package com.flipkart.fintech.pandora.api.model.request.cbc.upswing;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AnalyticsRequest {

    @JsonProperty("pci")
    private String userId;

    @JsonProperty("fsi")
    private String financialInstitute;

    @JsonProperty("amount")
    private BigDecimal amount;

    @JsonProperty("tenure")
    private String tenure;

    @JsonProperty("journeyId")
    private String journeyId;

    @JsonProperty("eventType")
    private String eventType;

    @JsonProperty("termDepositType")
    private String termDepositType;

    @JsonProperty("reason")
    private String reason;

    @JsonProperty("productVariant")
    private String productVariant;


}