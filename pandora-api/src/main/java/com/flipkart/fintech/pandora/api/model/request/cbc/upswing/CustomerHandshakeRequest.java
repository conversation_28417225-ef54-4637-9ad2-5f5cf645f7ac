package com.flipkart.fintech.pandora.api.model.request.cbc.upswing;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CustomerHandshakeRequest {

    @JsonProperty("partnerCustomerId")
    @NotBlank(message = "PCI can't be null or empty")
    private String partnerCustomerId;
}
