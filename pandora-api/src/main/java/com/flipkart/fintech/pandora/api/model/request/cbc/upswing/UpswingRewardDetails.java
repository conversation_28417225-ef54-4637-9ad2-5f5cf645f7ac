package com.flipkart.fintech.pandora.api.model.request.cbc.upswing;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pandora.api.model.enums.cbc.upswing.RewardTxnType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UpswingRewardDetails {

    @JsonProperty("RewardId")
    @NotBlank(message = "RewardId can't be blank")
    private String upswingRewardId;

    @JsonProperty("RewardBalance")
    @NotNull(message = "RewardBalance can't be null")
    private BigDecimal upswingCumulativeBalance;

    @JsonProperty("type")
    @NotNull(message = "type can't be null")
    private RewardTxnType upswingRewardTxnType;
}
