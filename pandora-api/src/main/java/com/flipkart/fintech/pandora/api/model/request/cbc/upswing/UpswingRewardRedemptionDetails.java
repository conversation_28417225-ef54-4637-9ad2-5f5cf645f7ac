package com.flipkart.fintech.pandora.api.model.request.cbc.upswing;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UpswingRewardRedemptionDetails {

    @JsonProperty("redemptionId")
    @NotBlank(message = "RewardId can't be blank")
    private String upswingRedemptionId;

    @JsonProperty("rewardAmount")
    @NotNull(message = "RewardBalance can't be null")
    private BigDecimal rewardAmount;

    @JsonProperty("rewardCurrencyCode")
    private String rewardCurrencyCode;

}
