package com.flipkart.fintech.pandora.api.model.request.cbc.upswing;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UpswingRewardRedemptionRequest {

    @JsonProperty("customerId")
    @NotBlank(message = "customerId can't be blank")
    private String userId;

    @JsonProperty("redeem")
    @NotNull(message = "redemptionDetails can't be null")
    private UpswingRewardRedemptionDetails redemptionDetails;
}
