package com.flipkart.fintech.pandora.api.model.request.cbc.upswing;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.flipkart.fintech.pandora.util.LocalTimestampSerializer;
import com.flipkart.fintech.pandora.util.LocalTimestampDeserializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.sql.Timestamp;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UpswingRewardsRequest {

    @JsonProperty("customerId")
    @NotBlank(message = "customerId can't be blank")
    private String userId;

    @JsonProperty("accountId")
    @NotBlank(message = "accountId can't be blank")
    private String upswingAccountId;

    @JsonProperty("updatedRewards")
    @NotNull(message = "updatedRewards can't be null")
    private UpswingUpdatedRewardDetails upswingUpdatedRewardDetails;

    @NotNull
    @JsonProperty("timestamp")
    @JsonSerialize(using = LocalTimestampSerializer.class)
    @JsonDeserialize(using = LocalTimestampDeserializer.class)
    private Timestamp timestamp;

}
