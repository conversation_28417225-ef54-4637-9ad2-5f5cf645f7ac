package com.flipkart.fintech.pandora.api.model.request.cfa.models;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pandora.api.model.enums.cfa.Platform;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 16/03/21.
 */

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@AllArgsConstructor
public class SessionInformation {

    private Platform platform;

    @JsonProperty("initiation_timestamp")
    private String timeStamp;

    @JsonProperty("ip_address")
    private String ipAddress;

    @JsonProperty("consent_given")
    private Boolean isConsentGiven;
}
