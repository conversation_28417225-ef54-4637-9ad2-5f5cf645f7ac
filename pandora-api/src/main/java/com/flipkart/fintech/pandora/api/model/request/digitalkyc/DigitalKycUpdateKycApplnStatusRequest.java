package com.flipkart.fintech.pandora.api.model.request.digitalkyc;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import javax.validation.constraints.NotNull;

@JsonIgnoreProperties(ignoreUnknown = true)
public class DigitalKycUpdateKycApplnStatusRequest {

  @NotNull
  private KycApplicationStatus status;

  private String comment;

  @JsonProperty("reject_reason")
  private KycApplnRejectReason rejectReason;

  @JsonProperty("retry_allowed")
  private Boolean retryAllowed;

  @JsonProperty("user_reference_id")
  private String userReferenceId;




  public KycApplicationStatus getStatus() {
    return status;
  }

  public void setStatus(
      KycApplicationStatus status) {
    this.status = status;
  }

  public String getComment() {
    return comment;
  }

  public void setComment(String comment) {
    this.comment = comment;
  }

  public KycApplnRejectReason getRejectReason() {
    return rejectReason;
  }

  public void setRejectReason(
      KycApplnRejectReason rejectReason) {
    this.rejectReason = rejectReason;
  }

  public Boolean getRetryAllowed() {
    return retryAllowed;
  }

  public void setRetryAllowed(Boolean retryAllowed) {
    this.retryAllowed = retryAllowed;
  }


  public String getUserReferenceId() {
    return userReferenceId;
  }

  public void setUserReferenceId(String userReferenceId) {
    this.userReferenceId = userReferenceId;
  }

  @Override
  public String toString() {
    return "DigitalKycUpdateKycApplnStatusRequest{" +
        "status=" + status +
        ", comment='" + comment + '\'' +
        ", rejectReason=" + rejectReason +
        ", retryAllowed=" + retryAllowed +
        ", userReferenceId='" + userReferenceId + '\'' +
        '}';
  }
}
