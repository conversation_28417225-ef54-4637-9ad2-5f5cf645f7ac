package com.flipkart.fintech.pandora.api.model.request.digitalkyc;

/**
 * Created by r<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.singh on 25/12/18.
 */
public enum DocRejectReason {

    NAME_MISMATCH("Name Mismatch"),
    ADDRESS_MISMATCH("Address Mismatch"),
    UNCLEAR_DOCUMENT("Unclear Document"),
    INCOMPLETE_DOCUMENT("Incomplete Document"),
    INCORRECT_DOCUMENT("Incorrect Document"),
    PASSWORD_REQUIRED("Password Required"),
    FILE_NOT_OPENING("Unable to view Document"),
    FACE_MISMATCH("Face Mismatch"),
    LOW_QUALITY_DOCUMENT("Low quality Document"),
    UNSUPPORTED_DOCUMENT("Unsupported Document"),
    FACE_NOT_VISIBLE_PROPERLY("Face not visible properly"),
    OTP_NOT_CALLED_OUT("OTP not called out"),
    OTP_UNCLEAR("OTP unclear"),
    OTP_MISMATCH("OTP mismatch");


    private String message;

    DocRejectReason(String message) {
        this.message = message;
    }

    public String getMessage() {
        return message;
    }
}
