package com.flipkart.fintech.pandora.api.model.request.digitalkyc;

/**
 * Created by rishabhdh<PERSON><PERSON>.singh on 01/12/18.
 */
public enum KycDocCategory {

    POA("address_proof"),
    KYC_VIDEO("KYC_video"),
    KYC_PHOTO("KYC_photo"),
    POI("identity_proof");

    private String displayName;

    KycDocCategory(String displayName) {
        this.displayName = displayName;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }
}
