package com.flipkart.fintech.pandora.api.model.request.ekyc;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pandora.api.model.request.EncryptedRequest;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * Created by aniruddha.sharma on 30/11/17.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EkycVerifyOtpRequest extends EncryptedRequest {
    @NotNull
    private String aadhaarNumber;

    @NotNull
    private String otp;

    private String externalRefKey;

    private String externalRefId;

    @JsonProperty("transaction_ref")
    private String transactionReference;

    private boolean ckycFlow;
}
