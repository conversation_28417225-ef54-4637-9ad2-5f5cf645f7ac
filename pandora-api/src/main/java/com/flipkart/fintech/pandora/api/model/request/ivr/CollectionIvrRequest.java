package com.flipkart.fintech.pandora.api.model.request.ivr;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@JsonIgnoreProperties(ignoreUnknown = true)
public class CollectionIvrRequest extends Context {

    @NotNull
    private String month;

    @NotNull
    private String dueDate;


    private BigDecimal amount;

    private String accountId;

    private String smsLink;

    private String customerName;

    public String getSmsLink() {
        return smsLink;
    }

    public void setSmsLink(String smsLink) {
        this.smsLink = smsLink;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    @NotNull
    public String getMonth() {
        return month;
    }

    public void setMonth(@NotNull String month) {
        this.month = month;
    }

    @NotNull
    public String getDueDate() {
        return dueDate;
    }

    public void setDueDate(@NotNull String dueDate) {
        this.dueDate = dueDate;
    }

    public String getAccountId() {
        return accountId;
    }

    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    public String toString() {
        return "CollectionIvrRequest{" +
                "month='" + month + '\'' +
                ", dueDate='" + dueDate + '\'' +
                ", amount=" + amount +
                ", accountId=" + accountId +
                ", smsLink=" + smsLink +
                ", customerName=" + customerName +
                "} " + super.toString();
    }
}
