package com.flipkart.fintech.pandora.api.model.request.onboarding;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.flipkart.fintech.pandora.api.model.common.CustomerBucket;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * Created by aniruddha.sharma on 13/11/17.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AdditionalData {

    @NotNull
    private CustomerBucket customerBucket;

    private Map surrogates;

    private List<Address> addresses;

    public CustomerBucket getCustomerBucket() {
        return customerBucket;
    }

    public void setCustomerBucket(CustomerBucket customerBucket) {
        this.customerBucket = customerBucket;
    }

    public Map getSurrogates() {
        return surrogates;
    }

    public void setSurrogates(Map surrogates) {
        this.surrogates = surrogates;
    }

    public List<Address> getAddresses() {
        return addresses;
    }

    public void setAddresses(List<Address> addresses) {
        this.addresses = addresses;
    }
}
