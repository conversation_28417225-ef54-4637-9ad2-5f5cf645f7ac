package com.flipkart.fintech.pandora.api.model.request.onboarding;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.flipkart.fintech.pandora.api.model.common.Consent;
import com.flipkart.fintech.pandora.api.model.common.CustomerDetails;
import com.flipkart.fintech.pandora.api.model.common.Demographics;
import com.flipkart.fintech.pandora.api.model.common.IdentificationDocument;

import java.math.BigDecimal;
import java.util.List;

/**
 * Created by sujee<PERSON><PERSON>.r on 11/07/18.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class Applicant {

    private Demographics demographics;

    private List<IdentificationDocument> identificationDocumentDetails;

    private CustomerDetails customerDetails;

    private List<Consent> consentDetails;

    private Name name;

    private List<Phone> phones;

    private List<Email> emails;

    private List<Address> addresses;

    private IncomeDetails incomeDetails;

    private ContactPreference contactPreference;

    private Education education;

    private EmploymentDetails employmentDetails;

    private KycInformation kycInformation;

    public Demographics getDemographics() {
        return demographics;
    }

    public void setDemographics(Demographics demographics) {
        this.demographics = demographics;
    }

    public CustomerDetails getCustomerDetails() {
        return customerDetails;
    }

    public void setCustomerDetails(CustomerDetails customerDetails) {
        this.customerDetails = customerDetails;
    }

    public List<Consent> getConsentDetails() {
        return consentDetails;
    }

    public void setConsentDetails(List<Consent> consentDetails) {
        this.consentDetails = consentDetails;
    }

    public List<IdentificationDocument> getIdentificationDocumentDetails() {
        return identificationDocumentDetails;
    }

    public void setIdentificationDocumentDetails(List<IdentificationDocument> identificationDocumentDetails) {
        this.identificationDocumentDetails = identificationDocumentDetails;
    }

    public Name getName() {
        return name;
    }

    public void setName(Name name) {
        this.name = name;
    }

    public List<Phone> getPhones() {
        return phones;
    }

    public void setPhones(List<Phone> phones) {
        this.phones = phones;
    }

    public List<Email> getEmails() {
        return emails;
    }

    public void setEmails(List<Email> emails) {
        this.emails = emails;
    }

    public List<Address> getAddresses() {
        return addresses;
    }

    public void setAddresses(List<Address> addresses) {
        this.addresses = addresses;
    }

    public IncomeDetails getIncomeDetails() {
        return incomeDetails;
    }

    public void setIncomeDetails(IncomeDetails incomeDetails) {
        this.incomeDetails = incomeDetails;
    }

    public ContactPreference getContactPreference() {
        return contactPreference;
    }

    public void setContactPreference(ContactPreference contactPreference) {
        this.contactPreference = contactPreference;
    }

    public Education getEducation() {
        return education;
    }

    public void setEducation(Education education) {
        this.education = education;
    }

    public EmploymentDetails getEmploymentDetails() {
        return employmentDetails;
    }

    public void setEmploymentDetails(EmploymentDetails employmentDetails) {
        this.employmentDetails = employmentDetails;
    }

    public KycInformation getKycInformation() {
        return kycInformation;
    }

    public void setKycInformation(KycInformation kycInformation) {
        this.kycInformation = kycInformation;
    }

    @Override
    public String toString() {
        return "Applicant{" +
                "demographics=" + demographics +
                ", identificationDocumentDetails=" + identificationDocumentDetails +
                ", customerDetails=" + customerDetails +
                ", consentDetails=" + consentDetails +
                ", name=" + name +
                ", phones=" + phones +
                ", emails=" + emails +
                ", addresses=" + addresses +
                ", incomeDetails=" + incomeDetails +
                ", contactPreference=" + contactPreference +
                ", education=" + education +
                ", employmentDetails=" + employmentDetails +
                ", kycInformation=" + kycInformation +
                '}';
    }

    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class Name {

        private String givenName;

        private String middleName;

        private String surname;

        public String getGivenName() {
            return givenName;
        }

        public void setGivenName(String givenName) {
            this.givenName = givenName;
        }

        public String getMiddleName() {
            return middleName;
        }

        public void setMiddleName(String middleName) {
            this.middleName = middleName;
        }

        public String getSurname() {
            return surname;
        }

        public void setSurname(String surname) {
            this.surname = surname;
        }
    }

    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class Phone {

        private String phoneType;

        private String phoneCountryCode;

        private String phoneNumber;

        public String getPhoneType() {
            return phoneType;
        }

        public void setPhoneType(String phoneType) {
            this.phoneType = phoneType;
        }

        public String getPhoneCountryCode() {
            return phoneCountryCode;
        }

        public void setPhoneCountryCode(String phoneCountryCode) {
            this.phoneCountryCode = phoneCountryCode;
        }

        public String getPhoneNumber() {
            return phoneNumber;
        }

        public void setPhoneNumber(String phoneNumber) {
            this.phoneNumber = phoneNumber;
        }
    }

    public static class Email{

        private String emailAddress;

        public String getEmailAddress() {
            return emailAddress;
        }

        public void setEmailAddress(String emailAddress) {
            this.emailAddress = emailAddress;
        }
    }

    public static class IncomeDetails {

        private String incomeType;
        private BigDecimal fixedAmount;
        private String frequency;

        public String getIncomeType() {
            return incomeType;
        }

        public void setIncomeType(String incomeType) {
            this.incomeType = incomeType;
        }

        public BigDecimal getFixedAmount() {
            return fixedAmount;
        }

        public void setFixedAmount(BigDecimal fixedAmount) {
            this.fixedAmount = fixedAmount;
        }

        public String getFrequency() {
            return frequency;
        }

        public void setFrequency(String frequency) {
            this.frequency = frequency;
        }
    }

    public static class ContactPreference {
        private boolean eStatementEnrollmentFlag;

        public boolean iseStatementEnrollmentFlag() {
            return eStatementEnrollmentFlag;
        }

        public void seteStatementEnrollmentFlag(boolean eStatementEnrollmentFlag) {
            this.eStatementEnrollmentFlag = eStatementEnrollmentFlag;
        }
    }

    public static class Education {
        private String highestEducationLevel;

        public String getHighestEducationLevel() {
            return highestEducationLevel;
        }

        public void setHighestEducationLevel(String highestEducationLevel) {
            this.highestEducationLevel = highestEducationLevel;
        }
    }

    public static class EmploymentDetails {

        private String employerName;
        private String employmentStatus;
        private String occupationCode;
        private String jobTitle;

        public String getEmployerName() {
            return employerName;
        }

        public void setEmployerName(String employerName) {
            this.employerName = employerName;
        }

        public String getEmploymentStatus() {
            return employmentStatus;
        }

        public void setEmploymentStatus(String employmentStatus) {
            this.employmentStatus = employmentStatus;
        }

        public String getOccupationCode() {
            return occupationCode;
        }

        public void setOccupationCode(String occupationCode) {
            this.occupationCode = occupationCode;
        }

        public String getJobTitle() {
            return jobTitle;
        }

        public void setJobTitle(String jobTitle) {
            this.jobTitle = jobTitle;
        }
    }

    public static class KycInformation{
        private boolean selfPublicFigureDeclarationFlag;
        private String taxDomicileCountryCode;

        public boolean isSelfPublicFigureDeclarationFlag() {
            return selfPublicFigureDeclarationFlag;
        }

        public void setSelfPublicFigureDeclarationFlag(boolean selfPublicFigureDeclarationFlag) {
            this.selfPublicFigureDeclarationFlag = selfPublicFigureDeclarationFlag;
        }

        public String getTaxDomicileCountryCode() {
            return taxDomicileCountryCode;
        }

        public void setTaxDomicileCountryCode(String taxDomicileCountryCode) {
            this.taxDomicileCountryCode = taxDomicileCountryCode;
        }
    }

}
