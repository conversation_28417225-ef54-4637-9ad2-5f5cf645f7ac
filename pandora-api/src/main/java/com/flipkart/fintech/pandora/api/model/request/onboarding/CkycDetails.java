package com.flipkart.fintech.pandora.api.model.request.onboarding;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class CkycDetails {

    @JsonProperty("mothersLastName")
    private String mothersLastName;

    @JsonProperty("fathersFirstName")
    private String fathersFirstName;

    @JsonProperty("fathersLastName")
    private String fathersLastName;

    @JsonProperty("residentialStatus")
    private String residentialStatus;

    @JsonProperty("bankOfficial")
    private String bankOffical;

    @JsonProperty("bankOfficialName")
    private String bankOfficialName;

    @JsonProperty("bankName")
    private String bankName;

    @JsonProperty("positionInBank")
    private String positionInBank;

    @JsonProperty("relationshipWithOfficial")
    private String relationshipWithOfficial;

    @JsonProperty("currentAddressSameAsMailingAddress")
    private String currentAddressSameAsMailingAddress;

    @JsonProperty("employerSector")
    private String employerSector;

    @JsonProperty("natureOfBusiness")
    private String natureOfBusiness;

    @JsonProperty("yrsInBusiness")
    private String yrsInBusiness;
}
