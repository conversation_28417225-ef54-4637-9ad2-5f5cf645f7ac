package com.flipkart.fintech.pandora.api.model.request.onboarding;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import lombok.Data;

/**
 * Created by aniruddha.sharma on 08/09/17.
 */

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonTypeInfo(
        property = "type",
        include = JsonTypeInfo.As.EXISTING_PROPERTY,
        use = JsonTypeInfo.Id.NAME,
        visible = true,
        defaultImpl = ExpandedUserRequest.class
)
@JsonSubTypes({@JsonSubTypes.Type(
        name = "COMPRESSED",
        value = CompressedUserRequest.class
), @JsonSubTypes.Type(
        name = "EXPANDED",
        value = ExpandedUserRequest.class
)})
public abstract class UserRequest {

    private RequestType type;

    public enum RequestType{
        COMPRESSED,
        EXPANDED
    }

    private String businessCategory;
}

