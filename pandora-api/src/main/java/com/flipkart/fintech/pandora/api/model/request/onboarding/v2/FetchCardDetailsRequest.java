package com.flipkart.fintech.pandora.api.model.request.onboarding.v2;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pandora.api.model.FingerprintInfo;
import com.flipkart.fintech.pandora.api.model.LenderInfoSection;
import lombok.Data;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class FetchCardDetailsRequest {
    @JsonProperty
    private LenderInfoSection lenderInfoSection;

    @JsonProperty
    private FingerprintInfo fingerprintInfo;
}
