package com.flipkart.fintech.pandora.api.model.request.onboarding.v2;

import com.flipkart.fintech.pandora.api.model.request.onboarding.Address;
import com.flipkart.fintech.pandora.api.model.request.onboarding.AddressType;
import com.flipkart.sensitive.annotation.SensitiveField;
import org.codehaus.jackson.annotate.JsonIgnoreProperties;

@JsonIgnoreProperties(
		ignoreUnknown = true
)
public class KYCSchedulerDetailsRequest {

	private AddressType addressType;
	private Address address;
	@SensitiveField(keyName = "cbcKey")
	private String mobileNumber;
	@SensitiveField(keyName = "cbcKey")
	private String alternateMobileNumber;
	private String appointmentDate;
	private String appointmentTime;
	private String caseReferenceId;
	private String userId;
	
	public AddressType getAddressType() {
		return addressType;
	}
	public void setAddressType(AddressType addressType) {
		this.addressType = addressType;
	}
	public Address getAddress() {
		return address;
	}
	public void setAddress(Address address) {
		this.address = address;
	}
	public String getMobileNumber() {
		return mobileNumber;
	}
	public void setMobileNumber(String mobileNumber) {
		this.mobileNumber = mobileNumber;
	}
	public String getAlternateMobileNumber() {
		return alternateMobileNumber;
	}
	public void setAlternateMobileNumber(String alternateMobileNumber) {
		this.alternateMobileNumber = alternateMobileNumber;
	}
	public String getAppointmentDate() {
		return appointmentDate;
	}
	public void setAppointmentDate(String appointmentDate) {
		this.appointmentDate = appointmentDate;
	}
	public String getAppointmentTime() {
		return appointmentTime;
	}
	public void setAppointmentTime(String appointmentTime) {
		this.appointmentTime = appointmentTime;
	}
	public String getCaseReferenceId() {
		return caseReferenceId;
	}
	public void setCaseReferenceId(String caseReferenceId) {
		this.caseReferenceId = caseReferenceId;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	@Override
	public String toString() {
		return "KYCSchedulerDetailsRequest{" +
				"addressType=" + addressType +
				", address=" + address +
				", mobileNumber='" + mobileNumber + '\'' +
				", alternateMobileNumber='" + alternateMobileNumber + '\'' +
				", appointmentDate='" + appointmentDate + '\'' +
				", appointmentTime='" + appointmentTime + '\'' +
				", caseReferenceId='" + caseReferenceId + '\'' +
				", userId='" + userId + '\'' +
				'}';
	}
}
