package com.flipkart.fintech.pandora.api.model.request.onboarding.v2;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pandora.api.model.LenderInfoSection;

@JsonIgnoreProperties(ignoreUnknown = true)
public class PanNoRequest {

    @JsonProperty
    private String panNumber;

    @JsonProperty
    private LenderInfoSection lenderInfoSection;

    public String getPanNumber() {
        return panNumber;
    }

    public void setPanNumber(String panNumber) {
        this.panNumber = panNumber;
    }

    public LenderInfoSection getLenderInfoSection() {
        return lenderInfoSection;
    }

    public void setLenderInfoSection(LenderInfoSection lenderInfoSection) {
        this.lenderInfoSection = lenderInfoSection;
    }

    @Override
    public String toString() {
        return "PanNoRequest{" +
                ", lenderInfoSection=" + lenderInfoSection +
                '}';
    }
}
