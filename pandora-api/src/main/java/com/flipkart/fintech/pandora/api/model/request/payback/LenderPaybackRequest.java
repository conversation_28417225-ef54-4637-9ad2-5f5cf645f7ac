package com.flipkart.fintech.pandora.api.model.request.payback;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.math.BigDecimal;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class LenderPaybackRequest {
    private String lan;
    private String crn;
    private String paymentType;
    private String chequeid;
    private String chequeNumber;
    private String receiptNumber;
    //not being used
    private String utrNumber;
    private String chequeAmt;
    private String chequeDate;
    private String lenderRealisationDate;
    private String receiptSource;
    private BigDecimal allocPos;
    private BigDecimal allocInterest;
    private BigDecimal allocCharges;
    private BigDecimal allocExcess;
    private BigDecimal limitReleasedAmt;

    private String loanStatus;

    private String schemeDesc;


    @Override
    public String toString() {
        return "DirectPaybackRequest{" +
                "lan='" + lan + '\'' +
                ", crn='" + crn + '\'' +
                ", chequeid='" + chequeid + '\'' +
                ", chequeNumber='" + chequeNumber + '\'' +
                ", receiptNumber='" + receiptNumber + '\'' +
                ", utrNumber='" + utrNumber + '\'' +
                ", chequeAmt='" + chequeAmt + '\'' +
                ", chequeDate='" + chequeDate + '\'' +
                ", lenderRealisationDate='" + lenderRealisationDate + '\'' +
                ", receiptSource='" + receiptSource + '\'' +
                ", allocPos=" + allocPos +
                ", allocInterest=" + allocInterest +
                ", allocCharges=" + allocCharges +
                ", allocExcess=" + allocExcess +
                ", limitReleasedAmt=" + limitReleasedAmt +
                ", status=" + loanStatus +
                ", schemeDesc=" + schemeDesc +
                '}';
    }

}



