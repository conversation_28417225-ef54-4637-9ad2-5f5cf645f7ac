package com.flipkart.fintech.pandora.api.model.request.penalty;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 * @since 24/05/18.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class LenderPenaltyWaiveOffRequest extends PenaltyWaiveOffRequest {

    @JsonProperty("dues_reverse_reference_number")
    private String lenderReversePenaltyRef;

    public String getLenderReversePenaltyRef() {
        return lenderReversePenaltyRef;
    }

    public void setLenderReversePenaltyRef(String lenderReversePenaltyRef) {
        this.lenderReversePenaltyRef = lenderReversePenaltyRef;
    }

    @Override
    public String toString() {
        return "LenderPenaltyWaiveOffRequest{" +
                "lenderReversePenaltyRef='" + lenderReversePenaltyRef + '\'' +
                '}';
    }
}
