package com.flipkart.fintech.pandora.api.model.request.penalty;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 22/05/18.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class PenaltyWaiveOffRequest {

    @JsonProperty("dues_reference_number")
    private String lenderForwardPenaltyRef;

    @JsonProperty("amount")
    private BigDecimal amount;

    @JsonProperty("waived_by")
    private String waivedBy;

    public String getLenderForwardPenaltyRef() {
        return lenderForwardPenaltyRef;
    }

    public void setLenderForwardPenaltyRef(String lenderForwardPenaltyRef) {
        this.lenderForwardPenaltyRef = lenderForwardPenaltyRef;
    }

    public String getWaivedBy() {
        return waivedBy;
    }

    public void setWaivedBy(String waivedBy) {
        this.waivedBy = waivedBy;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    @Override
    public String toString() {
        return "PenaltyWaiveOffRequest{" +
                "lenderForwardPenaltyRef='" + lenderForwardPenaltyRef + '\'' +
                ", amount=" + amount +
                ", waivedBy='" + waivedBy + '\'' +
                '}';
    }
}
