package com.flipkart.fintech.pandora.api.model.request.plOnboarding;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.flipkart.fintech.pandora.api.model.common.Gender;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * Created by pritam.raj on 21/03/23.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class Identification {

    @NotNull
    @Pattern(regexp = "[A-Z]{3}P[A-Z]{1}[0-9]{4}[A-Z]{1}", message = "Invalid PAN")
    private String pan;

    @NotNull
    @Pattern(regexp = "YYYY-MM-DD")
    private String dob;

    @NotNull
    private Gender gender;

    private String firstName;

    private String lastName;
}
