package com.flipkart.fintech.pandora.api.model.request.plOnboarding;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * Created by pritam.raj on 21/03/23.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SubmitLoanOfferRequest implements PlOnboardingRequest {

    @NotNull
    private String lspApplicationId;

    private Lender lender;

    @NotNull
    private LoanParameter loanDetails;

    private long requestTime;

    private ConsentMetadata consentMetadata;

    @NotNull
    private Consent consent;

    @NotNull
    private String callBackURL;
}
