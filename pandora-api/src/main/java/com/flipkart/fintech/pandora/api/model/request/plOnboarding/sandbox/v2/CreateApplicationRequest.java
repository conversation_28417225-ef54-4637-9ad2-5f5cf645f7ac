package com.flipkart.fintech.pandora.api.model.request.plOnboarding.sandbox.v2;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.flipkart.fintech.pandora.api.model.pl.sandbox.dto.enums.IncomeGranularity;
import com.flipkart.fintech.pandora.api.model.pl.sandbox.dto.enums.IncomeMode;
import com.flipkart.fintech.pandora.api.model.request.plOnboarding.ConsentMetadata;
import com.flipkart.fintech.pandora.api.model.request.plOnboarding.PlOnboardingRequest;
import lombok.*;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CreateApplicationRequest implements PlOnboardingRequest {

    private String merchant;

    private String lspApplicationId;

    private String lender;

    private String pan;

    private String dob;

    private String employmentType;

    private String organization;

    private String income;

    //todo change through to take integer values
    private String bonusIncome;

    private IncomeGranularity incomeGranularity;

    private IncomeMode incomeMode;

    private String phoneNo;

    private String emailId;

    private String firstName;

    private String lastName;

    private String gender;
    
    private String state;

    private String city;

    private String pincode;

    private String area;

    private String houseNumber;

    private String shippingAddressId;

    private String accountId;

    private String smUserId;

    private String leadId;

    private ConsentMetadata consentData;

    private List<ConsentMetadata> consentMetaDataList;

    private String offerId;

    private String callbackUrl;

    private Double cremoScore;

    private String cremoVersion;

    private String granularCremoScore;

    public Integer getValidIncome(String income) {
        if (StringUtils.isNotEmpty(income)) {
            income = income.replace(",", "");
            if(StringUtils.isNumeric(income)){
                return Integer.parseInt(income);
            }
        }
        return 0;
    }

}
