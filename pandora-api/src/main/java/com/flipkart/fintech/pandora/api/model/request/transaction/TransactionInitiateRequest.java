package com.flipkart.fintech.pandora.api.model.request.transaction;

import javax.ws.rs.DefaultValue;

/**
 * Created by aniruddha.sharma on 02/11/17.
 */
public class TransactionInitiateRequest {
    private int accountNumber;
    private String transactionId;
    private Double transactionAmount;
    @DefaultValue("false")
    private Boolean skipOtp;
    private Offers offer;

    public int getAccountNumber() {
        return accountNumber;
    }

    public void setAccountNumber(int accountNumber) {
        this.accountNumber = accountNumber;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public Double getTransactionAmount() {
        return transactionAmount;
    }

    public void setTransactionAmount(Double transactionAmount) {
        this.transactionAmount = transactionAmount;
    }

    public Boolean getSkipOtp() {
        return skipOtp;
    }

    public void setSkipOtp(Boolean skipOtp) {
        this.skipOtp = skipOtp;
    }

    public Offers getOffer() {
        return offer;
    }

    public void setOffer(Offers offer) {
        this.offer = offer;
    }
}
