package com.flipkart.fintech.pandora.api.model.request.upiPayment;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class ValidateVPARequest {

    @NotNull
    @JsonProperty("upi_address")
    private String upiAddress;

    @NotNull
    @JsonProperty("merchant_name")
    private String merchantName;

    @JsonProperty("contact")
    private String contact;

}
