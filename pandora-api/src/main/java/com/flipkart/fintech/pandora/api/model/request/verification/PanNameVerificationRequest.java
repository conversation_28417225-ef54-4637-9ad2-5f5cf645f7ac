package com.flipkart.fintech.pandora.api.model.request.verification;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

@Getter
@Builder
@RequiredArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class PanNameVerificationRequest {

    @JsonProperty("pan")
    private String pan;

    @JsonProperty("firstName")
    private String firstName;

    @JsonProperty("lastName")
    private String lastName;

    @JsonProperty("accountId")
    private String accountId;

    @JsonProperty("smUserId")
    private String smUserId;

    @JsonProperty("merchantId")
    private String merchantId;

}
