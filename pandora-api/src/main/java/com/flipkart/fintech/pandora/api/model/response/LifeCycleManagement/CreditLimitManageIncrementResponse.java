package com.flipkart.fintech.pandora.api.model.response.LifeCycleManagement;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pandora.api.model.StatementSection;
import com.flipkart.fintech.pandora.api.model.response.BaseResponse;
import lombok.*;
import com.flipkart.fintech.pandora.api.model.common.STATUS;

@AllArgsConstructor
@NoArgsConstructor
@ToString
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class CreditLimitManageIncrementResponse extends BaseResponse {

    @JsonProperty("STATUS")
    private STATUS status;

    @JsonProperty("message")
    private String message;

    // Note : added for adding custom fields to avoid models version bump up and redeployment of pinaka
    @JsonProperty("statementSection")
    private StatementSection statementSection;
}