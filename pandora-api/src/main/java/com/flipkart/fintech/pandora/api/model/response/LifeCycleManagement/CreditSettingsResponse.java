package com.flipkart.fintech.pandora.api.model.response.LifeCycleManagement;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pandora.api.model.StatementSection;
import com.flipkart.fintech.pandora.api.model.response.BaseResponse;
import lombok.*;

@AllArgsConstructor
@NoArgsConstructor
@ToString
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class CreditSettingsResponse extends BaseResponse {

    @JsonProperty("onlineShopping")
    private Boolean onlineShopping;

    @JsonProperty("offlineShoppingAndAtm")
    private Boolean offlineShoppingAndAtm;

    @JsonProperty("contactlessTxns")
    private Boolean contactlessTxns;

    @JsonProperty("internationalTxns")
    private Boolean internationalTxns;

    // Note : added for adding custom fields to avoid models version bump up and redeployment of pinaka
    @JsonProperty("statementSection")
    private StatementSection statementSection;

    @JsonProperty("deliveryFlag")
    private String deliveryFlag;
}