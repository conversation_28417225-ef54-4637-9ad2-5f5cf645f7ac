package com.flipkart.fintech.pandora.api.model.response.LifeCycleManagement;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pandora.api.model.response.UpdateRecordStatusResponse;
import lombok.Data;

@Data
public class UpdateSupercoinRecordResponse {
    @JsonProperty("updateRecordStatusResponse")
    private UpdateRecordStatusResponse updateRecordStatusResponse;
}
