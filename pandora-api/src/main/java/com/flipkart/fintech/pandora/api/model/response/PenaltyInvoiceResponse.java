package com.flipkart.fintech.pandora.api.model.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigDecimal;

/**
 * Created by kunal.keshwani on 13/02/18.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class PenaltyInvoiceResponse {

    @JsonProperty("due_amount")
    private BigDecimal dueAmount;
    @JsonProperty("base_amount")
    private BigDecimal baseAmount;
    @JsonProperty("igst_amount")
    private BigDecimal igstAmount;
    @JsonProperty("cgst_amount")
    private BigDecimal cgstAmount;
    @JsonProperty("sgst_amount")
    private BigDecimal sgstAmount;
    @JsonProperty("igst_rate")
    private BigDecimal igstRate;
    @JsonProperty("cgst_rate")
    private BigDecimal cgstRate;
    @JsonProperty("sgst_rate")
    private BigDecimal sgstRate;
    @JsonProperty("date")
    private String date;
    @JsonProperty("user_identifier")
    private String userIdentifier;
    @JsonProperty("invoice_number")
    private String invoiceNumber;
    @JsonProperty("status")
    private String status;

    public BigDecimal getDueAmount() {
        return dueAmount;
    }

    public void setDueAmount(BigDecimal dueAmount) {
        this.dueAmount = dueAmount;
    }

    public BigDecimal getBaseAmount() {
        return baseAmount;
    }

    public void setBaseAmount(BigDecimal baseAmount) {
        this.baseAmount = baseAmount;
    }

    public BigDecimal getIgstAmount() {
        return igstAmount;
    }

    public void setIgstAmount(BigDecimal igstAmount) {
        this.igstAmount = igstAmount;
    }

    public BigDecimal getCgstAmount() {
        return cgstAmount;
    }

    public void setCgstAmount(BigDecimal cgstAmount) {
        this.cgstAmount = cgstAmount;
    }

    public BigDecimal getSgstAmount() {
        return sgstAmount;
    }

    public void setSgstAmount(BigDecimal sgstAmount) {
        this.sgstAmount = sgstAmount;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public String getUserIdentifier() {
        return userIdentifier;
    }

    public void setUserIdentifier(String userIdentifier) {
        this.userIdentifier = userIdentifier;
    }

    public String getInvoiceNumber() {
        return invoiceNumber;
    }

    public void setInvoiceNumber(String invoiceNumber) {
        this.invoiceNumber = invoiceNumber;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public BigDecimal getIgstRate() {
        return igstRate;
    }

    public void setIgstRate(BigDecimal igstRate) {
        this.igstRate = igstRate;
    }

    public BigDecimal getCgstRate() {
        return cgstRate;
    }

    public void setCgstRate(BigDecimal cgstRate) {
        this.cgstRate = cgstRate;
    }

    public BigDecimal getSgstRate() {
        return sgstRate;
    }

    public void setSgstRate(BigDecimal sgstRate) {
        this.sgstRate = sgstRate;
    }

    @Override
    public String toString() {
        return "BnplPenaltyInvoiceResponse{" +
                "dueAmount=" + dueAmount +
                ", baseAmount=" + baseAmount +
                ", igstAmount=" + igstAmount +
                ", cgstAmount=" + cgstAmount +
                ", sgstAmount=" + sgstAmount +
                ", igstRate=" + igstRate +
                ", cgstRate=" + cgstRate +
                ", sgstRate=" + sgstRate +
                ", date='" + date + '\'' +
                ", userIdentifier='" + userIdentifier + '\'' +
                ", invoiceNumber='" + invoiceNumber + '\'' +
                ", status='" + status + '\'' +
                '}';
    }
}



