package com.flipkart.fintech.pandora.api.model.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pandora.api.model.common.STATUS;

@JsonIgnoreProperties(ignoreUnknown = true)
public class UpdateRecordStatusResponse {

    @JsonProperty("STATUS")
    private STATUS status;

    private String description;

    public STATUS getStatus() {
        return status;
    }

    public void setStatus(STATUS status) {
        this.status = status;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public String toString() {
        return "UpdateRecordStatusResponse{" +
                "status=" + status +
                ", description='" + description + '\'' +
                '}';
    }
}

