package com.flipkart.fintech.pandora.api.model.response.billing;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.flipkart.fintech.pandora.api.model.response.billing.enums.BillingTransactionType;
import com.flipkart.fintech.pandora.api.model.response.billing.transactionTypes.EmiPurchase;

/**
 * Created by aniruddha.sharma on 30/01/18.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class EmiPurchaseBillingTransaction extends BillingTransaction {

    private EmiPurchase emiPurchase;

    public EmiPurchaseBillingTransaction() {
        super(BillingTransactionType.EMI_PURCHASE);
    }

    public EmiPurchase getEmiPurchase() {
        return emiPurchase;
    }

    public void setEmiPurchase(EmiPurchase emiPurchase) {
        this.emiPurchase = emiPurchase;
    }
}
