package com.flipkart.fintech.pandora.api.model.response.billing;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.math.BigDecimal;

/**
 * Created by aniruddha.sharma on 12/12/17.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LoanResponse {
    private String loanReferenceNumber;

    private BigDecimal loanAmount;

    private String loanStartDate;

    private String loanEndDate;

    private BigDecimal transactionAmount;

    private BigDecimal downpaymentAmount;

    private BigDecimal interestRate;

    private BigDecimal totalInterestAmount;

    private BigDecimal installmentAmount;

    private int tenure;

    private BigDecimal principalOutstandingAmount;

    private BigDecimal totalPrincipalPaid;

    private BigDecimal totalInterestPaid;

    private int numberOfInstallmentsPaid;

    private String loanStatus;

    private String lenderTransactionId;

    private String loanType;

    public String getLoanReferenceNumber() {
        return loanReferenceNumber;
    }

    public void setLoanReferenceNumber(String loanReferenceNumber) {
        this.loanReferenceNumber = loanReferenceNumber;
    }

    public BigDecimal getLoanAmount() {
        return loanAmount;
    }

    public void setLoanAmount(BigDecimal loanAmount) {
        this.loanAmount = loanAmount;
    }

    public String getLoanStartDate() {
        return loanStartDate;
    }

    public void setLoanStartDate(String loanStartDate) {
        this.loanStartDate = loanStartDate;
    }

    public String getLoanEndDate() {
        return loanEndDate;
    }

    public void setLoanEndDate(String loanEndDate) {
        this.loanEndDate = loanEndDate;
    }

    public BigDecimal getTransactionAmount() {
        return transactionAmount;
    }

    public void setTransactionAmount(BigDecimal transactionAmount) {
        this.transactionAmount = transactionAmount;
    }

    public BigDecimal getDownpaymentAmount() {
        return downpaymentAmount;
    }

    public void setDownpaymentAmount(BigDecimal downpaymentAmount) {
        this.downpaymentAmount = downpaymentAmount;
    }

    public BigDecimal getInterestRate() {
        return interestRate;
    }

    public void setInterestRate(BigDecimal interestRate) {
        this.interestRate = interestRate;
    }

    public BigDecimal getInstallmentAmount() {
        return installmentAmount;
    }

    public void setInstallmentAmount(BigDecimal installmentAmount) {
        this.installmentAmount = installmentAmount;
    }

    public int getTenure() {
        return tenure;
    }

    public void setTenure(int tenure) {
        this.tenure = tenure;
    }

    public BigDecimal getPrincipalOutstandingAmount() {
        return principalOutstandingAmount;
    }

    public void setPrincipalOutstandingAmount(BigDecimal principalOutstandingAmount) {
        this.principalOutstandingAmount = principalOutstandingAmount;
    }

    public BigDecimal getTotalPrincipalPaid() {
        return totalPrincipalPaid;
    }

    public void setTotalPrincipalPaid(BigDecimal totalPrincipalPaid) {
        this.totalPrincipalPaid = totalPrincipalPaid;
    }

    public BigDecimal getTotalInterestPaid() {
        return totalInterestPaid;
    }

    public void setTotalInterestPaid(BigDecimal totalInterestPaid) {
        this.totalInterestPaid = totalInterestPaid;
    }

    public int getNumberOfInstallmentsPaid() {
        return numberOfInstallmentsPaid;
    }

    public void setNumberOfInstallmentsPaid(int numberOfInstallmentsPaid) {
        this.numberOfInstallmentsPaid = numberOfInstallmentsPaid;
    }

    public String getLoanStatus() {
        return loanStatus;
    }

    public void setLoanStatus(String loanStatus) {
        this.loanStatus = loanStatus;
    }

    public String getLenderTransactionId() {
        return lenderTransactionId;
    }

    public void setLenderTransactionId(String lenderTransactionId) {
        this.lenderTransactionId = lenderTransactionId;
    }

    public String getLoanType() {
        return loanType;
    }

    public void setLoanType(String loanType) {
        this.loanType = loanType;
    }

    public BigDecimal getTotalInterestAmount() {
        return totalInterestAmount;
    }

    public void setTotalInterestAmount(BigDecimal totalInterestAmount) {
        this.totalInterestAmount = totalInterestAmount;
    }

    @Override
    public String toString() {
        return "LoanResponse{" +
                "loanReferenceNumber='" + loanReferenceNumber + '\'' +
                ", loanAmount=" + loanAmount +
                ", loanStartDate='" + loanStartDate + '\'' +
                ", loanEndDate='" + loanEndDate + '\'' +
                ", transactionAmount=" + transactionAmount +
                ", downpaymentAmount=" + downpaymentAmount +
                ", interestRate=" + interestRate +
                ", totalInterestAmount=" + totalInterestAmount +
                ", installmentAmount=" + installmentAmount +
                ", tenure=" + tenure +
                ", principalOutstandingAmount=" + principalOutstandingAmount +
                ", totalPrincipalPaid=" + totalPrincipalPaid +
                ", totalInterestPaid=" + totalInterestPaid +
                ", numberOfInstallmentsPaid=" + numberOfInstallmentsPaid +
                ", loanStatus='" + loanStatus + '\'' +
                ", lenderTransactionId='" + lenderTransactionId + '\'' +
                ", loanType='" + loanType + '\'' +
                '}';
    }
}
