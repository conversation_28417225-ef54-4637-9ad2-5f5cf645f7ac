package com.flipkart.fintech.pandora.api.model.response.billing;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.flipkart.fintech.pandora.api.model.response.billing.enums.BillingTransactionType;
import com.flipkart.fintech.pandora.api.model.response.billing.transactionTypes.Payback;

/**
 * Created by aniruddha.sharma on 01/02/18.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class PaybackBillingTransaction extends BillingTransaction{

    private Payback payback;

    public Payback getPayback() {
        return payback;
    }

    public void setPayback(Payback payback) {
        this.payback = payback;
    }

    public PaybackBillingTransaction() {
        super(BillingTransactionType.PAYBACK);
    }
}
