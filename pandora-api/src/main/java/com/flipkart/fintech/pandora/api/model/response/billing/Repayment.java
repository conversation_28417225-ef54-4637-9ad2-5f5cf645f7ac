package com.flipkart.fintech.pandora.api.model.response.billing;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.math.BigDecimal;

/**
 * Created by aniruddha.sharma on 23/02/18.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class Repayment {
    private String repaymentReferenceNumber;

    private String loanReferenceNumber;

    private BigDecimal scheduledPaymentAmount;

    private String scheduledPaymentDate;

    private BigDecimal interestComponent;

    private BigDecimal principalComponent;

    private BigDecimal totalPaymentReceived;

    private BigDecimal paymentAppliedTowardsPrincipal;

    private BigDecimal paymentAppliedTowardsInterest;

    private String repaymentStatus;

    private String paymentStatus;

    public String getRepaymentReferenceNumber() {
        return repaymentReferenceNumber;
    }

    public void setRepaymentReferenceNumber(String repaymentReferenceNumber) {
        this.repaymentReferenceNumber = repaymentReferenceNumber;
    }

    public String getLoanReferenceNumber() {
        return loanReferenceNumber;
    }

    public void setLoanReferenceNumber(String loanReferenceNumber) {
        this.loanReferenceNumber = loanReferenceNumber;
    }

    public BigDecimal getScheduledPaymentAmount() {
        return scheduledPaymentAmount;
    }

    public void setScheduledPaymentAmount(BigDecimal scheduledPaymentAmount) {
        this.scheduledPaymentAmount = scheduledPaymentAmount;
    }

    public String getScheduledPaymentDate() {
        return scheduledPaymentDate;
    }

    public void setScheduledPaymentDate(String scheduledPaymentDate) {
        this.scheduledPaymentDate = scheduledPaymentDate;
    }

    public BigDecimal getInterestComponent() {
        return interestComponent;
    }

    public void setInterestComponent(BigDecimal interestComponent) {
        this.interestComponent = interestComponent;
    }

    public BigDecimal getPrincipalComponent() {
        return principalComponent;
    }

    public void setPrincipalComponent(BigDecimal principalComponent) {
        this.principalComponent = principalComponent;
    }

    public BigDecimal getTotalPaymentReceived() {
        return totalPaymentReceived;
    }

    public void setTotalPaymentReceived(BigDecimal totalPaymentReceived) {
        this.totalPaymentReceived = totalPaymentReceived;
    }

    public BigDecimal getPaymentAppliedTowardsPrincipal() {
        return paymentAppliedTowardsPrincipal;
    }

    public void setPaymentAppliedTowardsPrincipal(BigDecimal paymentAppliedTowardsPrincipal) {
        this.paymentAppliedTowardsPrincipal = paymentAppliedTowardsPrincipal;
    }

    public BigDecimal getPaymentAppliedTowardsInterest() {
        return paymentAppliedTowardsInterest;
    }

    public void setPaymentAppliedTowardsInterest(BigDecimal paymentAppliedTowardsInterest) {
        this.paymentAppliedTowardsInterest = paymentAppliedTowardsInterest;
    }

    public String getRepaymentStatus() {
        return repaymentStatus;
    }

    public void setRepaymentStatus(String repaymentStatus) {
        this.repaymentStatus = repaymentStatus;
    }

    public String getPaymentStatus() {
        return paymentStatus;
    }

    public void setPaymentStatus(String paymentStatus) {
        this.paymentStatus = paymentStatus;
    }

    @Override
    public String toString() {
        return "Repayment{" +
                "repaymentReferenceNumber='" + repaymentReferenceNumber + '\'' +
                ", loanReferenceNumber='" + loanReferenceNumber + '\'' +
                ", scheduledPaymentAmount=" + scheduledPaymentAmount +
                ", scheduledPaymentDate='" + scheduledPaymentDate + '\'' +
                ", interestComponent=" + interestComponent +
                ", principalComponent=" + principalComponent +
                ", totalPaymentReceived=" + totalPaymentReceived +
                ", paymentAppliedTowardsPrincipal=" + paymentAppliedTowardsPrincipal +
                ", paymentAppliedTowardsInterest=" + paymentAppliedTowardsInterest +
                ", repaymentStatus='" + repaymentStatus + '\'' +
                ", paymentStatus='" + paymentStatus + '\'' +
                '}';
    }
}
