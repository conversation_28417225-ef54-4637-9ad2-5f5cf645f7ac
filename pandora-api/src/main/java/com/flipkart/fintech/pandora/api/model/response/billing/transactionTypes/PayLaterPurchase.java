package com.flipkart.fintech.pandora.api.model.response.billing.transactionTypes;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * Created by aniruddha.sharma on 30/01/18.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class PayLaterPurchase {

    private String lenderTransactionId;

    private String loanReferenceNumber;

    public String getLenderTransactionId() {
        return lenderTransactionId;
    }

    public void setLenderTransactionId(String lenderTransactionId) {
        this.lenderTransactionId = lenderTransactionId;
    }

    public String getLoanReferenceNumber() {
        return loanReferenceNumber;
    }

    public void setLoanReferenceNumber(String loanReferenceNumber) {
        this.loanReferenceNumber = loanReferenceNumber;
    }
}
