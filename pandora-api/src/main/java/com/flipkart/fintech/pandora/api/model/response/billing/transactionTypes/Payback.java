package com.flipkart.fintech.pandora.api.model.response.billing.transactionTypes;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * Created by aniruddha.sharma on 01/02/18.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class Payback {

    private String pgTxnId;

    private String source;

    private String paymentReferenceNumber;

    public String getPgTxnId() {
        return pgTxnId;
    }

    public void setPgTxnId(String pgTxnId) {
        this.pgTxnId = pgTxnId;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getPaymentReferenceNumber() {
        return paymentReferenceNumber;
    }

    public void setPaymentReferenceNumber(String paymentReferenceNumber) {
        this.paymentReferenceNumber = paymentReferenceNumber;
    }
}
