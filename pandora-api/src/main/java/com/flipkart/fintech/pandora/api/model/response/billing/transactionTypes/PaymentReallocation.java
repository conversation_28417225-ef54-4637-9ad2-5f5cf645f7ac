package com.flipkart.fintech.pandora.api.model.response.billing.transactionTypes;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * Created by aniruddha.sharma on 01/02/18.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class PaymentReallocation {

    private String lenderTransactionId;

    private Integer emiInstallmentIndex;

    private Integer loanTenure;

    private String repaymentReferenceNumber;

    public String getLenderTransactionId() {
        return lenderTransactionId;
    }

    public void setLenderTransactionId(String lenderTransactionId) {
        this.lenderTransactionId = lenderTransactionId;
    }

    public Integer getEmiInstallmentIndex() {
        return emiInstallmentIndex;
    }

    public void setEmiInstallmentIndex(Integer emiInstallmentIndex) {
        this.emiInstallmentIndex = emiInstallmentIndex;
    }

    public Integer getLoanTenure() {
        return loanTenure;
    }

    public void setLoanTenure(Integer loanTenure) {
        this.loanTenure = loanTenure;
    }

    public String getRepaymentReferenceNumber() {
        return repaymentReferenceNumber;
    }

    public void setRepaymentReferenceNumber(String repaymentReferenceNumber) {
        this.repaymentReferenceNumber = repaymentReferenceNumber;
    }
}
