package com.flipkart.fintech.pandora.api.model.response.cbc.upswing;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.codehaus.jackson.annotate.JsonIgnoreProperties;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CustomerHandshakeResponse { // These token is needed for upswing sdk handshake

    @JsonProperty("upswingUserId")
    private String upswingUserId;

    @JsonProperty("guestSessionToken")
    private String guestSessionToken;
}
