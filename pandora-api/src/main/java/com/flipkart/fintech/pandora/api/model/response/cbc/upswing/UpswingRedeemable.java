package com.flipkart.fintech.pandora.api.model.response.cbc.upswing;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pandora.api.model.request.cbc.upswing.UpswingUpdatedRewardDetailsV2;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UpswingRedeemable {

    @JsonProperty("totalRewardBalance")
    @NotNull(message = "RewardBalance can't be null")
    private BigDecimal upswingRewardBalance;

    @JsonProperty("rewardCurrencyCode")
    private String rewardCurrencyId;

    @NotNull
    @JsonProperty("timestamp")
    private long timestamp;
}
