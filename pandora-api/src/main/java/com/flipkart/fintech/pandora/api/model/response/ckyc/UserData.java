package com.flipkart.fintech.pandora.api.model.response.ckyc;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.flipkart.sensitive.annotation.SensitiveField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(value = JsonInclude.Include.NON_EMPTY)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class UserData {

    private String recordIdentifier;
    private String applicationFormNo;
    private String branchCode;
    private String constiType;
    private String accType;
    @SensitiveField(keyName = "kycKey")
    private String ckycId;
    private String namePrefix;
    private String firstName;
    private String middleName;
    private String lastName;
    private String fullName;
    private String maidenNamePrefix;
    private String maidenFirstName;
    private String maidenMiddleName;
    private String maidenLastName;
    private String maidenFullName;
    private String fatherNamePrefix;
    private String fatherFirstName;
    private String fatherMiddleName;
    private String fatherLastName;
    private String fatherFullName;
    private String motherNamePrefix;
    private String motherFirstName;
    private String motherMiddleName;
    private String motherLastName;
    private String motherFullName;
    private String gender;
    private String maritalStatus;
    private String nationality;
    private String occupation;
    private String dob;
    private String residentialStatus;
    private String taxResidencyOutsideIndia;
    private String jurisdictionOfRes;
    private String tin;
    private String countryOfBirth;
    private String placeOfBirth;
    private String perAddType;
    @SensitiveField(keyName = "kycKey")
    private String perAdd1;
    @SensitiveField(keyName = "kycKey")
    private String perAdd2;
    @SensitiveField(keyName = "kycKey")
    private String perAdd3;
    @SensitiveField(keyName = "kycKey")
    private String perAddCity;
    @SensitiveField(keyName = "kycKey")
    private String perAddDistrict;
    @SensitiveField(keyName = "kycKey")
    private String perAddState;
    @SensitiveField(keyName = "kycKey")
    private String perAddCountry;
    @SensitiveField(keyName = "kycKey")
    private String perAddPin;
    private String perAddPoa;
    private String perAddPoaOthers;
    private String perAddSameAsCorAdd;
    @SensitiveField(keyName = "kycKey")
    private String corAdd1;
    @SensitiveField(keyName = "kycKey")
    private String corAdd2;
    @SensitiveField(keyName = "kycKey")
    private String corAdd3;
    @SensitiveField(keyName = "kycKey")
    private String corAddCity;
    @SensitiveField(keyName = "kycKey")
    private String corAddDistrict;
    @SensitiveField(keyName = "kycKey")
    private String corAddState;
    @SensitiveField(keyName = "kycKey")
    private String corAddCountry;
    @SensitiveField(keyName = "kycKey")
    private String corAddPin;
    private String perAddSameAsJurAdd;
    @SensitiveField(keyName = "kycKey")
    private String jurAdd1;
    @SensitiveField(keyName = "kycKey")
    private String jurAdd2;
    @SensitiveField(keyName = "kycKey")
    private String jurAdd3;
    @SensitiveField(keyName = "kycKey")
    private String jurAddCity;
    @SensitiveField(keyName = "kycKey")
    private String jurAddState;
    @SensitiveField(keyName = "kycKey")
    private String jurAddCountry;
    @SensitiveField(keyName = "kycKey")
    private String jurAddPin;
    private String resTelStd;
    @SensitiveField(keyName = "kycKey")
    private String resTelNumber;
    private String offTelStd;
    @SensitiveField(keyName = "kycKey")
    private String offTelNumber;
    private String mobileIsd;
    @SensitiveField(keyName = "kycKey")
    private String mobileNumber;
    private String faxStd;
    private String faxNumber;
    @SensitiveField(keyName = "kycKey")
    private String emailAdd;
    private String remarks;
    private String dateOfDeclaration;
    private String placeOfDeclaration;
    private String kycVerificationDate;
    private String typeOfDocSubmitted;
    private String kycVerificationName;
    private String kycVerificationDesg;
    private String kycVerificationBranch;
    private String kycVerificationEmpCode;
    private String numberOfIds;
    private String numberOfRelatedPersons;
    private String numberOfLocalAdds;
    private String numberOfImages;
    private String nameUpdated;
    private String personalOrEntityDetailsUpdated;
    private String addressDetailsUpdated;
    private String contactDetailsUpdated;
    private String remarksUpdated;
    private String kycVerificationUpdated;
    private String idDetailsUpdated;
    private String relatedPersonsUpdated;
    private String imageDetailsUpdated;

}
