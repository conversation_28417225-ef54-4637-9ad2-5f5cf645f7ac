package com.flipkart.fintech.pandora.api.model.response.ekyc;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.flipkart.sensitive.annotation.SensitiveField;

/**
 * Created by aniruddha.sharma on 30/11/17.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class AadhaarData {
    private String aadhaarReferenceNumber;
    private String name;
    private String dob;
    private String age;
    @SensitiveField(keyName = "kycKey")
    private String email;
    @SensitiveField(keyName = "kycKey")
    private String phone;
    private String gender;
    @SensitiveField(keyName = "kycKey")
    private String careOf;
    @SensitiveField(keyName = "kycKey")
    private String house;
    @SensitiveField(keyName = "kycKey")
    private String locality;
    @SensitiveField(keyName = "kycKey")
    private String landmark;
    @SensitiveField(keyName = "kycKey")
    private String street;
    @SensitiveField(keyName = "kycKey")
    private String vtc;
    @SensitiveField(keyName = "kycKey")
    private String district;
    @SensitiveField(keyName = "kycKey")
    private String subdistrict;
    @SensitiveField(keyName = "kycKey")
    private String state;
    @SensitiveField(keyName = "kycKey")
    private String vtccode;
    @SensitiveField(keyName = "kycKey")
    private String pincode;
    @SensitiveField(keyName = "kycKey")
    private String country;
    @SensitiveField(keyName = "kycKey")
    private String postOffice;

    public String getAadhaarReferenceNumber() {
        return aadhaarReferenceNumber;
    }

    public void setAadhaarReferenceNumber(String aadhaarReferenceNumber) {
        this.aadhaarReferenceNumber = aadhaarReferenceNumber;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDob() {
        return dob;
    }

    public void setDob(String dob) {
        this.dob = dob;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getCareOf() {
        return careOf;
    }

    public void setCareOf(String careOf) {
        this.careOf = careOf;
    }

    public String getHouse() {
        return house;
    }

    public void setHouse(String house) {
        this.house = house;
    }

    public String getLocality() {
        return locality;
    }

    public void setLocality(String locality) {
        this.locality = locality;
    }

    public String getLandmark() {
        return landmark;
    }

    public void setLandmark(String landmark) {
        this.landmark = landmark;
    }

    public String getStreet() {
        return street;
    }

    public void setStreet(String street) {
        this.street = street;
    }

    public String getVtc() {
        return vtc;
    }

    public void setVtc(String vtc) {
        this.vtc = vtc;
    }

    public String getDistrict() {
        return district;
    }

    public void setDistrict(String district) {
        this.district = district;
    }

    public String getSubdistrict() {
        return subdistrict;
    }

    public void setSubdistrict(String subdistrict) {
        this.subdistrict = subdistrict;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getVtccode() {
        return vtccode;
    }

    public void setVtccode(String vtccode) {
        this.vtccode = vtccode;
    }

    public String getPincode() {
        return pincode;
    }

    public void setPincode(String pincode) {
        this.pincode = pincode;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getPostOffice() {
        return postOffice;
    }

    public void setPostOffice(String postOffice) {
        this.postOffice = postOffice;
    }

    public String getAge() {
        return age;
    }

    public void setAge(String age) {
        this.age = age;
    }
}
