package com.flipkart.fintech.pandora.api.model.response.ekyc;

import com.flipkart.sensitive.annotation.SensitiveField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class KycDocumentData {
    private String documentType;
    private String documentId;
    private String name;
    private String dob;
    @SensitiveField(keyName = "kycKey")
    private String email;
    @SensitiveField(keyName = "kycKey")
    private String phone;
    private String gender;
    @SensitiveField(keyName = "kycKey")
    private String careOf;
    @SensitiveField(keyName = "kycKey")
    private String house;
    @SensitiveField(keyName = "kycKey")
    private String locality;
    @SensitiveField(keyName = "kycKey")
    private String landmark;
    @SensitiveField(keyName = "kycKey")
    private String street;
    @SensitiveField(keyName = "kycKey")
    private String vtc;
    @SensitiveField(keyName = "kycKey")
    private String district;
    @SensitiveField(keyName = "kycKey")
    private String subdistrict;
    @SensitiveField(keyName = "kycKey")
    private String state;
    @SensitiveField(keyName = "kycKey")
    private String vtccode;
    @SensitiveField(keyName = "kycKey")
    private String pincode;
    @SensitiveField(keyName = "kycKey")
    private String country;
    @SensitiveField(keyName = "kycKey")
    private String postOffice;
}
