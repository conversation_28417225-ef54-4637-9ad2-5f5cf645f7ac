package com.flipkart.fintech.pandora.api.model.response.ivr;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.flipkart.fintech.pandora.api.model.request.ivr.enums.Status;

@JsonIgnoreProperties(ignoreUnknown = true)
public class UbonaResponse {

    private Status status;

    public Status getStatus() {
        return status;
    }

    public void setStatus(Status status) {
        this.status = status;
    }
}
