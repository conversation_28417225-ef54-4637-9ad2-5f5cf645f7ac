package com.flipkart.fintech.pandora.api.model.response.onboarding;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pandora.api.model.common.STATUS;

/**
 * Created by aniruddha.sharma on 07/12/17.
 */
@JsonInclude(JsonInclude.Include.NON_DEFAULT)
public class AccountActivationResponse {
    @JsonProperty("STATUS")
    private STATUS status;

    private String info;

    public STATUS getStatus() {
        return status;
    }

    public void setStatus(STATUS status) {
        this.status = status;
    }

    public String getInfo() {
        return info;
    }

    public void setInfo(String info) {
        this.info = info;
    }
}
