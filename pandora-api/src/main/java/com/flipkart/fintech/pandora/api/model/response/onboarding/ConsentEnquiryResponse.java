package com.flipkart.fintech.pandora.api.model.response.onboarding;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.flipkart.fintech.pandora.api.model.response.BaseResponse;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>.r on 19/07/18.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class ConsentEnquiryResponse extends BaseResponse {

    private String consentType;

    private boolean isConsentGivenFlag;

    private String consentUpdateDate;

    private String cardId;

    public String getConsentType() {
        return consentType;
    }

    public void setConsentType(String consentType) {
        this.consentType = consentType;
    }

    public boolean isConsentGivenFlag() {
        return isConsentGivenFlag;
    }

    public void setConsentGivenFlag(boolean isConsentGivenFlag) {
        this.isConsentGivenFlag = isConsentGivenFlag;
    }

    public String getConsentUpdateDate() {
        return consentUpdateDate;
    }

    public void setConsentUpdateDate(String consentUpdateDate) {
        this.consentUpdateDate = consentUpdateDate;
    }

    public String getCardId() {
        return cardId;
    }

    public void setCardId(String cardId) {
        this.cardId = cardId;
    }
}
