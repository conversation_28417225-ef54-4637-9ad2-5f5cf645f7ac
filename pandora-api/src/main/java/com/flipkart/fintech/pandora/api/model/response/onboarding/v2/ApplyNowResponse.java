/*
 * Created by Harsh
 */

package com.flipkart.fintech.pandora.api.model.response.onboarding.v2;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pandora.api.model.LenderInfoSection;
import com.flipkart.fintech.pandora.api.model.response.BaseResponse;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ApplyNowResponse {

    @JsonProperty
    private LenderInfoSection lenderInfoSection;

    @JsonProperty
    private BaseResponse baseResponse;

    public LenderInfoSection getLenderInfoSection() {
        return lenderInfoSection;
    }

    public void setLenderInfoSection(LenderInfoSection lenderInfoSection) {
        this.lenderInfoSection = lenderInfoSection;
    }

    public BaseResponse getBaseResponse() {
        return baseResponse;
    }

    public void setBaseResponse(BaseResponse baseResponse) {
        this.baseResponse = baseResponse;
    }

    @Override
    public String toString() {
        return "ApplyNowResponse{" +
                "lenderInfoSection=" + lenderInfoSection +
                ", baseResponse=" + baseResponse +
                '}';
    }
}
