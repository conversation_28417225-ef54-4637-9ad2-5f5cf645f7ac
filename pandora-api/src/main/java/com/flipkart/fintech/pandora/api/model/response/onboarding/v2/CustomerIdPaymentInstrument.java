package com.flipkart.fintech.pandora.api.model.response.onboarding.v2;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class CustomerIdPaymentInstrument extends PaymentInstrument {
    @JsonProperty("customer_id")
    private String customerId;

    public CustomerIdPaymentInstrument() {
        super(PaymentInstrumentType.CUSTOMER_ID);
    }

    public String getCustomerId() {
        return this.customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String toString() {
        return "CustomerIdPaymentInstrument{customerId='" + this.customerId + '\'' + '}';
    }
}
