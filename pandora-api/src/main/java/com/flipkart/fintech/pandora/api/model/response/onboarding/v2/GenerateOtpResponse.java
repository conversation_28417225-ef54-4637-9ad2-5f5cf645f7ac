package com.flipkart.fintech.pandora.api.model.response.onboarding.v2;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pandora.api.model.LenderInfoSection;
import com.flipkart.fintech.pandora.api.model.request.onboarding.OtpDetails;
import com.flipkart.fintech.pandora.api.model.response.BaseResponse;


/**
 * <AUTHOR>
 * @since 02/05/19.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class GenerateOtpResponse {
    @JsonProperty
    private BaseResponse baseResponse;

    @JsonProperty("otpDetails")
    private OtpDetails otpDetails;

    @JsonProperty
    private LenderInfoSection lenderInfoSection;

    public BaseResponse getBaseResponse() {
        return baseResponse;
    }

    public void setBaseResponse(BaseResponse baseResponse) {
        this.baseResponse = baseResponse;
    }

    public OtpDetails getOtpDetails() {
        return otpDetails;
    }

    public void setOtpDetails(OtpDetails otpDetails) {
        this.otpDetails = otpDetails;
    }

    public LenderInfoSection getLenderInfoSection() {
        return lenderInfoSection;
    }

    public void setLenderInfoSection(LenderInfoSection lenderInfoSection) {
        this.lenderInfoSection = lenderInfoSection;
    }

    @Override
    public String toString() {
        return "GenerateOtpResponse{" +
                "baseResponse=" + baseResponse +
                ", otpDetails=" + otpDetails +
                ", lenderInfoSection=" + lenderInfoSection +
                '}';
    }
}
