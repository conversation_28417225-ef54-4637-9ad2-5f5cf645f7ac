
package com.flipkart.fintech.pandora.api.model.response.onboarding.v2;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pandora.api.model.response.BaseResponse;

import lombok.Data;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class HandshakeVideoKycDetailsResponse {

	@JsonProperty
	private String status;

	@JsonProperty
	private BaseResponse baseResponse;
	
	@JsonProperty
	private String dropOffURL;
	
	@JsonProperty
	private String redirectionURLToPartner;

	
}
