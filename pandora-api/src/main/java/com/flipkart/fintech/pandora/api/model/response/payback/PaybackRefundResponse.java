package com.flipkart.fintech.pandora.api.model.response.payback;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.flipkart.fintech.pandora.api.model.common.STATUS;

/**
 * Created by aniruddha.sharma on 02/01/18.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PaybackRefundResponse {
    @Override
    public String toString() {
        return "PaybackRefundResponse{" +
                "refundIdentifier='" + refundIdentifier + '\'' +
                ", status=" + status +
                '}';
    }

    private String refundIdentifier;
    private STATUS status;

    public STATUS getStatus() {
        return status;
    }

    public void setStatus(STATUS status) {
        this.status = status;
    }

    public String getRefundIdentifier() {
        return refundIdentifier;
    }

    public void setRefundIdentifier(String refundIdentifier) {
        this.refundIdentifier = refundIdentifier;
    }
}
