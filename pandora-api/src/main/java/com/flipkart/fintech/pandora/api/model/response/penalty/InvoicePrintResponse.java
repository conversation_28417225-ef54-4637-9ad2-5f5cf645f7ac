package com.flipkart.fintech.pandora.api.model.response.penalty;

import com.flipkart.fintech.pandora.api.model.common.STATUS;

/**
 * <AUTHOR>
 * @since 24/05/18.
 */
public class InvoicePrintResponse {
    private STATUS status;

    public STATUS getStatus() {
        return status;
    }

    public void setStatus(STATUS status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return "InvoicePrintResponse{" +
                "status='" + status + '\'' +
                '}';
    }
}
