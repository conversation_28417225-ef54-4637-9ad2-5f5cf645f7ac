package com.flipkart.fintech.pandora.api.model.response.pennyDrop;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.flipkart.fintech.pandora.api.model.common.ErrorInfo;
import com.flipkart.fintech.pandora.api.model.common.STATUS;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class PennyDropResponse {

    private STATUS status;
    private String transactionId;
    private String bankAccountNumber;
    private String customerName;
    private ErrorInfo errorInfo;

}
