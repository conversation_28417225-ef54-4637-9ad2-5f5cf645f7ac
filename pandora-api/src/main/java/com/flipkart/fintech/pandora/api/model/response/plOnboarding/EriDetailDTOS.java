package com.flipkart.fintech.pandora.api.model.response.plOnboarding;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * Created by pritam.raj on 28/03/23.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)

public class EriDetailDTOS {

    private String dueDate;

    private BigDecimal eriAmount;

    private BigDecimal principalContribution;

    private BigDecimal interestContribution;

    private int openingPrincipal;

    private BigDecimal closingPrincipal;
}
