package com.flipkart.fintech.pandora.api.model.response.plOnboarding;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * Created by pritam.raj on 21/03/23.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class IdentifyCustomerResponse implements PlOnboardingResponse {

    @NotNull
    private Status status;

    private String errorMessage;

    private CustomerSegment customerSegment;

    private String applicationId;

    private String lenderApplicationId;

    private long applicationValidTill;

    public PlOnboardingResponseType checkPlOnboardingResponseType() {
        return PlOnboardingResponseType.IdentifyCustomerResponse;
    }
}
