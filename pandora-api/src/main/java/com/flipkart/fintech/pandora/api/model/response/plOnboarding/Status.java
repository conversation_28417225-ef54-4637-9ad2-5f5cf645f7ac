package com.flipkart.fintech.pandora.api.model.response.plOnboarding;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * Created by pritam.raj on 21/03/23.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public enum Status {
    APPLICATION_COMPLETED,
    LOAN_DISBURSAL_SUCCESS,
    SUCCESS,
    REJECTED,
    RETRY_WITHOUT_EDIT,
    RETRY_WITH_EDIT,
    INPROGRESS,
    FAILED;
}
