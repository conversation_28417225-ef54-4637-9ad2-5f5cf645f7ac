package com.flipkart.fintech.pandora.api.model.response.plOnboarding;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.flipkart.fintech.pandora.api.model.EmploymentDetails;
import com.flipkart.fintech.pandora.api.model.common.EmploymentStatus;
import com.flipkart.fintech.pandora.api.model.request.plOnboarding.SubmitLoanOfferRequest;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by pritam.raj on 03/04/23.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class StatusResponse implements PlOnboardingResponse {

    private String redirectionUri;

    private Status status;

    private String errorMessage;

    private String subStatus;

    private EligibleLoanOfferResponse offerGeneratedAtLender;

    private long validity;

    private SubmitLoanOfferRequest offerSubmittedToLender;

    private LoanDisbursed loanDisbursedParams;

    private EmploymentVerification employmentVerification;

    private Object rawResponse;

    public PlOnboardingResponseType checkPlOnboardingResponseType() {
        return PlOnboardingResponseType.StatusResponse;
    }
}
