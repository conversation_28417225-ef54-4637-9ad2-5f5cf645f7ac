package com.flipkart.fintech.pandora.api.model.response.transaction;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pandora.api.model.common.STATUS;

/**
 * Created by aniruddha.sharma on 13/11/17.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_DEFAULT)
public class CreditLimitResponse {
    private double creditLimit;

    private double creditAvailable;

    private String info;

    @JsonProperty("STATUS")
    private STATUS status;

    public double getCreditLimit() {
        return creditLimit;
    }

    public void setCreditLimit(double creditLimit) {
        this.creditLimit = creditLimit;
    }

    public double getCreditAvailable() {
        return creditAvailable;
    }

    public void setCreditAvailable(double creditAvailable) {
        this.creditAvailable = creditAvailable;
    }

    public String getInfo() {
        return info;
    }

    public void setInfo(String info) {
        this.info = info;
    }

    public STATUS getStatus() {
        return status;
    }

    public void setStatus(STATUS status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return "CreditLimitResponse{" +
                "creditLimit=" + creditLimit +
                ", creditAvailable=" + creditAvailable +
                ", info='" + info + '\'' +
                ", status=" + status +
                '}';
    }
}
