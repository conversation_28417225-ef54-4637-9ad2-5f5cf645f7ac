package com.flipkart.fintech.pandora.api.model.response.transaction;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pandora.api.model.common.STATUS;

/**
 * Created by aniruddha.sharma on 02/11/17.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class TransactionFinishResponse {
    @JsonProperty("STATUS")
    private STATUS status;

    private String info;

    public STATUS getStatus() {
        return status;
    }

    public void setStatus(STATUS status) {
        this.status = status;
    }

    public String getInfo() {
        return info;
    }

    public void setInfo(String info) {
        this.info = info;
    }
}
