package com.flipkart.fintech.pandora.constants;

public class ApplicationEnums {

    public enum ApplicationType { UNSECURED }

    public enum ApplicationSubType { FRESH }

    public enum DisbursementType { SINGLE }

    public enum CustomerRole { BORROWER }

    public enum CustomerType { INDIVIDUAL }

    public enum DOC_TYPE { LOAN_APPLICATION_FORM, KEY_FACT_STATEMENT }

    public enum KYC { KYC_INITIATION, KYC_CONFIRMED }

    public enum PurposeType {
        PERSONAL_USE("Personal Use");

        private final String label;

        PurposeType(String label) {
            this.label = label;
        }

        public String getLabel() {
            return label;
        }
    }


    public enum EmploymentCategory {
        SALARIED("Salaried"),
        SELF_EMPLOYED("SelfEmployed");

        private final String value;

        EmploymentCategory(String value) {
            this.value = value;
        }

    }

    public enum IdentityType {
        PAN, AADHAR, PASSPORT, DL, VOTER_ID,
        MNREGA, DIN, PPO, FORM_60, OTHERS
    }

    public enum AddressType {
        RESIDENTIAL_CURRENT, RESIDENTIAL_PERMANENT, OFFICE, CORRESPONDENCE, OTHERS
    }

    public enum ConsentPurpose { BUREAU_PULL, PRIVACY }

    public enum CountryCode {
        INDIA(91);
        private int code;
        CountryCode(int code) {
            this.code = code;
        }
        public int getCode() {
            return this.code;
        }
    }

}
