package com.flipkart.fintech.pandora.idfc.client.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;


@JsonInclude(JsonInclude.Include.NON_NULL)
@Getter
@Setter
public class AutoDisbursalRequest implements IdfcRequest {

    @JsonProperty("entityCode")
    private String entityCode;

    @JsonProperty("reqType")
    private String reqType;

    @JsonProperty("preAuthId")
    private String preAuthId;

    @JsonProperty("consent")
    private Consent consent;

    @JsonProperty("entityReqId")
    private String entityReqId;

    @JsonProperty("loanId")
    private String loanId;

}
