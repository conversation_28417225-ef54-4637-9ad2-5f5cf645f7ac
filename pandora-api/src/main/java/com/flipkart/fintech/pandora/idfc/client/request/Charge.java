
package com.flipkart.fintech.pandora.idfc.client.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Getter
@Setter
public class Charge {

    @JsonProperty("chargeId")
    private String chargeId;
    
    @JsonProperty("chargeAmount")
    private String chargeAmount;
    
    @JsonProperty("fundedFlag")
    private String fundedFlag;
    
    @JsonProperty("chargeRate")
    private String chargeRate;

}
