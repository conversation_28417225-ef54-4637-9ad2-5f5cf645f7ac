
package com.flipkart.fintech.pandora.idfc.client.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.Data;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
    "eMandateConsent",
    "eMandateConsentTimestamp",
    "tncConsent",
    "tncConsentTimestamp"
})
@Data
public class Consent {

    /**
     * 
     * (Required)
     * 
     */
    @JsonProperty("eMandateConsent")
    private String eMandateConsent;
    /**
     * 
     * (Required)
     * 
     */
    @JsonProperty("eMandateConsentTimestamp")
    private String eMandateConsentTimestamp;
    /**
     * 
     * (Required)
     * 
     */
    @JsonProperty("tncConsent")
    private String tncConsent;
    /**
     * 
     * (Required)
     * 
     */
    @JsonProperty("tncConsentTimestamp")
    private String tncConsentTimestamp;

}
