package com.flipkart.fintech.pandora.idfc.client.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class Detail {
    
    @JsonProperty("TransactionId")
    private String transactionId;
    
    @JsonProperty("RecordIdentifier")
    private String recordIdentifier;
    
    @JsonProperty("ApplicationFormNo")
    private String applicationFormNo;
    
    @JsonProperty("BranchCode")
    private String branchCode;
    
    @JsonProperty("CKYCNumber")
    private String cKYCNumber;
    
    @JsonProperty("DOB")
    private String dob;
    
    @JsonProperty("MobileNumber")
    private String mobileNumber;
    
    @JsonProperty("Pincode")
    private String pincode;
    
    @JsonProperty("BirthYear")
    private String birthYear;
    
    @JsonProperty("dedupe_flag")
    private String dedupeFlag;
}
