package com.flipkart.fintech.pandora.idfc.client.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Getter
@Setter
public class KfsOtpGenerationRequest implements IdfcRequest {

    @JsonProperty("callId")
    private String callId;

    @JsonProperty("linkData")
    private String linkData;

    @JsonProperty("referenceNumber")
    private String referenceNumber;

    @JsonProperty("sendSms")
    private SendSms sendSms;

    @JsonProperty("verifyMobileNumber")
    private String verifyMobileNumber;

}
