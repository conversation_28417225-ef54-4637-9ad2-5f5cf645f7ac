
package com.flipkart.fintech.pandora.idfc.client.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Getter
@Setter
public class LoanOnboardingAddress {

    
    @JsonProperty("applicantCount")
    private String applicantCount;
    
    @JsonProperty("addressType")
    private String addressType;
    
    @JsonProperty("addressLine1")
    private String addressLine1;
    
    @JsonProperty("addressLine2")
    private String addressLine2;
    @JsonProperty("addressLine3")
    private String addressLine3;
    
    @JsonProperty("country")
    private String country;
    
    @JsonProperty("state")
    private String state;
    
    @JsonProperty("city")
    private String city;
    
    @JsonProperty("pincode")
    private String pincode;
    
    @JsonProperty("location")
    private String location;
    
    @JsonProperty("propertyType")
    private String propertyType;
    
    @JsonProperty("residencePhoneStdCode")
    private String residencePhoneStdCode;
    
    @JsonProperty("residencePhoneNo")
    private String residencePhoneNo;
    
    @JsonProperty("mobileNo")
    private String mobileNo;
    
    @JsonProperty("alternateMobileNo1")
    private String alternateMobileNo1;
    
    @JsonProperty("alternateMobileNo2")
    private String alternateMobileNo2;
    
    @JsonProperty("addDurationYears")
    private String addDurationYears;
    
    @JsonProperty("addDurationMonths")
    private String addDurationMonths;
    
    @JsonProperty("gstAddress")
    private String gstAddress;
    
    @JsonProperty("mailingAddress")
    private String mailingAddress;

}
