
package com.flipkart.fintech.pandora.idfc.client.request;

import java.util.ArrayList;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.Data;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
    "notepadDetails"
})
@Data
public class Notepad {

    /**
     * 
     * (Required)
     * 
     */
    @JsonProperty("notepadDetails")
    private List<NotepadDetail> notepadDetails;

}
