package com.flipkart.fintech.pandora.idfc.client.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class OfflineUploadDataRequest implements IdfcRequest {

  @JsonProperty("reqId")
  private String requestId;

  @JsonProperty("source")
  private String source;

  @JsonProperty("loanId")
  private String loanId;

  @JsonProperty("kycMode")
  private String kycMode;

  @JsonProperty("documents")
  private List<OfflineUploadDocument> documents;

  @JsonProperty("data")
  private List<OfflineUploadData> data;

}
