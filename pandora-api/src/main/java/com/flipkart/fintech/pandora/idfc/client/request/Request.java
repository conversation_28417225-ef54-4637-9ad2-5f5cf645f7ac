
package com.flipkart.fintech.pandora.idfc.client.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Getter
@Setter
public class Request {

    @JsonProperty("sourceRequestId")
    private String sourceRequestId;
     
    @JsonProperty("sourceSystem")
    private String sourceSystem;
     
    @JsonProperty("requestType")
    private String requestType;

}
