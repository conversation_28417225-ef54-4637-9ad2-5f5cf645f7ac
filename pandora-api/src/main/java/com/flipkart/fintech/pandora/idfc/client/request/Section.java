
package com.flipkart.fintech.pandora.idfc.client.request;

import java.util.List;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Getter
@Setter
public class Section {
    
    @JsonProperty("request")
    private Request request;
    
    @JsonProperty("sourcing")
    private Sourcing sourcing;
    
    @JsonProperty("loanDetails")
    private LoanDetails loanDetails;
    
    @JsonProperty("customerDetails")
    private List<CustomerDetail> customerDetails;
    
    @JsonProperty("addresses")
    private List<LoanOnboardingAddress> addresses;
    
    @JsonProperty("instrumentDetails")
    private InstrumentDetails instrumentDetails;
    
    @JsonProperty("assetDetails")
    private List<AssetDetail> assetDetails;
    
    @JsonProperty("bank")
    private Bank bank;
    
    @JsonProperty("references")
    private List<Reference> references;
    
    @JsonProperty("notepad")
    private Notepad notepad;
    
    @JsonProperty("charges")
    private List<Charge> charges;
    
    @JsonProperty("cibilDetails")
    private List<CibilDetail> cibilDetails;
    
    @JsonProperty("customerConsents")
    private CustomerConsents customerConsents;

}
