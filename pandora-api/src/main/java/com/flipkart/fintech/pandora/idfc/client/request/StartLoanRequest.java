package com.flipkart.fintech.pandora.idfc.client.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Getter
@Setter
public class StartLoanRequest implements IdfcRequest {

    @JsonProperty("reqId")
    private String reqId;

    @JsonProperty("source")
    private String source;

    @JsonProperty("kycMode")
    private String kycMode;

    @JsonProperty("livenessCheck")
    private LivenessCheck livenessCheck;

    @JsonProperty("comparePhoto")
    private ComparePhoto comparePhoto;

    @JsonProperty("underWriting")
    private UnderWriting underWriting;

}
