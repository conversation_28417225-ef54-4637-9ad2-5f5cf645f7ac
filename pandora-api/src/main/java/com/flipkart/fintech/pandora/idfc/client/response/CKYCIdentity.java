package com.flipkart.fintech.pandora.idfc.client.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;


@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class CKYCIdentity {

    @JsonProperty("CKYCIDSequence")
    private String cKYCIDSequence;

    @JsonProperty("CKYCIDType")
    private String cKYCIDType;

    @JsonProperty("CKYCIDNumber")
    private String cKYCIDNumber;

    @JsonProperty("CKYCIDExpiryDate")
    private String cKYCIDExpiryDate;

    @JsonProperty("CKYCIDProofSubmitted")
    private String cKYCIDProofSubmitted;

    @JsonProperty("CKYCIDVerificationStatus")
    private String cKYCIDVerificationStatus;
}
