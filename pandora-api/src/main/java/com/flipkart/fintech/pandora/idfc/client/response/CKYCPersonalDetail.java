package com.flipkart.fintech.pandora.idfc.client.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class CKYCPersonalDetail {
    
    @JsonProperty("RecordIdentifier")
    private String recordIdentifier;
    
    @JsonProperty("ApplicationFormNo")
    private String applicationFormNo;
    
    @JsonProperty("BranchCode")
    private String branchCode;
    
    @JsonProperty("CKYCConstiType")
    private String cKYCConstiType;
    
    @JsonProperty("CKYCAccType")
    private String cKYCAccType;
    
    @JsonProperty("CKYCNumber")
    private String cKYCNumber;
    
    @JsonProperty("CKYCNamePrefix")
    private String cKYCNamePrefix;
    
    @JsonProperty("CKYCFirstName")
    private String cKYCFirstName;
    
    @JsonProperty("CKYCMiddleName")
    private String cKYCMiddleName;
    
    @JsonProperty("CKYCLastName")
    private String cKYCLastName;
    
    @JsonProperty("CKYCFullName")
    private String cKYCFullName;
    
    @JsonProperty("CKYCMaidenNamePrefix")
    private String cKYCMaidenNamePrefix;
    
    @JsonProperty("CKYCMaidenFirstName")
    private String cKYCMaidenFirstName;
    
    @JsonProperty("CKYCMaidenMiddleName")
    private String cKYCMaidenMiddleName;
    
    @JsonProperty("CKYCMaidenLastName")
    private String cKYCMaidenLastName;
    
    @JsonProperty("CKYCMaidenFullName")
    private String cKYCMaidenFullName;
    
    @JsonProperty("CKYCFatherNamePrefix")
    private String cKYCFatherNamePrefix;
    
    @JsonProperty("CKYCFatherFirstName")
    private String cKYCFatherFirstName;
    
    @JsonProperty("CKYCFatherMiddleName")
    private String cKYCFatherMiddleName;
    
    @JsonProperty("CKYCFatherLastName")
    private String cKYCFatherLastName;
    
    @JsonProperty("CKYCFatherFullName")
    private String cKYCFatherFullName;
    
    @JsonProperty("CKYCMotherNamePrefix")
    private String cKYCMotherNamePrefix;
    
    @JsonProperty("CKYCMotherFirstName")
    private String cKYCMotherFirstName;
    
    @JsonProperty("CKYCMotherMiddletName")
    private String cKYCMotherMiddletName;
    
    @JsonProperty("CKYCMotherLastName")
    private String cKYCMotherLastName;
    
    @JsonProperty("CKYCMotherFullName")
    private String cKYCMotherFullName;
    
    @JsonProperty("CKYCGender")
    private String cKYCGender;
    
    @JsonProperty("CKYCMaritalStatus")
    private String cKYCMaritalStatus;
    
    @JsonProperty("CKYCNationality")
    private String cKYCNationality;
    
    @JsonProperty("CKYCOccupation")
    private String cKYCOccupation;
    
    @JsonProperty("CKYCDOB")
    private String ckycdob;
    
    @JsonProperty("CKYCResidentialStatus")
    private String cKYCResidentialStatus;
    
    @JsonProperty("CKYCTaxResidencyOutsideIndia")
    private String cKYCTaxResidencyOutsideIndia;
    
    @JsonProperty("CKYCJurisdictionofRes")
    private String cKYCJurisdictionofRes;
    
    @JsonProperty("CKYCTIN")
    private String ckyctin;
    
    @JsonProperty("CKYCCountryOfBirth")
    private String cKYCCountryOfBirth;
    
    @JsonProperty("CKYCPlaceOfBirth")
    private String cKYCPlaceOfBirth;
    
    @JsonProperty("CKYCPerAddType")
    private String cKYCPerAddType;
    
    @JsonProperty("CKYCPerAdd1")
    private String cKYCPerAdd1;
    
    @JsonProperty("CKYCPerAdd2")
    private String cKYCPerAdd2;
    
    @JsonProperty("CKYCPerAdd3")
    private String cKYCPerAdd3;
    
    @JsonProperty("CKYCPerAddCity")
    private String cKYCPerAddCity;
    
    @JsonProperty("CKYCPerAddDistrict")
    private String cKYCPerAddDistrict;
    
    @JsonProperty("CKYCPerAddState")
    private String cKYCPerAddState;
    
    @JsonProperty("CKYCPerAddCountry")
    private String cKYCPerAddCountry;
    
    @JsonProperty("CKYCPerAddPin")
    private String cKYCPerAddPin;
    
    @JsonProperty("CKYCPerAddPinType")
    private String cKYCPerAddPinType;
    
    @JsonProperty("CKYCPerAddPOA")
    private String cKYCPerAddPOA;
    
    @JsonProperty("CKYCPerAddPOAOthers")
    private String cKYCPerAddPOAOthers;
    
    @JsonProperty("CKYCPerAddSameasCorAdd")
    private String cKYCPerAddSameasCorAdd;
    
    @JsonProperty("CKYCCorAdd1")
    private String cKYCCorAdd1;
    
    @JsonProperty("CKYCCorAdd2")
    private String cKYCCorAdd2;
    
    @JsonProperty("CKYCCorAdd3")
    private String cKYCCorAdd3;
    
    @JsonProperty("CKYCCorAddCity")
    private String cKYCCorAddCity;
    
    @JsonProperty("CKYCCorAddDistrict")
    private String cKYCCorAddDistrict;
    
    @JsonProperty("CKYCCorAddState")
    private String cKYCCorAddState;
    
    @JsonProperty("CKYCCorAddCountry")
    private String cKYCCorAddCountry;
    
    @JsonProperty("CKYCCorAddPin")
    private String cKYCCorAddPin;
    
    @JsonProperty("CKYCCorAddPinType")
    private String cKYCCorAddPinType;
    
    @JsonProperty("CKYCPerAddSameAsJurAdd")
    private String cKYCPerAddSameAsJurAdd;
    
    @JsonProperty("CKYCJurAdd1")
    private String cKYCJurAdd1;
    
    @JsonProperty("CKYCJurAdd2")
    private String cKYCJurAdd2;
    
    @JsonProperty("CKYCJurAdd3")
    private String cKYCJurAdd3;
    
    @JsonProperty("CKYCJurAddCity")
    private String cKYCJurAddCity;
    
    @JsonProperty("CKYCJurAddState")
    private String cKYCJurAddState;
    
    @JsonProperty("CKYCJurAddCountry")
    private String cKYCJurAddCountry;
    
    @JsonProperty("CKYCJurAddPin")
    private String cKYCJurAddPin;
    
    @JsonProperty("CKYCResTelSTD")
    private String cKYCResTelSTD;
    
    @JsonProperty("CKYCResTelNumber")
    private String cKYCResTelNumber;
    
    @JsonProperty("CKYCOffTelSTD")
    private String cKYCOffTelSTD;
    
    @JsonProperty("CKYCOffTelNumber")
    private String cKYCOffTelNumber;
    
    @JsonProperty("CKYCMobileISD")
    private String cKYCMobileISD;
    
    @JsonProperty("CKYCMobileNumber")
    private String cKYCMobileNumber;
    
    @JsonProperty("CKYCFAXSTD")
    private String ckycfaxstd;
    
    @JsonProperty("CKYCFaxNumber")
    private String cKYCFaxNumber;
    
    @JsonProperty("CKYCEmailAdd")
    private String cKYCEmailAdd;
    
    @JsonProperty("CKYCRemarks")
    private String cKYCRemarks;
    
    @JsonProperty("CKYCDateofDeclaration")
    private String cKYCDateofDeclaration;
    
    @JsonProperty("CKYCPlaceofDeclaration")
    private String cKYCPlaceofDeclaration;
    
    @JsonProperty("CKYCKYCVerificationDate")
    private String cKYCKYCVerificationDate;
    
    @JsonProperty("CKYCTypeofDocSubmitted")
    private String cKYCTypeofDocSubmitted;
    
    @JsonProperty("CKYCKYCVerificationName")
    private String cKYCKYCVerificationName;
    
    @JsonProperty("CKYCKYCVerificationDesg")
    private String cKYCKYCVerificationDesg;
    
    @JsonProperty("CKYCKYCVerificationBranch")
    private String cKYCKYCVerificationBranch;
    
    @JsonProperty("CKYCKYCVerificationEmpcode")
    private String cKYCKYCVerificationEmpcode;
    
    @JsonProperty("CKYCNumberofIds")
    private String cKYCNumberofIds;
    
    @JsonProperty("CKYCNumberofRelatedPersons")
    private String cKYCNumberofRelatedPersons;
    
    @JsonProperty("CKYCNumberofLocalAdds")
    private String cKYCNumberofLocalAdds;
    
    @JsonProperty("CKYCNumberofImages")
    private String cKYCNumberofImages;
    
    @JsonProperty("CKYCNameUpdated")
    private String cKYCNameUpdated;
    
    @JsonProperty("CKYCPersonalorEntityDetailsUpdated")
    private String cKYCPersonalorEntityDetailsUpdated;
    
    @JsonProperty("CKYCAddressDetailsUpdated")
    private String cKYCAddressDetailsUpdated;
    
    @JsonProperty("CKYCContactDetailsUpdated")
    private String cKYCContactDetailsUpdated;
    
    @JsonProperty("CKYCRemarksUpdated")
    private String cKYCRemarksUpdated;
    
    @JsonProperty("CKYCKYCVerificationUpdated")
    private String cKYCKYCVerificationUpdated;
    
    @JsonProperty("CKYCIDDetailsUpdated")
    private String cKYCIDDetailsUpdated;
    
    @JsonProperty("CKYCRelatedPersonsUpdated")
    private String cKYCRelatedPersonsUpdated;
    
    @JsonProperty("CKYCImageDetailsUpdated")
    private String cKYCImageDetailsUpdated;
}
