package com.flipkart.fintech.pandora.idfc.client.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class CkycDownloadResponse {

    @JsonProperty("requestId")
    private String requestId;

    @JsonProperty("accountId")
    private String accountId;

    @JsonProperty("smUserId")
    private String smUserId;

    @JsonProperty("DownloadCkycResponse")
    private DownloadCkycResponse downloadCkycResponse;
}
