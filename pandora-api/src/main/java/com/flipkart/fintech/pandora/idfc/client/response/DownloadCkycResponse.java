package com.flipkart.fintech.pandora.idfc.client.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
@ToString
public class DownloadCkycResponse {

    @JsonProperty("ApiToken")
    private String apiToken;

    @JsonProperty("RequestId")
    private String requestId;

    @JsonProperty("RequestStatus")
    private String requestStatus;

    @JsonProperty("Details")
    private Details details;

}
