
package com.flipkart.fintech.pandora.idfc.client.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Getter
@Setter
public class KfsOtpGenerationResourceDatum {

    @JsonProperty("referenceNumber")
    private String referenceNumber;

    @JsonProperty("linkData")
    private String linkData;

    @JsonProperty("mobileOtp")
    private MobileOtp mobileOtp;

}
