package com.flipkart.fintech.pandora.idfc.client.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;


@JsonInclude(JsonInclude.Include.NON_NULL)
@Getter
@Setter
public class LoanOnboardingResponse {

    @JsonProperty("sourceRequestId")
    private String sourceRequestId;
    
    @JsonProperty("applicationID")
    private String applicationID;
    
    @JsonProperty("status")
    private String status;
    
    @JsonProperty("message")
    private String message;
    
    @JsonProperty("code")
    private String code;
    
    @JsonProperty("response")
    private Response response;

}
