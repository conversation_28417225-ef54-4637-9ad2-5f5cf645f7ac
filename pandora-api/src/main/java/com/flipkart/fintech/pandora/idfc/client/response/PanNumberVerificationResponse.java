package com.flipkart.fintech.pandora.idfc.client.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class PanNumberVerificationResponse {
    @JsonProperty("reqId")
    private String reqId;

    @JsonProperty("panVerificationResponse")
    private PanVerificationResponse panVerificationResponse;
}

