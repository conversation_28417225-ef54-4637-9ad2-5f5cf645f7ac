package com.flipkart.fintech.pandora.idfc.client.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@ToString
public class PositiveConfirmationStatusResponse {
  @JsonProperty("reqId")
  private String reqId;

  @JsonProperty("source")
  private String source;

  @JsonProperty("PositiveConfirmation")
  private String positiveConfirmation;
}
