
package com.flipkart.fintech.pandora.idfc.client.response;

import java.util.List;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Getter
@Setter
public class ResourceDatum {

    @JsonProperty("entityReqId")
    private String entityReqId;
    
    @JsonProperty("loanId")
    private String loanId;
    
    @JsonProperty("kfsDetails")
    private List<KfsDetail> kfsDetails;
    
    @JsonProperty("charges")
    private List<AutoDisbursalCharge> charges;
    
    @JsonProperty("repaySchedule")
    private List<RepaySchedule> repaySchedule;

}
