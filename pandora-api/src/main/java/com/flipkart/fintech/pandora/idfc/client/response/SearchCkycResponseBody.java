package com.flipkart.fintech.pandora.idfc.client.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 16/04/20.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SearchCkycResponseBody {
    @JsonProperty("RequestId")
    private String requestId;
    @JsonProperty("RequestStatus")
    private String requestStatus;
    @JsonProperty("RequestRejectionDescription")
    private String requestRejectionDescription;
    @JsonProperty("RequestRejectionCode")
    private String requestRejectionCode;
    @JsonProperty("Detail")
    private List<SearchCkycResponseDetail> detailList;
}
