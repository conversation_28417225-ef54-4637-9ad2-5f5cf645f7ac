package com.flipkart.fintech.pandora.idfc.client.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 16/04/20.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SearchCkycResponseDetail {
    @JsonProperty("TransactionId")
    private String transactionId;
    @JsonProperty("CKYCID")
    private String ckycId;
    @JsonProperty("TransactionStatus")
    private String transactionStatus;
    @JsonProperty("TransactionRejectionDescription")
    private String transactionRejectionDescription;
    @JsonProperty("TransactionRejectionCode")
    private String transactionRejectionCode;
    @JsonProperty("BranchCode")
    private String branchCode;
    @JsonProperty("RecordIdentifier")
    private String recordIdentifier;
    @JsonProperty("ApplicationFormNo")
    private String applicationFormNo;
    @JsonProperty("CKYCAvailable")
    private String ckycAvailable;
    @JsonProperty("CKYCAccType")
    private String ckycAccType;
    @JsonProperty("CKYCName")
    private String ckycName;
    @JsonProperty("CKYCAge")
    private Integer ckycAge;
    @JsonProperty("CKYCFatherName")
    private String ckycFatherName;
    @JsonProperty("CKYCPhotoImageType")
    private String ckycPhotoImageType;
    @JsonProperty("CKYCKYCDate")
    private String ckycDate;
    @JsonProperty("CKYCGenDate")
    private String ckycGenDate;
    @JsonProperty("CKYCPhoto")
    private String ckycPhoto;
    @JsonProperty("CKYCRequestId")
    private String ckycRequestId;
    @JsonProperty("CKYCRequestDate")
    private String ckycRequestDate;
    @JsonProperty("CKYCUpdatedDate")
    private String ckycUpdatedDate;
    @JsonProperty("CKYCRemarks")
    private String ckycRemarks;
    @JsonProperty("CKYCIDDetails")
    private List<SearchCkycIdDetail> ckycIdDetails;
    @JsonProperty("CKYCPhotoBytes")
    private String ckycPhotoBytes;
}
