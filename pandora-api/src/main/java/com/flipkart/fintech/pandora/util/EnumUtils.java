package com.flipkart.fintech.pandora.util;

import com.flipkart.fintech.pandora.api.model.cbc.enums.IEnum;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

@Slf4j
public class EnumUtils {
    public static <E extends Enum<E> & IEnum<E>> E getEnumFromId(Class<E> enumClass, String id) {
        for (E e : enumClass.getEnumConstants()) {
            if (e.getId().equals(id)) {
                return e;
            }
        }
        throw new IllegalArgumentException(String.format("No matching %s found for id: %d", enumClass.getName(), id));
    }

    public static <E extends Enum<E> & IEnum<E>> E getEnumFromValue(Class<E> enumClass, String value) {
        for (E e : enumClass.getEnumConstants()) {
            if (e.getValue().equals(value)) {
                return e;
            }
        }
        throw new IllegalArgumentException(String.format("No matching %s found for value: %s", enumClass.getName(), value));
    }

    /*public static <T extends Enum<T> & IEnum<T>> Option<T> getEnumOptionFromValue(Class<T> enumClass, String strValue) {
        if (StringUtils.isEmpty(strValue)) {
            return Option.empty();
        }
        for (T t : enumClass.getEnumConstants()) {
            if (t.getValue().equals(strValue)) {
                return Option.of(t);
            }
        }
        log.error("No matching {} found for string: {}!", enumClass.getName(), strValue);
        return Option.empty();
    }*/

    public static <S extends Enum<S> & IEnum<S>, T extends Enum<T>> T transformEnum(S sourceValue, Map<S, T> enumMap) {
        if (enumMap.containsKey(sourceValue)) {
            return enumMap.get(sourceValue);
        }
        throw new IllegalArgumentException("Unrecognized enum value " + sourceValue);
    }
}
