package com.flipkart.fintech.pandora.util;

import javax.ws.rs.core.Response;
import java.util.Objects;

public class HttpStatusUtil {

    public static boolean is2xxSuccessful(Response response) {

        return (Objects.nonNull(response) && Response.Status.Family.familyOf(response.getStatus()) == Response.Status.Family.SUCCESSFUL);
    }

    public static boolean is5xxServerError(Response response) {

        return (Objects.nonNull(response) && Response.Status.Family.familyOf(response.getStatus()) == Response.Status.Family.SERVER_ERROR);
    }
}
