package com.flipkart.fintech.pandora.yubi.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import metus.client.shade.javax.validation.Valid;

import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.util.List;

@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ConfirmCommercialRequest {

    @NotBlank
    @JsonProperty("clientApplicationId")
    private String clientApplicationId;

    @NotBlank
    @JsonProperty("partnershipId")
    private String partnershipId;

    @Valid
    @NotNull
    @JsonProperty("applicationTerms")
    private ApplicationTerms applicationTerms;

    @Getter
    @Setter
    public static class ApplicationTerms {

        @NotNull
        @DecimalMin(value = "0.0", inclusive = false)
        @JsonProperty("emiAmount")
        private BigDecimal emiAmount;

        @NotNull
        @DecimalMin(value = "0.0", inclusive = false)
        @JsonProperty("principalAmount")
        private BigDecimal principalAmount;

        @NotNull
        @DecimalMin(value = "0.0", inclusive = false)
        @JsonProperty("interestRate")
        private BigDecimal interestRate;

        @Valid
        @NotNull
        @JsonProperty("tenure")
        private Tenure tenure;

        @NotNull
        @Min(1)
        @Max(31)
        @JsonProperty("cycleDate")
        private Integer cycleDate;

        @Valid
        @JsonProperty("disbursementDeductions")
        private List<DisbursementDeduction> disbursementDeductions;
    }

    @Getter
    @Setter
    public static class Tenure {

        @Min(0)
        @JsonProperty("days")
        private Integer days;

        @Min(0)
        @JsonProperty("months")
        private Integer months;

        @Min(0)
        @JsonProperty("years")
        private Integer years;
    }

    @Getter
    @Setter
    public static class DisbursementDeduction {

        @NotBlank
        @JsonProperty("deductionType")
        private String deductionType;

        @NotBlank
        @JsonProperty("deductionFormat")
        private String deductionFormat;

        @NotNull
        @DecimalMin(value = "0.0", inclusive = false)
        @JsonProperty("deductionValue")
        private BigDecimal deductionValue;
    }
}
