package com.flipkart.fintech.pandora.yubi.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pandora.constants.ApplicationEnums;
import lombok.Data;
import javax.validation.Valid;

import javax.validation.constraints.*;
import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CreateApplicationRequest {

    @NotBlank
    @JsonProperty("clientApplicationId")
    private String clientApplicationId;

    @JsonProperty("clientLeadId")
    private String clientLeadId;

    @NotEmpty
    @Size(min = 1, max = 1, message = "Only one partnershipId allowed")
    @JsonProperty("partnershipId")
    private List<String> partnershipId;

    @NotNull
    @JsonProperty("applicationType")
    private ApplicationEnums.ApplicationType applicationType;

    @NotNull
    @JsonProperty("applicationSubType")
    private ApplicationEnums.ApplicationSubType applicationSubType;

    @NotNull
    @JsonProperty("disbursementType")
    private ApplicationEnums.DisbursementType disbursementType;

    @NotBlank
    @JsonProperty("purpose")
    private String purpose;

    @NotEmpty
    @Valid
    @JsonProperty("customerDetails")
    private List<CustomerDetails> customerDetails;

    @NotEmpty
    @Valid
    @JsonProperty("applicationExt")
    private List<ApplicationExt> applicationExt;

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class CustomerDetails {

        @NotNull
        @JsonProperty("customerRole")
        private ApplicationEnums.CustomerRole customerRole;

        @NotNull
        @JsonProperty("customerType")
        private ApplicationEnums.CustomerType customerType;

        @NotBlank
        @JsonProperty("clientCustomerId")
        private String clientCustomerId;

        @Valid
        @NotNull
        @JsonProperty("individualCustomerData")
        private IndividualCustomerData individualCustomerData;

        @NotEmpty
        @Valid
        @JsonProperty("identityDetails")
        private List<IdentityDetails> identityDetails;

        @NotEmpty
        @Valid
        @JsonProperty("addressDetails")
        private List<AddressDetails> addressDetails;

        @Valid
        @NotNull
        @JsonProperty("consentDetails")
        private ConsentDetails consentDetails;
    }

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class IndividualCustomerData {

        @NotBlank
        @JsonProperty("firstName")
        private String firstName;

        @JsonProperty("middleName")
        private String middleName;

        @NotBlank
        @JsonProperty("lastName")
        private String lastName;

        @NotNull
        @Past(message = "Date of birth must be in the past")
        private String dateOfBirth;

        @NotNull
        @JsonProperty("gender")
        private String gender;

        @NotNull
        @JsonProperty("mobile")
        private Long mobile;

        @JsonProperty("mobileCode")
        private Integer mobileCode;

        @JsonProperty("employmentCategory")
        private ApplicationEnums.EmploymentCategory employmentCategory;

        @NotNull
        @Email(message = "Invalid email format")
        @JsonProperty("email")
        private String email;
    }

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class IdentityDetails {

        @NotNull
        @JsonProperty("identityType")
        private ApplicationEnums.IdentityType identityType;

        @NotBlank
        @JsonProperty("identityValue")
        private String identityValue;
    }

    @Data
    public static class AddressDetails {

        @NotNull
        @JsonProperty("addressType")
        private ApplicationEnums.AddressType addressType;

        @NotBlank(message = "HBA required for RESIDENTIAL_CURRENT")
        @Size(min = 3)
        @JsonProperty("addressHBA")
        private String addressHBA;

        @JsonProperty("addressSRL")
        private String addressSRL;

        @JsonProperty("addressALS")
        private String addressALS;

        @JsonProperty("addressDistrict")
        private String addressDistrict;

        @JsonProperty("addressVTC")
        private String addressVTC;

        @JsonProperty("addressLandmark")
        private String addressLandmark;

        @NotNull
        @Digits(integer = 6, fraction = 0)
        @JsonProperty("addressPincode")
        private Integer addressPincode;

        @JsonProperty("addressState")
        private String addressState;

        @Pattern(regexp = "^\\d*$", message = "Country must not contain numbers")
        @JsonProperty("addressCountry")
        private String addressCountry;

        @JsonProperty("addressLatitude")
        private String addressLatitude;

        @JsonProperty("addressLongitude")
        private String addressLongitude;
    }

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class ConsentDetails {

        @NotEmpty
        @JsonProperty("consentPurpose")
        private List<ApplicationEnums.ConsentPurpose> consentPurpose;

        @Pattern(
                regexp = "^(((\\d{1,3}\\.){3}\\d{1,3})|(([0-9a-fA-F]{1,4}:){1,7}[0-9a-fA-F]{1,4}))$",
                message = "Invalid IP address format"
        )
        @JsonProperty("ipAddress")
        private String ipAddress;

        @NotBlank
        @JsonProperty("timestamp")
        private String timestamp;
    }

    @Data
    public static class ApplicationExt {

        @NotBlank
        @JsonProperty("key")
        private String key;

        @NotBlank
        @JsonProperty("value")
        private String value;
    }
}
