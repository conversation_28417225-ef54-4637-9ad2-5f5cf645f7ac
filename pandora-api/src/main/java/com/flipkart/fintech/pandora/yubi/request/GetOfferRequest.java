package com.flipkart.fintech.pandora.yubi.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetOfferRequest {

    @JsonProperty("clientCustomerId")
    private String clientCustomerId;

    @JsonProperty("clientApplicationId")
    private String clientApplicationId;

    @JsonProperty("partnershipId")
    private String partnershipId;
}
