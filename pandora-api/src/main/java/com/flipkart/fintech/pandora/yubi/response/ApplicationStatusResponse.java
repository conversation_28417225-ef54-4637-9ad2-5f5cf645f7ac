package com.flipkart.fintech.pandora.yubi.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.flipkart.fintech.pandora.api.model.pl.response.LoanResponse;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class ApplicationStatusResponse extends LoanResponse {

    @JsonProperty("applicationId")
    private String applicationId;

    @JsonProperty("clientCustomerId")
    private String clientCustomerId;

    @JsonProperty("clientApplicationId")
    private String clientApplicationId;

    @JsonProperty("clientId")
    private String clientId;

    @JsonProperty("status")
    private List<PartnershipStatus> status;

    @JsonProperty("applicationCreationStatus")
    private String applicationCreationStatus;

    @Getter
    @Setter
    public static class PartnershipStatus {

        @JsonProperty("partnershipId")
        private String partnershipId;

        @JsonProperty("partnershipApplicationId")
        private String partnershipApplicationId;

        @JsonProperty("partnershipApplicationStatus")
        private String partnershipApplicationStatus;
    }
}
