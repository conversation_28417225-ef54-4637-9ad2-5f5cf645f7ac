package com.flipkart.fintech.pandora.yubi.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class ConfirmCommercialResponse extends YubiResponse{
    @JsonProperty("clientId")
    private String clientId;

    @JsonProperty("clientApplicationId")
    private String clientApplicationId;

    @JsonProperty("status")
    private List<PartnershipStatus> status;

    @Getter
    @Setter
    public static class PartnershipStatus {
        @JsonProperty("partnershipId")
        private String partnershipId;

        @JsonProperty("partnershipApplicationId")
        private String partnershipApplicationId;

        @JsonProperty("partnershipApplicationStatus")
        private String partnershipApplicationStatus;
    }
}
