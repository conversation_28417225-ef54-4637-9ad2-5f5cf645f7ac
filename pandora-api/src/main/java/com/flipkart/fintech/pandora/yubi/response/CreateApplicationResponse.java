package com.flipkart.fintech.pandora.yubi.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Getter
@Setter
public class CreateApplicationResponse extends YubiResponse {

    @JsonProperty("clientApplicationId")
    private String clientApplicationId;

    @JsonProperty("message")
    private String message;

}