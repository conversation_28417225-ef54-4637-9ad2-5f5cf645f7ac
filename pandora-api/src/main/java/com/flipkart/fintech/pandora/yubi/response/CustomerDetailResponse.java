package com.flipkart.fintech.pandora.yubi.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.flipkart.fintech.pandora.api.model.pl.response.CustomerDetailsResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class CustomerDetailResponse extends YubiResponse {
    private String clientCustomerId;
    private String partnershipId;
    private CustomerDetailsResponse.CustomerData customerData;

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class CustomerData {
        private List<CustomerDetailsResponse.Task> tasks;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Task {
        private String type;
        private String status;
        private String message;
        private CustomerDetailsResponse.TaskData data;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TaskData {
        private String limitId;
        private String limitValidity;
        private String limitAssessmentDate;
        private Double totalApprovedLimit;
        private Double availableLimit;
    }
}
