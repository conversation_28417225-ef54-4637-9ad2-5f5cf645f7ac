package com.flipkart.fintech.pandora.yubi.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class DisbursementDetailResponse extends YubiResponse {
    private String clientApplicationId;
    private String partnershipId;
    private ApplicationDetails applicationDetails;

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ApplicationDetails {
        private String status;
        private String message;
        private String lenderLoanId;
        private String lenderApplicationId;
        private List<Task> tasks;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Task {
        private String type;
        private String status;
        private String message;
        private TaskData data;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TaskData {
        private List<DisbursementDetail> disbursementDetails;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class DisbursementDetail {
        private String entityType;
        private String clientCustomerId;
        private String clientBankAccountId;
        private String bankAccountNumber;
        private String disbursementStatus;
        private String utrNumber;
        private String message;
    }
}
