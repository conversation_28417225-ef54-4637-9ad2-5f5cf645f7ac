package com.flipkart.fintech.pandora.yubi.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Getter
@Setter
public class FetchDocumentResponse extends YubiResponse{

    @JsonProperty("clientApplicationId")
    private String clientApplicationId;

    @JsonProperty("partnershipId")
    private String partnershipId;

    @JsonProperty("partnershipApplicationId")
    private String partnershipApplicationId;

    @JsonProperty("applicationDetails")
    private ApplicationDetails applicationDetails;

    @Getter
    @Setter
    public static class ApplicationDetails {

        @JsonProperty("tasks")
        private List<Task> tasks;
    }

    @Getter
    @Setter
    public static class Task {

        @JsonProperty("type")
        private String type;

        @JsonProperty("status")
        private String status;

        @JsonProperty("message")
        private String message;

        @JsonProperty("data")
        private DocumentGenerationData data;
    }

    @Getter
    @Setter
    public static class DocumentGenerationData {

        @JsonProperty("documentDetails")
        private List<DocumentDetail> documentDetails;
    }

    @Getter
    @Setter
    public static class DocumentDetail {

        @JsonProperty("documentType")
        private String documentType;

        @JsonProperty("documentSubType")
        private String documentSubType;

        @JsonProperty("generatedDocumentLink")
        private String generatedDocumentLink;

        @JsonProperty("documentGenerationStatus")
        private String documentGenerationStatus;
    }
}
