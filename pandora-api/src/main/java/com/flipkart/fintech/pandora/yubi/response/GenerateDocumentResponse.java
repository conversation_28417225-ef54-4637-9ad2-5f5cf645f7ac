package com.flipkart.fintech.pandora.yubi.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Getter
@Setter
public class GenerateDocumentResponse extends YubiResponse{

    @JsonProperty("requestId")
    private String requestId;

    @JsonProperty("clientApplicationId")
    private String clientApplicationId;

    @JsonProperty("partnershipId")
    private String partnershipId;

    @JsonProperty("documentDetails")
    private List<DocumentDetail> documentDetails;

    @Getter
    @Setter
    public static class DocumentDetail {

        @JsonProperty("documentId")
        private String documentId;

        @JsonProperty("documentType")
        private String documentType;

        @JsonProperty("documentSubType")
        private String documentSubType;

        @JsonProperty("documentGenerationStatus")
        private String documentGenerationStatus;
    }
}
