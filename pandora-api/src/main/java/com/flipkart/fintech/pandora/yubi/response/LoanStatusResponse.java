package com.flipkart.fintech.pandora.yubi.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Getter
@Setter
public class LoanStatusResponse extends YubiResponse {

    @JsonProperty("applicationId")
    private String applicationId;

    @JsonProperty("sector")
    private String sector;

    @JsonProperty("clientCustomerId")
    private String clientCustomerId;

    @JsonProperty("clientApplicationId")
    private String clientApplicationId;

    @JsonProperty("clientId")
    private String clientId;

    @JsonProperty("scheme")
    private String scheme;

    @JsonProperty("status")
    private List<Status> status;

    @JsonProperty("applicationCreationStatus")
    private String applicationCreationStatus;

    @JsonProperty("partnershipId")
    private String partnershipId;

    @JsonProperty("partnershipApplicationId")   // 👉 ADDED missing field here
    private String partnershipApplicationId;

    @JsonProperty("applicationDetails")
    private ApplicationDetails applicationDetails;

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Status {

        @JsonProperty("partnershipId")
        private String partnershipId;

        @JsonProperty("partnershipApplicationId")
        private String partnershipApplicationId;

        @JsonProperty("partnershipApplicationStatus")
        private String partnershipApplicationStatus;
    }

    @Getter
    @Setter
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ApplicationDetails {

        @JsonProperty("status")
        private String status;

        @JsonProperty("message")
        private String message;

        @JsonProperty("lenderLoanId")
        private String lenderLoanId;

        @JsonProperty("lenderApplicationId")
        private String lenderApplicationId;

        @JsonProperty("dataRequired")
        private List<DataRequired> dataRequired;

        @JsonProperty("tasks")
        private List<Task> tasks;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class DataRequired {

        @JsonProperty("key")
        private String key;

        @JsonProperty("value")
        private String value;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Task {

        @JsonProperty("type")
        private String type;

        @JsonProperty("status")
        private String status;

        @JsonProperty("message")
        private String message;

        @JsonProperty("data")
        private Map<String, Object> data;
    }
}
