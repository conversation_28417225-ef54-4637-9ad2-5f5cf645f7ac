package com.flipkart.fintech.pandora.yubi.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class OfferResponse extends YubiResponse {

    @NotEmpty
    @JsonProperty("clientApplicationId")
    private String clientApplicationId;

    @JsonProperty("partnershipApplicationId")
    private String partnershipApplicationId;

    @NotEmpty
    @JsonProperty("partnershipId")
    private String partnershipId;

    @NotEmpty
    @JsonProperty("clientCustomerId")
    private String clientCustomerId;

    @JsonProperty("customerData")
    private CustomerData customerData;

    @JsonProperty("applicationDetails")
    private ApplicationDetails applicationDetails;

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class CustomerData {
        @JsonProperty("tasks")
        private List<Task> tasks;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ApplicationDetails {
        @NotEmpty
        @JsonProperty("status")
        private String status;

        @JsonProperty("message")
        private String message;

        @JsonProperty("lenderLoanId")
        private String lenderLoanId;

        @JsonProperty("lenderApplicationId")
        private String lenderApplicationId;

        @JsonProperty("dataRequired")
        private List<KeyValue> dataRequired;

        @JsonProperty("tasks")
        private List<Task> tasks;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class KeyValue {
        @NotEmpty
        @JsonProperty("key")
        private String key;

        @NotEmpty
        @JsonProperty("value")
        private String value;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Task {
        @JsonProperty("type")
        private String type;

        @NotEmpty
        @JsonProperty("status")
        private String status;

        @JsonProperty("message")
        private String message;

        @JsonProperty("data")
        private TaskData data;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TaskData {
        @JsonProperty("data")
        private LimitData data;

        @JsonProperty("offerDetails")
        private List<OfferDetail> offerDetails;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class LimitData {
        @JsonProperty("limitId")
        private String limitId;

        @JsonProperty("limitValidity")
        private String limitValidity;

        @JsonProperty("limitAssessmentDate")
        private String limitAssessmentDate;

        @JsonProperty("totalApprovedLimit")
        private Double totalApprovedLimit;

        @JsonProperty("availableLimit")
        private Double availableLimit;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class OfferDetail {
        @JsonProperty("offerId")
        private String offerId;

        @JsonProperty("created_on")
        private String createdOn;

        @JsonProperty("expires_on")
        private String expiresOn;

        @JsonProperty("cycleDate")
        private List<Integer> cycleDate;

        @JsonProperty("slabs")
        private List<Slab> slabs;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Slab {
        @JsonProperty("minLoanAmount")
        private Double minLoanAmount;

        @JsonProperty("maxLoanAmount")
        private Double maxLoanAmount;

        @JsonProperty("minTenure")
        private Tenure minTenure;

        @JsonProperty("maxTenure")
        private Tenure maxTenure;

        @JsonProperty("minInterestRate")
        private Double minInterestRate;

        @JsonProperty("maxInterestRate")
        private Double maxInterestRate;

        @JsonProperty("minEmiAmount")
        private Double minEmiAmount;

        @JsonProperty("maxEmiAmount")
        private Double maxEmiAmount;

        @JsonProperty("netDisbursementAmount")
        private Double netDisbursementAmount;

        @JsonProperty("disbursementDeductions")
        private List<DisbursementDeduction> disbursementDeductions;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Tenure {
        @JsonProperty("days")
        private Integer days;

        @JsonProperty("months")
        private Integer months;

        @JsonProperty("years")
        private Integer years;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class DisbursementDeduction {
        @JsonProperty("deductionType")
        private String deductionType;

        @JsonProperty("deductionFormat")
        private String deductionFormat;

        @JsonProperty("deductionValue")
        private Double deductionValue;
    }
}
