package com.flipkart.fintech.pandora.yubi.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pandora.api.model.pl.response.RpsData;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Getter
@Setter
public class RepaymentDetailsResponse extends YubiResponse{

    @JsonProperty("clientApplicationId")
    private String clientApplicationId;

    @JsonProperty("partnershipId")
    private String partnershipId;

    @JsonProperty("partnershipApplicationId")
    private String partnershipApplicationId;

    @JsonProperty("applicationDetails")
    private ApplicationDetails applicationDetails;

    @Getter
    @Setter
    public static class ApplicationDetails {

        @JsonProperty("principalAmount")
        private Double principalAmount;

        @JsonProperty("interestRate")
        private Double interestRate;

        @JsonProperty("apr")
        private Double apr;

        @JsonProperty("tenure")
        private RpsData.Tenure tenure;

        @JsonProperty("loanFirstDisbursalDate")
        private String loanFirstDisbursalDate;

        @JsonProperty("firstRepaymentDate")
        private String firstRepaymentDate;

        @JsonProperty("interestAmount")
        private Double interestAmount;

        @JsonProperty("totalRepaymentAmount")
        private Double totalRepaymentAmount;

        @JsonProperty("netDisbursementAmount")
        private Double netDisbursementAmount;

        @JsonProperty("emiAmount")
        private Double emiAmount;

        @JsonProperty("disbursementDeductions")
        private List<RpsData.DisbursementDeduction> disbursementDeductions;

        @JsonProperty("tasks")
        private List<RpsData.Task> tasks;
    }
}
