package com.flipkart.fintech.pandora.yubi.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class YubiResponse {

    @JsonProperty("errors")
    private List<YubiErrorDetail> errors;

    private String statusCode;

    @Getter
    @Setter
    @ToString
    public static class YubiErrorDetail {
        @JsonProperty("description")
        private String description;

        @JsonProperty("field")
        private String field;

        @JsonProperty("code")
        private String code;

        @JsonProperty("expectedAction")
        private String expectedAction;
    }
}