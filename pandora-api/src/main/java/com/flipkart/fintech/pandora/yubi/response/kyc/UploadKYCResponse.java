package com.flipkart.fintech.pandora.yubi.response.kyc;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.flipkart.fintech.pandora.yubi.response.YubiResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class UploadKYCResponse extends YubiResponse {
    private String clientApplicationId;

    private String partnershipId;

    private String requestId;

    private List<CustomerDetail> customerDetails;

    private String kycStatus;

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class CustomerDetail {
        private String clientCustomerId;

        private String selfieStatus;
    }
}
