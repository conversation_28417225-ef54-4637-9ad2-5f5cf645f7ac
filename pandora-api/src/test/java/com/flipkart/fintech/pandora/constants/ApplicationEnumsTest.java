// File: pandora-api/src/test/java/com/flipkart/fintech/pandora/constants/ApplicationEnumsTest.java
package com.flipkart.fintech.pandora.constants;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

class ApplicationEnumsTest {

    @Test
    void testPurposeTypeLabel() {
        assertEquals("Personal Use", ApplicationEnums.PurposeType.PERSONAL_USE.getLabel());
    }

    @Test
    void testEmploymentCategoryValue() {
        assertEquals("SALARIED", ApplicationEnums.EmploymentCategory.SALARIED.name());
        assertEquals("SELF_EMPLOYED", ApplicationEnums.EmploymentCategory.SELF_EMPLOYED.name());
    }

    @Test
    void testCountryCode() {
        assertEquals(91, ApplicationEnums.CountryCode.INDIA.getCode());
    }

    @Test
    void testIdentityTypeValues() {
        assertNotNull(ApplicationEnums.IdentityType.valueOf("PAN"));
        assertNotNull(ApplicationEnums.IdentityType.valueOf("AADHAR"));
    }

    @Test
    void testAddressTypeValues() {
        assertNotNull(ApplicationEnums.AddressType.valueOf("RESIDENTIAL_CURRENT"));
        assertNotNull(ApplicationEnums.AddressType.valueOf("OFFICE"));
    }

    @Test
    void testDocTypeValues() {
        assertNotNull(ApplicationEnums.DOC_TYPE.valueOf("LOAN_APPLICATION_FORM"));
        assertNotNull(ApplicationEnums.DOC_TYPE.valueOf("KEY_FACT_STATEMENT"));
    }

    @Test
    void testKYCValues() {
        assertNotNull(ApplicationEnums.KYC.valueOf("KYC_INITIATION"));
        assertNotNull(ApplicationEnums.KYC.valueOf("KYC_CONFIRMED"));
    }
}