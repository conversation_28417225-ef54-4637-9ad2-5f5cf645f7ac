package com.flipkart.fintech.pandora.client;

import com.flipkart.fintech.pandora.api.model.request.LifeCycleManagement.UpdatePlusMembershipRequestData;
import com.flipkart.fintech.pandora.api.model.request.LifeCycleManagement.UpdateRewardProfileRequestDetails;
import com.google.inject.ImplementedBy;

@ImplementedBy(LifeCycleManagementClientImpl.class)
public interface LifeCycleManagementClient {

    void sendPlusMembershipEvent(String lender, UpdateRewardProfileRequestDetails updateRewardProfileRequestDetails, String productCode);
}
