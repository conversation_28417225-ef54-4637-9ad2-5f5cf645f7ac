package com.flipkart.fintech.pandora.client;

import com.flipkart.fintech.common.enums.Tenant;
import com.flipkart.fintech.exception.Error;
import com.flipkart.fintech.exception.ServiceErrorResponse;
import com.flipkart.fintech.exception.ServiceException;
import com.flipkart.fintech.filter.RequestContextThreadLocal;
import com.flipkart.fintech.pandora.api.model.common.ConsentType;
import com.flipkart.fintech.pandora.api.model.common.FinancialProvider;
import com.flipkart.fintech.pandora.api.model.request.onboarding.*;
import com.flipkart.fintech.pandora.api.model.response.onboarding.*;
import com.flipkart.fintech.pandora.service.client.pl.request.CheckLoanStatusRequest;
import com.flipkart.fintech.pandora.service.client.pl.response.CheckLoanStatusResponse;
import com.flipkart.sensitive.core.SensitiveUtil;
import org.apache.commons.lang3.StringUtils;
import org.glassfish.jersey.client.ClientProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

import javax.inject.Inject;
import javax.ws.rs.ProcessingException;
import javax.ws.rs.client.Client;
import javax.ws.rs.client.ClientBuilder;
import javax.ws.rs.client.Entity;
import javax.ws.rs.client.WebTarget;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.MultivaluedHashMap;
import javax.ws.rs.core.Response;

/**
 * Created by aniruddha.sharma on 10/11/17.
 */
public class OnboardingClientImpl implements OnboardingClient {

    private static final Logger logger = LoggerFactory.getLogger(OnboardingClientImpl.class);

    private static final String LENDER_CHECK_ELIGIBILITY_PATH = "/1/%s/check-eligibility";
    private static final String CREATE_LOAN_PATH              = "/1/%s/create-loan";
    private static final String UPDATE_LOAN_PATH              = "/1/%s/update-loan";
    private static final String CHECK_LOAN_STATUS_PATH        = "/idfc/check-loan-status";

    private final WebTarget webTarget;

    private final PandoraClientConfiguration pandoraClientConfiguration;

    @Inject
    public OnboardingClientImpl(PandoraClientConfiguration pandoraClientConfiguration) {
        this.pandoraClientConfiguration = pandoraClientConfiguration;

        if (StringUtils.isEmpty(pandoraClientConfiguration.getUrl())) {
            throw new IllegalArgumentException("Endpoint cannot be blank");
        }

        Client client = ClientBuilder.newClient();
        client.property(ClientProperties.CONNECT_TIMEOUT, 60000);
        client.property(ClientProperties.READ_TIMEOUT, 60000);

        this.webTarget = client.target(pandoraClientConfiguration.getUrl());
    }

    public UserResponse checkEligibility(UserRequest user, FinancialProvider financialProvider) {

        String path = "/1/" + financialProvider + "/checkEligibilityRequest";

        UserResponse userResponse = null;

        Response response = null;
        try{
            MultivaluedHashMap<String, Object> headers = getHeaders();
            headers.add(Constants.TENANT_ID_HEADER, getTenant());
            response = webTarget.path(path).request(MediaType.APPLICATION_JSON).headers(headers)
                    .post(Entity.json(user));
            if (response.getStatus() == 200) {
                userResponse = response.readEntity(UserResponse.class);
                logger.info("Response is {}", userResponse);
            }
            else if(response.getStatus() == 500){
                throw new PandoraClientException("Error response from Pandora" + response.readEntity(String.class));
            }
            else {
                userResponse = response.readEntity(UserResponse.class);
                logger.info("Response is {}", userResponse);
                throw new PandoraClientException(response.getStatus(), userResponse);
            }
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return userResponse;
    }

    public PanNumberResponse verifyPan(PanNumberRequest panNumberRequest, FinancialProvider financialProvider) {

        panNumberRequest = SensitiveUtil.encryptSensitiveFields(panNumberRequest);
        String path = "/1/" + financialProvider + "/verifyPan";

        PanNumberResponse panNumberResponse = null;

        Response response = null;

        try {
            MultivaluedHashMap<String, Object> headers = getHeaders();
            headers.add(Constants.TENANT_ID_HEADER, getTenant());
            response = webTarget.path(path).request(MediaType.APPLICATION_JSON)
                    .headers(headers).post(Entity.json(panNumberRequest));
            if (response.getStatus() == 200) {
                panNumberResponse = response.readEntity(PanNumberResponse.class);
                logger.info("Response is {}", panNumberResponse);
            }
            else {
                logger.error("Some error occured contacting with Pandora while IDFC verifyPan API {} ",panNumberRequest.getAccId());
                mapResponseToException(response);
            }
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return panNumberResponse;
    }

    public AccountActivationResponse updateActivationStatus(AccountActivationRequest accountActivationRequest,
                                                            FinancialProvider financialProvider, String referenceNumber) {

        String path = "/1/" + financialProvider + "/update_activation_status/"+referenceNumber;

        AccountActivationResponse accountActivationResponse = null;

        Response response = null;

        MultivaluedHashMap<String, Object> headers = getHeaders();
        headers.add(Constants.TENANT_ID_HEADER, getTenant());

        try {
            response = webTarget.path(path).request(MediaType.APPLICATION_JSON)
                    .headers(headers).post(Entity.json(accountActivationRequest));
            if (response.getStatus() == 200) {
                accountActivationResponse = response.readEntity(AccountActivationResponse.class);
                logger.info("Response is {}", accountActivationResponse);
            } else {
                mapResponseToException(response);
            }
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return accountActivationResponse;
    }

    @Override
    public MobileNumberUpdationResponse updateMobileNumber(String accountNumber, MobileNumberUpdationRequest
            mobileNumberUpdationRequest, FinancialProvider financialProvider){
        String path = String.format("/1/%s/%s/update_mobile_number", financialProvider.name(), accountNumber);
        MobileNumberUpdationResponse mobileNumberUpdationResponse = null;
        Response response = null;
        try {
            response = webTarget.path(path).request(MediaType.APPLICATION_JSON).headers(getHeaders()).
                    post(Entity.json(mobileNumberUpdationRequest));
            if (response.getStatus() == 200) {
                mobileNumberUpdationResponse = response.readEntity(MobileNumberUpdationResponse.class);
                logger.info("Response is {}", mobileNumberUpdationResponse);
            } else if(response.getStatus() == 500){
                throw new PandoraClientException("Error response from Pandora" + response.readEntity(String.class));
            }
            else{
                mobileNumberUpdationResponse = response.readEntity(MobileNumberUpdationResponse.class);
                logger.info("Response is {}", mobileNumberUpdationResponse);
                throw new PandoraClientException(response.getStatus(), mobileNumberUpdationResponse);
            }
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return mobileNumberUpdationResponse;
    }

    @Override
    public ApplicationCreateResponse createApplication(
            ApplicationCreateRequest request, FinancialProvider financialProvider) {
        String path = String.format(Constants.CREATE_APPLICATION, financialProvider.name());
        ApplicationCreateResponse applicationCreateResponse = null;
        Response response = null;
        try {
            response = webTarget.path(path).request(MediaType.APPLICATION_JSON).headers(getHeaders())
                    .post(Entity.json(request));
            if (response.getStatus() == 200) {
                applicationCreateResponse = response.readEntity(ApplicationCreateResponse.class);
                logger.info("Response is {}", applicationCreateResponse);
            } else if(response.getStatus() == 500){
                throw new PandoraClientException("Error response from Pandora" + response.readEntity(String.class));
            }
            else{
                applicationCreateResponse = response.readEntity(ApplicationCreateResponse.class);
                logger.info("Response is {}", applicationCreateResponse);
                throw new PandoraClientException(response.getStatus(), applicationCreateResponse);
            }
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return applicationCreateResponse;
    }

    @Override
    public ApplicationUpdateResponse updateApplication(ApplicationUpdateRequest request,
                                                       FinancialProvider financialProvider) {
        String path = String.format(Constants.UPDATE_APPLICATION, financialProvider.name());
        ApplicationUpdateResponse applicationUpdateResponse = null;
        Response response = null;
        try {
            response = webTarget.path(path).request(MediaType.APPLICATION_JSON).headers(getHeaders())
                    .put(Entity.json(request));
            if (response.getStatus() == 200) {
                applicationUpdateResponse = response.readEntity(ApplicationUpdateResponse.class);
                logger.info("Response is {}", applicationUpdateResponse);
            } else if(response.getStatus() == 500){
                throw new PandoraClientException("Error response from Pandora" + response.readEntity(String.class));
            }
            else{
                applicationUpdateResponse = response.readEntity(ApplicationUpdateResponse.class);
                logger.info("Response is {}", applicationUpdateResponse);
                throw new PandoraClientException(response.getStatus(), applicationUpdateResponse);
            }
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return applicationUpdateResponse;
    }

    @Override
    public CustomerAccountsResponse getCustomerAccounts(String customerId, FinancialProvider financialProvider) {
        String path = String.format(Constants.FETCH_CUSTOMER_ACCOUNTS_PATH, financialProvider.name(), customerId);
        CustomerAccountsResponse customerAccountsResponse = null;
        Response response = null;
        try {
            response = webTarget.path(path).request(MediaType.APPLICATION_JSON).headers(getHeaders()).get();

            if (response.getStatus() == 200) {
                customerAccountsResponse = response.readEntity(CustomerAccountsResponse.class);
                logger.info("Response is {}", customerAccountsResponse);
            } else if(response.getStatus() == 500){
                throw new PandoraClientException("Error response from Pandora" + response.readEntity(String.class));
            }
            else{
                customerAccountsResponse = response.readEntity(CustomerAccountsResponse.class);
                logger.info("Response is {}", customerAccountsResponse);
                throw new PandoraClientException(response.getStatus(), customerAccountsResponse);
            }
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return customerAccountsResponse;
    }

    @Override
    public AccountAccessCodeResponse fetchAccountAccessCode(
            String customerId, String accountId, FinancialProvider financialProvider) {
        String path = String.format(Constants.FETCH_ACCOUNT_ACCESS_CODE_PATH, financialProvider.name(), customerId);
        AccountAccessCodeResponse accountAccessCodeResponse = null;
        Response response = null;
        try {
            response = webTarget.path(path).queryParam("account_id", accountId).request(MediaType.APPLICATION_JSON)
                    .headers(getHeaders()).get();
            if (response.getStatus() == 200) {
                accountAccessCodeResponse = response.readEntity(AccountAccessCodeResponse.class);
                logger.info("Response is {}", accountAccessCodeResponse);
            } else if(response.getStatus() == 500){
                throw new PandoraClientException("Error response from Pandora" + response.readEntity(String.class));
            }
            else{
                accountAccessCodeResponse = response.readEntity(AccountAccessCodeResponse.class);
                logger.info("Response is {}", accountAccessCodeResponse);
                throw new PandoraClientException(response.getStatus(), accountAccessCodeResponse);
            }
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return accountAccessCodeResponse;
    }

    @Override
    public ConsentEnquiryResponse consentEnquiry(
            String customerId, ConsentType consentType, String accountId, FinancialProvider financialProvider) {
        String path =
                String.format(Constants.CONSENT_ENQUIRY_PATH, financialProvider.name(), customerId, consentType.name());
        ConsentEnquiryResponse consentEnquiryResponse = null;
        Response response = null;
        try {
            response = webTarget.path(path).queryParam("account_id", accountId).request(MediaType.APPLICATION_JSON)
                    .headers(getHeaders()).get();
            if (response.getStatus() == 200) {
                consentEnquiryResponse = response.readEntity(ConsentEnquiryResponse.class);
                logger.info("Response is {}", consentEnquiryResponse);
            } else if(response.getStatus() == 500){
                throw new PandoraClientException("Error response from Pandora" + response.readEntity(String.class));
            }
            else{
                consentEnquiryResponse = response.readEntity(ConsentEnquiryResponse.class);
                logger.info("Response is {}", consentEnquiryResponse);
                throw new PandoraClientException(response.getStatus(), consentEnquiryResponse);
            }
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return consentEnquiryResponse;
    }

    @Override
    public UserResponse lenderCheckEligibility(UserRequest user, FinancialProvider financialProvider) {
        String path = String.format(LENDER_CHECK_ELIGIBILITY_PATH, financialProvider.name());
        return getUserResponse(path, user);
    }

    @Override
    public UserResponse createLoan(UserRequest user, FinancialProvider financialProvider) {
        String path = String.format(CREATE_LOAN_PATH, financialProvider.name());
        return getUserResponse(path, user);
    }

    @Override
    public UserResponse updateLoan(UpdateLoanRequest updateLoanRequest, FinancialProvider financialProvider) {
        String path = String.format(UPDATE_LOAN_PATH, financialProvider.name());
        UserResponse userResponse = null;

        Response response = null;
        try{
            MultivaluedHashMap<String, Object> map = getHeaders();
            map.add(Constants.TENANT_ID_HEADER, getTenant());
            response = webTarget.path(path).request(MediaType.APPLICATION_JSON).headers(map)
                    .post(Entity.json(updateLoanRequest));
            if (response.getStatus() == 200) {
                userResponse = response.readEntity(UserResponse.class);
                logger.info("Response is {}", userResponse);
            } else {
                mapResponseToException(response);
            }
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return userResponse;
    }

    @Override
    public CheckLoanStatusResponse checkLoan(CheckLoanStatusRequest checkLoanStatusRequest, String externalUserId) {
        String path = CHECK_LOAN_STATUS_PATH;
        CheckLoanStatusResponse checkLoanStatusResponse = null;
        MultivaluedHashMap<String, Object> headers = new MultivaluedHashMap<>();
        headers.add("X-External-User-Id", externalUserId);

        Response response = null;
        try{
             response =  webTarget.path(path).request(MediaType.APPLICATION_JSON).headers(headers).post(Entity.json(checkLoanStatusRequest));
            if (response.getStatus() == 200) {
                checkLoanStatusResponse = response.readEntity(CheckLoanStatusResponse.class);
                logger.info("Response is {}", checkLoanStatusResponse);
            }
            else if(response.getStatus() == 500){
                throw new PandoraClientException("Error response from Pandora" + response.readEntity(String.class));
            }
            else {
                checkLoanStatusResponse = response.readEntity(CheckLoanStatusResponse.class);
                logger.info("Response is {}", checkLoanStatusResponse);
                throw new PandoraClientException(response.getStatus(), checkLoanStatusResponse);
            }
        } finally {
            if (response != null) {
                response.close();
            }
        }

        return checkLoanStatusResponse;
    }

    private UserResponse getUserResponse(String path, UserRequest user) {
        UserResponse userResponse = null;

        Response response = null;
        MultivaluedHashMap<String, Object> headers = getHeaders();
        headers.add(Constants.TENANT_ID_HEADER, getTenant());
        try{
            response = webTarget.path(path).request(MediaType.APPLICATION_JSON).headers(headers)
                    .post(Entity.json(user));
            if (response.getStatus() == 200) {
                userResponse = response.readEntity(UserResponse.class);
                logger.info("Response is {}", userResponse);
            } else {
                mapResponseToException(response);
            }
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return userResponse;
    }

    private MultivaluedHashMap<String, Object> getHeaders(){
        MultivaluedHashMap<String, Object> map = new MultivaluedHashMap<>();
        String userDetails = MDC.get(Constants.DEVICE_DETAILS_HEADER);
        if(!StringUtils.isEmpty(userDetails)) {
            map.add(Constants.DEVICE_DETAILS_HEADER, userDetails);
        }
        if (RequestContextThreadLocal.get().isPerfRequest()) {
            map.add("X-PERF-TEST", true);
        }
        return map;
    }

    private Tenant getTenant() {
        return RequestContextThreadLocal.get() != null && RequestContextThreadLocal.get().getTenantId() != null ? RequestContextThreadLocal.get().getTenantId() : Tenant.FK_CONSUMER_CREDIT;
    }

    private void mapResponseToException(Response response) {
        try {
            Error error = response.readEntity(Error.class);
            throw new ServiceException(new ServiceErrorResponse(
                    Response.Status.fromStatusCode(response.getStatus()), error.getCode(), error.getMessage()));
        } catch (IllegalStateException | ProcessingException e) {
            throw new ServiceException(new ServiceErrorResponse(
                    Response.Status.INTERNAL_SERVER_ERROR, Response.Status.INTERNAL_SERVER_ERROR.getReasonPhrase(), e.getMessage()));
        }
    }
}
