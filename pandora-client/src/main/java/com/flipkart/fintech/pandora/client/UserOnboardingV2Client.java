package com.flipkart.fintech.pandora.client;

import com.flipkart.fintech.pandora.api.model.CardCaptureValidationRequest;
import com.flipkart.fintech.pandora.api.model.common.FinancialProvider;
import com.flipkart.fintech.pandora.api.model.request.mandate.MandateConfirmationRequest;
import com.flipkart.fintech.pandora.api.model.request.onboarding.v2.AltDataInfoRequest;
import com.flipkart.fintech.pandora.api.model.request.onboarding.v2.ApplyNowRequest;
import com.flipkart.fintech.pandora.api.model.request.onboarding.v2.CreditAssessmentInfoRequest;
import com.flipkart.fintech.pandora.api.model.request.onboarding.v2.FetchCardDetailsRequest;
import com.flipkart.fintech.pandora.api.model.request.onboarding.v2.GenerateOtpRequest;
import com.flipkart.fintech.pandora.api.model.request.onboarding.v2.GetPersonalInfoRequest;
import com.flipkart.fintech.pandora.api.model.request.onboarding.v2.HandshakeRequest;
import com.flipkart.fintech.pandora.api.model.request.onboarding.v2.KYCSchedulerDetailsRequest;
import com.flipkart.fintech.pandora.api.model.request.onboarding.v2.PanNoRequest;
import com.flipkart.fintech.pandora.api.model.request.onboarding.v2.PersonalInfoRequest;
import com.flipkart.fintech.pandora.api.model.request.onboarding.v2.UserAcceptanceRequest;
import com.flipkart.fintech.pandora.api.model.response.CardCaptureValidationResponse;
import com.flipkart.fintech.pandora.api.model.response.mandate.MandateConfirmationResponse;
import com.flipkart.fintech.pandora.api.model.response.onboarding.v2.ApplyNowResponse;
import com.flipkart.fintech.pandora.api.model.response.onboarding.v2.CreditAssessmentInfoResponse;
import com.flipkart.fintech.pandora.api.model.response.onboarding.v2.FetchCardDetailsResponse;
import com.flipkart.fintech.pandora.api.model.response.onboarding.v2.GenerateOtpResponse;
import com.flipkart.fintech.pandora.api.model.response.onboarding.v2.GetPersonalInfoResponse;
import com.flipkart.fintech.pandora.api.model.response.onboarding.v2.HandshakeVideoKycDetailsResponse;
import com.flipkart.fintech.pandora.api.model.response.onboarding.v2.KYCSchedulerDetailsResponse;
import com.flipkart.fintech.pandora.api.model.response.onboarding.v2.PanNoResponse;
import com.flipkart.fintech.pandora.api.model.response.onboarding.v2.PersonalInfoResponse;
import com.flipkart.fintech.pandora.api.model.response.onboarding.v2.UserAcceptanceResponse;
import com.google.inject.ImplementedBy;


@ImplementedBy(UserOnboardingV2ClientImpl.class)
public interface UserOnboardingV2Client {
    public ApplyNowResponse applyNow (ApplyNowRequest applyNowRequest, FinancialProvider financialProvider);
    public PersonalInfoResponse personalInfo (PersonalInfoRequest personalInfoRequest, FinancialProvider financialProvider);
    public CreditAssessmentInfoResponse creditAssessmentFromCreditAssessmentRequest (CreditAssessmentInfoRequest creditAssessmentInfoRequest, FinancialProvider financialProvider);
    public CreditAssessmentInfoResponse creditAssessmentFromAlternatDataRequest (AltDataInfoRequest altDataInfoRequest, FinancialProvider financialProvider);
    public UserAcceptanceResponse userAcceptance (UserAcceptanceRequest userAcceptanceRequest, FinancialProvider financialProvider);
    public PanNoResponse panNameService (PanNoRequest panNoRequest, FinancialProvider financialProvider);
    public GetPersonalInfoResponse getpersonalInfo(GetPersonalInfoRequest getPersonalInfoRequest,FinancialProvider financialProvider);
    public GenerateOtpResponse generateOtp(GenerateOtpRequest generateOtpRequest,FinancialProvider financialProvider);
    public MandateConfirmationResponse mandateConfirmation (MandateConfirmationRequest mandateConfirmationRequest, FinancialProvider financialProvider);
    public CardCaptureValidationResponse cardCaptureValidation(CardCaptureValidationRequest cardCaptureValidationRequest, FinancialProvider financialProvider);
    public FetchCardDetailsResponse fetchCardDetails(FetchCardDetailsRequest fetchCardDetailsRequest,FinancialProvider financialProvider);
    public KYCSchedulerDetailsResponse kycSchedule(KYCSchedulerDetailsRequest kycSchedulerDetailsRequest,FinancialProvider financialProvider);
    public HandshakeVideoKycDetailsResponse handshake(HandshakeRequest handshakeRequest,FinancialProvider financialProvider);
}
