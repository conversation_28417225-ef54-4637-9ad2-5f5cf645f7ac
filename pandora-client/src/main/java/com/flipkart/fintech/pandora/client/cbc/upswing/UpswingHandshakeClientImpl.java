package com.flipkart.fintech.pandora.client.cbc.upswing;

import com.flipkart.fintech.pandora.api.model.request.cbc.upswing.CustomerHandshakeRequest;
import com.flipkart.fintech.pandora.api.model.response.cbc.upswing.CustomerHandshakeResponse;
import com.flipkart.fintech.pandora.api.model.response.cbc.upswing.JwtTokenResponse;
import com.flipkart.fintech.pandora.client.Constants;
import com.flipkart.fintech.pandora.client.PandoraClientConfiguration;
import com.flipkart.fintech.pandora.client.cbc.upswing.util.ClientUtil;
import com.google.inject.Inject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.glassfish.jersey.client.ClientProperties;

import javax.ws.rs.client.Client;
import javax.ws.rs.client.ClientBuilder;
import javax.ws.rs.client.Entity;
import javax.ws.rs.client.WebTarget;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.MultivaluedHashMap;

@Slf4j
public class UpswingHandshakeClientImpl implements UpswingHandshakeClient {

    private final WebTarget webTarget;

    @Inject
    public UpswingHandshakeClientImpl(PandoraClientConfiguration pandoraClientConfiguration) {
        if (StringUtils.isEmpty(pandoraClientConfiguration.getUrl())) {
            throw new IllegalArgumentException("Endpoint cannot be blank");
        }
        Client client = ClientBuilder.newClient();
        client.property(ClientProperties.CONNECT_TIMEOUT, pandoraClientConfiguration.getConnectTimeout() > 0 ? pandoraClientConfiguration.getConnectTimeout() : Constants.PANDORA_CONNECT_TIMEOUT);
        client.property(ClientProperties.READ_TIMEOUT, pandoraClientConfiguration.getReadTimeout() > 0 ? pandoraClientConfiguration.getReadTimeout() : Constants.PANDORA_READ_TIMEOUT);
        this.webTarget = client.target(pandoraClientConfiguration.getUrl());
    }

    @Override
    public CustomerHandshakeResponse customerHandshake(CustomerHandshakeRequest request) {
        String path = Constants.UpswingCard.CUSTOMER_HANDSHAKE;
        log.info("Customer handshake API path: {}", path);
        return ClientUtil.getPostResponse(webTarget, path, MediaType.APPLICATION_JSON,
                Entity.json(request), getHeaders(), CustomerHandshakeResponse.class);
    }

    private MultivaluedHashMap<String, Object> getHeaders() {
        MultivaluedHashMap<String, Object> headers = new MultivaluedHashMap<>();
        headers.add(Constants.CONTENT_TYPE, MediaType.APPLICATION_JSON);
        return headers;
    }


}
