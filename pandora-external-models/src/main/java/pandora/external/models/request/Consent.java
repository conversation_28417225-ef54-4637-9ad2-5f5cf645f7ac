package pandora.external.models.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import lombok.Builder;
import lombok.Getter;

@Getter
@Builder
@JsonDeserialize(builder = SbAccountAggregatorRequest.SbAccountAggregatorRequestBuilder.class)
public class Consent {

  @JsonProperty("for")
  private final ConsentFor consentFor;

  @JsonProperty("provided")
  private final boolean provided;

  @JsonProperty("ip")
  private final String ip;

  @JsonProperty("deviceId")
  private final String deviceId;

  @JsonProperty("deviceInfo")
  private final String deviceInfo;

  @JsonProperty("ts")
  private final Long ts;

}
