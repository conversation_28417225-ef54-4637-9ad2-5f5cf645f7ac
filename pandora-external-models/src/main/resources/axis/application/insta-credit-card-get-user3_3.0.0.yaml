swagger: '2.0'
info:
  x-ibm-name: insta-credit-card-get-user3
  title: Insta Credit Card Get User3
  version: 3.0.0
  description: >-
    API Description:- This API is used to fetch demographic details for ETB/ETC
    customers. Should not be invoked for NTB customers


    URL:- https:
    //sakshamuat.axisbank.co.in/gateway/api/insta-credit-card/getuser/v3/financialservices/user/get


    Method:- POST


    Non-encrypted JSON request sample:-


    {
        "GetUserDtlsRequest": {
            "SubHeader": {
                "requestUUID": "4dc349d8-6af9-4cea-b24c-e85144",
                "serviceRequestId": "AX.GEN.ICC.GET.USER",
                "serviceRequestVersion": "1.0",
                "channelId": "XXX"
            },
            "GetUserDtlsRequestBody": {
                "data": {
                    "phoneNumber": "91xxxxxxxxxx",
                    "tokenReferenceId": "d6febbde4358b15a5b78e",
                    "tokenValue": "123456",
                    "custType": "ETB"
                }
            }
        }
    }



    Encrypted JSON request sample:-


    {
        "GetUserDtlsRequest": {
            "SubHeader": {
                "requestUUID": "4dc349d8-6af9-4cea-b24c-e85144",
                "serviceRequestId": "AX.GEN.ICC.GET.USER",
                "serviceRequestVersion": "1.0",
                "channelId": "XXX"
            },
            "GetUserDtlsRequestBodyEncrypted": "UfUcStRqv3nbLU8fB2PPT+M3XDlniQCH+3Nmgy+kw+63macA5FGmjqdgJsInOrgO1dUl6PVQ3HHK5g5E/WxKSBdDuhbZ+eIMWRCIC6ZTog5Nq3q2mGg8XKjiYFWtO/roznoIx11ZmAQaU789oiA30qBvhLLQWfluE8i4HlEg3EqDo/ih8LP7HLhZgnjFriIf56uY3Ra5+7goQAbwMFmWug=="
        }
    }



    Non-encrypted JSON response sample:-


    {
        "GetUserDtlsResponse": {
            "SubHeader": {
                "requestUUID": "4dc349d8-6af9-4cea-b24c-e85144",
                "serviceRequestId": "AX.GEN.ICC.GET.USER",
                "serviceRequestVersion": "1.0",
                "channelId": "XXX"
            },
            "GetUserDtlsResponseBody": {
                "data": {
                    "phoneNumber": "91xxxxxxxxxx",
                    "addresses": [
                        {
                            "addressLine1": "101, building name",
                            "addressLine2": "west of main road",
                            "addressLine3": "west of main road",
                            "landmark": "near famous watertower",
                            "postalCode": "123456",
                            "city": "somecity",
                            "state": "somestate",
                            "country": "somecountry",
                            "addressType": "RESIDENTIAL "
                        }
                    ],
                    "displayName": "Bruce Wayne",
                    "existingCreditCardDetails": [
                        {
                            "maskedCardNumber": "4567",
                            "cardSerialNumber": "326382792"
                        }
                    ]
                }
            }
        }
    }



    Encrypted JSON response sample:-

    {
        "GetUserDtlsResponse": {
            "SubHeader": {
                "requestUUID": "4dc349d8-6af9-4cea-b24c-e85144",
                "serviceRequestId": "AX.GEN.ICC.GET.USER",
                "serviceRequestVersion": "1.0",
                "channelId": "XXX"
            },
            "GetUserDtlsResponseBodyEncrypted": "wQPZ7CAKjEXvzV4PuPyy/qjfSglMSMdaoc6Hpa3//OKuylXcOYIIWOPpEIWAuocMjEMb+on1jwWqVokt6Nt2rPYFEVxrlmLNDs72Taiai5GFt5GWi6lvmVVtmCzXoIj4OfzfHCpEVZ+Sfk3C3iJkAoospnCcCZYT0Ku1Os3pfZrxtWAnKxX2yxvcQDFiKF4QuAAZi1SlRrqYLqMkgpVbLiAw4usy76p6X0nAzocwOlHLjypqABuhMW9uLqBGCUR3H5Q48b/bu1MSPeqX8HyII/nB4vl0JtJX6OobIxOIXkt0mwz8vyXT6DmnTsqCML2iuKd5kCeYMmvIdGqtL8miCNgMlmk55hpnrDh4W5ahqCe26MG1DsoWvRMdyMbIsZM88d7WgBUpoxlEvOCOyKq2T0dA9mYxsun/vmmyM2ENmLi2xBX516Yo+itHiXbIGNVxO8c8TdjKdfdFqoIDAyCsnS3TJmndCXUweu5z7NMyc4R/8U7FNRZ+Bw+lAYC+3+dLs/7uNWtqSZuZ6195RHENeUHuGLdmHrI2SQ8oFiTqvQZNEdfOxxu90DhnaxbJLDfEa0AM+3Yie4fXLhmYTV8eB/NKvCy0j0RrTNIZEbNAGUkfZ96LjONgT60ofwVZsbbrIE1olAYqQCOWkF+Q7kE3o2813K8hAJuo8ezgXX0f/DmUCa16n35xgFVH7pXsDocr"
        }
    }
schemes:
  - https
basePath: /api/insta-credit-card/getuser
consumes:
  - application/json
produces:
  - application/json
securityDefinitions:
  X-IBM-Client-Id:
    in: header
    name: X-IBM-Client-Id
    type: apiKey
  X-IBM-Client-Secret:
    description: ''
    in: header
    name: X-IBM-Client-Secret
    type: apiKey
security:
  - X-IBM-Client-Id: []
    X-IBM-Client-Secret: []
x-ibm-configuration:
  testable: true
  enforced: true
  cors:
    enabled: true
  assembly:
    finally: []
  phase: realized
  properties:
    product-name:
      value: insta credit card
      description: ''
      encoded: false
    target-fetchCCOffer:
      value: https://esbuat2.axisb.com/JSON/fetch_CC_Details
      description: ''
      encoded: false
    target-validateotp-chub:
      value: https://esbuat2.axisb.com/chub-otp/enc/validate-otp
    target-fetchdemog:
      value: https://esbuat2.axisb.com/JSON/FinacleESBService
      description: ''
      encoded: false
    target-custdetail:
      value: https://esbuat2.axisb.com/JSON/CreditCardESBService_4_0
      description: ''
      encoded: false
  gateway: datapower-api-gateway
  type: rest
  compatibility:
    enforce-required-params: false
    request-headers: true
    suppress-limitation-errors: true
  activity-log:
    enabled: true
    success-content: activity
    error-content: payload
paths:
  /v3/financialservices/user/get:
    post:
      responses:
        '200':
          description: 200 OK
          schema:
            $ref: '#/definitions/getUserDetail-Response'
      parameters: []
definitions:
  getUserDetail-Request:
    description: ''
    type: object
    properties:
      GetUserDtlsRequest:
        type: object
        properties:
          SubHeader:
            $ref : './schemas/SubHeader.yaml'
            required:
              - requestUUID
              - serviceRequestId
              - serviceRequestVersion
              - channelId
          GetUserDtlsRequestBody:
            type: object
            properties:
              data:
                type: object
                properties:
                  phoneNumber:
                    type: string
                  tokenReferenceId:
                    type: string
                  tokenValue:
                    type: string
                  custType:
                    type: string
                required:
                  - phoneNumber
                  - tokenReferenceId
                  - tokenValue
                  - custType
            required:
              - data
        required:
          - SubHeader
          - GetUserDtlsRequestBody
    example: >-
      {"GetUserDtlsRequest":{"SubHeader":{"requestUUID":"ABC123","serviceRequestId":"API.API.GET.USER","serviceRequestVersion":"1.0","channelId":"XXX"},"GetUserDtlsRequestBody":{"data":{"phoneNumber":"************","tokenReferenceId":"1104f4ec45ca828e67bb8","tokenValue":"123456","custType":"ETB"}}}}
    required:
      - GetUserDtlsRequest
  getUserDetail-Response:
    type: object
    properties:
      GetUserDtlsResponse:
        type: object
        properties:
          SubHeader:
            $ref : './schemas/SubHeader.yaml'
          GetUserDtlsResponseBody:
            type: object
            properties:
              data:
                type: object
                properties:
                  phoneNumber:
                    type: string
                  addresses:
                    type: array
                    items:
                      type: object
                      properties:
                        addressLine1:
                          type: string
                        addressLine2:
                          type: string
                        addressLine3:
                          type: string
                        landmark:
                          type: string
                        postalCode:
                          type: string
                        city:
                          type: string
                        state:
                          type: string
                        country:
                          type: string
                        addressType:
                          type: string
                  displayName:
                    type: string
                  existingCreditCardDetails:
                    type: array
                    items:
                      type: object
                      properties:
                        maskedCardNumber:
                          type: string
                        cardSerialNumber:
                          type: string
                  maskedAccountNumber:
                    type: string
                required:
                  - phoneNumber
                  - addresses
                  - addressLine1
                  - postalCode
                  - city
                  - state
                  - country
                  - addressType
                  - displayName
    example: >-
      {"GetUserDtlsResponse":{"SubHeader":{"requestUUID":"4dc349d8-6af9-4cea-b24c-e85144","serviceRequestId":"AX.GEN.ICC.GET.USER","serviceRequestVersion":"1.0","channelId":"XXX"},"GetUserDtlsResponseBody":{"data":{"phoneNumber":"91xxxxxxxxxx","addresses":[{"addressLine1":"101,
      building name","addressLine2":"west of main road","addressLine3":"west of
      main road","landmark":"near famous
      watertower","postalCode":"123456","city":"somecity","state":"somestate","country":"somecountry","addressType":"RESIDENTIAL
      "}],"displayName":"Bruce
      Wayne","existingCreditCardDetails":[{"maskedCardNumber":"4567","cardSerialNumber":"326382792"}]}}}}
  error-response:
    $ref: './schemas/ErrorResponse.yaml'
    example: >-
      {"errorDescription":"Invalid
      OTP","errorCode":"CCIC01","invalidFieldNames":"data.tokenValue"}
tags:
  - name: Category
    description: Defines category of api
    externalDocs:
      url: Credit Card
