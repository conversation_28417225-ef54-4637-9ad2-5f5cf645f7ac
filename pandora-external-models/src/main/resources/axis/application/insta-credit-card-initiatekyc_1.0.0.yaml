swagger: '2.0'
info:
  x-ibm-name: insta-credit-card-initiatekyc
  title: Insta Credit Card Initiatekyc
  version: 1.0.0
  description: >-
    Description:- This api is used to initiate KYC journey, user has to pass
    applicationId, MobileNumber and callBackURL has to send in request, api will
    return authCode and expirationTime in reponse.


    URL:-
    https://sakshamuat.axisbank.co.in/gateway/api/insta-credit-card/v1/financialservices/credit/application/kyc/initiate


    Method:- POST


    Non-encrypted JSON request sample:-


    {
        "InitiateKycRequest": {
            "SubHeader": {
                "requestUUID": "e5c3ad32-5f3b-448b-80b5-b1cac",
                "serviceRequestId": "AX.GEN.ICC.INITI.KYC",
                "serviceRequestVersion": "1.0",
                "channelId": "XXX"
            },
            "InitiateKycRequestBody": {
                "data": {
                    "phoneNumber": "91xxxxxxxxxx",
                    "applicationId": "some-application-id",
                    "callbackUrl": "partner-callback-url"
                }
            }
        }
    }


    Encrypted JSON request sample:-


    {
        "InitiateKycRequest": {
            "SubHeader": {
                "requestUUID": "e5c3ad32-5f3b-448b-80b5-b1cac",
                "serviceRequestId": "AX.GEN.ICC.INITI.KYC",
                "serviceRequestVersion": "1.0",
                "channelId": "XXX"
            },
            "InitiateKycRequestBodyEncrypted": "bwgaOXRe00IjHAiZHgdPEojL5OjktFFTwypVk7glYSqdiiUUhNkel2SW7jrHu3EwSN75Hli7cBloTN0AUslF3PjOTOKdUH0Azxumsm9oK7kZNinPMnZvZjm5ZJhWArJH/ZNZEXlQEi6s2AxlyzbOP7Zh1EoQnrZeeetiMYGMIzbVfyFbRTtOFhgbL4K0qEvK"
        }
    }


    Non-encrypted JSON response sample:-


    {
        "InitiateKycResponse": {
            "SubHeader": {
                "requestUUID": "e5c3ad32-5f3b-448b-80b5-b1cac",
                "serviceRequestId": "AX.GEN.ICC.INITI.KYC",
                "serviceRequestVersion": "1.0",
                "channelId": "XXX"
            },
            "InitiateKycResponseBody": {
                "data": {
                    "authCode": "727d8bee-7f03-4225-8d54-60407326a6ba",
                    "expirationTime": 1234567890
                }
            }
        }
    }



    Encrypted JSON response sample:-


    {
        "InitiateKycResponse": {
            "SubHeader": {
                "requestUUID": "e5c3ad32-5f3b-448b-80b5-b1cac",
                "serviceRequestId": "AX.GEN.ICC.INITI.KYC",
                "serviceRequestVersion": "1.0",
                "channelId": "XXX"
            },
            "InitiateKycResponseBodyEncrypted": "78RkvQZjecTPdRhoS7eOrP/cYKE4n5MaHzF00QGowYy6iNRGPBinOgFSERmC26lSlVZbt486q4Yqwt7vsMVgr7vBT/pDGQUL2UR9DvU+7//aeVjrezD1fHRFfNm29N1kyyXrqJBNFx32LGUGoQ3Hkg=="
        }
    }
schemes:
  - https
consumes:
  - application/json
produces:
  - application/json
securityDefinitions:
  X-IBM-Client-Id:
    description: ''
    in: header
    name: X-IBM-Client-Id
    type: apiKey
  X-IBM-Client-Secret:
    in: header
    name: X-IBM-Client-Secret
    type: apiKey
security:
  - X-IBM-Client-Id: []
    X-IBM-Client-Secret: []
x-ibm-configuration:
  testable: true
  enforced: true
  cors:
    enabled: true
  assembly:
    finally: []
  phase: realized
  properties:
    target-InitiateKYC:
      value: >-
        https://api-dlp-uat.uat.maximus.axisb.com/gpay/v1/financialservices/credit/application/customer-journey/initiate
      description: ''
      encoded: false
    product-name:
      value: insta-credit-card
  gateway: datapower-api-gateway
  type: rest
  compatibility:
    enforce-required-params: false
    request-headers: true
  activity-log:
    enabled: true
    success-content: activity
    error-content: payload
paths:
  /v1/financialservices/credit/application/kyc/initiate:
    post:
      responses:
        '200':
          description: 200 OK
          schema:
            $ref: '#/definitions/initiateKycRes'
      parameters:
        - name: input-request
          in: body
          schema:
            $ref: '#/definitions/initiateKycReq'
definitions:
  initiateKycReq:
    type: object
    properties:
      InitiateKycRequest:
        type: object
        properties:
          SubHeader:
            $ref : './schemas/SubHeader.yaml'
            required:
              - requestUUID
              - serviceRequestId
              - serviceRequestVersion
              - channelId
          InitiateKycRequestBody:
            type: object
            properties:
              data:
                type: object
                properties:
                  phoneNumber:
                    type: string
                  applicationId:
                    type: string
                  callbackUrl:
                    type: string
                required:
                  - phoneNumber
                  - applicationId
                  - callbackUrl
            required:
              - data
        required:
          - SubHeader
          - InitiateKycRequestBody
    example: >-
      {"InitiateKycRequest":{"SubHeader":{"requestUUID":"e5c3ad32-5f3b-448b-80b5-b1cac","serviceRequestId":"AX.GEN.ICC.INITI.KYC","serviceRequestVersion":"1.0","channelId":"XXX"},"InitiateKycRequestBody":{"data":{"phoneNumber":"91xxxxxxxxxx","applicationId":"some-application-id","callbackUrl":"partner-callback-url"}}}}
    required:
      - InitiateKycRequest
  initiateKycRes:
    type: object
    properties:
      InitiateKycResponse:
        type: object
        properties:
          SubHeader:
            $ref : './schemas/SubHeader.yaml'
            required:
              - requestUUID
              - serviceRequestId
              - serviceRequestVersion
              - channelId
          InitiateKycResponseBody:
            type: object
            properties:
              data:
                type: object
                properties:
                  authCode:
                    type: string
                  expirationTime:
                    type: object
                    properties:
                      epochMillis:
                        type: number
                required:
                  - authCode
                  - expirationTime
            required:
              - data
        required:
          - SubHeader
          - InitiateKycResponseBody
    example: >-
      {"InitiateKycResponse":{"SubHeader":{"requestUUID":"e5c3ad32-5f3b-448b-80b5-b1cac","serviceRequestId":"AX.GEN.ICC.INITI.KYC","serviceRequestVersion":"1.0","channelId":"XXX"},"InitiateKycResponseBody":{"data":{"authCode":"727d8bee-7f03-4225-8d54-60407326a6ba","expirationTime":1234567890}}}}
    required:
      - InitiateKycResponse
tags: []
basePath: /api/insta-credit-card
