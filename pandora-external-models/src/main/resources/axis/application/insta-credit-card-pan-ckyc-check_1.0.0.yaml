swagger: '2.0'
info:
  x-ibm-name: insta-credit-card-pan-ckyc-check
  title: Insta Credit Card Pan CKYC Check
  version: 1.0.0
  description: >-
    API Description:- This API is used to pan validation and to fetch ckyc
    details for NTB customers. If user wants to fetch CKYC details, they have to
    trigger initiate challenge for OTP generation and has to pass OTP and
    tokenRefId in request, else OTP generation is not required for only pan
    validation case.


    URL:- https:
    //sakshamuat.axisbank.co.in/gateway/api/insta-credit-card/v3/financialservices/get-pan/ckycdetails


    Method:- POST


    Non-encrypted JSON request sample:-


    {
        "GetPanCkycDetailsRequest": {
            "SubHeader": {
                "requestUUID": "0f60cdcf-4678-4aa9-b3f5-461237",
                "serviceRequestId": "AX.GEN.ICC.PAN.CKYC",
                "serviceRequestVersion": "1.0",
                "channelId": "XXX"
            },
            "GetPanCkycDetailsRequestBody": {
                "data": {
                    "pan": "**********",
                    "phoneNumber": "************",
                    "dob": "30-03-1999",
                    "checkCKYC": "Y/N",
                    "tokenReferenceId": "2087b4cf4bcf90eec3b8d",
                    "tokenValue": "123456"
                }
            }
        }
    }


    Encrypted JSON request sample:-


    {
        "GetPanCkycDetailsRequest": {
            "SubHeader": {
                "requestUUID": "0f60cdcf-4678-4aa9-b3f5-461237",
                "serviceRequestId": "AX.GEN.ICC.PAN.CKYC",
                "serviceRequestVersion": "1.0",
                "channelId": "XXX"
            },
            "GetPanCkycDetailsRequestBodyEncrypted": "hrcjElD2sHzSTXN2cLKJOAvx9xkEqEDE7fc7gDYDeCAhrbjJvyq1TAL45GrHDdBXcCOVjAnxX8DvAlkrVNS4wKCHOaRzWOw06eskobcKCdvSB4vzjt5xm02N6ckRYJMZdgTq2MQ5u4A6KE2D0tdxpXbXKzcOoCNAcm+/aTJFx+mKGM4f+XT10aqZ/nrIxppg"
        }
    }


    Non-encrypted JSON response sample:-


    {
        "GetPanCkycDetailsResponse": {
            "SubHeader": {
                "requestUUID": "0f60cdcf-4678-4aa9-b3f5-461237",
                "serviceRequestId": "AX.GEN.ICC.PAN.CKYC",
                "serviceRequestVersion": "1.0",
                "channelId": "XXX"
            },
            "GetPanCkycDetailsResponseBody": {
                "data": {
                    "panVerification": "success",
                    "customerDetails": {
                        "fullName": "MR SWAPNEET LAHOTI",
                        "maidenPrefix": "MR",
                        "maidenFName": "SIT",
                        "maidenMName": "KO",
                        "maidenLName": "KI",
                        "fatherPrefix": "MR",
                        "fatherFName": "ko",
                        "fatherMName": "ji",
                        "fatherLName": "kp",
                        "fatherFullName": "Mr ko kp",
                        "motherPrefix": "MS",
                        "motherFName": "Kos",
                        "motherMName": "NI",
                        "motherLName": "Ki",
                        "motherFullName": "MS Kos Ki",
                        "gender": "M",
                        "dob": "24-03-1995",
                        "corresLine1": "jj lane",
                        "corresLine2": "some area",
                        "corresLine3": "near park",
                        "corresCity": "Thane",
                        "corresDist": "Thane",
                        "corresState": "MH",
                        "corresCountry": "IN",
                        "corresPin": "400609",
                        "corresPOA": "09"
                    }
                }
            }
        }
    }


    Encrypted JSON response sample:-


    {
        "GetPanCkycDetailsResponse": {
            "SubHeader": {
                "requestUUID": "0f60cdcf-4678-4aa9-b3f5-461237",
                "serviceRequestId": "AX.GEN.ICC.PAN.CKYC",
                "serviceRequestVersion": "1.0",
                "channelId": "XXX"
            },
            "GetPanCkycDetailsResponseBodyEncrypted": "mHD7dkYGsXZ0/zaRSdaxS6t3C/MUutiEIGSsAI/FBi39UzhWsPpMH6PQbKOHle5djqi6nf81ZYrSs0Oxuml6Z6KqmkH1AJ18NHQFLI6mt0vEZZpug0Opu3SXH4Q2T0ARnUq3OJVfstXjqBDU2rAMjnH9iFEV8aA9vYwimUOfB1s7MqqYj9Dt1CC6dDiqurg/ogFJVCSNYURpqjYAyOF8SY7jjS2+TC2kSz8THvHbjT8Tr/1AraV6dgFAiCs8ty9XigG48vMnWFMeprAcR+mq9891LoOJhSU186sIqepMoAxjMD9BhGIIJ7ctAfnJO8Ps5kc6uZF/njjCIDqmNyBskM4z97kOeeN1yLOBym6N/MDBmTENNGBKwWZOLICahO/4CfsnJSn9WoyZNTL8HpndeXyR2E1MWRV5aR3ofwq6eWsFTIUuVpWFdwk48hhsu+99CuW5zlPrOu/fO1YlBH2YeP9PPOmbFhgxoULNZNiOsi6xpINyR+DGUmSzyVXGTPZJWivl0G3IymZevBrSpK3cereURM7StCAmQsIrTaoI5lfjfUpSBomHjhztWQ4rSka3pUOigxHy3SVk6vH3SmnC3JWfcY5Uqw3TBB7YaHj5v91KD4SBj3Ikit67B6JtSAfLOHKohcuVzaMwENpaQmkUj2wco3IqZL39BpUEwzIwQRfIhGwL2WO9ZoCWROvRfJ7NRCbBAx0F0QI8z1z1ItrbDFGjEf5Lx4NQ/joBwYUv5eYgCx3lznrYgJvmVikLrqpjjqPN3AowGoNr6sArYXqxEYxNORoqylYarqq5jaeonUJL3gKAYuS52G9jc5+PAyl/bw6SajSiG0FgkQtBumSYOg=="
        }
    }
schemes:
  - https
host: $(catalog.host)
basePath: /api/insta-credit-card
consumes:
  - application/json
produces:
  - application/json
securityDefinitions:
  X-IBM-Client-Secret:
    type: apiKey
    description: ''
    in: header
    name: X-IBM-Client-Secret
  X-IBM-Client-Id:
    type: apiKey
    in: header
    name: X-IBM-Client-Id
security:
  - X-IBM-Client-Id: []
    X-IBM-Client-Secret: []
x-ibm-configuration:
  testable: true
  enforced: true
  cors:
    enabled: true
  assembly:
    finally: []
  phase: realized
  properties:
    target-panVerification:
      value: https://esbuat2.axisb.com/json/v2/nsdl-panverification
      description: ''
      encoded: false
    target-ckycDownload:
      value: https://esbuat2.axisb.com/cersaiesbservice/ckyc/v2/download
      description: ''
      encoded: false
    target-ckycSearch:
      value: https://esbuat2.axisb.com/cersaiesbservice/ckyc/v2/search
      description: ''
      encoded: false
    target-validateotp-chub:
      value: https://esbuat2.axisb.com/chub-otp/enc/validate-otp
      description: ''
      encoded: false
    product-name:
      description: ''
      encoded: false
      value: insta credit card
    target-validateotp:
      value: https://esbuat2.axisb.com/JSON/OTPService
      description: ''
      encoded: false
  type: rest
  gateway: datapower-api-gateway
  activity-log:
    enabled: true
    success-content: activity
    error-content: payload
paths:
  /v3/financialservices/get-pan/ckycdetails:
    post:
      responses:
        '200':
          description: 200 OK
          schema:
            $ref: '#/definitions/panCkycDetailsRes'
      parameters:
        - name: request
          required: false
          in: body
          schema:
            $ref: '#/definitions/panCkycDetailsReq'
definitions:
  panCkycDetailsReq:
    description: ''
    type: object
    properties:
      GetPanCkycDetailsRequest:
        type: object
        properties:
          SubHeader:
            $ref : './schemas/SubHeader.yaml'
            required:
              - requestUUID
              - serviceRequestId
              - serviceRequestVersion
              - channelId
          GetPanCkycDetailsRequestBody:
            type: object
            properties:
              data:
                type: object
                properties:
                  phoneNumber:
                    type: string
                  tokenReferenceId:
                    type: string
                  tokenValue:
                    type: string
                  pan:
                    type: string
                  dob:
                    type: string
                  checkCKYC:
                    type: string
                required:
                  - phoneNumber
                  - pan
                  - checkCKYC
            required:
              - data
        required:
          - SubHeader
          - GetPanCkycDetailsRequestBody
    example: >-
      {"GetPanCkycDetailsRequest":{"SubHeader":{"requestUUID":"string","serviceRequestId":"string","serviceRequestVersion":"string","channelId":"string","additionalProp1":{}},"GetPanCkycDetailsRequestBody":{"data":{"phoneNumber":"91828380xxxx","tokenReferenceId":"asdfe3r434ge","tokenValue":"123456","pan":"BRNPK960xx","dob":"30-03-1999","checkCKYC":"Y/N"}}}}
    required:
      - GetPanCkycDetailsRequest
  panCkycDetailsRes:
    type: object
    properties:
      GetPanCkycDetailsResponse:
        type: object
        properties:
          SubHeader:
            $ref : './schemas/SubHeader.yaml'
          GetPanCkycDetailsResponseBody:
            type: object
            properties:
              data:
                type: object
                properties:
                  panVerification:
                    type: string
                  customerDetails:
                    type: object
                    properties:
                      fullName:
                        type: string
                      maidenPrefix:
                        type: string
                      maidenFName:
                        type: string
                      maidenMName:
                        type: string
                      maidenLName:
                        type: string
                      fatherPrefix:
                        type: string
                      fatherFName:
                        type: string
                      fatherMName:
                        type: string
                      fatherLName:
                        type: string
                      fatherFullName:
                        type: string
                      motherPrefix:
                        type: string
                      motherFName:
                        type: string
                      motherMName:
                        type: string
                      motherLName:
                        type: string
                      motherFullName:
                        type: string
                      gender:
                        type: string
                      dob:
                        type: string
                      corresLine1:
                        type: string
                      corresLine2:
                        type: string
                      corresLine3:
                        type: string
                      corresCity:
                        type: string
                      corresDist:
                        type: string
                      corresState:
                        type: string
                      corresCountry:
                        type: string
                      corresPin:
                        type: string
                      corresPOA:
                        type: string
                required:
                  - panVerification
                  - fullName
                  - dob
                  - motherPrefix
                  - motherFName
                  - motherFullName
                  - gender
                  - orgName
                  - corresLine1
                  - corresCity
                  - corresDist
                  - corresState
                  - corresCountry
                  - corresPOA
    example: >-
      {"GetPanCkycDetailsResponse":{"SubHeader":{"requestUUID":"0f60cdcf-4678-4aa9-b3f5-461237","serviceRequestId":"AX.GEN.ICC.PAN.CKYC","serviceRequestVersion":"1.0","channelId":"XXX"},"GetPanCkycDetailsResponseBody":{"data":{"panVerification":"success","customerDetails":{"fullName":"MR
      SWAPNEET
      LAHOTI","maidenPrefix":"MR","maidenFName":"SIT","maidenMName":"KO","maidenLName":"KI","fatherPrefix":"MR","fatherFName":"ko","fatherMName":"ji","fatherLName":"kp","fatherFullName":"Mr
      ko
      kp","motherPrefix":"MS","motherFName":"Kos","motherMName":"NI","motherLName":"Ki","motherFullName":"MS
      Kos Ki","gender":"M","dob":"24-03-1995","corresLine1":"jj
      lane","corresLine2":"some area","corresLine3":"near
      park","corresCity":"Thane","corresDist":"Thane","corresState":"MH","corresCountry":"IN","corresPin":"400609","corresPOA":"09"}}}}}
  error-response:
    type: object
    properties:
      GetPanCkycDetailsResponse:
        type: object
        properties:
          SubHeader:
            $ref : './schemas/SubHeader.yaml'
          GetPanCkycDetailsResponseBody:
            type: object
            properties:
              data:
                type: object
                properties:
                  panVerification:
                    type: string
                  customerDetails:
                    type: object
                    properties:
                      errorDescription:
                        type: string
                      errorCode:
                        type: string
    example: >-
      {"GetPanCkycDetailsResponse":{"SubHeader":{"requestUUID":"2f653b9b-f42b-4e23-b94f-7e50a4fc5ca3","serviceRequestId":"API.API.CUST.DTLS","serviceRequestVersion":"1.0","channelId":"XXX"},"GetPanCkycDetailsResponseBody":{"data":{"panVerification":"SUCCESS","customerDetails":{"errorDescription":"Error
      from backend - 34","errorCode":"SCCSYS"}}}}}
tags:
  - name: Category
    description: Defines category of api
    externalDocs:
      url: Credit Card
