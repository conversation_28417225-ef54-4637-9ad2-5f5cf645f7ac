package com.flipkart.fintech.pandora.service.client.Ivr.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.flipkart.fintech.pandora.service.client.Ivr.request.enums.Status;

@JsonIgnoreProperties(ignoreUnknown = true)
public class UbonaResponse {

    private Status status;

    public Status getStatus() {
        return status;
    }

    public void setStatus(Status status) {
        this.status = status;
    }
}