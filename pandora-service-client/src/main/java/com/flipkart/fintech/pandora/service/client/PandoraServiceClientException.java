package com.flipkart.fintech.pandora.service.client;

import com.fxmart.offers.model.svcresponse.ResponseCode;

import javax.ws.rs.WebApplicationException;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

/**
 * Created by aniruddha.sharma on 12/09/17.
 */
public class PandoraServiceClientException extends WebApplicationException {
    private String downstreamRequest;
    private String downstreamResponse;
    private String downstreamEndpoint;
    private Response.Status responseCode;
    public PandoraServiceClientException() {
        super();
    }

    public PandoraServiceClientException(String msg) {
        super(msg);
    }

    public PandoraServiceClientException(Response.Status responseCode,String message) {
        super(message);
    }

    public PandoraServiceClientException(String msg, String downstreamEndpoint, String downstreamRequest, String downstreamResponse) {
        super(msg);
        this.responseCode = Response.Status.INTERNAL_SERVER_ERROR;
        this.downstreamEndpoint = downstreamEndpoint;
        this.downstreamRequest = downstreamRequest;
        this.downstreamResponse = downstreamResponse;
    }

    public PandoraServiceClientException(Response.Status responseCode,String msg, String downstreamEndpoint, String downstreamRequest, String downstreamResponse) {
        super(msg);
        this.responseCode = responseCode;
        this.downstreamEndpoint = downstreamEndpoint;
        this.downstreamRequest = downstreamRequest;
        this.downstreamResponse = downstreamResponse;
    }

    public <T> PandoraServiceClientException(int statusCode, T entity){
        super(Response.status(statusCode).
                entity(entity).type(MediaType.APPLICATION_JSON_TYPE).build());
    }

    public <T> PandoraServiceClientException(Response.Status status, T entity){
        super(Response.status(status.getStatusCode()).
                entity(entity).type(MediaType.APPLICATION_JSON_TYPE).build());
    }

    public PandoraServiceClientException(Throwable cause) {
        super(cause);
    }

    public String getDownstreamRequest() {
        return downstreamRequest;
    }

    public String getDownstreamResponse() {
        return downstreamResponse;
    }

    public String getDownstreamEndpoint() {
        return downstreamEndpoint;
    }

    public Response.Status getResponseCode() {
        return responseCode;
    }
}
