package com.flipkart.fintech.pandora.service.client;

import static com.flipkart.fintech.pandora.service.client.cbc.kotak.Constants.CBC_KOTAK_CLIENT;

import com.amazonaws.ClientConfiguration;
import com.amazonaws.Protocol;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3Client;
import com.amazonaws.services.s3.S3ClientOptions;
import com.flipkart.fintech.pandora.service.client.auth.*;
import com.flipkart.fintech.pandora.service.client.cbc.axis.SmAxisCbcConfiguration;
import com.flipkart.fintech.pandora.service.client.cbc.axis.client.onboarding.AxisOnboardingClient;
import com.flipkart.fintech.pandora.service.client.cbc.axis.client.onboarding.AxisOnboardingClientImpl;
import com.flipkart.fintech.pandora.service.client.cbc.axis.client.onboarding.MockAxisOnboardingClientImp;
import com.flipkart.fintech.pandora.service.client.cbc.kotak.KotakInterceptor;
import com.flipkart.fintech.pandora.service.client.cbc.kotak.SmKotakCbcConfiguration;
import com.flipkart.fintech.pandora.service.client.cbc.kotak.client.onboarding.KotakOnboardingClient;
import com.flipkart.fintech.pandora.service.client.cbc.kotak.client.onboarding.KotakOnboardingClientImpl;
import com.flipkart.fintech.pandora.service.client.cbc.kotak.client.onboarding.MockKotakOnboardingClientImpl;
import com.flipkart.fintech.pandora.service.client.cbc.kotak.encryption.KotakEncryptionUtil;
import com.flipkart.fintech.pandora.service.client.config.MpockketClientConfig;
import com.flipkart.fintech.pandora.service.client.configuration.SandboxLendersPlConfiguration;
import com.flipkart.fintech.pandora.service.client.digio.config.DigioConfiguration;
import com.flipkart.fintech.pandora.service.client.digio.transformers.request.KycRequestTransformer;
import com.flipkart.fintech.pandora.service.client.digio.transformers.request.MandateRequestTransformer;
import com.flipkart.fintech.pandora.service.client.digio.transformers.response.MandateResponseToDtoTransformer;
import com.flipkart.fintech.pandora.service.client.digio.transformers.response.MandateResponseTransformer;
import com.flipkart.fintech.pandora.service.client.digio.transformers.response.*;
import com.flipkart.fintech.pandora.service.client.digitapAi.DigitapAiClient;
import com.flipkart.fintech.pandora.service.client.digitapAi.DigitapAiConfiguration;
import com.flipkart.fintech.pandora.service.client.digitapAi.DigitapClient;
import com.flipkart.fintech.pandora.service.client.digitapAi.MockDecoratedDigitapClient;
import com.flipkart.fintech.pandora.service.client.document.DocumentInteractionClient;
import com.flipkart.fintech.pandora.service.client.document.DocumentInteractionWithAwsAndPinaka;
import com.flipkart.fintech.pandora.service.client.document.DocumentStoreConfiguration;
import com.flipkart.fintech.pandora.service.client.encryptor.*;
import com.flipkart.fintech.pandora.service.client.filters.*;
import com.flipkart.fintech.pandora.service.client.hyperverge.HyperVergeConfiguration;
import com.flipkart.fintech.pandora.service.client.interceptors.DigitapEncryptionInterceptor;
import com.flipkart.fintech.pandora.service.client.interceptors.EncryptionInterceptor;
import com.flipkart.fintech.pandora.service.client.kotak.kyc.KYCClient;
import com.flipkart.fintech.pandora.service.client.kotak.kyc.KotakKYCClient;
import com.flipkart.fintech.pandora.service.client.kotak.loan_activation.KotakLoanActivationClient;
import com.flipkart.fintech.pandora.service.client.kotak.loan_activation.LoanActivationClient;
import com.flipkart.fintech.pandora.service.client.kotak.penny_drop.KotakPennyDropClient;
import com.flipkart.fintech.pandora.service.client.kotak.penny_drop.PennyDropClient;
import com.flipkart.fintech.pandora.service.client.mapper.sandbox.ErrorMapper;
import com.flipkart.fintech.pandora.service.client.mapper.sandbox.SandboxErrorMapper;
import com.flipkart.fintech.pandora.service.client.pl.IdfcConfiguration;
import com.flipkart.fintech.pandora.service.client.pl.client.*;
import com.flipkart.fintech.pandora.service.client.pl.ebc.IdfcEbcServiceClient;
import com.flipkart.fintech.pandora.service.client.pl.ebc.IdfcEbcServiceClientImpl;
import com.flipkart.fintech.pandora.service.client.pl.kyc.IdfcClient;
import com.flipkart.fintech.pandora.service.client.pl.kyc.IdfcClientImpl;
import com.flipkart.fintech.pandora.service.client.pl.kyc.IdfcClientV2;
import com.flipkart.fintech.pandora.service.client.pl.kyc.IdfcClientV2Impl;
import com.flipkart.fintech.pandora.service.client.razorpay.RazorpayClient;
import com.flipkart.fintech.pandora.service.client.razorpay.RazorpayClientImpl;
import com.flipkart.fintech.pinaka.client.PinakaClientConfig;
import com.google.inject.AbstractModule;
import com.google.inject.Inject;
import com.google.inject.Provides;
import com.google.inject.Singleton;
import com.google.inject.name.Named;
import com.google.inject.name.Names;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;
import javax.net.ssl.SSLContext;
import javax.ws.rs.client.Client;
import javax.ws.rs.client.ClientBuilder;
import javax.ws.rs.client.WebTarget;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.EnumUtils;
import org.glassfish.jersey.SslConfigurator;
import org.glassfish.jersey.client.ClientProperties;
import org.glassfish.jersey.media.multipart.MultiPartFeature;
import org.modelmapper.ModelMapper;

/**
 * Created by sujeetkumar.r on 09/07/18.
 */

@Slf4j
public class PandoraServiceClientModule extends AbstractModule {


    private final IdfcConfiguration idfcConfiguration;
    private final List<SandboxLendersPlConfiguration> sandboxLendersPlConfigurationList;

    private final HyperVergeConfiguration hyperVergeConfiguration;
    private final DocumentStoreConfiguration documentStoreConfiguration;
    private final PinakaClientConfig pinakaClientConfig;
    private final SmAxisCbcConfiguration smAxisCbcConfiguration;
    private final SmKotakCbcConfiguration smKotakCbcConfiguration;
    private final DigitapAiConfiguration digitapAiConfiguration;

    @Inject
    public PandoraServiceClientModule(IdfcConfiguration idfcConfiguration, HyperVergeConfiguration hyperVergeConfiguration, DocumentStoreConfiguration documentStoreConfiguration,
                                      PinakaClientConfig pinakaClientConfig, List<SandboxLendersPlConfiguration> sandboxLendersPlConfigurationList, SmAxisCbcConfiguration smAxisCbcConfiguration, SmKotakCbcConfiguration smKotakCbcConfiguration, DigitapAiConfiguration digitapAiConfiguration) {
        this.idfcConfiguration = idfcConfiguration;
        this.hyperVergeConfiguration = hyperVergeConfiguration;
        this.documentStoreConfiguration = documentStoreConfiguration;
        this.sandboxLendersPlConfigurationList = sandboxLendersPlConfigurationList;
        this.pinakaClientConfig = pinakaClientConfig;
        this.smAxisCbcConfiguration = smAxisCbcConfiguration;
        this.digitapAiConfiguration = digitapAiConfiguration;
        this.smKotakCbcConfiguration = smKotakCbcConfiguration;
    }


    @Override
    protected void configure() {
        bind(IdfcClient.class).to(IdfcClientImpl.class);
        bind(IdfcClientV2.class).to(IdfcClientV2Impl.class);
        bind(IdfcEbcServiceClient.class).to(IdfcEbcServiceClientImpl.class);
        bind(RazorpayClient.class).to(RazorpayClientImpl.class);
        bind(KYCClient.class).to(KotakKYCClient.class);
        bind(PennyDropClient.class).to(KotakPennyDropClient.class);
        bind(LoanActivationClient.class).to(KotakLoanActivationClient.class);
        bind(SandboxLendersPlClient.class).to(SmSandboxLendersPlClient.class);
        bind(String.class).annotatedWith(Names.named("KID")).toInstance(idfcConfiguration.getKid());
        bind(String.class).annotatedWith(Names.named("JwtAudience")).toInstance(idfcConfiguration.getJwtTokenAudience());
        bind(String.class).annotatedWith(Names.named("IdfcCallerId")).toInstance(idfcConfiguration.getCallerId());
        bind(String.class).annotatedWith(Names.named("IdfcApiUrl")).toInstance(idfcConfiguration.getProxyUriV2());
        bind(String.class).annotatedWith(Names.named("HyperVergeAppId")).toInstance(hyperVergeConfiguration.getAppId());
        bind(String.class).annotatedWith(Names.named("HyperVergeAppKey")).toInstance(hyperVergeConfiguration.getAppKey());
        bind(String.class).annotatedWith(Names.named("HyperVergeAuthUrl")).toInstance(hyperVergeConfiguration.getAuthUrl());
        bind(String.class).annotatedWith(Names.named("HyperVergeApiUrl")).toInstance(hyperVergeConfiguration.getApiUrl());
        bind(String.class).annotatedWith(Names.named("HyperVergeUrlPath")).toInstance(hyperVergeConfiguration.getUrlPath());
        bind(String.class).annotatedWith(Names.named("DigitapAiClientId")).toInstance(digitapAiConfiguration.getClientId());
        bind(String.class).annotatedWith(Names.named("DigitapAiClientSecret")).toInstance(digitapAiConfiguration.getClientSecret());
        bind(String.class).annotatedWith(Names.named("DigitapAiClientUrl")).toInstance(digitapAiConfiguration.getUrl());
        bind(DataEncryptorDecryptor.class).annotatedWith(Names.named("aes")).to(IdfcAesEncryptorDecryptor.class);
        bind(DataEncryptorDecryptor.class).annotatedWith(Names.named("AesEncryptorDecryptor")).to(AesEncryptorDecryptor.class);
        bind(DocumentInteractionClient.class).to(DocumentInteractionWithAwsAndPinaka.class);
        bind(String.class).annotatedWith(Names.named("kycBucketName")).toInstance(documentStoreConfiguration.getKycBucketName());
        bind(String.class).annotatedWith(Names.named("selfieBucketName")).toInstance(documentStoreConfiguration.getSelfieBucketName());
        bind(IdfcPlClient.class).annotatedWith(Names.named("default")).to(SmIdfcPlClient.class);
        bind(AxisOnboardingClient.class).annotatedWith(Names.named("default")).to(AxisOnboardingClientImpl.class);
        if(smAxisCbcConfiguration.isMockTestingON()) {
            bind(AxisOnboardingClient.class).to(MockAxisOnboardingClientImp.class);
        }else {
            bind(AxisOnboardingClient.class).to(AxisOnboardingClientImpl.class);
        }
        if (idfcConfiguration.getMockBucketConfig() != null) {
            bind(IdfcPlClient.class).to(MockConfigDecoraterdIdfcPlClient.class);
        } else {
            bind(IdfcPlClient.class).to(SmIdfcPlClient.class);
        }

        bind(KotakOnboardingClient.class).annotatedWith(Names.named("default")).to(KotakOnboardingClientImpl.class);
        if(smKotakCbcConfiguration.isMockTestingON()) {
            bind(KotakOnboardingClient.class).to(MockKotakOnboardingClientImpl.class);
        }else {
            bind(KotakOnboardingClient.class).to(KotakOnboardingClientImpl.class);
        }

        bind(DigitapClient.class).annotatedWith(Names.named("directDigitapIntegrationClient")).to(DigitapAiClient.class);

        if(digitapAiConfiguration.getIsMockEnabled() != null && Boolean.TRUE.equals(digitapAiConfiguration.getIsMockEnabled())) {
            bind(DigitapClient.class).to(MockDecoratedDigitapClient.class);
        } else {
            bind(DigitapClient.class).to(DigitapAiClient.class);
        }

    }

    @Provides
    @Singleton
    public AmazonS3 providesAmazonS3() {
        AWSCredentials credentials = new BasicAWSCredentials(documentStoreConfiguration.getAccessKey(), documentStoreConfiguration.getSecretKey());
        ClientConfiguration clientConfiguration = new ClientConfiguration();
        clientConfiguration.setProtocol(Protocol.HTTPS);
        clientConfiguration.setMaxConnections(documentStoreConfiguration.getMaxConnections());
        clientConfiguration.setConnectionTimeout(documentStoreConfiguration.getConnectionTimeout());
        AmazonS3 amazonS3 = new AmazonS3Client(credentials, clientConfiguration);
        amazonS3.setEndpoint(documentStoreConfiguration.getEndPoint());
        amazonS3.setS3ClientOptions(new S3ClientOptions().withPathStyleAccess(true));
        return amazonS3;
    }

    @Provides
    @Named("pinakaWebTarget")
    public WebTarget getPinakaWebTarget() {
        Client client = ClientBuilder.newBuilder().register(MultiPartFeature.class).build();
        return client.target(pinakaClientConfig.getUrl());
    }

    @Provides
    @Named("DigitapAiWebTarget")
    public WebTarget provideDigitapAiWebTarget(@Named("DigitapAiClientUrl") String url) {
        Client client = ClientBuilder.newClient();
        JweEncryptorDecryptor jweEncryptorDecryptor = new JweEncryptorDecryptor(digitapAiConfiguration.getDigitapPublicKey(), digitapAiConfiguration.getSmPrivateKey());
        DigitapEncryptionInterceptor encryptionInterceptor = new DigitapEncryptionInterceptor(jweEncryptorDecryptor);
        client.register(encryptionInterceptor);
        client.property(ClientProperties.CONNECT_TIMEOUT, 15000);
        client.property(ClientProperties.READ_TIMEOUT, 15000);
        return client.target(url);
    }


    @Provides
    @Singleton
    @Named("SmallLendersPlConfigurationList")
    public List<SandboxLendersPlConfiguration> provideSmallLendersPlConfigurationList(){
        return this.sandboxLendersPlConfigurationList;
    }

    @Provides
    @Named("OAuthClientCommonMap")
    @Singleton
    public Map<Scope, Client> provideCommonOAuthClientForSandboxLenders(SandboxLendersHeaderProvider sandboxLendersHeaderProvider,
                                                                        SmallLendersResponseHeaderModifier responseHeaderModifier,
                                                                        SandboxLendersContextProvider sandboxLendersContextProvider,
                                                                        MpockketClientConfig mpockketClientConfig){
        Map<Scope, Client> clientMap = new HashMap<>();

        this.sandboxLendersPlConfigurationList.forEach((config) -> {
            if(EnumUtils.isValidEnum(Scope.class, config.getScope())) {

                Scope scope = EnumUtils.getEnum(Scope.class, config.getScope());
                clientMap.put(scope, new ClientFactory(mpockketClientConfig).getClient(config.getScope(), config, sandboxLendersHeaderProvider, responseHeaderModifier, sandboxLendersContextProvider));
            }
        });


        return clientMap;
    }


    @Provides
    @Named("idfcAadhaarEncryptionKey")
    public String provideAadhaarEncryptionKey() {
        return new String(Base64.getEncoder().encode(idfcConfiguration.getIdfcEncryptionKey().getBytes(StandardCharsets.UTF_8)));
    }

    @Provides
    @Named("idfcSecretKey")
    public String provideIdfcSecretKey() {
        return new String(Base64.getDecoder().decode(idfcConfiguration.getSecretKey()));
    }

    @Provides
    @Named("kotakSecretKey")
    public String provideKotakSecretKey() {
        return smKotakCbcConfiguration.getEncryptionKey();
    }

    @Provides
    @Singleton
    @Named("TokenFetchingClient")
    public Client provideTokenFetchingClient() {
        return ClientBuilder.newClient();
    }

    @Provides
    @Named("TokenServiceURL")
    public String provideTokenServiceURL() {
        return idfcConfiguration.getTokenServiceURL();
    }

    @Provides
    @Named("HyperVergeClient")
    public Client provideHyperVergeClient() {
        Client client = ClientBuilder.newClient();
        client.register(MultiPartFeature.class);
        return client;
    }

    @Provides
    @Named("ExperianClient")
    public Client provideExperianClient(){
        Client client = ClientBuilder.newClient();
        client.register(MultiPartFeature.class);
        client.property(ClientProperties.CONNECT_TIMEOUT, 1000);
        client.property(ClientProperties.READ_TIMEOUT, 7000);
        return client;
    }

    @Provides
    @Named("CreditSaisonClient")
    public Client provideCreditSaisonClient(){
        Client client = ClientBuilder.newClient();
        client.register(MultiPartFeature.class);
        client.property(ClientProperties.CONNECT_TIMEOUT, 10000);
        client.property(ClientProperties.READ_TIMEOUT, 60000);
        return client;
    }

    @Provides
    @Named("idfcClient")
    public Client provideIdfcClient(@Named("aes") DataEncryptorDecryptor dataEncryptor, IdfcHeaderProvider headerProvider, IdfcResponseHeadersModifier responseHeadersModifier) {
        Client client = ClientBuilder.newClient();
        EncryptionInterceptor encryptionInterceptor = new EncryptionInterceptor(dataEncryptor);
        client.register(headerProvider);
        client.register(responseHeadersModifier);
        client.register(encryptionInterceptor);
        return client;
    }

    @Provides
    @Singleton
    @Named("digioClient")
    public WebTarget getDigioClient(DigioConfiguration digioConfiguration, DigioHeaderFilter digioHeaderFilter) {
        ClientBuilder builder = ClientBuilder.newBuilder();
        if(!digioConfiguration.isGcp()) {
            final SslConfigurator sslConfig = SslConfigurator.newInstance()
                    .keyStoreFile(digioConfiguration.getKeyStorePath())
                    .keyPassword(digioConfiguration.getKeyStorePass())
                    .trustStoreFile(digioConfiguration.getTrustStorePath())
                    .trustStorePassword(digioConfiguration.getTrustStorePass());
            SSLContext sslContext = sslConfig.createSSLContext();
            builder.sslContext(sslContext);
        }
        builder.property(ClientProperties.CONNECT_TIMEOUT, 10000);
        builder.property(ClientProperties.READ_TIMEOUT, 60000);
        Client digioClient = builder.build();
        digioClient.register(digioHeaderFilter);
        DataEncryptorDecryptor dataEncryptorDecryptor = new DigioEncryptor(digioConfiguration.getEncryption().getPublicKey(), digioConfiguration.getEncryption().getPrivateKey());
        EncryptionInterceptor encryptionInterceptor = new EncryptionInterceptor(dataEncryptorDecryptor);
        digioClient.register(encryptionInterceptor);
        return digioClient.target(digioConfiguration.getServerUrl());
    }

    @Provides
    @Named(CBC_KOTAK_CLIENT)
    public Client provideSmKotakClient( KotakEncryptionUtil kotakEncryptionUtil) {
        ClientBuilder builder = ClientBuilder.newBuilder();
        builder.property(ClientProperties.CONNECT_TIMEOUT, 30000);
        builder.property(ClientProperties.READ_TIMEOUT, 30000);
        Client client = builder.build();
        KotakInterceptor kotakInterceptor = new KotakInterceptor(kotakEncryptionUtil);
        client.register(kotakInterceptor);
        return client;
    }

    @Provides
    @Named("ClientID")
    public String provideClientId() {
        return idfcConfiguration.getPlClientId();
    }

    @Provides
    @Named("PrivateKey")
    @Singleton
    public String readPrivateKeyFromFile() {
        String path = idfcConfiguration.getPrivateKeyPath();

        try (InputStream stream = getClass().getClassLoader().getResourceAsStream(path);
            InputStreamReader isr = new InputStreamReader(Objects.requireNonNull(stream, "Private key resource not found."), StandardCharsets.UTF_8);
            BufferedReader reader = new BufferedReader(isr)) {

            return reader.lines().collect(Collectors.joining(System.lineSeparator()));
        } catch (IOException e) {
            // Not throwing any exception here as Guice recommends not to throw checked exceptions from providers.
            log.error("Error reading private key from file", e);
            throw new RuntimeException(e.getMessage());
        }
    }

    @Provides
    @Singleton
    public MandateRequestTransformer provideMandateTransformer(ModelMapper modelMapper, DigioConfiguration digioConfiguration) {
        return new MandateRequestTransformer(modelMapper, digioConfiguration);
    }

    @Provides
    @Singleton
    public MandateResponseTransformer provideMandateResponseTransformer(ModelMapper modelMapper) {
        return new MandateResponseTransformer(modelMapper);
    }

    @Provides
    @Singleton
    public KycRequestTransformer provideKycRequestTransformer(ModelMapper modelMapper) {
        return new KycRequestTransformer(modelMapper);
    }

    @Provides
    @Singleton
    public MandateResponseToDtoTransformer provideMandateResponseToDtoTransformer(ModelMapper modelMapper) {
        return new MandateResponseToDtoTransformer(modelMapper);
    }

    @Provides
    @Singleton
    public RPDKycResponseTransformer provideRPDKycResponseTransformer(ModelMapper modelMapper) {
        return new RPDKycResponseTransformer(modelMapper);
    }

    @Provides
    @Singleton
    public CancelRegisteredMandateResponseTransformer provideCancelRegisteredMandateResponseTransformer(ModelMapper modelMapper) {
        return new CancelRegisteredMandateResponseTransformer(modelMapper);
    }

    @Provides
    @Singleton
    public RPDStatusTransformer provideRPDStatusTransformer(ModelMapper modelMapper) {
        return new RPDStatusTransformer(modelMapper);
    }

    @Provides
    @Named("ScopeToErrorMapperMap")
    public Map<Scope, ErrorMapper> provideScopeToErrorMapperMap() {
        Map<Scope, ErrorMapper> scopeToErrorMapperMap = new EnumMap<>(Scope.class);
        // Create a single instance of SandboxErrorMapper to be reused for all lender scopes
        ErrorMapper sandboxErrorMapper = new SandboxErrorMapper();
        sandboxLendersPlConfigurationList.forEach(lenderConfiguration ->
                scopeToErrorMapperMap.put(Scope.valueOf(lenderConfiguration.getScope()), sandboxErrorMapper)
        );
        return scopeToErrorMapperMap;
    }

}