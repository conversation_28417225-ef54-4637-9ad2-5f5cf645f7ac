package com.flipkart.fintech.pandora.service.client.auth;

import com.flipkart.fintech.pandora.service.client.sandbox.ApiParamModel;
import com.google.inject.Inject;
import com.google.inject.name.Named;

import javax.ws.rs.client.Client;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 16/01/24
 */
public class FibeAuthenticationClient extends CommonLendersAuthenticationClient implements LenderAuthenticationClient{

    @Inject
    public FibeAuthenticationClient(@Named("OAuthClientCommonMap") Map<Scope, Client> clientMap, ApiParamModel apiParamModel) {
        super(apiParamModel.getOAuthTokenConfiguration(Scope.LENDING_FIBE),clientMap.get(Scope.LENDING_FIBE));
    }

    @Override
    public String generateAccessToken() {
        return generateAccessToken(this.oAuthTokenConfiguration, this.client);
    }
}