package com.flipkart.fintech.pandora.service.client.auth;

import com.flipkart.fintech.pandora.service.client.sandbox.ApiParamModel;
import com.google.inject.Inject;
import com.google.inject.name.Named;

import javax.ws.rs.client.Client;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 24/04/24
 */
public class FinnableAuthenticationClient extends CommonLendersAuthenticationClient implements LenderAuthenticationClient{

    @Inject
    public FinnableAuthenticationClient(@Named("OAuthClientCommonMap") Map<Scope, Client> clientMap, ApiParamModel apiParamModel) {
        super(apiParamModel.getOAuthTokenConfiguration(Scope.LENDING_FINNABLE),clientMap.get(Scope.LENDING_FINNABLE));

    }

    @Override
    public String generateAccessToken() {
        return generateAccessToken(this.oAuthTokenConfiguration, this.client);
    }
}