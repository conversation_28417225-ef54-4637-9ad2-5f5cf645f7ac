package com.flipkart.fintech.pandora.service.client.auth;


import com.flipkart.fintech.pandora.service.client.sandbox.ApiParamModel;
import com.google.inject.Inject;
import com.google.inject.name.Named;

import javax.ws.rs.client.Client;
import java.util.Map;

public class MoneyviewMfiAuthenticationClient extends CommonLendersAuthenticationClient implements LenderAuthenticationClient{

    @Inject
    public MoneyviewMfiAuthenticationClient(@Named("OAuthClientCommonMap") Map<Scope, Client> clientMap, ApiParamModel apiParamModel) {
        super(apiParamModel.getOAuthTokenConfiguration(Scope.LENDING_MONEYVIEWMFI),clientMap.get(Scope.LENDING_MONEYVIEWMFI));
    }

    @Override
    public String generateAccessToken() {
        return generateAccessToken(this.oAuthTokenConfiguration, this.client);
    }
}

