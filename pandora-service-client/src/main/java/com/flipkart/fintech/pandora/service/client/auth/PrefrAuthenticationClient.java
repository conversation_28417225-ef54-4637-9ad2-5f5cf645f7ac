package com.flipkart.fintech.pandora.service.client.auth;

import com.flipkart.fintech.pandora.service.client.sandbox.ApiParamModel;
import com.google.inject.Inject;
import com.google.inject.name.Named;

import javax.ws.rs.client.Client;
import java.util.Map;

public class PrefrAuthenticationClient extends CommonLendersAuthenticationClient implements LenderAuthenticationClient{

    @Inject
    public PrefrAuthenticationClient(@Named("OAuthClientCommonMap") Map<Scope, Client> clientMap, ApiParamModel apiParamModel) {
        super(apiParamModel.getOAuthTokenConfiguration(Scope.LENDING_PREFR),clientMap.get(Scope.LENDING_PREFR));
    }

    @Override
    public String generateAccessToken() {
        return generateAccessToken(this.oAuthTokenConfiguration, this.client);
    }
}