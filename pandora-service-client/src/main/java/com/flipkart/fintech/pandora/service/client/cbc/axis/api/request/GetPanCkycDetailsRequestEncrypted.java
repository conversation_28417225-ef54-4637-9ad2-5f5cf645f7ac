package com.flipkart.fintech.pandora.service.client.cbc.axis.api.request;

import com.axis.model.SubHeader;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
public class GetPanCkycDetailsRequestEncrypted {
    @JsonProperty("SubHeader")
    SubHeader subHeader;
    @JsonProperty("GetPanCkycDetailsRequestBodyEncrypted")
    String getPanCkycDetailsRequestBodyEncrypted;
}
