package com.flipkart.fintech.pandora.service.client.cbc.axis.api.request;

import com.axis.model.SubHeader;
import lombok.Builder;
import lombok.Data;
import lombok.extern.jackson.Jacksonized;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
public class LCMGetCardDetailsRequestEncrypted {
    @JsonProperty("SubHeader")
    SubHeader subHeader;
    @JsonProperty("LCMGetCardDetailsRequestBodyEncrypted")
    String lcmGetCardDetailsRequestBodyEncrypted;
}
