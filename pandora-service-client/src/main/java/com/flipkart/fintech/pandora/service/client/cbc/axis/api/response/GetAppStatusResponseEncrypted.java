package com.flipkart.fintech.pandora.service.client.cbc.axis.api.response;

import com.axis.model.SubHeader;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;
import lombok.Value;
import lombok.extern.jackson.Jacksonized;
import org.codehaus.jackson.annotate.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Builder
@Jacksonized
public class GetAppStatusResponseEncrypted {
    @JsonProperty("SubHeader")
    SubHeader subHeader;
    @JsonProperty("GetAppStatusResponseBodyEncrypted")
    String getAppStatusResponseBodyEncrypted;
}
