package com.flipkart.fintech.pandora.service.client.cbc.axis.client.onboarding;

import com.axis.model.*;
import com.codahale.metrics.annotation.ExceptionMetered;
import com.codahale.metrics.annotation.Timed;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.pandora.service.client.cbc.axis.AxisEndPoints;
import com.flipkart.fintech.pandora.service.client.cbc.axis.Constants;
import com.flipkart.fintech.pandora.service.client.cbc.axis.Interceptors;
import com.flipkart.fintech.pandora.service.client.cbc.axis.SmAxisCbcConfiguration;
import com.flipkart.fintech.pandora.service.client.cbc.axis.api.response.*;
import com.flipkart.fintech.pandora.service.client.cbc.exceptions.ErrorResponse;
import com.flipkart.fintech.pandora.service.client.cbc.exceptions.CbcClientErrorException;
import com.flipkart.fintech.pandora.service.client.cbc.exceptions.CbcException;
import com.flipkart.fintech.pandora.service.client.cbc.exceptions.CbcServerErrorException;
import com.flipkart.fintech.pandora.service.client.exceptions.NetworkException;
import com.flipkart.kloud.config.DynamicBucket;
import com.google.inject.Inject;
import com.google.inject.name.Named;
import lombok.extern.slf4j.Slf4j;

import javax.ws.rs.InternalServerErrorException;
import javax.ws.rs.client.Client;
import javax.ws.rs.client.Entity;
import javax.ws.rs.client.WebTarget;
import javax.ws.rs.core.*;

import java.util.Map;

import static com.flipkart.fintech.pandora.service.client.cbc.axis.Constants.*;

@Slf4j
public class AxisOnboardingClientImpl implements AxisOnboardingClient {
    private final SmAxisCbcConfiguration clientConfig;
    private final DynamicBucket dynamicBucket;
    private final WebTarget axisWebTarget;
    private final ObjectMapper objectMapper;
    private final Interceptors axisInterceptors;
    @Inject
    public AxisOnboardingClientImpl(SmAxisCbcConfiguration clientConfig, @Named(Constants.CBC_AXIS_CLIENT) Client client,
                                    Interceptors axisInterceptors, DynamicBucket dynamicBucket) {
        this.dynamicBucket = dynamicBucket;
        this.clientConfig = clientConfig;
        this.axisInterceptors = axisInterceptors;
        this.axisWebTarget = client.target(clientConfig.getUrl());
        this.objectMapper = new ObjectMapper();

    }

    private <T> T handleResponse(Response response, Class<T> successEncResponseType) throws CbcServerErrorException, CbcClientErrorException {
        String responseBody = response.readEntity(String.class);
        log.info(ENC_RES_RECEIVED, response.getStatus(), responseBody);
        Response.Status.Family status = response.getStatusInfo().getFamily();

        if (status == Response.Status.Family.SUCCESSFUL) {
            JsonNode jsonNode;
            try {
                jsonNode = objectMapper.readTree(responseBody);
            } catch (JsonProcessingException e) {
                throw new InternalServerErrorException(RES_PARSE_ERROR);
            }
            if (jsonNode.isArray() && jsonNode.size() == 1) {
                return objectMapper.convertValue(jsonNode.get(0), successEncResponseType);
            }
            return objectMapper.convertValue(jsonNode, successEncResponseType);
        } else if (status == Response.Status.Family.CLIENT_ERROR) {

            try {
                ErrorResponse errorResponse = objectMapper.readValue(responseBody,ErrorResponse.class);
                throw new CbcClientErrorException(response.getStatus(), errorResponse);
            }catch (JsonProcessingException e) {
                throw new InternalServerErrorException(RES_PARSE_ERROR);
            }
        } else if (status == Response.Status.Family.SERVER_ERROR) {
            try {
                ErrorResponse errorResponse = objectMapper.readValue(responseBody,ErrorResponse.class);
                throw new CbcClientErrorException(response.getStatus(), errorResponse);
            }catch (JsonProcessingException e) {
                throw new InternalServerErrorException(RES_PARSE_ERROR);
            }
        } else {
            throw new NetworkException(UNEXPECTED_RESPONSE_FROM_SERVER);
        }
    }

    private <T, E> E getPostResponse(T request, Class<E> responseClass, String path,
                                     MultivaluedMap<String, Object> headers) throws CbcException {
        log.info("Enc request :{}",request);
        Response response = null;
        try {
            response = axisWebTarget.path(path)
                    .request(MediaType.APPLICATION_JSON)
                    .headers(headers)
                    .post(Entity.json(request));
            return handleResponse(response, responseClass);
        } catch (CbcClientErrorException | CbcServerErrorException e) {
            log.error(FAIL_TO_PROCESS_REQ_RES, e);
            throw e;
        } catch (Exception e) {
            log.error(FAIL_TO_PROCESS_REQ_RES, e);
            throw new CbcException(ErrorResponse.builder().errorCode(FAILURE).errorDescription(NET_ISSUE).build());
        } finally {
            if (response != null) {
                response.close();
            }
        }
    }


    @Timed
    @ExceptionMetered
    @Override
    public GetAppStatusOutput fetchApplicationStatus(GetAppStatusInputGetAppStatusRequest request) throws CbcException{
        log.info("fetchApplicationStatus Request : {}" , request );
        GetAppStatusResponseEnc getAppStatusResponseEnc =  getPostResponse(axisInterceptors.requestInterceptor(request),GetAppStatusResponseEnc.class,
                AxisEndPoints.GET_APPLICATION_STATUS.getApiPath(),getHeaders());
        GetAppStatusOutput response = axisInterceptors.responseInterceptor(getAppStatusResponseEnc);
        log.info("fetchApplicationStatus response : {}" , response );
        return  response;
    }

    @Timed
    @ExceptionMetered
    @Override
    public GetOfferOutput fetchOfferDetails(GetOfferInputGetOfferRequest request) throws CbcException {
        log.info("fetchOfferDetails Request : {}" ,request );
        GetOfferResponseEnc getOfferResponseEnc = getPostResponse(axisInterceptors.requestInterceptor(request), GetOfferResponseEnc.class,
                AxisEndPoints.GET_OFFER_DETAILS.getApiPath(), getHeaders());
        GetOfferOutput response= axisInterceptors.responseInterceptor(getOfferResponseEnc);
        log.info("fetchOfferDetails Response : {}" ,response );
        return response;
    }

    @Timed
    @ExceptionMetered
    @Override
    public GetUserDetailResponse fetchUserDetails(GetUserDetailRequestGetUserDtlsRequest request) throws CbcException {
        log.info("fetchUserDetails Request : {}" ,request );
        GetUserDtlsResponseEnc getUserDtlsResponseEnc = getPostResponse(axisInterceptors.requestInterceptor(request),GetUserDtlsResponseEnc.class,
                AxisEndPoints.GET_USER_DETAILS.getApiPath(), getHeaders());
        GetUserDetailResponse response = axisInterceptors.responseInterceptor(getUserDtlsResponseEnc);
        log.info("fetchUserDetails response : {}" ,response );
        return response;
    }

    @Timed
    @ExceptionMetered
    @Override
    public InitiateChallengeOutput initiateChallenge(InitiateChallengeInputInitiateChallengeRequest request) throws CbcException {
        log.info("initiateChallenge Request : {}" ,request );
        InitiateChallengeResponseEnc reponse = getPostResponse(axisInterceptors.requestInterceptor(request), InitiateChallengeResponseEnc.class,
                AxisEndPoints.INITIATE_CHALLENGE.getApiPath(), getHeaders());
        InitiateChallengeOutput response = axisInterceptors.responseInterceptor(reponse);
        log.info("initiateChallenge response : {}" ,response );
        return response;
    }

    @Timed
    @ExceptionMetered
    @Override
    public com.axis.model.SubmitApplicationResponse submitApplicationETC(ETCInputRequestSubmitApplicationRequest request) throws  CbcException{
        log.info("submitApplicationETC Request : {}" ,request );
        SubmitApplicationResponseEnc submitApplicationResponseEnc =  getPostResponse(axisInterceptors.requestInterceptor(request),SubmitApplicationResponseEnc.class,
                AxisEndPoints.SUBMIT_APPLICATION_ETC.getApiPath(),getHeaders());
        com.axis.model.SubmitApplicationResponse response = axisInterceptors.responseInterceptor(submitApplicationResponseEnc);
        log.info("submitApplicationETC response : {}" ,response );
        return response;
    }

    @Timed
    @ExceptionMetered
    @Override
    public com.axis.model.SubmitApplicationResponse submitApplicationNTB(NTBInputRequestSubmitApplicationRequest request) throws  CbcException{
        log.info("submitApplicationNTB Request : {}" ,request );
        SubmitApplicationResponseEnc submitApplicationResponseEnc =  getPostResponse(axisInterceptors.requestInterceptor(request),SubmitApplicationResponseEnc.class,
        AxisEndPoints.SUBMIT_APPLICATION_NTB.getApiPath(),getHeaders());
        com.axis.model.SubmitApplicationResponse response = axisInterceptors.responseInterceptor(submitApplicationResponseEnc);
        log.info("submitApplicationNTB response : {}" ,response );
        return response;
    }

    @Timed
    @ExceptionMetered
    @Override
    public com.axis.model.SubmitApplicationResponse submitApplicationETB(ETBInputRequestSubmitApplicationRequest request) throws  CbcException{
        log.info("submitApplicationETB Request : {}" ,request );
        SubmitApplicationResponseEnc submitApplicationResponseEnc =  getPostResponse(axisInterceptors.requestInterceptor(request),SubmitApplicationResponseEnc.class,
                AxisEndPoints.SUBMIT_APPLICATION_ETB.getApiPath(),getHeaders());
        com.axis.model.SubmitApplicationResponse response =  axisInterceptors.responseInterceptor(submitApplicationResponseEnc);
        log.info("submitApplicationETB Response : {}" ,response );
        return response;
    }

    @Timed
    @ExceptionMetered
    @Override
    public com.axis.model.SubmitApplicationResponse submitApplicationETBNoPA(ETBNPAInputRequestSubmitApplicationRequest request) throws CbcException {
        log.info("submitApplicationETBNoPA Request : {}" ,request );
        SubmitApplicationResponseEnc submitApplicationResponseEnc = getPostResponse(axisInterceptors.requestInterceptor(request),SubmitApplicationResponseEnc.class,
                AxisEndPoints.SUBMIT_APPLICATION_ETB.getApiPath(),getHeaders());
        com.axis.model.SubmitApplicationResponse response = axisInterceptors.responseInterceptor(submitApplicationResponseEnc);
        log.info("submitApplicationETBNoPA response : {}" ,response );
        return response;

    }

    @Timed
    @ExceptionMetered
    @Override
    public GetCardDetailResponse fetchMaskedCardDetails(GetCardDetailRequestLCMGetCardDetailsRequest request) throws CbcException {
        log.info("fetchMaskedCardDetails Request : {}" ,request );
        LCMGetCardDetailsResponseEnc lcmGetCardDetailsResponseEnc =  getPostResponse(axisInterceptors.requestInterceptor(request),LCMGetCardDetailsResponseEnc.class,
                AxisEndPoints.GET_MASKED_CARD.getApiPath(), getHeaders());
        GetCardDetailResponse response = axisInterceptors.responseInterceptor(lcmGetCardDetailsResponseEnc);
        log.info("fetchMaskedCardDetails response : {}" ,response );
        return response;
    }

    @Timed
    @ExceptionMetered
    @Override
    public AccountDetailsRes fetchAccountDetails(AccountDetailsReqGetAccountDetailsRequest request) throws CbcException {
        log.info("fetchAccountDetails Request : {}" ,request );
        GetAccountDetailsResponseEnc getAccountDetailsResponseEnc =  getPostResponse(axisInterceptors.requestInterceptor(request),GetAccountDetailsResponseEnc.class,
                AxisEndPoints.GET_ACCOUNT_DETAILS.getApiPath(),getHeaders());
        AccountDetailsRes response = axisInterceptors.responseInterceptor(getAccountDetailsResponseEnc);
        log.info("fetchAccountDetails response : {}" ,response );
        return response;
    }

    @Timed
    @ExceptionMetered
    @Override
    public KycStatusRes fetchKycStatus(KycStatusReqGetKycStatusRequest request) throws CbcException {
        log.info("fetchKycStatus Request : {}" ,request );
        GetKycStatusResponseEnc getKycStatusResponseEnc =  getPostResponse(axisInterceptors.requestInterceptor(request),GetKycStatusResponseEnc.class,
                AxisEndPoints.GET_KYC_STATUS.getApiPath(),getHeaders());
        KycStatusRes response = axisInterceptors.responseInterceptor(getKycStatusResponseEnc);
        log.info("fetchKycStatus response : {}" ,response );
        return response;
    }

    @Timed
    @ExceptionMetered
    @Override
    public PanCkycDetailsRes fetchPanCkycDetails(PanCkycDetailsReqGetPanCkycDetailsRequest request) throws CbcException {
        log.info("fetchPanCkycDetails Request : {}" ,request );
        GetPanCkycDetailsResponseEnc getPanCkycDetailsResponseEnc =  getPostResponse(axisInterceptors.requestInterceptor(request),GetPanCkycDetailsResponseEnc.class,
                AxisEndPoints.GET_PAN_CKYC_DETAILS.getApiPath(),getHeaders());
        PanCkycDetailsRes  response =  axisInterceptors.responseInterceptor(getPanCkycDetailsResponseEnc);
        log.info("fetchPanCkycDetails response : {}" ,response );
        return response;
    }

    @Timed
    @ExceptionMetered
    @Override
    public InitiateKycRes initiatingKyc(InitiateKycReqInitiateKycRequest request) throws CbcException {
        log.info("initiatingKyc Request : {}" ,request );
        InitiateKycResponseEnc initiateKycResponseEnc =  getPostResponse(axisInterceptors.requestInterceptor(request),InitiateKycResponseEnc.class,
                AxisEndPoints.INITIATE_KYC.getApiPath(),getHeaders());
        InitiateKycRes response = axisInterceptors.responseInterceptor(initiateKycResponseEnc);
        log.info("initiatingKyc response : {}" ,response );
        return response;
    }

    private MultivaluedMap<String, Object> getHeaders() throws CbcException {
        String staticHeaderString = dynamicBucket.getString("smCBC.Axis.staticHeaders");
        try {
            Map<String, Object> headers = objectMapper.readValue(
                    staticHeaderString, new TypeReference<Map<String, Object>>() {
                    });
            MultivaluedMap<String, Object> map = new MultivaluedHashMap<>();
            for (Map.Entry<String, Object> entry : headers.entrySet()) {
                map.add(entry.getKey(), entry.getValue());
            }
            return map;
        } catch (JsonProcessingException e) {
            throw new CbcException(null, e.getMessage());
        }
    }
}
