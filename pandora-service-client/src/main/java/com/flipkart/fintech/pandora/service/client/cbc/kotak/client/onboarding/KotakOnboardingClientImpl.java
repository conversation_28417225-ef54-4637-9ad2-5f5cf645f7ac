package com.flipkart.fintech.pandora.service.client.cbc.kotak.client.onboarding;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.pandora.service.client.cbc.exceptions.CbcClientErrorException;
import com.flipkart.fintech.pandora.service.client.cbc.exceptions.CbcException;
import com.flipkart.fintech.pandora.service.client.cbc.exceptions.CbcServerErrorException;
import com.flipkart.fintech.pandora.service.client.cbc.exceptions.ErrorResponse;
import com.flipkart.fintech.pandora.service.client.cbc.kotak.Constants;
import com.flipkart.fintech.pandora.service.client.cbc.kotak.KotakEndPoints;
import com.flipkart.fintech.pandora.service.client.cbc.kotak.SmKotakCbcConfiguration;
import com.flipkart.fintech.pandora.service.client.exceptions.NetworkException;
import com.flipkart.kloud.config.DynamicBucket;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.stream.JsonReader;
import com.google.inject.Inject;
import com.google.inject.name.Named;
import com.kotak.model.*;
import lombok.extern.slf4j.Slf4j;

import javax.ws.rs.InternalServerErrorException;
import javax.ws.rs.client.Client;
import javax.ws.rs.client.Entity;
import javax.ws.rs.client.WebTarget;
import javax.ws.rs.core.*;
import java.io.StringReader;
import java.util.Map;

import static com.flipkart.fintech.pandora.service.client.cbc.kotak.Constants.*;

@Slf4j
public class KotakOnboardingClientImpl implements KotakOnboardingClient{
    private final SmKotakCbcConfiguration clientConfig;
    private final WebTarget webTarget;
    private final ObjectMapper objectMapper;
    private final Gson gson;
    private final DynamicBucket dynamicBucket;
    @Inject
    public KotakOnboardingClientImpl(SmKotakCbcConfiguration clientConfig, @Named(CBC_KOTAK_CLIENT) Client client, ObjectMapper objectMapper,
                                     Gson gson, DynamicBucket dynamicBucket) {
        this.clientConfig = clientConfig;
        this.gson = gson;
        this.webTarget = client.target(clientConfig.getHost());
        this.objectMapper = objectMapper;
        this.dynamicBucket = dynamicBucket;
    }

    private <T> T handleResponse(Response response, Class<T> successEncResponseType) throws CbcServerErrorException, CbcClientErrorException {
        String responseBody = response.readEntity(String.class);
        log.info("Response {}, {}", response.getStatus(), responseBody);
        Response.Status.Family status = response.getStatusInfo().getFamily();

        if (status == Response.Status.Family.SUCCESSFUL || status == Response.Status.Family.CLIENT_ERROR || status == Response.Status.Family.SERVER_ERROR) {
            JsonParser parser = new JsonParser();
            JsonReader reader = new JsonReader(new StringReader(responseBody));
            reader.setLenient(true);
            JsonObject jsonObject = parser.parse(reader).getAsJsonObject();
            return gson.fromJson(jsonObject, successEncResponseType);
        } else {
            throw new NetworkException(UNEXPECTED_RESPONSE_FROM_SERVER);
        }
    }

    <T, E> E getPostResponse(T request, Class<E> responseClass, String path,
                             MultivaluedMap<String, Object> headers) throws CbcException {
        log.info("Enc request :{}", request);
        Response response = null;
        try {
            response = webTarget.path(path)
                    .request(MediaType.APPLICATION_JSON)
                    .headers(headers)
                    .post(Entity.json(request));
            return handleResponse(response, responseClass);
        } catch (CbcClientErrorException | CbcServerErrorException e) {
            log.error(FAIL_TO_PROCESS_REQ_RES, e);
            throw e;
        } catch (Exception e) {
            log.error(FAIL_TO_PROCESS_REQ_RES, e);
            throw new CbcException(ErrorResponse.builder().errorCode(FAILURE).errorDescription(NET_ISSUE).build());
        } finally {
            if (response != null) {
                response.close();
            }
        }
    }

    <T, E> E getGetResponse(Class<E> responseClass, String path,
                            MultivaluedMap<String, Object> headers) throws CbcException {
        Response response = null;
        try {
            response = webTarget.path(path)
                    .request(MediaType.APPLICATION_JSON)
                    .headers(headers)
                    .get();
            return handleResponse(response, responseClass);
        } catch (CbcClientErrorException | CbcServerErrorException e) {
            log.error(FAIL_TO_PROCESS_REQ_RES, e);
            throw e;
        } catch (Exception e) {
            log.error(FAIL_TO_PROCESS_REQ_RES, e);
            throw new CbcException(ErrorResponse.builder().errorCode(FAILURE).errorDescription(NET_ISSUE).build());
        } finally {
            if (response != null) {
                response.close();
            }
        }
    }
//    public <T, E> E getResponse(String path, MultivaluedMap<String, Object> headers,
//                                Class<E> type) throws CbcException {
//        Invocation.Builder invocationBuilder = webTarget.path(path).request(MediaType.APPLICATION_JSON);
//        invocationBuilder.headers(headers);
//        invocationBuilder.property(ClientProperties.CONNECT_TIMEOUT, 40000);
//        invocationBuilder.property(ClientProperties.READ_TIMEOUT, 40000);
//        Response response = null;
//        String responseEntity = null;
//        try {
//            response = invocationBuilder.get();
//            responseEntity = response.readEntity(String.class);
//            log.info("String {}", responseEntity);
//            if (response.getStatus() == Response.Status.OK.getStatusCode()) {
//                return objectMapper.readValue(responseEntity, type);
//            } else {
//                throw new WebApplicationException(responseEntity);
//            }
//        } catch (IOException e) {
//            log.error(String.format("Error in GET %s", e));
//            throw new RuntimeException(e);
//        } finally {
//            if (Objects.nonNull(response)) {
//                response.close();
//            }
//        }
//    }






    @Override
    public ResponseDTOUpdatePanResponseDTO updatePanDetails(UpdatePanRequest updatePanRequest) throws CbcException {
        ResponseDTOUpdatePanResponseDTO response = getPostResponse(updatePanRequest, ResponseDTOUpdatePanResponseDTO.class,
                KotakEndPoints.UPDATE_PAN_DETAILS.getHost(), getHeaders());;
        return response;
    }

    @Override
    public ResponseDTOPaymentStatusResponse paymentStatus(PaymentStatusRequest paymentStatusRequest) throws CbcException {
        ResponseDTOPaymentStatusResponse response = getPostResponse(paymentStatusRequest, ResponseDTOPaymentStatusResponse.class,
                KotakEndPoints.PAYMENT_STATUS.getHost(), getHeaders());;
        return response;
    }

    @Override
    public ResponseDTOCreateNewTransactionResponseDTO payNowUpi(CreateNewTransactionRequest createNewTransactionRequest) throws CbcException {
        ResponseDTOCreateNewTransactionResponseDTO response = getPostResponse(createNewTransactionRequest, ResponseDTOCreateNewTransactionResponseDTO.class,
                KotakEndPoints.PAY_NOW_UPI.getHost(), getHeaders());;
        return response;
    }

    @Override
    public ResponseDTOFdInterestRateResponse fdConfig(FdInterestRateRequest fdInterestRateRequest) throws CbcException {
        ResponseDTOFdInterestRateResponse response = getPostResponse(fdInterestRateRequest, ResponseDTOFdInterestRateResponse.class,
                KotakEndPoints.FD_CONFIG.getHost(), getHeaders());;
        return response;
    }

    @Override
    public ResponseDTOPinServiceabilityResponse pinServiceable(String pincode) throws CbcException {
        ResponseDTOPinServiceabilityResponse response =
//                abstractClient.getResponse(KotakEndPoints.FD_CONFIG.getHost() +"/"+ pincode,
//                getHeaders(),ResponseDTOPinServiceabilityResponse.class );
        getGetResponse(ResponseDTOPinServiceabilityResponse.class,KotakEndPoints.PIN_SERVICABLE.getHost() +"/"+ pincode,getHeaders());
        return response;
    }

    @Override
    public ResponseDTOVkycStatusResponseDTO vkycStatus(VKycStatusRequest vKycStatusRequest) throws CbcException {
        ResponseDTOVkycStatusResponseDTO response = getPostResponse(vKycStatusRequest, ResponseDTOVkycStatusResponseDTO.class,
                KotakEndPoints.VKYC_STATUS.getHost(), getHeaders());;
        return response;
    }

    @Override
    public ResponseDTOVkycResponseDTO initiateVkyc(VideoKycRequest videoKycRequest) throws CbcException {
        ResponseDTOVkycResponseDTO response = getPostResponse(videoKycRequest, ResponseDTOVkycResponseDTO.class,
                KotakEndPoints.INITIATE_VKYC.getHost(), getHeaders());;
        return response;
    }

    @Override
    public ResponseDTOLeadStatusResponseDTO leadStatus(LeadIdRequest leadIdRequest) throws CbcException {
        ResponseDTOLeadStatusResponseDTO response = getPostResponse(leadIdRequest, ResponseDTOLeadStatusResponseDTO.class,
                KotakEndPoints.LEAD_STATUS.getHost(), getHeaders());;
        return response;
    }

    @Override
    public ResponseDTOCreateLeadResponseDTO createLead(CreateLeadRequest createLeadRequest) throws CbcException {
        ResponseDTOCreateLeadResponseDTO response = getPostResponse(createLeadRequest, ResponseDTOCreateLeadResponseDTO.class,
                KotakEndPoints.CREATE_LEAD.getHost(), getHeaders());;
        return response;
    }

    @Override
    public ResponseDTOLeadDTO getLead(LeadIdRequest leadIdRequest) throws CbcException {
        ResponseDTOLeadDTO response = getPostResponse(leadIdRequest, ResponseDTOLeadDTO.class,
                KotakEndPoints.LEADS.getHost(), getHeaders());;
        return response;
    }

    @Override
    public ResponseDTOUpdateUserDetailsResponseDTO submitUserDetails(GetUserDetailsRequest userDetailsRequest) throws CbcException {
        ResponseDTOUpdateUserDetailsResponseDTO response = getPostResponse(userDetailsRequest, ResponseDTOUpdateUserDetailsResponseDTO.class,
                KotakEndPoints.UPDATE_USER_DETAILS.getHost(), getHeaders());;
        return response;
    }

    @Override
    public ResponseDTOKycResponse initiateKyc(KycLeadRequest kycLeadRequest) throws CbcException {
        ResponseDTOKycResponse response = getPostResponse(kycLeadRequest, ResponseDTOKycResponse.class,
                KotakEndPoints.INITIATE_KYC.getHost(), getHeaders());;
        return response;
    }

    @Override
    public ResponseDTOCMSContentResponseDTO consent() throws CbcException {
        return getGetResponse(ResponseDTOCMSContentResponseDTO.class,KotakEndPoints.CONSENT.getHost(),getHeaders());
    }

    private MultivaluedMap<String, Object> getHeaders() throws CbcException {
        String staticHeaderString = dynamicBucket.getString("smCBC.Kotak.staticHeaders");
        try {
            Map<String, Object> headers = objectMapper.readValue(
                    staticHeaderString, new TypeReference<Map<String, Object>>() {
                    });
            MultivaluedMap<String, Object> map = new MultivaluedHashMap<>();
            for (Map.Entry<String, Object> entry : headers.entrySet()) {
                map.add(entry.getKey(), entry.getValue());
            }
            return map;
        } catch (JsonProcessingException e) {
            throw new CbcException(null, e.getMessage());
        }
    }
}
