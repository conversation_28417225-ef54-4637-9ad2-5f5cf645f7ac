package com.flipkart.fintech.pandora.service.client.cbc.upswing.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.flipkart.fintech.pandora.api.model.enums.cbc.CardForm;
import com.flipkart.fintech.pandora.api.model.enums.cbc.PhysicalCardEligibilityStatus;
import com.flipkart.fintech.pandora.api.model.enums.cbc.UpswingCardStatus;
import com.flipkart.fintech.pandora.util.LocalDateDeserializer;
import com.flipkart.fintech.pandora.util.LocalDateSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AccountDetails {

    @JsonProperty("shouldNudgeUserForAdditionalFD")
    private Boolean shouldNudgeForAdditionalFD;

    //Avoid using "is" in variable names
    //ref - https://stackoverflow.com/questions/********/jackson-renames-primitive-boolean-field-by-removing-is
    @JsonProperty("isBillGenerated")
    private Boolean billGenerated;

    @JsonProperty("isBillPaid")
    private Boolean billPaid;

    @JsonProperty("paymentDueDate")
    @JsonSerialize(using = LocalDateSerializer.class)
    @JsonDeserialize(using = LocalDateDeserializer.class)
    private LocalDate paymentDueDate;

    @JsonProperty("isOverdue")
    private Boolean overdue;

    @JsonProperty("cardForm")
    private CardForm cardForm;

    @JsonProperty("physicalCardEligibilityStatus")
    private PhysicalCardEligibilityStatus physicalCardEligibilityStatus;

}
