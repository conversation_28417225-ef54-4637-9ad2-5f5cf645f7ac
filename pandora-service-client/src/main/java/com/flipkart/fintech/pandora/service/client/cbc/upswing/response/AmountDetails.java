package com.flipkart.fintech.pandora.service.client.cbc.upswing.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AmountDetails {
    BigDecimal amount ;
    String currency;

    public AmountDetails add(AmountDetails other) {
        if (!this.currency.equals(other.currency)) {
            throw new IllegalArgumentException("Currencies must match to add AmountDetails");
        }
        return new AmountDetails(this.amount.add(other.amount), this.currency);
    }
}
