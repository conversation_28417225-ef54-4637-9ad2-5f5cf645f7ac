package com.flipkart.fintech.pandora.service.client.cbc.upswing.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RegisterTokenResponse {

    @JsonProperty("ici")
    private String upswingUserId;

    @JsonProperty("guestSessionToken")
    private String guestSessionToken;

}
