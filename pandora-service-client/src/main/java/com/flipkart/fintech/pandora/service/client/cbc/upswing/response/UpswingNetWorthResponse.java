package com.flipkart.fintech.pandora.service.client.cbc.upswing.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.flipkart.fintech.pandora.api.model.enums.cbc.UpswingApplicationStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UpswingNetWorthResponse {
    AmountDetails totalInvestedAmount;
    AmountDetails totalInterestEarned;
    AmountDetails currentAmount;
    int activeTermDepositCount;
}
