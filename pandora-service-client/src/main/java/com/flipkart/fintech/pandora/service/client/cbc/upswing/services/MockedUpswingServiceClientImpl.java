package com.flipkart.fintech.pandora.service.client.cbc.upswing.services;

import com.axis.model.GetAppStatusOutput;
import com.codahale.metrics.annotation.ExceptionMetered;
import com.codahale.metrics.annotation.Timed;
import com.flipkart.fintech.pandora.api.model.enums.cbc.UpswingApplicationStatus;
import com.flipkart.fintech.pandora.api.model.enums.cbc.UpswingCardStatus;
import com.flipkart.fintech.pandora.api.model.request.cbc.upswing.UpswingRewardRedemptionRequest;
import com.flipkart.fintech.pandora.api.model.response.cbc.upswing.UpswingRewardRedemptionResponse;
import com.flipkart.fintech.pandora.service.client.PandoraServiceClientException;
import com.flipkart.fintech.pandora.service.client.cbc.axis.client.onboarding.AxisOnboardingClient;
import com.flipkart.fintech.pandora.service.client.cbc.exceptions.CbcException;
import com.flipkart.fintech.pandora.service.client.cbc.exceptions.ErrorResponse;
import com.flipkart.fintech.pandora.service.client.cbc.upswing.config.UpswingConfig;
import com.flipkart.fintech.pandora.service.client.cbc.upswing.constants.Constants;
import com.flipkart.fintech.pandora.service.client.cbc.upswing.helper.UpswingInMemoryCache;
import com.flipkart.fintech.pandora.service.client.cbc.upswing.helper.UpswingInMemoryCacheImpl;
import com.flipkart.fintech.pandora.service.client.cbc.upswing.request.RegisterCustomerRequest;
import com.flipkart.fintech.pandora.service.client.cbc.upswing.response.*;
import com.flipkart.fintech.pandora.service.client.cfa.MockHelper;
import com.flipkart.fintech.pandora.util.HttpStatusUtil;
import com.google.inject.Inject;
import lombok.extern.slf4j.Slf4j;
import org.glassfish.jersey.client.ClientProperties;

import javax.inject.Named;
import javax.ws.rs.client.Client;
import javax.ws.rs.client.Entity;
import javax.ws.rs.client.Invocation;
import javax.ws.rs.client.WebTarget;
import javax.ws.rs.core.*;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ExecutionException;

import static com.flipkart.fintech.pandora.service.client.cbc.upswing.constants.Constants.APIConstants.PCI;

@Slf4j
public class MockedUpswingServiceClientImpl implements UpswingServiceClient {

    private final MockHelper mockHelper;
    private final UpswingServiceClient upswingServiceClient;

    @Inject
    public MockedUpswingServiceClientImpl(@Named("default") UpswingServiceClient upswingServiceClient, MockHelper mockHelper) {
        this.mockHelper = mockHelper;
        this.upswingServiceClient = upswingServiceClient;
    }


    /**
     * Responsibilities :
     * 1. Fetch the Jwt-Token from local cache.
     * 2. If Token is unavailable in cache, first call the Upswing Token generation API and cache it in the local.
     * @return {JwtTokenResponse} - Return response with jwt-token
     */
    @Timed
    @ExceptionMetered
    @Override
    public String getJwtToken() {

        return upswingServiceClient.getJwtToken();
    }

    private MultivaluedHashMap<String, Object> getHeaders(String mediaType) {
        MultivaluedHashMap<String, Object> headers = new MultivaluedHashMap<>();
        headers.add(HttpHeaders.CONTENT_TYPE, mediaType);
        return headers;
    }


    @Timed
    @ExceptionMetered
    @Override
    public RegisterTokenResponse registerCustomer(String partnerCustomerId) throws ExecutionException {

        Optional<MockHelper.MockedApi> mockedApi = mockHelper.findMockedApi("UpswingRegisterTokenResponse");
        if (mockedApi.isPresent()) {
            RegisterTokenResponse output = mockHelper.getData(mockedApi.get().getDataKey(), RegisterTokenResponse.class);
            if (output == null) {
                ErrorResponse errorResponse = mockHelper.getData(mockedApi.get().getDataKey(), ErrorResponse.class);
                log.info("RegisterTokenResponse mock response for registerCustomer error :{} ", errorResponse);
                throw new  ExecutionException(new CbcException(errorResponse));
            }
            log.info("RegisterTokenResponse mock response for registerCustomer :{} ", output);
            return output;
        }
        return upswingServiceClient.registerCustomer(partnerCustomerId);
    }

    @Timed
    @ExceptionMetered
    @Override
    public ApplicationStatusDetails fetchApplicationStatusDetails(String userId) throws ExecutionException {

        Optional<MockHelper.MockedApi> mockedApi = mockHelper.findMockedApi("UpswingApplicationStatusDetails");
        if (mockedApi.isPresent()) {
            ApplicationStatusDetails output = mockHelper.getData(mockedApi.get().getDataKey(), ApplicationStatusDetails.class);
            if (output == null) {
                ErrorResponse errorResponse = mockHelper.getData(mockedApi.get().getDataKey(), ErrorResponse.class);
                log.info("ApplicationStatusDetails mock response for fetchApplicationStatusDetails error :{} ", errorResponse);
                throw new  ExecutionException(new CbcException(errorResponse));
            }
            log.info("ApplicationStatusDetails mock response for fetchApplicationStatusDetails :{} ", output);
            return output;
        }
        return upswingServiceClient.fetchApplicationStatusDetails(userId);
    }

    @Timed
    @ExceptionMetered
    @Override
    public UpswingCreditCardAccountDetails fetchAccountDetails(String userId) throws ExecutionException {

        Optional<MockHelper.MockedApi> mockedApi = mockHelper.findMockedApi("UpswingCreditCardAccountDetails");
        if (mockedApi.isPresent()) {
            UpswingCreditCardAccountDetails output = mockHelper.getData(mockedApi.get().getDataKey(), UpswingCreditCardAccountDetails.class);
            if (output == null) {
                ErrorResponse errorResponse = mockHelper.getData(mockedApi.get().getDataKey(), ErrorResponse.class);
                log.info("UpswingCreditCardAccountDetails mock response for fetchAccountDetails error :{} ", errorResponse);
                throw new ExecutionException(new CbcException(errorResponse));
            }
            log.info("UpswingCreditCardAccountDetails mock response for fetchAccountDetails :{} ", output);
            return output;
        }
        return upswingServiceClient.fetchAccountDetails(userId);
    }

    @Timed
    @ExceptionMetered
    @Override
    public UpswingNetWorthResponse netWorth(String userId) throws ExecutionException {

        Optional<MockHelper.MockedApi> mockedApi = mockHelper.findMockedApi("UpswingNetWorth");
        if (mockedApi.isPresent()) {
            UpswingNetWorthResponse output = mockHelper.getData(mockedApi.get().getDataKey(), UpswingNetWorthResponse.class);
            if (output == null) {
                ErrorResponse errorResponse = mockHelper.getData(mockedApi.get().getDataKey(), ErrorResponse.class);
                log.info("UpswingNetWorthResponse mock response for netWorth error :{} ", errorResponse);
                throw new  ExecutionException(new CbcException(errorResponse));
            }
            log.info("UpswingNetWorthResponse mock response for netWorth :{} ", output);
            return output;
        }
        return upswingServiceClient.netWorth(userId);
    }

    @Timed
    @ExceptionMetered
    @Override
    public UpswingNetWorthResponseV2 netWorthV2(String userId) throws ExecutionException {

        Optional<MockHelper.MockedApi> mockedApi = mockHelper.findMockedApi("UpswingNetWorthResponseV2");
        if (mockedApi.isPresent()) {
            UpswingNetWorthResponseV2 output = mockHelper.getData(mockedApi.get().getDataKey(), UpswingNetWorthResponseV2.class);
            if (output == null) {
                ErrorResponse errorResponse = mockHelper.getData(mockedApi.get().getDataKey(), ErrorResponse.class);
                log.info("UpswingNetWorthResponseV2 mock response for netWorthV2 error :{} ", errorResponse);
                throw new  ExecutionException(new CbcException(errorResponse));
            }
            log.info("UpswingNetWorthResponseV2 mock response for netWorthV2 :{} ", output);
            return output;
        }
        return upswingServiceClient.netWorthV2(userId);
    }
    @Timed
    @ExceptionMetered
    @Override
    public PendingJourneyResponse pendingJourney(String userId) throws ExecutionException {
        Optional<MockHelper.MockedApi> mockedApi = mockHelper.findMockedApi("UpswingPendingJourneyResponse");
        if (mockedApi.isPresent()) {
            PendingJourneyResponse output = mockHelper.getData(mockedApi.get().getDataKey(), PendingJourneyResponse.class);
            if (output == null) {
                ErrorResponse errorResponse = mockHelper.getData(mockedApi.get().getDataKey(), ErrorResponse.class);
                log.info("PendingJourneyResponse mock response for pendingJourney error :{} ", errorResponse);
                throw new  ExecutionException(new CbcException(errorResponse));
            }
            log.info("PendingJourneyResponse mock response for pendingJourney :{} ", output);
            return output;
        }
        return upswingServiceClient.pendingJourney(userId);
    }

    @Timed
    @ExceptionMetered
    @Override
    public UpswingRewardRedemptionResponse redeemReward(UpswingRewardRedemptionRequest request) throws ExecutionException {
        Optional<MockHelper.MockedApi> mockedApi = mockHelper.findMockedApi("UpswingRewardRedemptionResponse");
        if (mockedApi.isPresent()) {
            UpswingRewardRedemptionResponse output = mockHelper.getData(mockedApi.get().getDataKey(), UpswingRewardRedemptionResponse.class);
            if (output == null) {
                ErrorResponse errorResponse = mockHelper.getData(mockedApi.get().getDataKey(), ErrorResponse.class);
                log.info("UpswingRewardRedemptionResponse mock response for redeemReward error :{} ", errorResponse);
                throw new  ExecutionException(new CbcException(errorResponse));
            }
            log.info("UpswingRewardRedemptionResponse mock response for redeemReward :{} ", output);
            return output;
        }
        return upswingServiceClient.redeemReward(request);
    }

}
