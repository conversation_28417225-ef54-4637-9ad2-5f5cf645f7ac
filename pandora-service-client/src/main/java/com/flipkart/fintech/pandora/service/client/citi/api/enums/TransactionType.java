package com.flipkart.fintech.pandora.service.client.citi.api.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>.r on 31/07/18.
 */
public enum TransactionType {
    EMI_PURCHASE,
    PAYLATER_PURCHASE,
    EMI_INSTALLMENT,
    PAYLATER_INSTALLMENT,
    PAYBACK,
    LATE_FEES,
    PAYMENT_REALLOCATION,
    GST,
    INTEREST,
    EMI_INSTALLMENT_REVERSAL,
    UNKNOWN;

    private static Map<String, TransactionType> map = new HashMap<>();

    static {
        map.put("", EMI_PURCHASE);
        map.put("002", PAYLATER_PURCHASE);
        map.put("202", PAYLATER_PURCHASE);
        /**
         * EMI installment - forward, always billed
         */
        map.put("312", EMI_INSTALLMENT);
        /**
         * EMI installment - reversal, always unbilled
         */
        map.put("480", EMI_INSTALLMENT_REVERSAL);
        map.put("018", EMI_INSTALLMENT_REVERSAL);
        map.put("221", EMI_INSTALLMENT_REVERSAL);
        /**
         * Payback
         */
        map.put("720", PAYBACK);
        /**
         * Payback reversal
         */
        map.put("813", PAYBACK);
        /**
         * Penalty
         */
        map.put("527", LATE_FEES);
        /**
         * Penalty reversal
         */
        map.put("570", LATE_FEES);
        map.put("367", GST);
        map.put("369", GST);
        map.put("370", GST);
        map.put("387", GST);
        map.put("389", GST);
        map.put("390", GST);
    }

    @JsonCreator
    public static TransactionType forValue(String value){
        if(StringUtils.isEmpty(value)) value = "";
        TransactionType transactionType = map.get(value);
        if(null == transactionType){
            transactionType = UNKNOWN;
        }
        return transactionType;
    }
}
