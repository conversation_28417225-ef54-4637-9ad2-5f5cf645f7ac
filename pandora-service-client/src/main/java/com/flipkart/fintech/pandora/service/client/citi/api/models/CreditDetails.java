package com.flipkart.fintech.pandora.service.client.citi.api.models;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>.<PERSON> on 13/07/18.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class CreditDetails {

    @JsonProperty("recommendedCreditLimit")
    private Double creditLimit;

    @JsonProperty("btMonthlyInterestRate")
    private Double monthlyInterest;

    public Double getCreditLimit() {
        return creditLimit;
    }

    public void setCreditLimit(Double creditLimit) {
        this.creditLimit = creditLimit;
    }

    public Double getMonthlyInterest() {
        return monthlyInterest;
    }

    public void setMonthlyInterest(Double monthlyInterest) {
        this.monthlyInterest = monthlyInterest;
    }
}
