package com.flipkart.fintech.pandora.service.client.citi.api.models;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pandora.service.client.citi.api.enums.TransactionMode;
import com.flipkart.fintech.pandora.service.client.citi.api.enums.TransactionStatus;
import com.flipkart.fintech.pandora.service.client.citi.api.enums.TransactionType;

import java.math.BigDecimal;

/**
 * Created by su<PERSON><PERSON>kumar.r on 31/07/18.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class Transaction {

    @JsonProperty("transactionDate")
    private String billingTransactionDate;

    @JsonProperty("transactionAmount")
    private BigDecimal billingTransactionAmount;

    @JsonProperty("transactionType")
    private TransactionMode billingTransactionMode;

    @JsonProperty("transactionCode")
    private TransactionType billingTransactionType;

    @JsonProperty("transactionStatus")
    private TransactionStatus billingTransactionStatus;

    private String transactionDescription;

    private String transactionReferenceId;

    public String getBillingTransactionDate() {
        return billingTransactionDate;
    }

    public void setBillingTransactionDate(String billingTransactionDate) {
        this.billingTransactionDate = billingTransactionDate;
    }

    public BigDecimal getBillingTransactionAmount() {
        return billingTransactionAmount;
    }

    public void setBillingTransactionAmount(BigDecimal billingTransactionAmount) {
        this.billingTransactionAmount = billingTransactionAmount;
    }

    public TransactionMode getBillingTransactionMode() {
        return billingTransactionMode;
    }

    public void setBillingTransactionMode(TransactionMode billingTransactionMode) {
        this.billingTransactionMode = billingTransactionMode;
    }

    public TransactionType getBillingTransactionType() {
        return billingTransactionType;
    }

    public void setBillingTransactionType(TransactionType billingTransactionType) {
        this.billingTransactionType = billingTransactionType;
    }

    public TransactionStatus getBillingTransactionStatus() {
        return billingTransactionStatus;
    }

    public void setBillingTransactionStatus(TransactionStatus billingTransactionStatus) {
        this.billingTransactionStatus = billingTransactionStatus;
    }

    public String getTransactionDescription() {
        return transactionDescription;
    }

    public void setTransactionDescription(String transactionDescription) {
        this.transactionDescription = transactionDescription;
    }

    public String getTransactionReferenceId() {
        return transactionReferenceId;
    }

    public void setTransactionReferenceId(String transactionReferenceId) {
        this.transactionReferenceId = transactionReferenceId;
    }

    @Override
    public String toString() {
        return "Transaction{" +
                "billingTransactionDate='" + billingTransactionDate + '\'' +
                ", billingTransactionAmount=" + billingTransactionAmount +
                ", billingTransactionMode=" + billingTransactionMode +
                ", billingTransactionType=" + billingTransactionType +
                ", billingTransactionStatus=" + billingTransactionStatus +
                ", transactionDescription='" + transactionDescription + '\'' +
                ", transactionReferenceId='" + transactionReferenceId + '\'' +
                '}';
    }
}
