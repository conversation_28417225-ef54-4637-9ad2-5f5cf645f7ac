package com.flipkart.fintech.pandora.service.client.citi.api.request;

import com.flipkart.fintech.pandora.service.client.citi.api.enums.TransactionStatus;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>.r on 31/07/18.
 */
public class BillingTransactionsRequest extends AccessToken {

    private String accountId;

    private Integer limit;

    private Integer offset;

    private String transactionFromDate;

    private String transactionToDate;

    private TransactionStatus transactionStatus;

    public String getAccountId() {
        return accountId;
    }

    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public String getTransactionFromDate() {
        return transactionFromDate;
    }

    public void setTransactionFromDate(String transactionFromDate) {
        this.transactionFromDate = transactionFromDate;
    }

    public String getTransactionToDate() {
        return transactionToDate;
    }

    public void setTransactionToDate(String transactionToDate) {
        this.transactionToDate = transactionToDate;
    }

    public TransactionStatus getTransactionStatus() {
        return transactionStatus;
    }

    public void setTransactionStatus(TransactionStatus transactionStatus) {
        this.transactionStatus = transactionStatus;
    }
}
