package com.flipkart.fintech.pandora.service.client.citi.api.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pandora.service.client.citi.api.models.ErrorResponse;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>.<PERSON> on 11/07/18.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class ApplicationCreateResponse extends ErrorResponse {

    private String applicationId;

    @JsonProperty("controlFlowId")
    private String sessionId;

    public String getApplicationId() {
        return applicationId;
    }

    public void setApplicationId(String applicationId) {
        this.applicationId = applicationId;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }
}
