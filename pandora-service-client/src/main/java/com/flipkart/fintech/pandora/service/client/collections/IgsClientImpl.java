package com.flipkart.fintech.pandora.service.client.collections;

import com.flipkart.affordability.model.igs.IgsDispositionChange;
import com.flipkart.fintech.pandora.service.client.PandoraServiceClientException;
import com.flipkart.fintech.pandora.service.client.collections.config.CollectionsConfiguration;
import com.flipkart.fintech.pandora.service.client.utils.Constants;
import org.apache.commons.lang3.StringUtils;
import org.glassfish.jersey.client.ClientProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.inject.Inject;
import javax.ws.rs.HeaderParam;
import javax.ws.rs.client.Client;
import javax.ws.rs.client.ClientBuilder;
import javax.ws.rs.client.Entity;
import javax.ws.rs.client.WebTarget;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.MultivaluedHashMap;
import javax.ws.rs.core.Response;

public class IgsClientImpl implements IgsClient {
    private static final Logger logger = LoggerFactory.getLogger(IgsClientImpl.class);

    private final WebTarget webTarget;
    private final CollectionsConfiguration collectionsConfiguration;
    private static final String igsDispositionPath = "/1/igs/dispositionChange";

    @Inject
    public IgsClientImpl(CollectionsConfiguration collectionsConfiguration) {
        this.collectionsConfiguration = collectionsConfiguration;

        if (StringUtils.isEmpty(collectionsConfiguration.getUrl())) {
            throw new IllegalArgumentException("Endpoint cannot be blank");
        }

        Client client = ClientBuilder.newClient();
        client.property(ClientProperties.CONNECT_TIMEOUT, 60000);
        client.property(ClientProperties.READ_TIMEOUT, 60000);

        this.webTarget = client.target(collectionsConfiguration.getUrl());
    }

    public Response processIgsDispositionChange(IgsDispositionChange igsDispositionChange,
                                                @HeaderParam("X_CLIENT_ID") String xClientId) {

        Response dispositionChangeResponse = webTarget.path(igsDispositionPath).request(MediaType.APPLICATION_JSON).
                headers(getHeaders(xClientId, igsDispositionChange.getAccountId())).post(Entity.json(igsDispositionChange));
        if (dispositionChangeResponse.getStatus() == 200) {
            logger.info("Response is : {}", dispositionChangeResponse);
        } else if(dispositionChangeResponse.getStatus() == 403) {
            logger.error("FAILURE! Invalid client id : {}", xClientId);
            throw new PandoraServiceClientException("Error response from Pandora : " + dispositionChangeResponse.readEntity(String.class));
        }
        else {
            String error = dispositionChangeResponse.readEntity(String.class);
            logger.error("Non 200 response by IGS Disposition : {}", error);
            throw new PandoraServiceClientException(dispositionChangeResponse.getStatus(), error);
        }
        return dispositionChangeResponse;
    }

    private MultivaluedHashMap<String, Object> getHeaders(String xClientId, String accountId){
        MultivaluedHashMap<String, Object> map = new MultivaluedHashMap<>();
        if(!StringUtils.isEmpty(xClientId)) {
            map.add(Constants.X_CLIENT_ID, xClientId);
        }
        if (accountId != null) {
            map.add(Constants.X_USER_ID, accountId);
        }
        return map;
    }
}
