package com.flipkart.fintech.pandora.service.client.config;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.flipkart.fintech.cryptex.annotation.EncryptedConfig;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class GupshupWebhookConfig {
    @EncryptedConfig(key = "gupshupWebhookApiKey")
    private String webhookApiKey;
}
