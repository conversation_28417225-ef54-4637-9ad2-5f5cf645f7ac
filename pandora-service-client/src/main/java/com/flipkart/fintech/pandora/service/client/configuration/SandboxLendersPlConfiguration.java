package com.flipkart.fintech.pandora.service.client.configuration;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.flipkart.fintech.pandora.service.client.sandbox.v1.utils.ApiModel;
import com.flipkart.fintech.pandora.service.client.sandbox.v1.utils.PlActions;
import com.flipkart.kloud.config.DynamicBucket;
import lombok.*;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 28/11/23
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Getter
@Setter
@RequiredArgsConstructor
@Builder
@AllArgsConstructor
public class SandboxLendersPlConfiguration {
    private String host;
    private String tokenEndpoint = "/lender/1/token";
    private String username;
    private String password;
    private String authCode;
    private String clientId;
    private String xApiKey;
    private Boolean cremoEnabled = false;
    private Boolean granularCremoEnabled = false;
    private Boolean needShippingAddress = true;
    private String lenderBase64PublicKey;
    private String lspBase64PrivateKey;
    private Map<PlActions, ApiModel> apiModel;
    /**
     * use Scope enum values here
     */
    private String scope;

    private DynamicBucket dynamicBucket;

//    public String getLspBase64PrivateKey() {
//        return dynamicBucket.getString(scope+"_"+LSP_BASE64_PRIVATE_KEY);
//    }
//
//    public String getPassword() {
//        return dynamicBucket.getString(scope+"_"+OAUTH_PASSWORD);
//    }

}
