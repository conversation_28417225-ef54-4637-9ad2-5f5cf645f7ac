package com.flipkart.fintech.pandora.service.client.creditsaison;

import com.flipkart.fintech.pandora.api.model.creditsaison.request.LoanRequest;
import com.flipkart.fintech.pandora.service.client.hmac.HmacAuthentication;
import lombok.extern.slf4j.Slf4j;
import org.codehaus.jackson.map.ObjectMapper;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.Instant;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;
import java.util.logging.Level;
import java.util.stream.Collectors;
import java.net.URLEncoder;
import java.io.UnsupportedEncodingException;


@Slf4j
public class CreditSaisonAuthenticationImpl implements HmacAuthentication {

    private final ObjectMapper objectMapper = new ObjectMapper();
    private final String API_URI = "/api/v2/realtime/underwriting";
    private static final String LINE_SEPARATOR = "\n";

    @Override
    public Map<String, String> generateAuthToken(Object request, String request1) {

        try {

            String h1 = generateRequestBodyHash(request1);
            long signedDate = generateTimestamp();
            String queryParamString = "signedDate=" + signedDate;
            String canonicalRequestC1 = createCanonicalRequest("POST",API_URI,queryParamString,h1);
            String h2 = generateRequestBodyHash(canonicalRequestC1);
            String signature = generateHMACSHA256Signature(h2, "J3K4N6P7Q9SATBVDWEXGZH2J4M");

            Map<String, String> result = new HashMap<>();
            result.put("signature", signature);
            result.put("signedDate", String.valueOf(signedDate));
            result.put("h1", h1);
            result.put("c1", canonicalRequestC1);
            result.put("h2", h2);
            return result;

        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public String generateHMACSHA256Signature(String h2, String secretKey) throws Exception {

        try {
            Mac sha256HMAC = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKeySpec = new SecretKeySpec(secretKey.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
            sha256HMAC.init(secretKeySpec);

            byte[] hash = sha256HMAC.doFinal(h2.getBytes(StandardCharsets.UTF_8));
            String signature = bytesToHex(hash);

            return Base64.getEncoder().encodeToString(signature.getBytes(StandardCharsets.UTF_8));
        } catch (Exception e) {
            log.info("Error while generating signature , {}", e);
            throw new RuntimeException("Error generating signature", e);
        }
    }

    private String generateRequestBodyHash(String object) throws NoSuchAlgorithmException, IOException {

        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(object.getBytes(StandardCharsets.UTF_8));

            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) hexString.append('0');
                hexString.append(hex);
            }
            return hexString.toString().toUpperCase();
        } catch (Exception e) {
            throw new RuntimeException("Error generating SHA-256 hash", e);
        }

    }

    private Map<String, String> generateQueryParams(String signedDate) {
        Map<String, String> queryParams = new TreeMap<>();
        queryParams.put("signedDate", signedDate);
        return queryParams;
    }

    private String bytesToHex(byte[] hash) {
        StringBuilder hexString = new StringBuilder(2 * hash.length);
        for (byte b : hash) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }
        return hexString.toString();
    }

    private long generateTimestamp() {
        return System.currentTimeMillis();
    }

    private String createCanonicalRequest(String... args) {

        StringBuilder canonicalReq = new StringBuilder();
        for (String arg : args) {
            canonicalReq.append(arg).append(LINE_SEPARATOR);
        }
        return canonicalReq.substring(0, canonicalReq.length() - 1);

    }

    private String urlEncode(String value) throws UnsupportedEncodingException {
        return URLEncoder.encode(value, StandardCharsets.UTF_8.toString());
    }
}
