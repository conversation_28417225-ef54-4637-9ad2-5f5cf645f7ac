package com.flipkart.fintech.pandora.service.client.datafeed;

import com.flipkart.fintech.pandora.service.client.PandoraServiceClientException;
import com.flipkart.fintech.pandora.service.client.cfa.Constants;
import com.flipkart.fintech.pandora.service.client.datafeed.config.DatafeedConfiguration;
import com.flipkart.fintech.pandora.service.client.datafeed.response.SellerPerformanceData;
import com.flipkart.kloud.authn.AuthTokenService;
import com.google.inject.Inject;
import lombok.extern.slf4j.Slf4j;

import javax.ws.rs.client.Client;
import javax.ws.rs.client.Invocation;
import javax.ws.rs.client.WebTarget;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

/**
 * <AUTHOR>
 * @since 06/11/20.
 */
@Slf4j
public class DatafeedClientImpl implements DatafeedClient {

    private final WebTarget webTarget;
    private final DatafeedConfiguration datafeedConfiguration;
    private static final String SELLER_PREFORMACE_DATA_PATH = "/fintech-datafeed/seller_performance_data/%s";
    public static final String BEARER = "Bearer %s";

    @Inject
    public DatafeedClientImpl(Client client, DatafeedConfiguration datafeedConfiguration) {
        this.datafeedConfiguration = datafeedConfiguration;
        this.webTarget = client.target(datafeedConfiguration.getUrl());
    }

    @Override
    public SellerPerformanceData getSellerPerformanceData(String sellerId) {
        log.info("In get seller performance data method for seller id : {}", sellerId);
        String path = String.format(SELLER_PREFORMACE_DATA_PATH, sellerId);
        String authToken = AuthTokenService.getInstance().fetchToken(datafeedConfiguration.getClientId()).toAuthorizationHeader();
        Response response = null;
        SellerPerformanceData sellerPerformanceData;
        try {
            Invocation.Builder invocationBuilder = webTarget.path(path)
                    .request(MediaType.APPLICATION_JSON)
                    .header(Constants.AUTHORIZATION_HEADER, authToken);
            response = invocationBuilder.get();

            if (response.getStatus() == 200) {
                sellerPerformanceData = response.readEntity(SellerPerformanceData.class);
                log.info("Seller performance data response for seller id {} : {}", sellerId, sellerPerformanceData.toString());
            } else {
                String errorString = response.readEntity(String.class);
                log.error("Error response for seller performance data for seller id {}, response: {}", sellerId,
                        errorString);
                throw new PandoraServiceClientException("Non 200 response from datafeed service for seller performance " +
                        "data - " + errorString);
            }
        } catch (Exception e) {
            throw new PandoraServiceClientException(e.getMessage());
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return sellerPerformanceData;
    }

}
