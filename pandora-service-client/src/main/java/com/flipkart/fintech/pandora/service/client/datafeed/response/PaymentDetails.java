package com.flipkart.fintech.pandora.service.client.datafeed.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 09/11/20.
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class PaymentDetails {
    @JsonProperty(value = "amount")
    private BigDecimal amount;

    @JsonProperty(value = "from_date")
    private Date fromDate;

    @JsonProperty(value = "to_date")
    private Date toDate;

    @JsonProperty(value = "last_updated_date")
    private Date lastUpdatedDate;
}
