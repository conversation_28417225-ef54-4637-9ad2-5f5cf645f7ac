package com.flipkart.fintech.pandora.service.client.digio.config;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 08/01/25.
 */
@Data
public class DigioConfiguration {

    @JsonProperty
    @NotEmpty
    private String serverUrl;

    @JsonProperty
    @NotEmpty
    private String clientId;

    @JsonProperty
    @NotEmpty
    private String clientSecret;

    @JsonProperty
    private String trustStorePath;

    @JsonProperty
    private String trustStorePass;

    @JsonProperty
    private String keyStorePath;

    @JsonProperty
    private String keyStorePass;

    @JsonProperty
    @NotEmpty
    private String upiMandateCorporateConfigId;

    @JsonProperty
    @NotEmpty
    private String apiMandateCorporateConfigId;

    @JsonProperty
    @NotNull
    private DigioEncryption encryption;

    @JsonProperty("isgcp")
    @NotNull
    private boolean isGcp;

    @JsonProperty
    @NotNull
    private String encryptionKey;
}
