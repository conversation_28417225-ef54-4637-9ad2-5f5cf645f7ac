package com.flipkart.fintech.pandora.service.client.digio.responses.kyc.rpd;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 08/01/25.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CustomerDetails {

    private String id;

    private String beneficiaryAccountNo;

    private String ifsc;

    private BigDecimal amount;

    private String transferType;

    private String status;

    private String completedTransferType;

    private String beneficiaryNameWithBank;

    private String bankTxnRefOnCompleted;

    private String bankName;

    private String branchAddress;

    private String customerVpa;

}
