package com.flipkart.fintech.pandora.service.client.digio.responses.kyc.rpd.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.math.BigDecimal;


@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class KycRequestStatusResponseDTO {

    private String id;
    private String updatedAt;
    private String createdAt;
    private String status;
    private String customerIdentifier;
    private String customerName;
    private Boolean reminderRegistered;
    private Boolean autoApproved;
    private String beneficiaryAccountNo;
    private String ifsc;
    private BigDecimal amount;
    private String customerStatus;
    private String beneficiaryNameWithBank;
    private String bankTxnRefOnCompleted;
    private String bankName;
    private String branchAddress;
    private String customerVpa;
}
