package com.flipkart.fintech.pandora.service.client.digio.responses.mandate;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;


@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CancelRegisteredUPIMandateResponse {

    private String id;
    private String customerId;
    private String authType;
    private String status;
    private String umrn;

}
