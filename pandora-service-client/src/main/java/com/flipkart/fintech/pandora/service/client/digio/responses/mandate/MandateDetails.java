package com.flipkart.fintech.pandora.service.client.digio.responses.mandate;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 08/01/25.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class MandateDetails {

    private String customerIdentifier;

    private String customerName;

    private String customerMobile;

    private String authType;

    private String frequency;

    private String firstCollectionDate;

    private String finalCollectionDate;

    private Double maximumAmount;

    private String customerAccountNumber;

    private String customerAccountType;

    private String destinationBankId;

    private String destinationBankName;

    private String sponsorBankName;

    private String npciTxnId;

    private String customerVpa;

    private String partnerBankCode;
    @JsonProperty("mandateType")
    private String mandateType;

    private String debitAccount;

}
