package com.flipkart.fintech.pandora.service.client.digio.transformers.request;

import com.flipkart.fintech.pandora.service.client.digio.config.DigioConfiguration;
import com.flipkart.fintech.pandora.service.client.digio.requests.mandate.ApiMandateRequest;
import com.flipkart.fintech.pandora.api.model.request.nach.mandate.CreateMandateRequest;
import com.flipkart.fintech.pandora.service.client.digio.requests.mandate.MandateData;
import com.flipkart.fintech.pandora.service.client.digio.requests.mandate.UpiMandateRequest;
import org.modelmapper.Converter;
import org.modelmapper.ModelMapper;
import org.modelmapper.spi.MappingContext;

import javax.inject.Inject;

public class MandateRequestTransformer {

    private final ModelMapper modelMapper;
    private final DigioConfiguration digioConfiguration;

    @Inject
    public MandateRequestTransformer(ModelMapper requestMapper, DigioConfiguration digioConfiguration) {
        requestMapper.addConverter(new ApiMandateRequestConverter(digioConfiguration));
        requestMapper.addConverter(new UpiMandateRequestConverter(digioConfiguration));
        this.modelMapper = requestMapper;
        this.digioConfiguration = digioConfiguration;
    }

    public ApiMandateRequest transformToApiMandateRequest(CreateMandateRequest mandateRequest) {
        return modelMapper.map(mandateRequest, ApiMandateRequest.class);
    }

    public UpiMandateRequest transformToUpiMandateRequest(CreateMandateRequest mandateRequest) {
        return modelMapper.map(mandateRequest, UpiMandateRequest.class);
    }

    private static class ApiMandateRequestConverter implements Converter<CreateMandateRequest, ApiMandateRequest> {
        private final DigioConfiguration digioConfiguration;

        ApiMandateRequestConverter(DigioConfiguration digioConfiguration) {
            this.digioConfiguration = digioConfiguration;
        }

        @Override
        public ApiMandateRequest convert(MappingContext<CreateMandateRequest, ApiMandateRequest> mappingContext) {
            CreateMandateRequest createMandateRequest = mappingContext.getSource();
            MandateData mandateData = MandateData.builder()
                    .maximumAmount(createMandateRequest.getMaximumAmount())
                    .firstCollectionDate(createMandateRequest.getFirstCollectionDate())
                    .customerName(createMandateRequest.getFirstName() + " " + createMandateRequest.getLastName())
                    .customerAccountNumber(createMandateRequest.getCustomerAccountNumber())
                    .customerRefNumber(createMandateRequest.getCustomerRefNumber())
                    .destinationBankId(createMandateRequest.getDestinationBankId())
                    .destinationBankName(createMandateRequest.getDestinationBankName())
                    .build();
            return ApiMandateRequest.builder()
                    .mandateData(mandateData)
                    .customerIdentifier(createMandateRequest.getCustomerIdentifier())
                    .apiMandateCorporateConfigId(digioConfiguration.getApiMandateCorporateConfigId())
                    .build();
        }

    }

    private static class UpiMandateRequestConverter implements Converter<CreateMandateRequest, UpiMandateRequest> {

        private final DigioConfiguration digioConfiguration;

        public UpiMandateRequestConverter(DigioConfiguration digioConfiguration) {
            this.digioConfiguration = digioConfiguration;
        }

        @Override
        public UpiMandateRequest convert(MappingContext<CreateMandateRequest, UpiMandateRequest> mappingContext) {
            CreateMandateRequest createMandateRequest = mappingContext.getSource();

            return UpiMandateRequest.builder()
                    .customerIdentifier(createMandateRequest.getCustomerIdentifier())
                    .customerName(createMandateRequest.getFirstName() + " " + createMandateRequest.getLastName())
                    .customerVpa(createMandateRequest.getCustomerVpa())
                    .firstCollectionDate(createMandateRequest.getFirstCollectionDate())
                    .finalCollectionDate(createMandateRequest.getFinalCollectionDate())
                    .maximumAmount(createMandateRequest.getMaximumAmount())
                    .customerAccountNumber(createMandateRequest.getCustomerAccountNumber())
                    .upiMandateCorporateConfigId(digioConfiguration.getUpiMandateCorporateConfigId())
                    .build();
        }


    }
}
