package com.flipkart.fintech.pandora.service.client.digio.transformers.response;

import com.flipkart.fintech.pandora.service.client.digio.responses.kyc.rpd.RPDKycResponse;
import com.flipkart.fintech.pandora.service.client.digio.responses.kyc.rpd.dto.RPDKycResponseDTO;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.Converter;
import org.modelmapper.ModelMapper;
import org.modelmapper.spi.MappingContext;

import javax.inject.Inject;

@Slf4j
public class RPDKycResponseTransformer {

    private final ModelMapper modelMapper;

    @Inject
    public RPDKycResponseTransformer(ModelMapper modelMapper) {
        this.modelMapper = modelMapper;
        init();
    }

    private void init() {
        this.modelMapper.addConverter(new RPDKycRequestConverter());
    }

    public RPDKycResponseDTO transformToDto(RPDKycResponse rpdKycResponse) {
        return this.modelMapper.map(rpdKycResponse, RPDKycResponseDTO.class);
    }

    private static class RPDKycRequestConverter implements Converter<RPDKycResponse, RPDKycResponseDTO> {

        @Override
        public RPDKycResponseDTO convert(MappingContext<RPDKycResponse, RPDKycResponseDTO> mappingContext) {
            RPDKycResponse source = mappingContext.getSource();
            RPDKycResponseDTO target = new RPDKycResponseDTO();
            target.setId(source.getId());
            target.setCreatedAt(source.getCreatedAt());
            target.setStatus(source.getStatus());
            target.setCustomerIdentifier(source.getCustomerIdentifier());
            target.setReferenceId(source.getReferenceId());
            target.setTransactionId(source.getTransactionId());
            if (source.getAccessToken() != null) {
                target.setGwtToken(source.getAccessToken().getId());
            }
            target.setAutoApproved(source.getAutoApproved());
            return target;
        }
    }

}
