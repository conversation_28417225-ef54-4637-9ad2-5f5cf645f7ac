package com.flipkart.fintech.pandora.service.client.digitapAi;

import com.flipkart.fintech.pandora.service.client.cfa.Constants;
import com.google.inject.Inject;
import com.google.inject.name.Named;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import pandora.external.models.request.DigitapAiPanValRequest;
import pandora.external.models.response.DigitapAiPanValResponse;

import javax.ws.rs.client.Entity;
import javax.ws.rs.client.WebTarget;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.util.Base64;
import java.util.Optional;

@Slf4j
public class DigitapAiClient implements DigitapClient {
    public static final String PATH = "/validation/kyc/v2/pan_details";
    private final WebTarget target;

    private final String clientId;
    private final String clientSecret;

    @Inject
    public DigitapAiClient(
            @Named("DigitapAiWebTarget")WebTarget target,
            @Named("DigitapAiClientId") String clientId,
            @Named("DigitapAiClientSecret") String clientSecret
    ) {
        this.target = target;
        this.clientId = clientId;
        this.clientSecret = clientSecret;
    }

    public String generateBase64EncodedString(String clientId, String clientSecret) {
      return Base64.getEncoder().encodeToString((clientId + ":" + clientSecret).getBytes());
    }

    @Override
    public DigitapAiPanValResponse digitapAiPanValResponse(DigitapAiPanValRequest digitApaiPanValRequest){
        Response response = null;
        try {
            response = target
                    .path(PATH)
                    .request(MediaType.APPLICATION_JSON_TYPE)
                    .header("content-type", "application/json")
                    .header(Constants.AUTHORIZATION_HEADER, "Basic " + generateBase64EncodedString(clientId, clientSecret))
                    .post(Entity.json(digitApaiPanValRequest));

            return Optional.ofNullable(response)
                    .filter(res -> res.getStatus() == 200)
                    .map(res -> res.readEntity(DigitapAiPanValResponse.class))
                    .orElse(new DigitapAiPanValResponse());
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            if (Objects.nonNull(response)) {
                response.close();
            }
        }
    }
}