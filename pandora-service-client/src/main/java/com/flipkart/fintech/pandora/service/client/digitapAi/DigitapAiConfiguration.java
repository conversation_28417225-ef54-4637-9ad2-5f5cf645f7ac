package com.flipkart.fintech.pandora.service.client.digitapAi;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@Builder
@RequiredArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class DigitapAiConfiguration {

    private String url;

    private String clientId;

    private String clientSecret;

    private String smPrivateKey;

    private String digitapPublicKey;

    private Boolean isMockEnabled;

}
