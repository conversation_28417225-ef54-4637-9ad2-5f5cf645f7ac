package com.flipkart.fintech.pandora.service.client.digitapAi;

import com.flipkart.fintech.pandora.service.client.cfa.MockHelper;
import com.google.inject.Inject;
import com.google.inject.name.Named;
import pandora.external.models.request.DigitapAiPanValRequest;
import pandora.external.models.response.DigitapAiPanValResponse;

import java.util.Optional;

public class MockDecoratedDigitapClient implements DigitapClient {

    private final DigitapClient digitapClient;

    private final MockHelper mockHelper;

    @Inject
    public MockDecoratedDigitapClient(@Named("directDigitapIntegrationClient") DigitapClient digitapClient, MockHelper mockHelper) {
        this.digitapClient = digitapClient;
        this.mockHelper = mockHelper;
    }

    @Override
    public DigitapAiPanValResponse digitapAiPanValResponse(DigitapAiPanValRequest digitApaiPanValRequest) {
        Optional<MockHelper.MockedApi> mockedApi = mockHelper.findMockedApi("verifyPanFromDigitap");
        if (mockedApi.isPresent()) {
            return mockHelper.getData(mockedApi.get().getDataKey(), DigitapAiPanValResponse.class);
        }
        return digitapClient.digitapAiPanValResponse(digitApaiPanValRequest);
    }
}
