package com.flipkart.fintech.pandora.service.client.encryptor;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.pandora.service.client.exceptions.DigioEncryptionException;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@Slf4j
public class DigioEncryptor implements DataEncryptorDecryptor {

    private final JweAes128EncryptorDecryptor dataEncryptorDecryptor;
    private final ObjectMapper objectMapper = new ObjectMapper();

    public DigioEncryptor(String base64EncodedPublicKey, String base64EncodedPrivateKey) {
        dataEncryptorDecryptor = new JweAes128EncryptorDecryptor(base64EncodedPublicKey, base64EncodedPrivateKey);
    }

    @Override
    public String encrypt(String data) {
        String encryptedPayload = dataEncryptorDecryptor.encrypt(data);
        Map<String, String> sandboxPayload = new HashMap<>();
        sandboxPayload.put("body", encryptedPayload);
        sandboxPayload.put("txn_id",  UUID.randomUUID().toString());
        log.info("encrypted payload: {}", sandboxPayload);
        try {
            return objectMapper.writeValueAsString(sandboxPayload);
        } catch (JsonProcessingException e) {
            log.error("Failed to serialize map to JSON map {}", sandboxPayload, e);
            throw new DigioEncryptionException("JSON serialization error");
        }
    }

    @Override
    public String decrypt(String data) {
        String responseValue = "";
        try {
            Map<String, String> jsonMap = objectMapper.readValue(data, Map.class);
            if(!jsonMap.containsKey("response") ||  !jsonMap.containsKey("txn_id")){
                throw new DigioEncryptionException("digio response does not contain txn_id or response");
            }
            responseValue = jsonMap.get("response");
            String txnId = jsonMap.get("txn_id");
            log.info("got response for txnID {} response {}", txnId, responseValue);
        } catch (JsonProcessingException e) {
            log.error("Could not parse JSON data: {}. Received: {}", e.getMessage(), data);
            throw new DigioEncryptionException("Failed to parse JSON: " + e.getMessage());
        } catch (ClassCastException e) {
            log.error("Unexpected data type in JSON: {}. Received: {}", e.getMessage(), data);
            throw new DigioEncryptionException("Unexpected data type in JSON: " + e.getMessage());
        }
        return dataEncryptorDecryptor.decrypt(responseValue);
    }
}
