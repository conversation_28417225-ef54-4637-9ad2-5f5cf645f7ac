package com.flipkart.fintech.pandora.service.client.epaylater;

import com.flipkart.fintech.pandora.service.client.epaylater.request.EpaylaterPaybackRequest;
import com.flipkart.fintech.pandora.service.client.epaylater.response.EpaylaterLateFeeResponse;
import com.flipkart.fintech.pandora.service.client.epaylater.response.EpaylaterPaybackResponse;
import com.flipkart.fintech.pandora.service.client.epaylater.request.EpaylaterRefundRequest;
import com.flipkart.fintech.pandora.service.client.epaylater.request.EpaylaterSaleRequest;
import com.flipkart.fintech.pandora.service.client.epaylater.response.EpaylaterRefundResponse;
import com.flipkart.fintech.pandora.service.client.epaylater.response.EpaylaterSaleResponse;

public interface EpaylaterServiceClient {
    EpaylaterSaleResponse recordSaleTransaction(EpaylaterSaleRequest epaylaterSaleRequest);
    EpaylaterRefundResponse recordRefundTransaction(EpaylaterRefundRequest epaylaterRefundRequest,String epaylaterOrderId);
    EpaylaterPaybackResponse recordPaybackForwardTransaction(EpaylaterPaybackRequest epaylaterPaybackRequest,String pgIdentifier);
    EpaylaterLateFeeResponse getEpaylaterInvoiceDetails(String invoiceNumber);
}
