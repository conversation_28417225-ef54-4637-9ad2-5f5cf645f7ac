package com.flipkart.fintech.pandora.service.client.epaylater.response;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigDecimal;

public class EpaylaterLateFeeResponse {
    @JsonProperty("dues_amount")
    private BigDecimal duesAmount;
    @JsonProperty("base_amount")
    private BigDecimal baseAmount;
    @JsonProperty("igst_amount")
    private BigDecimal igstAmount;
    @JsonProperty("igst_rate")
    private BigDecimal igstRate;
    @JsonProperty("cgst_amount")
    private BigDecimal cgstAmount;
    @JsonProperty("cgst_rate")
    private BigDecimal cgstRate;
    @JsonProperty("sgst_amount")
    private BigDecimal sgstAmount;
    @JsonProperty("sgst_rate")
    private BigDecimal sgstRate;
    @JsonProperty("invoice_number")
    private String invoiceNumber;
    @JsonProperty("user_identifier")
    private String userIdentifier;
    @JsonProperty("status")
    private String status;

    public BigDecimal getDuesAmount() {
        return duesAmount;
    }

    public void setDuesAmount(BigDecimal duesAmount) {
        this.duesAmount = duesAmount;
    }

    public BigDecimal getBaseAmount() {
        return baseAmount;
    }

    public void setBaseAmount(BigDecimal baseAmount) {
        this.baseAmount = baseAmount;
    }

    public BigDecimal getIgstAmount() {
        return igstAmount;
    }

    public void setIgstAmount(BigDecimal igstAmount) {
        this.igstAmount = igstAmount;
    }

    public BigDecimal getIgstRate() {
        return igstRate;
    }

    public void setIgstRate(BigDecimal igstRate) {
        this.igstRate = igstRate;
    }

    public BigDecimal getCgstAmount() {
        return cgstAmount;
    }

    public void setCgstAmount(BigDecimal cgstAmount) {
        this.cgstAmount = cgstAmount;
    }

    public BigDecimal getCgstRate() {
        return cgstRate;
    }

    public void setCgstRate(BigDecimal cgstRate) {
        this.cgstRate = cgstRate;
    }

    public BigDecimal getSgstAmount() {
        return sgstAmount;
    }

    public void setSgstAmount(BigDecimal sgstAmount) {
        this.sgstAmount = sgstAmount;
    }

    public BigDecimal getSgstRate() {
        return sgstRate;
    }

    public void setSgstRate(BigDecimal sgstRate) {
        this.sgstRate = sgstRate;
    }

    public String getInvoiceNumber() {
        return invoiceNumber;
    }

    public void setInvoiceNumber(String invoiceNumber) {
        this.invoiceNumber = invoiceNumber;
    }

    public String getUserIdentifier() {
        return userIdentifier;
    }

    public void setUserIdentifier(String userIdentifier) {
        this.userIdentifier = userIdentifier;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }


    @Override
    public String toString() {
        return "EpaylaterLateFeeResponse{" +
                "duesAmount=" + duesAmount +
                ", baseAmount=" + baseAmount +
                ", igstAmount=" + igstAmount +
                ", igstRate=" + igstRate +
                ", cgstAmount=" + cgstAmount +
                ", cgstRate=" + cgstRate +
                ", sgstAmount=" + sgstAmount +
                ", sgstRate=" + sgstRate +
                ", invoiceNumber='" + invoiceNumber + '\'' +
                ", userIdentifier='" + userIdentifier + '\'' +
                ", status='" + status + '\'' +
                '}';
    }
}
