package com.flipkart.fintech.pandora.service.client.epaylater.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;


@JsonIgnoreProperties(ignoreUnknown = true)
public class EpaylaterPaybackResponse {
    private List<EpaylaterPaybackPaymentStatusResponse> customerPaymentStatus;
    @JsonProperty("lateFee")
    private EpaylaterLateFeeResponse lateFee;

    public EpaylaterLateFeeResponse getEpaylaterLateFee() {
        return lateFee;
    }

    public void setEpaylaterLateFee(EpaylaterLateFeeResponse lateFee) {
        this.lateFee = lateFee;
    }


    public List<EpaylaterPaybackPaymentStatusResponse> getCustomerPaymentStatus() {
        return customerPaymentStatus;
    }

    public void setCustomerPaymentStatus(List<EpaylaterPaybackPaymentStatusResponse> customerPaymentStatus) {
        this.customerPaymentStatus = customerPaymentStatus;
    }

    @Override
    public String toString() {
        return "EpaylaterPaybackResponse{" +
                ", customerPaymentStatus=" + customerPaymentStatus +
                ", epaylaterLateFee=" + lateFee +
                '}';
    }

}
