package com.flipkart.fintech.pandora.service.client.exceptions;

import com.flipkart.fintech.pandora.service.client.pl.response.ErrorResponse;

public class ClientErrorException extends LenderException {

  private Integer statusCode;


  public ClientErrorException(Integer statusCode, ErrorResponse error) {
    super(error);
    this.statusCode = statusCode;
  }

  public Integer getStatusCode() {
    return statusCode;
  }
}
