package com.flipkart.fintech.pandora.service.client.experian;


import com.flipkart.fintech.pandora.service.client.experian.configs.ExperianConfiguration;
import com.flipkart.fintech.pandora.service.client.experian.requests.EnhanceMatchRequest;
import com.flipkart.fintech.pandora.service.client.experian.requests.OnDemandRequest;
import com.flipkart.fintech.pandora.service.client.experian.responses.EnhanceMatchResponse;
import com.flipkart.fintech.pandora.service.client.experian.responses.OnDemandResponse;
import com.google.inject.Inject;
import com.google.inject.name.Named;
import lombok.extern.slf4j.Slf4j;

import javax.ws.rs.client.Client;
import javax.ws.rs.client.Entity;
import javax.ws.rs.client.Invocation;
import javax.ws.rs.client.WebTarget;
import javax.ws.rs.core.Form;
import javax.ws.rs.core.Response;
@Slf4j
public class CheckBureauScoreByExperian {

    private final Client client;
    private final ExperianConfiguration experianConfiguration;

    @Inject
    public CheckBureauScoreByExperian(@Named("ExperianClient") Client client, ExperianConfiguration experianConfiguration){
        this.client = client;
        this.experianConfiguration = experianConfiguration;
    }

    public EnhanceMatchResponse getCreditReport(String authToken, EnhanceMatchRequest enhanceMatchRequest){
        Response response = null;
        EnhanceMatchResponse enhanceMatchResponse = null;
        try{
            String enhanceMatchEndPoint = experianConfiguration.getEnhanceMatchUrl();
            WebTarget webTarget = client.target(enhanceMatchEndPoint);
            Invocation.Builder invocationBuilder = webTarget.request();
            invocationBuilder.header("Content-Type", "application/x-www-form-urlencoded");
            invocationBuilder.header("Authorization","Bearer " + authToken);
            invocationBuilder.header("Accept","*/*");
            Form form = constructForm(enhanceMatchRequest);
            response = invocationBuilder.post(Entity.form(form));
            if (response.getStatus() == 200){
                enhanceMatchResponse = response.readEntity(EnhanceMatchResponse.class);
            }
        }catch (Exception e){
            log.error("Error calling experian : {} , {}", e.getMessage(), enhanceMatchRequest.getFirstName());
            throw new RuntimeException(e);
        }finally {
            if (response != null){
                response.close();
            }
        }
        return enhanceMatchResponse;
    }

    public OnDemandResponse getRefreshCreditReport(String authToken, OnDemandRequest onDemandRequest){
        Response response = null;
        OnDemandResponse onDemandResponse = null;
        try{
            String onDemandEndPoint = experianConfiguration.getOnDemandUrl();
            WebTarget webTarget = client.target(onDemandEndPoint);
            Invocation.Builder invocationBuilder = webTarget.request();
            invocationBuilder.header("Content-Type", "application/x-www-form-urlencoded");
            invocationBuilder.header("Content-Type", "text/plain");
            invocationBuilder.header("Authorization","Bearer " + authToken);
            invocationBuilder.header("Accept","*/*");
            Form form = constructOnDemandForm(onDemandRequest);
            response = invocationBuilder.post(Entity.form(form));
            if (response.getStatus() == 200){
                onDemandResponse = response.readEntity(OnDemandResponse.class);
            }
        }catch (Exception e){
            log.error("Error calling experian : {} , {}", e.getMessage(), onDemandRequest.getHitId());
            throw new RuntimeException(e);
        }finally {
            if (response != null){
                response.close();
            }
        }
        return onDemandResponse;
    }

    private Form constructOnDemandForm(OnDemandRequest onDemandRequest){
        Form form = new Form();
        form.param("clientName",onDemandRequest.getClientName());
        form.param("hitId",onDemandRequest.getHitId());
        return form;
    }
    private Form constructForm(EnhanceMatchRequest enhanceMatchRequest){
        Form form = new Form();
        form.param("clientName",enhanceMatchRequest.getClientName());
        form.param("firstName",enhanceMatchRequest.getFirstName());
        form.param("surName",enhanceMatchRequest.getSurName());
        form.param("mobileNo",enhanceMatchRequest.getMobileNo());
        form.param("email",enhanceMatchRequest.getEmail());
        form.param("pan",enhanceMatchRequest.getPan());
        form.param("allowInput",enhanceMatchRequest.getAllowInput());
        form.param("allowEdit",enhanceMatchRequest.getAllowEdit());
        form.param("allowCaptcha",enhanceMatchRequest.getAllowCaptcha());
        form.param("allowConsent",enhanceMatchRequest.getAllowConsent());
        form.param("allowEmailVerify",enhanceMatchRequest.getAllowEmailVerify());
        form.param("allowVoucher",enhanceMatchRequest.getAllowVoucher());
        form.param("voucherCode",experianConfiguration.getCredentials().getVoucherCode());
        form.param("noValidationByPass",enhanceMatchRequest.getNoValidationByPass());
        form.param("emailConditionalByPass",enhanceMatchRequest.getEmailConditionalByPass());
        form.param("reason",enhanceMatchRequest.getReason());
        return form;
    }
}
