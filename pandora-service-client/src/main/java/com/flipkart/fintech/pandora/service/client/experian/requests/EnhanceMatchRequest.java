package com.flipkart.fintech.pandora.service.client.experian.requests;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.ToString;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
@ToString
public class EnhanceMatchRequest {
    @JsonProperty("firstName")
    private String firstName;

    @JsonProperty("surName")
    private String surName;

    @JsonProperty("mobileNo")
    private String mobileNo;

    @JsonProperty("email")
    private String email;

    @JsonProperty("pan")
    private String pan;

    @JsonProperty("mobileNo")
    private String allowInput;

    @JsonProperty("clientName")
    private String clientName;

    @JsonProperty("email")
    private String allowEdit;

    @JsonProperty("pan")
    private String allowCaptcha;

    @JsonProperty("allowConsent")
    private String allowConsent;

    @JsonProperty("allowEmailVerify")
    private String allowEmailVerify;

    @JsonProperty("allowVoucher")
    private String allowVoucher;

    @JsonProperty("voucherCode")
    private String voucherCode;

    @JsonProperty("reason")
    private String reason;

    @JsonProperty("noValidationByPass")
    private String noValidationByPass;

    @JsonProperty("emailConditionalByPass")
    private String emailConditionalByPass;
}
