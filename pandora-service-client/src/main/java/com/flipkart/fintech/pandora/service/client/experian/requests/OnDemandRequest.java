package com.flipkart.fintech.pandora.service.client.experian.requests;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.ToString;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class OnDemandRequest {
    @JsonProperty("clientName")
    private String clientName;

    @JsonProperty("hitId")
    private String hitId;
}
