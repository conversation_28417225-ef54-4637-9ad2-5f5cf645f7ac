package com.flipkart.fintech.pandora.service.client.experian.responses;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.ToString;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class OnDemandResponse {
    @JsonProperty("errorString")
    private String errorString;

    @JsonProperty("stgOneHitId")
    private String stgOneHitId;

    @JsonProperty("showHtmlReportForCreditReport")
    private String showHtmlReportForCreditReport;

    @JsonProperty("userId")
    private int userId;
}
