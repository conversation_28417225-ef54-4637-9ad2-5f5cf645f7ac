package com.flipkart.fintech.pandora.service.client.filters.ClientFilter;

import javax.ws.rs.core.Feature;
import javax.ws.rs.core.FeatureContext;

/**
 * <AUTHOR>
 * @date 28/11/23
 */
class ClientFeature implements Feature {

    private final ClientFilter filter;

    public ClientFeature(String header, String key) {
        this.filter = new ClientFilter(header, key);
    }

    public ClientFeature(){
        this.filter = new ClientFilter();
    }


    @Override
    public boolean configure(FeatureContext context) {
        context.register(this.filter);
        return true;
    }
}
