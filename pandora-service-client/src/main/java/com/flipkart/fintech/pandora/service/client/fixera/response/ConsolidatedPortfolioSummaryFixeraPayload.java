package com.flipkart.fintech.pandora.service.client.fixera.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class ConsolidatedPortfolioSummaryFixeraPayload {
    private Integer totalInvestmentAmount;
    private Integer totalInterestAmount;
    private Integer totalFd;
    private BigDecimal currentGainsTillDate;
    private List<Object> issuers;
}
