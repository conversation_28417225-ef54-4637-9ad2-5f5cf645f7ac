package com.flipkart.fintech.pandora.service.client.fixera.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

@JsonIgnoreProperties
@Getter
@Setter
@NoArgsConstructor(force = true)
public class FDData {
    @JsonProperty("last_state")
    private final FDLastState lastState;
    @JsonProperty("next_state")
    private final FDNextState nextState;


}
