package com.flipkart.fintech.pandora.service.client.fixera.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.sun.org.apache.xpath.internal.operations.Bool;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@NoArgsConstructor
@Getter
@Data
public class FDPrevState {
    @JsonProperty
    private String state;
    @JsonProperty
    private String issuerCode;
    @JsonProperty
    private String subState;
    @JsonProperty
    private String fUserTransactionId;
    @JsonProperty
    private Integer daysToGo;
    @JsonProperty
    private String maturityDate;
    @JsonProperty
    private String maturityAmount;
    @JsonProperty
    private String investedAmount;
    @JsonProperty
    private String investmentAmount;
    @JsonProperty
    private Boolean productSeniorCitizen;
    @JsonProperty
    private Boolean productWomen;
    @JsonProperty
    private Boolean productTaxSaver;
    @JsonProperty
    private String autoRenewal;
    @JsonProperty
    private String interest;
    @JsonProperty
    private long createdAt;
    @JsonProperty
    private String status;
    @JsonProperty
    private String provisionalId;
    @JsonProperty
    private String eventDetails;
    @JsonProperty
    private String partnerUserId;
}
