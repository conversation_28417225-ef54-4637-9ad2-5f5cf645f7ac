package com.flipkart.fintech.pandora.service.client.fixera.response;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.*;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
public class PartnerUser {

    @JsonProperty("f_partner_id")
    private String fPartnerId;
    @JsonProperty("f_partner_user_id")
    private String fPartnerUserId;
}
