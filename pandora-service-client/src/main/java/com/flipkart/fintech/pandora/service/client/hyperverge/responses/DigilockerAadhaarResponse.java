package com.flipkart.fintech.pandora.service.client.hyperverge.responses;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Map;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class DigilockerAadhaarResponse {
    @JsonProperty("status")
    private String status;

    @JsonProperty("statusCode")
    private String statusCode;

    @JsonProperty("result")
    private DigilockerAadhaarResult result;

    private Object error;
}
