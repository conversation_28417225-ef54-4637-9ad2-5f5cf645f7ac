package com.flipkart.fintech.pandora.service.client.hyperverge.responses;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class DigilockerAddresFields {
    @JsonProperty("co")
    private String careOf;

    @JsonProperty("country")
    private String country;

    @JsonProperty("district")
    private String district;

    @JsonProperty("subDistrict")
    private String subDistrict;

    @JsonProperty("postOffice")
    private String postOffice;

    @JsonProperty("house")
    private String house;

    @JsonProperty("locality")
    private String locality;

    @JsonProperty("pincode")
    private String pincode;

    @JsonProperty("state")
    private String state;

    @JsonProperty("street")
    private String street;

    @JsonProperty("vtc")
    private String villageTownCity;

    @JsonProperty("landmark")
    private String landmark;
}
