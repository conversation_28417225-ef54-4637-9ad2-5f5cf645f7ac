package com.flipkart.fintech.pandora.service.client.idfcV2.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class IdfcError {

    @JsonProperty("errorCode")
    private String errorCode;

    @JsonProperty("errorType")
    private String errorType;

    @JsonProperty("errorDescription")
    private String errorDescription;

    @JsonProperty("correlationId")
    private String correlationId;

    @JsonProperty("dateTime")
    private String dateTime;

}