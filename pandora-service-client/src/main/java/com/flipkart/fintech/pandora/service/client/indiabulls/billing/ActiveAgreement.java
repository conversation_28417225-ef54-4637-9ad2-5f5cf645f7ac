package com.flipkart.fintech.pandora.service.client.indiabulls.billing;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigDecimal;

/**
 * <AUTHOR> H Adavi
 *
 * // TODO Need Tenure Information in this to differentiate Pay Later and EMI
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class ActiveAgreement {
	
	/**
	 * Transaction Id generated during Sale Authorization
	 */
	@JsonProperty("RRN_No")
	private String rrnNo;
	
	/**
	 * Identifier generated after loan creation
	 */
	@JsonProperty("AGREEMENTID")
	private String agreementId;
	
	@JsonProperty("Loan_Amount")
	private String loanAmount;
	
	/**
	 * Not Applicable for Flipkart
	 */
	@JsonProperty("Transaction_Date")
	private String transactionDate;
	
	/**
	 * Not Applicable for Flipkart
	 */
	@JsonProperty("Transaction_Amount")
	private String transactionAmount;
	
	/**
	 * Latest Late Fees applied to a loan. ( Previous late fees applied if paid would be reset. As confirmed by Indranil )
	 */
	@JsonProperty("Late_Fees")
	private String lateFees;
	
	/**
	 * Remaining principal to be paid by the customer
	 */
	@JsonProperty("Principal_Pending")
	private String principalPending;
	
	/**
	 * Interest component remaining
	 */
	@JsonProperty("Interest_Pending")
	private String interestPending;
	
	@JsonProperty("Order_Value")
	private String orderValue;
	
	@JsonProperty("Interest_Applied")
	private String interestApplied;
	
	@JsonProperty("Last_EMI_amount")
	private String lastEMIAmount;
	
	@JsonProperty("Last_EMI_Date")
	private String lastEMIDate;
	
	@JsonProperty("GraceDays")
	private String graceDays;
	
	@JsonProperty("Current_Month_Due_date")
	private String currentMonthDueDate;
	
	@JsonProperty("Next_statement_date")
	private String nextStatementDate;
	
	@JsonProperty("Unbilled_amount")
	private String unbilledAmount;
	
	@JsonProperty("Amount_due")
	private String amountDue;
	
	@JsonProperty("EMI_Amount")
	private String emiAmount;
	
	@JsonProperty("tenure")
	private Integer tenure;

	@JsonProperty("CHARGE_DETAIL_CUR_MONTH")
	private ChargeDetail chargeDetailCurrentMonth;

	@JsonProperty("CHARGE_DETAIL_PREV_MONTH")
	private ChargeDetail chargeDetailPrevMonth;

	public ChargeDetail getChargeDetailCurrentMonth() {
		return chargeDetailCurrentMonth;
	}

	public void setChargeDetailCurrentMonth(ChargeDetail chargeDetailCurrentMonth) {
		this.chargeDetailCurrentMonth = chargeDetailCurrentMonth;
	}

	public ChargeDetail getChargeDetailPrevMonth() {
		return chargeDetailPrevMonth;
	}

	public void setChargeDetailPrevMonth(ChargeDetail chargeDetailPrevMonth) {
		this.chargeDetailPrevMonth = chargeDetailPrevMonth;
	}

	public String getRrnNo() {
		return rrnNo;
	}
	
	public void setRrnNo(String rrnNo) {
		this.rrnNo = rrnNo;
	}
	
	public String getAgreementId() {
		return agreementId;
	}
	
	public void setAgreementId(String agreementId) {
		this.agreementId = agreementId;
	}
	
	public String getLoanAmount() {
		return loanAmount;
	}
	
	public void setLoanAmount(String loanAmount) {
		this.loanAmount = loanAmount;
	}
	
	public String getTransactionDate() {
		return transactionDate;
	}
	
	public void setTransactionDate(String transactionDate) {
		this.transactionDate = transactionDate;
	}
	
	public String getTransactionAmount() {
		return transactionAmount;
	}
	
	public void setTransactionAmount(String transactionAmount) {
		this.transactionAmount = transactionAmount;
	}
	
	public BigDecimal getLateFees() {
		return new BigDecimal(lateFees);
	}
	
	public void setLateFees(String lateFees) {
		this.lateFees = lateFees;
	}
	
	public String getPrincipalPending() {
		return principalPending;
	}
	
	public void setPrincipalPending(String principalPending) {
		this.principalPending = principalPending;
	}
	
	public String getInterestPending() {
		return interestPending;
	}
	
	public void setInterestPending(String interestPending) {
		this.interestPending = interestPending;
	}
	
	public String getOrderValue() {
		return orderValue;
	}
	
	public void setOrderValue(String orderValue) {
		this.orderValue = orderValue;
	}
	
	public String getInterestApplied() {
		return interestApplied;
	}
	
	public void setInterestApplied(String interestApplied) {
		this.interestApplied = interestApplied;
	}
	
	public String getLastEMIAmount() {
		return lastEMIAmount;
	}
	
	public void setLastEMIAmount(String lastEMIAmount) {
		this.lastEMIAmount = lastEMIAmount;
	}
	
	public String getLastEMIDate() {
		return lastEMIDate;
	}
	
	public void setLastEMIDate(String lastEMIDate) {
		this.lastEMIDate = lastEMIDate;
	}
	
	public String getGraceDays() {
		return graceDays;
	}
	
	public void setGraceDays(String graceDays) {
		this.graceDays = graceDays;
	}
	
	public String getCurrentMonthDueDate() {
		return currentMonthDueDate;
	}
	
	public void setCurrentMonthDueDate(String currentMonthDueDate) {
		this.currentMonthDueDate = currentMonthDueDate;
	}
	
	public String getNextStatementDate() {
		return nextStatementDate;
	}
	
	public void setNextStatementDate(String nextStatementDate) {
		this.nextStatementDate = nextStatementDate;
	}
	
	public String getUnbilledAmount() {
		return unbilledAmount;
	}
	
	public void setUnbilledAmount(String unbilledAmount) {
		this.unbilledAmount = unbilledAmount;
	}
	
	public String getAmountDue() {
		return amountDue;
	}
	
	public void setAmountDue(String amountDue) {
		this.amountDue = amountDue;
	}
	
	public String getEmiAmount() {
		return emiAmount;
	}
	
	public void setEmiAmount(String emiAmount) {
		this.emiAmount = emiAmount;
	}
	
	public Integer getTenure() {
		return tenure;
	}
	
	public void setTenure(Integer tenure) {
		this.tenure = tenure;
	}

	@Override
	public String toString() {
		return "ActiveAgreement{" +
				"rrnNo='" + rrnNo + '\'' +
				", agreementId='" + agreementId + '\'' +
				", loanAmount='" + loanAmount + '\'' +
				", transactionDate='" + transactionDate + '\'' +
				", transactionAmount='" + transactionAmount + '\'' +
				", lateFees='" + lateFees + '\'' +
				", principalPending='" + principalPending + '\'' +
				", interestPending='" + interestPending + '\'' +
				", orderValue='" + orderValue + '\'' +
				", interestApplied='" + interestApplied + '\'' +
				", lastEMIAmount='" + lastEMIAmount + '\'' +
				", lastEMIDate='" + lastEMIDate + '\'' +
				", graceDays='" + graceDays + '\'' +
				", currentMonthDueDate='" + currentMonthDueDate + '\'' +
				", nextStatementDate='" + nextStatementDate + '\'' +
				", unbilledAmount='" + unbilledAmount + '\'' +
				", amountDue='" + amountDue + '\'' +
				", emiAmount='" + emiAmount + '\'' +
				", tenure=" + tenure +
				", chargeDetailCurrentMonth=" + chargeDetailCurrentMonth +
				", chargeDetailPrevMonth=" + chargeDetailPrevMonth +
				'}';
	}
}
