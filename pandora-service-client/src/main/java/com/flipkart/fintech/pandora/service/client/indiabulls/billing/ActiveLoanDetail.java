package com.flipkart.fintech.pandora.service.client.indiabulls.billing;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * <AUTHOR> H Adavi
 *
 * Statement Late fee = Sum of #ActiveAgreement.LateFees
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class ActiveLoanDetail {
	
	@JsonProperty("ACTIVE_AGREEMENT")
	private List<ActiveAgreement> activeAgreements;

	@JsonProperty("")
	public List<ActiveAgreement> getActiveAgreements() {
		return activeAgreements;
	}
	
	public void setActiveAgreements(List<ActiveAgreement> activeAgreements) {
		this.activeAgreements = activeAgreements;
	}
	
	@Override
	public String toString() {
		return "ActiveLoanDetail{" +
				"activeAgreements=" + activeAgreements +
				'}';
	}
}
