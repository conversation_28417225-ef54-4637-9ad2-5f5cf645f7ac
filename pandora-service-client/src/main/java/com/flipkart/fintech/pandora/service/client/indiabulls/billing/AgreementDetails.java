package com.flipkart.fintech.pandora.service.client.indiabulls.billing;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigDecimal;

@JsonIgnoreProperties(ignoreUnknown = true)
public class AgreementDetails {

    @JsonProperty("TRANSACTIONDATE")
    private String transactionDate;

    @JsonProperty("TRANSACTIONDESCRIPTION")
    private String transactionDescription;

    @JsonProperty("CHEQUENO")
    private String chequeNumber;

    @JsonProperty("AMOUNTDR")
    private String amountDebit;

    @JsonProperty("AMOUNTCR")
    private String amountCredit;

    @JsonProperty("OUTSTANDINGBAL")
    private String outstandingBalance;

    @JsonProperty("OPENINGBAL")
    private String openingBalance;

    @JsonProperty("CLOSINGBAL")
    private String closingBalance;


    public String getTransactionDate() {
        return transactionDate;
    }

    public void setTransactionDate(String transactionDate) {
        this.transactionDate = transactionDate;
    }

    public String getTransactionDescription() {
        return transactionDescription;
    }

    public void setTransactionDescription(String transactionDescription) {
        this.transactionDescription = transactionDescription;
    }

    public String getChequeNumber() {
        return chequeNumber;
    }

    public void setChequeNumber(String chequeNumber) {
        this.chequeNumber = chequeNumber;
    }

    public BigDecimal getAmountDebit() {
        return new BigDecimal(amountDebit);
    }

    public void setAmountDebit(String amountDebit) {
        this.amountDebit = amountDebit;
    }

    public BigDecimal getAmountCredit() {
        return new BigDecimal(amountCredit);
    }

    public void setAmountCredit(String amountCredit) {
        this.amountCredit = amountCredit;
    }

    public BigDecimal getOutstandingBalance() {
        return new BigDecimal(outstandingBalance);
    }

    public void setOutstandingBalance(String outstandingBalance) {
        this.outstandingBalance = outstandingBalance;
    }

    public BigDecimal getOpeningBalance() {
        return new BigDecimal(openingBalance);
    }

    public void setOpeningBalance(String openingBalance) {
        this.openingBalance = openingBalance;
    }

    public BigDecimal getClosingBalance() {
        return new BigDecimal(closingBalance);
    }

    public void setClosingBalance(String closingBalance) {
        this.closingBalance = closingBalance;
    }

    @Override
    public String toString() {
        return "AgreementDetails{" +
                "transactionDate='" + transactionDate + '\'' +
                ", transactionDescription='" + transactionDescription + '\'' +
                ", chequeNumber='" + chequeNumber + '\'' +
                ", amountDebit='" + amountDebit + '\'' +
                ", amountCredit='" + amountCredit + '\'' +
                ", outstandingBalance='" + outstandingBalance + '\'' +
                ", openingBalance='" + openingBalance + '\'' +
                ", closingBalance='" + closingBalance + '\'' +
                '}';
    }
}
