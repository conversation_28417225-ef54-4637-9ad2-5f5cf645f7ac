package com.flipkart.fintech.pandora.service.client.indiabulls.billing;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ChargeIB {

    @JsonProperty("Chargeid_p")
    private String chargeId;

    @JsonProperty("Chargedesc_p")
    private String chargeDescription;

    @JsonProperty("Total_Amount_Due_p")
    private String totalAmountDue;

    @JsonProperty("Total_Adjusted_p")
    private String totalAdjusted ;

    public String getChargeId() {
        return chargeId;
    }

    public void setChargeId(String chargeId) {
        this.chargeId = chargeId;
    }

    public String getChargeDescription() {
        return chargeDescription;
    }

    public void setChargeDescription(String chargeDescription) {
        this.chargeDescription = chargeDescription;
    }

    public String getTotalAmountDue() {
        return totalAmountDue;
    }

    public void setTotalAmountDue(String totalAmountDue) {
        this.totalAmountDue = totalAmountDue;
    }

    public String getTotalAdjusted() {
        return totalAdjusted;
    }

    public void setTotalAdjusted(String totalAdjusted) {
        this.totalAdjusted = totalAdjusted;
    }

    @Override
    public String toString() {
        return "ChargeIB{" +
                "chargeId='" + chargeId + '\'' +
                ", chargeDescription='" + chargeDescription + '\'' +
                ", totalAmountDue='" + totalAmountDue + '\'' +
                ", totalAdjusted='" + totalAdjusted + '\'' +
                '}';
    }
}
