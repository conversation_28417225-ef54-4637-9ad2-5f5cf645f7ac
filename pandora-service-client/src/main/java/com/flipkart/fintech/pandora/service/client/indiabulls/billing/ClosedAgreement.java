package com.flipkart.fintech.pandora.service.client.indiabulls.billing;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ClosedAgreement {

    @JsonProperty("RRN_No")
    private String rrnNo;

    @JsonProperty("AGREEMENTID")
    private String agreementId;

    @JsonProperty("Order_Value")
    private String orderValue;

    @JsonProperty("Interest_Applied")
    private String interestApplied;

    @JsonProperty("Loan_Amount")
    private String loanAmount;

    @JsonProperty("EMI_amount")
    private String emiAmount;

    @JsonProperty("Last_EMI_Date")
    private String lastEMIDate;

    @JsonProperty("Status")
    private String status;


    public ClosedAgreement(){
        status = "C";
    }

    @JsonProperty("Closure_date")
    private String closureDate;

    public String getRrnNo() {
        return rrnNo;
    }

    public void setRrnNo(String rrnNo) {
        this.rrnNo = rrnNo;
    }

    public String getAgreementId() {
        return agreementId;
    }

    public void setAgreementId(String agreementId) {
        this.agreementId = agreementId;
    }

    public String getOrderValue() {
        return orderValue;
    }

    public void setOrderValue(String orderValue) {
        this.orderValue = orderValue;
    }

    public String getInterestApplied() {
        return interestApplied;
    }

    public void setInterestApplied(String interestApplied) {
        this.interestApplied = interestApplied;
    }

    public String getLoanAmount() {
        return loanAmount;
    }

    public void setLoanAmount(String loanAmount) {
        this.loanAmount = loanAmount;
    }

    public String getEmiAmount() {
        return emiAmount;
    }

    public void setEmiAmount(String emiAmount) {
        this.emiAmount = emiAmount;
    }

    public String getLastEMIDate() {
        return lastEMIDate;
    }

    public void setLastEMIDate(String lastEMIDate) {
        this.lastEMIDate = lastEMIDate;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getClosureDate() {
        return closureDate;
    }

    public void setClosureDate(String closureDate) {
        this.closureDate = closureDate;
    }

    @Override
    public String toString() {
        return "ClosedAgreement{" +
                "rrnNo='" + rrnNo + '\'' +
                ", agreementId='" + agreementId + '\'' +
                ", orderValue='" + orderValue + '\'' +
                ", interestApplied='" + interestApplied + '\'' +
                ", loanAmount='" + loanAmount + '\'' +
                ", emiAmount='" + emiAmount + '\'' +
                ", lastEMIDate='" + lastEMIDate + '\'' +
                ", status='" + status + '\'' +
                ", closureDate='" + closureDate + '\'' +
                '}';
    }
}
