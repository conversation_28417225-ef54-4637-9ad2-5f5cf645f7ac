package com.flipkart.fintech.pandora.service.client.indiabulls.billing;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ClosedLoanDetail {

    @JsonProperty("CLOSED_AGREEMENT")
    private List<ClosedAgreement> closedAgreementList;

    public List<ClosedAgreement> getClosedAgreementList() {
        return closedAgreementList;
    }

    public void setClosedAgreementList(List<ClosedAgreement> closedAgreementList) {
        this.closedAgreementList = closedAgreementList;
    }

    @Override
    public String toString() {
        return "ClosedLoanDetail{" +
                "closedAgreementList=" + closedAgreementList +
                '}';
    }
}