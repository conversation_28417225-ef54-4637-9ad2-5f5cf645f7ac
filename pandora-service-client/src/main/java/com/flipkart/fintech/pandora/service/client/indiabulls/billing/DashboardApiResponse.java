package com.flipkart.fintech.pandora.service.client.indiabulls.billing;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR> H Adavi
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class DashboardApiResponse {
	
	@JsonProperty("Customer_dashboard")
	public DashboardResponse dashboardResponse;

	@JsonProperty("success")
	private boolean success;

	@JsonProperty("status")
	private String status;

	public DashboardResponse getDashboardResponse() {
		return dashboardResponse;
	}
	
	public void setDashboardResponse(DashboardResponse dashboardResponse) {
		this.dashboardResponse = dashboardResponse;
	}

	public boolean isSuccess() {
		return success;
	}

	public void setSuccess(boolean success) {
		this.success = success;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	@Override
	public String toString() {
		return "DashboardApiResponse{" +
				"dashboardResponse=" + dashboardResponse +
				", success=" + success +
				", status='" + status + '\'' +
				'}';
	}
}
