package com.flipkart.fintech.pandora.service.client.indiabulls.billing;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigDecimal;

/**
 * <AUTHOR> H Adavi
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class DashboardResponse {

	@JsonProperty("used_limit")
	private String usedLimit;

	@JsonProperty("total_limit")
	private String totalLimit;

	// Statement Widget Amount
	//TODO : verify with IB the due amount represents statement due amount or not ?

	@JsonProperty("due_amount")
	private String dueAmount;
	
	@JsonProperty("unbilled_amount")
	private String unbilledAmount;
	
	@JsonProperty("ACTIVE_LOAN_DETAIL")
	private ActiveLoanDetail activeLoanDetail;

	@JsonProperty("CLOSED_CANCELLED_LOAN")
	private ClosedLoanDetail closedLoanDetail;

	@JsonProperty("error_code")
	private String errorCode;

	@JsonProperty("error_message")
	private String errorMessage;
	
	public BigDecimal getTotalLimit() {
		return new BigDecimal(totalLimit);
	}
	
	public void setTotalLimit(String totalLimit) {
		this.totalLimit = totalLimit;
	}
	
	public BigDecimal getDueAmount() {
		return new BigDecimal(dueAmount);
	}
	
	public void setDueAmount(String dueAmount) {
		this.dueAmount = dueAmount;
	}
	
	public BigDecimal getUnbilledAmount() {
		return new BigDecimal(unbilledAmount);
	}
	
	public void setUnbilledAmount(String unbilledAmount) {
		this.unbilledAmount = unbilledAmount;
	}
	
	public ActiveLoanDetail getActiveLoanDetail() {
		return activeLoanDetail;
	}
	
	public void setActiveLoanDetail(ActiveLoanDetail activeLoanDetail) {
		this.activeLoanDetail = activeLoanDetail;
	}

	public BigDecimal getUsedLimit() {
		return new BigDecimal(usedLimit);
	}

	public void setUsedLimit(String usedLimit) {
		this.usedLimit = usedLimit;
	}

	public String getErrorCode() {
		return errorCode;
	}

	public void setErrorCode(String errorCode) {
		this.errorCode = errorCode;
	}

	public String getErrorMessage() {
		return errorMessage;
	}

	public void setErrorMessage(String errorMessage) {
		this.errorMessage = errorMessage;
	}

	public ClosedLoanDetail getClosedLoanDetail() {
		return closedLoanDetail;
	}

	public void setClosedLoanDetail(ClosedLoanDetail closedLoanDetail) {
		this.closedLoanDetail = closedLoanDetail;
	}

	@Override
	public String toString() {
		return "DashboardResponse{" +
				"usedLimit='" + usedLimit + '\'' +
				", totalLimit='" + totalLimit + '\'' +
				", dueAmount='" + dueAmount + '\'' +
				", unbilledAmount='" + unbilledAmount + '\'' +
				", activeLoanDetail=" + activeLoanDetail +
				", closedLoanDetail=" + closedLoanDetail +
				", errorCode='" + errorCode + '\'' +
				", errorMessage='" + errorMessage + '\'' +
				'}';
	}
}
