package com.flipkart.fintech.pandora.service.client.interceptors;

import com.flipkart.fintech.pandora.service.client.encryptor.DataEncryptorDecryptor;
import com.google.inject.Inject;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

import javax.ws.rs.WebApplicationException;
import javax.ws.rs.ext.ReaderInterceptor;
import javax.ws.rs.ext.ReaderInterceptorContext;
import javax.ws.rs.ext.WriterInterceptor;
import javax.ws.rs.ext.WriterInterceptorContext;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class DigitapEncryptionInterceptor implements ReaderInterceptor, WriterInterceptor {
    private final DataEncryptorDecryptor encryptor;
    @Inject
    public DigitapEncryptionInterceptor(DataEncryptorDecryptor encryptor) {
        this.encryptor = encryptor;
    }
    @Override
    public Object aroundReadFrom(ReaderInterceptorContext context) throws IOException, WebApplicationException {
        InputStream inputStream = context.getInputStream();
        String data = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))
                .lines()
                .collect(Collectors.joining(System.lineSeparator()));
        log.debug(data);
        JSONObject jsonObject = new JSONObject(data);
        String decryptedString = encryptor.decrypt(jsonObject.getString("encrypted_data"));
        log.info("decrypted string : {}", decryptedString);
        context.setInputStream(new ByteArrayInputStream(decryptedString.getBytes()));
        return context.proceed();
    }
    @Override
    public void aroundWriteTo(WriterInterceptorContext context) throws IOException, WebApplicationException {
        OutputStream originalOutputStream = context.getOutputStream();
        ByteArrayOutputStream buffer = new ByteArrayOutputStream();
        context.setOutputStream(buffer);
        context.proceed();

        String originalContent = buffer.toString(StandardCharsets.UTF_8.name());
        log.info("original content : {}", originalContent);
        String encryptedContent = encryptor.encrypt(originalContent);
        Map<String, String> digitapPayload = new HashMap<>();
        digitapPayload.put("encrypted_data", encryptedContent);
        log.info(digitapPayload.toString());
        String payloadString = new JSONObject(digitapPayload).toString();

        originalOutputStream.write(payloadString.getBytes(StandardCharsets.UTF_8));
        originalOutputStream.flush();
    }
}
