package com.flipkart.fintech.pandora.service.client.interceptors;

import com.flipkart.fintech.pandora.service.client.encryptor.DataEncryptorDecryptor;
import com.google.inject.Inject;
import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.util.stream.Collectors;
import javax.ws.rs.WebApplicationException;
import javax.ws.rs.ext.ReaderInterceptor;
import javax.ws.rs.ext.ReaderInterceptorContext;
import javax.ws.rs.ext.WriterInterceptor;
import javax.ws.rs.ext.WriterInterceptorContext;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class EncryptionInterceptor implements ReaderInterceptor, WriterInterceptor {

  private final DataEncryptorDecryptor encryptor;

  @Inject
  public EncryptionInterceptor(DataEncryptorDecryptor encryptor) {
    this.encryptor = encryptor;
  }

  @Override
  public Object aroundReadFrom(ReaderInterceptorContext context) throws IOException, WebApplicationException {
    Boolean isError = (Boolean) context.getProperty("isError");
    Object encryptionDisabledProperty = context.getProperty("encryptionDisabled");
    boolean encryptionDisabled = encryptionDisabledProperty != null && (Boolean) encryptionDisabledProperty;
    if (encryptionDisabled) return context.proceed();
    InputStream inputStream = context.getInputStream();
    String data = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))
        .lines()
        .collect(Collectors.joining(System.lineSeparator()));
    log.debug(data);
    if (Boolean.TRUE.equals(isError) && data.split("\\.").length != 5) {
        log.debug("Error response is probably not encrypted");
        return context.proceed();
    }
    try {
        String decryptedString = encryptor.decrypt(data);
        context.setInputStream(new ByteArrayInputStream(decryptedString.getBytes()));
    } catch (Exception e) { // payload might have 5 . and not be an encrypted string
        log.error("Failed to decrypt data {}", e.getMessage());
    }
    return context.proceed();
  }

  @Override
  public void aroundWriteTo(WriterInterceptorContext context) throws IOException, WebApplicationException {
      OutputStream originalOutputStream = context.getOutputStream();
      ByteArrayOutputStream buffer = new ByteArrayOutputStream();
      Object decryptionDisabledProperty = context.getProperty("decryptionDisabled");
      boolean decryptionDisabled = decryptionDisabledProperty != null ? (Boolean) decryptionDisabledProperty : false;
      context.setOutputStream(buffer);
      context.proceed();

      String originalContent = buffer.toString(StandardCharsets.UTF_8.name());
      if (decryptionDisabled) {
          originalOutputStream.write(originalContent.getBytes(StandardCharsets.UTF_8));
      } else {
          String encryptedContent = encryptor.encrypt(originalContent);
          log.debug(encryptedContent);

          originalOutputStream.write(encryptedContent.getBytes(StandardCharsets.UTF_8));
      }
      originalOutputStream.flush();
  }
}
