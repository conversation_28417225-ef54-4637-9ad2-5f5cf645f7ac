package com.flipkart.fintech.pandora.service.client.interceptors;

import com.flipkart.fintech.pandora.service.client.encryptor.DataEncryptorDecryptor;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONException;
import org.json.JSONObject;

import javax.ws.rs.WebApplicationException;
import javax.ws.rs.ext.ReaderInterceptor;
import javax.ws.rs.ext.ReaderInterceptorContext;
import javax.ws.rs.ext.WriterInterceptor;
import javax.ws.rs.ext.WriterInterceptorContext;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 29/11/23
 */
@Slf4j
public class SmePlEncryptionInterceptor implements ReaderInterceptor, WriterInterceptor {

    private final DataEncryptorDecryptor encryptor;
    private final String secretKey;

    public SmePlEncryptionInterceptor(DataEncryptorDecryptor encryptor, String secretKey) {
        this.encryptor = encryptor;
        this.secretKey = secretKey;
    }

    @Override
    public Object aroundReadFrom(ReaderInterceptorContext context) throws IOException, WebApplicationException {
        Boolean isError = (Boolean) context.getProperty("isError");
        if(Boolean.TRUE.equals(isError)) {
            return context.proceed();
        }
        InputStream inputStream = context.getInputStream();
        String data = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))
                .lines()
                .collect(Collectors.joining(System.lineSeparator()));
        String decryptedString = encryptor.decrypt(data);
        JSONObject jsonObject;
        try {
            jsonObject = new JSONObject(decryptedString);
            log.info("decrypted string : {}", decryptedString);
            context.setInputStream(new ByteArrayInputStream(jsonObject.getString("data").getBytes()));
        } catch (JSONException e) {
            log.error("Error while processing json from decrypted String");
        }
        return context.proceed();
    }

    @Override
    public void aroundWriteTo(WriterInterceptorContext context) throws IOException, WebApplicationException {
        OutputStream originalOutputStream = context.getOutputStream();
        ByteArrayOutputStream buffer = new ByteArrayOutputStream();
        context.setOutputStream(buffer);
        context.proceed();

        String originalContent = buffer.toString(StandardCharsets.UTF_8.name());
        String encryptedContent = encryptor.encrypt(originalContent);

        JSONObject jsonObject = new JSONObject(new HashMap<String, String>(){{put("data", encryptedContent);}});

        originalOutputStream.write(jsonObject.toString().getBytes(StandardCharsets.UTF_8));
        originalOutputStream.flush();
    }
}