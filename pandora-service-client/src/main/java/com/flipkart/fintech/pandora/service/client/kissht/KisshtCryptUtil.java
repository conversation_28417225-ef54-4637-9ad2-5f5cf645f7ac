package com.flipkart.fintech.pandora.service.client.kissht;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import javax.crypto.CipherInputStream;
import javax.crypto.CipherOutputStream;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigInteger;
import java.security.MessageDigest;

/**
 * Created by <PERSON><PERSON> on 07-07-2016.
 */
public class KisshtCryptUtil {
    private static final Logger logger = LoggerFactory.getLogger(KisshtCryptUtil.class);
    Cipher ecipher;
    Cipher dcipher;
    byte[] buf = new byte[1024];
    static final String HEXES = "0123456789ABCDEF";

    public KisshtCryptUtil() {
        try {
            KeyGenerator var1 = KeyGenerator.getInstance("AES");
            var1.init(128);
            this.setupCrypto(var1.generateKey());
        } catch (Exception var2) {
            logger.error("Error encountered in KisshtCryptUtil ", var2);
        }

    }

    public KisshtCryptUtil(String var1) {

        SecretKeySpec var2 = new SecretKeySpec(getMD5(var1), "AES");
        this.setupCrypto(var2);
    }

    private void setupCrypto(SecretKey var1) {
        //System.out.println("The value of var1::"+var1);
        byte[] var2 = new byte[]{(byte) 0, (byte) 1, (byte) 2, (byte) 3, (byte) 4, (byte) 5, (byte) 6, (byte) 7,
                (byte) 8, (byte) 9, (byte) 10, (byte) 11, (byte) 12, (byte) 13, (byte) 14, (byte) 15};
        IvParameterSpec var3 = new IvParameterSpec(var2);


        // String str = new String(var2);


        try {
            this.ecipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            this.dcipher = Cipher.getInstance("AES/CBC/NoPadding");
            //System.out.println("var1::"+var1.getEncoded() +" var3::"+var3.getIV());
            this.ecipher.init(1, var1, var3);
            this.dcipher.init(2, var1, var3);

        } catch (Exception var5) {
            logger.error("Error encountered in setupCrypto ", var5);
        }

    }

    public void encrypt(InputStream var1, OutputStream var2) {
        try {
            CipherOutputStream var5 = new CipherOutputStream(var2, this.ecipher);
            boolean var3 = false;

            int var6;
            while ((var6 = var1.read(this.buf)) >= 0) {
                var5.write(this.buf, 0, var6);
            }

            var5.close();
        } catch (IOException var4) {
            logger.error("Error encountered in encrypt ", var4);
        }

    }

    public String encrypt(String var1) {
        try {

            byte[] var2 = this.ecipher.doFinal(var1.getBytes("UTF-8"));
            return byteToHex(var2);
        } catch (Exception var3) {
            logger.error("Error encountered in encrypt ", var3);
            return null;
        }
    }

    public void decrypt(InputStream var1, OutputStream var2) {
        try {
            CipherInputStream var5 = new CipherInputStream(var1, this.dcipher);
            boolean var3 = false;

            int var6;
            while ((var6 = var5.read(this.buf)) >= 0) {
                var2.write(this.buf, 0, var6);
            }

            var2.close();
        } catch (IOException var4) {
            logger.error("Error encountered in decrypt ", var4);
        }

    }

    public String decrypt(String var1) {
        try {
            String var2 = new String(this.dcipher.doFinal(hexToByte(var1)), "UTF-8");
            return var2;
        } catch (Exception var3) {
            logger.error("Error encountered in decrypt ", var3);
            return null;
        }
    }

    public String decrypt(byte[] var1) {
        try {
            String var2 = new String(this.dcipher.doFinal(var1), "UTF-8");
            return var2;
        } catch (Exception var3) {
            logger.error("Error encountered in decrypt ", var3);
            return null;
        }
    }

    private static byte[] getMD5(String var0) {
        //md5(var0);

        try {
            byte[] var1 = var0.getBytes("UTF-8");

            MessageDigest var2 = MessageDigest.getInstance("MD5");
            byte[] hash = var2.digest();
            byte[] i = var2.digest(var0.getBytes());
            String output = String.format("%032X", new BigInteger(1, i));
            return var2.digest(var1);

        } catch (Exception var3) {
            return null;
        }
    }


    public static String byteToHex(byte[] var0) {
        if (var0 == null) {
            return null;
        } else {
            String var1 = "";

            for (int var2 = 0; var2 < var0.length; ++var2) {
                var1 = var1 + Integer.toString((var0[var2] & 255) + 256, 16).substring(1);
            }

            return var1;
        }
    }

    public static byte[] hexToByte(String var0) {
        int var1 = var0.length();
        byte[] var2 = new byte[var1 / 2];

        for (int var3 = 0; var3 < var1; var3 += 2) {
            var2[var3 / 2] = (byte) ((Character.digit(var0.charAt(var3), 16) << 4) + Character.digit(var0.charAt(var3
                    + 1), 16));
        }

        return var2;
    }
}
