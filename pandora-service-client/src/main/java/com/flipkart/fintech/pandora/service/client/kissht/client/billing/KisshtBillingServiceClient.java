package com.flipkart.fintech.pandora.service.client.kissht.client.billing;

import com.flipkart.fintech.pandora.service.client.kissht.request.PenaltyWaiveOffRequest;
import com.flipkart.fintech.pandora.service.client.kissht.response.AllLoansResponse;
import com.flipkart.fintech.pandora.service.client.kissht.response.BillingSummaryResponse;
import com.flipkart.fintech.pandora.service.client.kissht.response.BillingTransactionsResponse;
import com.flipkart.fintech.pandora.service.client.kissht.response.StatementDetailsResponse;
import com.flipkart.fintech.pandora.service.client.kissht.response.StatementsSummaryResponse;
import com.flipkart.fintech.pandora.service.client.kissht.response.billing.LoanDetailsResponse;
import com.flipkart.fintech.pandora.service.client.kissht.response.billing.PenaltyWaiveOffResponse;

/**
 * Created by aniruddha.sharma on 05/12/17.
 */
public interface KisshtBillingServiceClient {
    BillingSummaryResponse getSummary(String accountId, Boolean noCache);

    StatementsSummaryResponse getStatementsSummary(String accountId, Integer index, Integer count);

    StatementDetailsResponse getStatementDetails(String statementId);

    BillingTransactionsResponse getAllTransactions(String accountId, String startDate, String endDate, Boolean
            returnBilledTransactions, Boolean returnUnbilledTransactions, Integer index, Integer count);

    AllLoansResponse getAllLoans(String accountId, Integer index, Integer count, Boolean returnOpen,
                                 Boolean returnClosed);

    LoanDetailsResponse getLoanDetails(String loanReferenceNumber);

    PenaltyWaiveOffResponse waiveOffPenalty(PenaltyWaiveOffRequest penaltyWaiveOffRequest);
}
