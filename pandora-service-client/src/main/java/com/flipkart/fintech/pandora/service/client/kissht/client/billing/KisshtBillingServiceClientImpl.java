package com.flipkart.fintech.pandora.service.client.kissht.client.billing;

import com.flipkart.fintech.pandora.service.client.PandoraServiceClientException;
import com.flipkart.fintech.pandora.service.client.kissht.KisshtCryptUtil;
import com.flipkart.fintech.pandora.service.client.kissht.KisshtServiceClientConfigEfa;
import com.flipkart.fintech.pandora.service.client.kissht.request.KisshtRequestHeaders;
import com.flipkart.fintech.pandora.service.client.kissht.request.PenaltyWaiveOffRequest;
import com.flipkart.fintech.pandora.service.client.kissht.response.AllLoansResponse;
import com.flipkart.fintech.pandora.service.client.kissht.response.BillingSummaryResponse;
import com.flipkart.fintech.pandora.service.client.kissht.response.BillingTransactionsResponse;
import com.flipkart.fintech.pandora.service.client.kissht.response.StatementDetailsResponse;
import com.flipkart.fintech.pandora.service.client.kissht.response.StatementsSummaryResponse;
import com.flipkart.fintech.pandora.service.client.kissht.response.billing.LoanDetailsResponse;
import com.flipkart.fintech.pandora.service.client.kissht.response.billing.PenaltyWaiveOffResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.inject.Inject;
import javax.ws.rs.client.Client;
import javax.ws.rs.client.Entity;
import javax.ws.rs.client.Invocation;
import javax.ws.rs.client.WebTarget;
import javax.ws.rs.core.HttpHeaders;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * Created by aniruddha.sharma on 05/12/17.
 */
public class KisshtBillingServiceClientImpl implements KisshtBillingServiceClient {
    private static final Logger logger = LoggerFactory.getLogger(KisshtBillingServiceClientImpl.class);
    private final KisshtServiceClientConfigEfa clientConfig;
    private final WebTarget webTarget;

    @Inject
    public KisshtBillingServiceClientImpl(KisshtServiceClientConfigEfa clientConfig, Client client) {
        this.clientConfig = clientConfig;
        this.webTarget = client.target(clientConfig.getUrl()).path("/v1");
    }

    @Override
    public BillingSummaryResponse getSummary(String accountId, Boolean noCache) {
        String path = String.format("/user/%s/get_summary", accountId);
        Response response = null;
        BillingSummaryResponse billingSummaryResponse = null;
        KisshtCryptUtil kisshtCryptUtil = new KisshtCryptUtil(clientConfig.getSecretKey());
        try {
            Invocation.Builder invocationBuilder = webTarget.path(path).queryParam("no_cache",(noCache == null)?0:(noCache?1:0)).request(MediaType.APPLICATION_JSON);
            KisshtRequestHeaders kisshtRequestHeaders = getKisshtRequestHeaders();
            setKisshtRequestHeaders(invocationBuilder, kisshtCryptUtil, kisshtRequestHeaders);
            response = invocationBuilder.get();
            if (response.getStatus() == 200 || response.getStatus() == 404) {
                billingSummaryResponse = response.readEntity(BillingSummaryResponse.class);
                logger.info("Response from KISSHT {}", billingSummaryResponse);
            }else{
                logger.error("Response from KISSHT {}", response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PandoraServiceClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return billingSummaryResponse;
    }

    @Override
    public StatementsSummaryResponse getStatementsSummary(String accountId, Integer index, Integer count) {
        String path = String.format("/statement/get_statement_summary/%s", accountId);
        Response response = null;
        StatementsSummaryResponse statementsSummaryResponse = null;
        KisshtCryptUtil kisshtCryptUtil = new KisshtCryptUtil(clientConfig.getSecretKey());
        try {
            Invocation.Builder invocationBuilder = webTarget.path(path).queryParam("index", index).queryParam("count",
                    count).request(MediaType.APPLICATION_JSON);
            KisshtRequestHeaders kisshtRequestHeaders = getKisshtRequestHeaders();
            setKisshtRequestHeaders(invocationBuilder, kisshtCryptUtil, kisshtRequestHeaders);
            response = invocationBuilder.get();
            if (response.getStatus() == 200 || response.getStatus() == 404) {
                statementsSummaryResponse = response.readEntity(StatementsSummaryResponse.class);
                logger.info("Response from KISSHT {}", statementsSummaryResponse);
            }else{
                logger.error("Response from KISSHT {}", response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PandoraServiceClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return statementsSummaryResponse;
    }

    @Override
    public StatementDetailsResponse getStatementDetails(String statementId) {
        String path = String.format("/statements/get_statement_details/%s", statementId);
        Response response = null;
        StatementDetailsResponse statementDetailsResponse = null;
        KisshtCryptUtil kisshtCryptUtil = new KisshtCryptUtil(clientConfig.getSecretKey());
        try {
            Invocation.Builder invocationBuilder = webTarget.path(path).request(MediaType.APPLICATION_JSON);
            KisshtRequestHeaders kisshtRequestHeaders = getKisshtRequestHeaders();
            setKisshtRequestHeaders(invocationBuilder, kisshtCryptUtil, kisshtRequestHeaders);
            response = invocationBuilder.get();
            if (response.getStatus() == 200 || response.getStatus() == 404) {
                statementDetailsResponse = response.readEntity(StatementDetailsResponse.class);
                logger.info("Response from KISSHT {}", statementDetailsResponse);
            }else{
                logger.error("Response from KISSHT {}", response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PandoraServiceClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return statementDetailsResponse;
    }

    @Override
    public BillingTransactionsResponse getAllTransactions(String accountId, String startDate, String endDate, Boolean
            returnBilledTransactions, Boolean returnUnbilledTransactions, Integer index, Integer count) {
        String path = String.format("/billing/get_billing_transactions/%s", accountId);
        Response response = null;
        BillingTransactionsResponse billingTransactionsResponse = null;
        KisshtCryptUtil kisshtCryptUtil = new KisshtCryptUtil(clientConfig.getSecretKey());
        try {
            Invocation.Builder invocationBuilder = webTarget.path(path).queryParam("start_date", startDate).queryParam
                    ("end_date", endDate).queryParam("return_billed", returnBilledTransactions?1:0).queryParam
                    ("return_unbilled", returnUnbilledTransactions?1:0).queryParam("index", index).queryParam
                    ("count", count).request(MediaType.APPLICATION_JSON);
            KisshtRequestHeaders kisshtRequestHeaders = getKisshtRequestHeaders();
            setKisshtRequestHeaders(invocationBuilder, kisshtCryptUtil, kisshtRequestHeaders);
            response = invocationBuilder.get();
            if (response.getStatus() == 200 || response.getStatus() == 404) {
                billingTransactionsResponse = response.readEntity(BillingTransactionsResponse.class);
                logger.info("Response from KISSHT {}", billingTransactionsResponse);
            }else{
                logger.error("Response from KISSHT {}", response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PandoraServiceClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return billingTransactionsResponse;
    }

    @Override
    public AllLoansResponse getAllLoans(String accountId, Integer index, Integer count, Boolean returnOpen,
                                        Boolean returnClosed) {
        String path = String.format("/loans/get_all_loans/%s",accountId);
        Response response = null;
        AllLoansResponse allLoansResponse = null;
        KisshtCryptUtil kisshtCryptUtil = new KisshtCryptUtil(clientConfig.getSecretKey());
        try {
            Invocation.Builder invocationBuilder = webTarget.path(path).queryParam("index", index).queryParam
                    ("count", count).queryParam("return_open", returnOpen?1:0).queryParam
                    ("return_closed", returnClosed?1:0).request(MediaType.APPLICATION_JSON);
            KisshtRequestHeaders kisshtRequestHeaders = getKisshtRequestHeaders();
            setKisshtRequestHeaders(invocationBuilder, kisshtCryptUtil, kisshtRequestHeaders);
            response = invocationBuilder.get();
            if (response.getStatus() == 200 || response.getStatus() == 404) {
                allLoansResponse = response.readEntity(AllLoansResponse.class);
                logger.info("Response from KISSHT {}", allLoansResponse);
            }else{
                logger.error("Response from KISSHT {}", response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PandoraServiceClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return allLoansResponse;
    }

    @Override
    public LoanDetailsResponse getLoanDetails(String loanReferenceNumber) {
        String path = String.format("/loans/get_loan_details/%s",loanReferenceNumber);
        Response response = null;
        LoanDetailsResponse loanDetailsResponse = null;
        KisshtCryptUtil kisshtCryptUtil = new KisshtCryptUtil(clientConfig.getSecretKey());
        try {
            Invocation.Builder invocationBuilder = webTarget.path(path).request(MediaType.APPLICATION_JSON);
            KisshtRequestHeaders kisshtRequestHeaders = getKisshtRequestHeaders();
            setKisshtRequestHeaders(invocationBuilder, kisshtCryptUtil, kisshtRequestHeaders);
            response = invocationBuilder.get();
            if (response.getStatus() == 200 || response.getStatus() == 404) {
                loanDetailsResponse = response.readEntity(LoanDetailsResponse.class);
                logger.info("Response from KISSHT {}", loanDetailsResponse);
            }else{
                logger.error("Response from KISSHT {}", response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PandoraServiceClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return loanDetailsResponse;
    }


    @Override
    public PenaltyWaiveOffResponse waiveOffPenalty(PenaltyWaiveOffRequest penaltyWaiveOffRequest) {

        String path = "/payback/waive_penalty";
        Response response = null;
        PenaltyWaiveOffResponse penaltyWaiveOffResponse = null;
        KisshtCryptUtil kisshtCryptUtil = new KisshtCryptUtil(clientConfig.getSecretKey());
        try {
            Invocation.Builder invocationBuilder = webTarget.path(path).request(MediaType.APPLICATION_JSON);
            KisshtRequestHeaders kisshtRequestHeaders = getKisshtRequestHeaders();
            setKisshtRequestHeaders(invocationBuilder, kisshtCryptUtil, kisshtRequestHeaders);

            response = invocationBuilder.post(Entity.json(penaltyWaiveOffRequest));
            if (response.getStatus() == 200 ||response.getStatus() == 400 ||response.getStatus() == 404) {
                penaltyWaiveOffResponse = response.readEntity(PenaltyWaiveOffResponse.class);
                logger.info("Response from KISSHT {}", penaltyWaiveOffResponse);
            }
            else{
                logger.error("Response from KISSHT {}", response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PandoraServiceClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return penaltyWaiveOffResponse;
    }

    private KisshtRequestHeaders getKisshtRequestHeaders() {
        KisshtRequestHeaders kisshtRequestHeaders = new KisshtRequestHeaders();
        kisshtRequestHeaders.setSiteKey(clientConfig.getSiteKey());
        kisshtRequestHeaders.setTimestamp((new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date())));
        return kisshtRequestHeaders;
    }

    private void setKisshtRequestHeaders(Invocation.Builder invocationBuilder, KisshtCryptUtil kisshtCryptUtil,
                                         KisshtRequestHeaders kisshtRequestHeaders) {
        invocationBuilder.header("key", clientConfig.getSiteKey());
        invocationBuilder.header("data", kisshtCryptUtil.encrypt(kisshtRequestHeaders.toString()));
        invocationBuilder.header(HttpHeaders.CONTENT_ENCODING, MediaType.APPLICATION_JSON_TYPE.withCharset("utf-8"));
    }
}
