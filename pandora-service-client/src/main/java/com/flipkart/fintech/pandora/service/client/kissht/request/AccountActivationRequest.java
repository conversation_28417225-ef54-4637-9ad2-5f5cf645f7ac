package com.flipkart.fintech.pandora.service.client.kissht.request;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Created by aniruddha.sharma on 07/12/17.
 */
public class AccountActivationRequest {

    @JsonProperty("is_activated")
    private boolean isActivated;

    public boolean isActivated() {
        return isActivated;
    }

    public void setActivated(boolean isActivated) {
        this.isActivated = isActivated;
    }
}
