package com.flipkart.fintech.pandora.service.client.kissht.request;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigDecimal;

/**
 * Created by kunal.keshwani on 24/11/17.
 */
public class BnplKisshtPaybackReverseRequest {

    @JsonProperty("user_identifier")
    private String userIdentifier;

    @JsonProperty("merchant_order_id")
    private String merchantOrderId;

    @JsonProperty("kissht_order_id")
    private String kisshtOrderId;

    @JsonProperty("transaction_date")
    private String transactionDate;

    @JsonProperty("mobile")
    private String mobile;

    @JsonProperty("pg_identifier")
    private String pgIdentifier;

    @JsonProperty("amount")
    private BigDecimal amount;

    public String getUserIdentifier() {
        return userIdentifier;
    }

    public void setUserIdentifier(String userIdentifier) {
        this.userIdentifier = userIdentifier;
    }

    public String getMerchantOrderId() {
        return merchantOrderId;
    }

    public void setMerchantOrderId(String merchantOrderId) {
        this.merchantOrderId = merchantOrderId;
    }

    public String getKisshtOrderId() {
        return kisshtOrderId;
    }

    public void setKisshtOrderId(String kisshtOrderId) {
        this.kisshtOrderId = kisshtOrderId;
    }

    public String getTransactionDate() {
        return transactionDate;
    }

    public void setTransactionDate(String transactionDate) {
        this.transactionDate = transactionDate;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getPgIdentifier() {
        return pgIdentifier;
    }

    public void setPgIdentifier(String pgIdentifier) {
        this.pgIdentifier = pgIdentifier;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }
}
