package com.flipkart.fintech.pandora.service.client.kissht.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Created by aniruddha.sharma on 12/09/17.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CheckEligibilityRequest {

    @NotNull
    @Size(min = 2, max = 50)
    @JsonProperty("first_name")
    private String firstName;

    @Size(max = 50)
    @JsonProperty("middle_name")
    private String middleName;

    @NotNull
    @Size(min = 2, max = 50)
    @JsonProperty("last_name")
    private String lastName;

    @NotNull
    @Pattern(regexp = "[6-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9]")
    @JsonProperty("mobile_number")
    private String mobileNumber;

    @Pattern(regexp = "[0-9][0-9][0-9][0-9][-][0-9][0-9][-][0-9][0-9]")
    @JsonProperty("dob")
    private String dateOfBirth;

    @NotNull
    @JsonProperty("gender")
    private String gender;

    @NotNull
    @JsonProperty("address_line_1")
    private String addressLine1;

    @NotNull
    @JsonProperty("address_line_2")
    private String addressLine2;

    @Size(max = 60)
    @JsonProperty("addrLine3")
    private String addressLine3;

    @Size(max = 60)
    @JsonProperty("addrLine4")
    private String addressLine4;

    @Size(max = 60)
    @JsonProperty("addrLine5")
    private String addressLine5;

    @NotNull
    @JsonProperty("city")
    private String city;

    @NotNull
    @JsonProperty("state")
    private String state;

    @NotNull
    @Pattern(regexp = "[0-9][0-9][0-9][0-9][0-9][0-9]")
    @JsonProperty("address_pincode")
    private String pincode;

    @JsonProperty("fk_underwriting_bucket")
    private Integer fkUnderwritingBucket;

    @JsonProperty("suggested_credit_limit")
    private BigDecimal suggestedCreditLimit;

    @NotNull
    @JsonProperty("addressType")
    private String addressType;

    @NotNull
    @Size(min = 3, max = 60)
    @JsonProperty("addrLine21")
    private String address2Line1;

    @NotNull
    @Size(min = 3, max = 60)
    @JsonProperty("addrLine22")
    private String address2Line2;

    @Size(max = 60)
    @JsonProperty("addrLine23")
    private String address2Line3;

    @Size(max = 60)
    @JsonProperty("addrLine24")
    private String address2Line4;

    @Size(max = 60)
    @JsonProperty("addrLine25")
    private String address2Line5;

    @NotNull
    @JsonProperty("city2")
    private String city2;

    @NotNull
    @JsonProperty("state2")
    private String state2;

    @NotNull
    @Pattern(regexp = "[0-9][0-9][0-9][0-9][0-9][0-9]")
    @JsonProperty("postal2")
    private String pincode2;

    @NotNull
    @JsonProperty("addressType2")
    private String addressType2;

    @JsonProperty("aadhaar_reference_number")
    private String aadhaarReferenceNumber;

    @JsonProperty("isAadharVerified")
    private Boolean aadhaarVerficationFlag;

    @Size(min = 10, max = 10)
    @JsonProperty("pan")
    private String panNumber;

    @Size(min = 2, max = 50)
    @JsonProperty("fatherFirstName")
    private String fatherFirstName;

    @Size(max = 50)
    @JsonProperty("fatherMiddleName")
    private String fatherMiddleName;

    @Size(min = 2, max = 50)
    @JsonProperty("fatherLastName")
    private String fatherLastName;

    @JsonProperty("email")
    private String email;

    @JsonProperty("loanAmount")
    private Integer loanAmount;

    @NotNull
    @JsonProperty("isMobileVerified")
    private Boolean mobileVerificationFlag;

    @JsonProperty("isEmailVerified")
    private Boolean emailVerificationFlag;

    @JsonProperty("iBankCustomer")
    private String iBankCustomer;

    @JsonProperty("customerType1")
    private String customerType1;

    @JsonProperty("customerType2")
    private String customerType2;

    @JsonProperty("customerVintage")
    private Integer customerVintageInMonths;

    @JsonProperty("lastTransactionDate")
    private Date lastTransactionDate;

    @NotNull
    @JsonProperty("averageMonthlyTransaction6months")
    private Double averageMonthlyTransactionInLast6Months;

    @JsonProperty("normalizedPercentageTransactionAboveAverage")
    private Double normalisedPercentageOfTransactions;

    @JsonProperty("normalizedRatio")
    private Double normalisedRatio;

    @JsonProperty("promoCodesCount6months")
    private Integer countOfPromoCodes;

    @JsonProperty("uberRidesCount6months")
    private Integer countOfRidesAvailed;

    @JsonProperty("prepaidRechargeCount6months")
    private Integer countOfPrepaidRecharges;

    @JsonProperty("marketplacePurchaseCount6months")
    private Integer countOfMarketplace;

    @JsonProperty("variable1")
    private String variable1;

    @JsonProperty("variable2")
    private String variable2;

    @JsonProperty("variable3")
    private String variable3;

    @JsonProperty("variable4")
    private String variable4;

    @JsonProperty("variable5")
    private String variable5;

    @JsonProperty("variable6")
    private Double variable6;

    @JsonProperty("variable7")
    private Double variable7;

    @JsonProperty("variable8")
    private Double variable8;

    @JsonProperty("variable9")
    private Double variable9;

    @JsonProperty("variable10")
    private Double variable10;

    @NotNull
    @JsonProperty("is_kyc_completed")
    private boolean isKycCompleted;

    @NotNull
    @JsonProperty("additional_data")
    private AdditionalData additionalData;

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getMiddleName() {
        return middleName;
    }

    public void setMiddleName(String middleName) {
        this.middleName = middleName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getMobileNumber() {
        return mobileNumber;
    }

    public void setMobileNumber(String mobileNumber) {
        this.mobileNumber = mobileNumber;
    }

    public String getDateOfBirth() {
        return dateOfBirth;
    }

    public void setDateOfBirth(String dateOfBirth) {
        this.dateOfBirth = dateOfBirth;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getAddressLine1() {
        return addressLine1;
    }

    public void setAddressLine1(String addressLine1) {
        this.addressLine1 = addressLine1;
    }

    public String getAddressLine2() {
        return addressLine2;
    }

    public void setAddressLine2(String addressLine2) {
        this.addressLine2 = addressLine2;
    }

    public String getAddressLine3() {
        return addressLine3;
    }

    public void setAddressLine3(String addressLine3) {
        this.addressLine3 = addressLine3;
    }

    public String getAddressLine4() {
        return addressLine4;
    }

    public void setAddressLine4(String addressLine4) {
        this.addressLine4 = addressLine4;
    }

    public String getAddressLine5() {
        return addressLine5;
    }

    public void setAddressLine5(String addressLine5) {
        this.addressLine5 = addressLine5;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getPincode() {
        return pincode;
    }

    public void setPincode(String pincode) {
        this.pincode = pincode;
    }

    public String getAddressType() {
        return addressType;
    }

    public void setAddressType(String addressType) {
        this.addressType = addressType;
    }

    public String getAddress2Line1() {
        return address2Line1;
    }

    public void setAddress2Line1(String address2Line1) {
        this.address2Line1 = address2Line1;
    }

    public String getAddress2Line2() {
        return address2Line2;
    }

    public void setAddress2Line2(String address2Line2) {
        this.address2Line2 = address2Line2;
    }

    public String getAddress2Line3() {
        return address2Line3;
    }

    public void setAddress2Line3(String address2Line3) {
        this.address2Line3 = address2Line3;
    }

    public String getAddress2Line4() {
        return address2Line4;
    }

    public void setAddress2Line4(String address2Line4) {
        this.address2Line4 = address2Line4;
    }

    public String getAddress2Line5() {
        return address2Line5;
    }

    public void setAddress2Line5(String address2Line5) {
        this.address2Line5 = address2Line5;
    }

    public String getCity2() {
        return city2;
    }

    public void setCity2(String city2) {
        this.city2 = city2;
    }

    public String getState2() {
        return state2;
    }

    public void setState2(String state2) {
        this.state2 = state2;
    }

    public String getPincode2() {
        return pincode2;
    }

    public void setPincode2(String pincode2) {
        this.pincode2 = pincode2;
    }

    public String getAddressType2() {
        return addressType2;
    }

    public void setAddressType2(String addressType2) {
        this.addressType2 = addressType2;
    }

    public String getAadhaarReferenceNumber() {
        return aadhaarReferenceNumber;
    }

    public void setAadhaarReferenceNumber(String aadhaarReferenceNumber) {
        this.aadhaarReferenceNumber = aadhaarReferenceNumber;
    }

    public Boolean getAadhaarVerficationFlag() {
        return aadhaarVerficationFlag;
    }

    public void setAadhaarVerficationFlag(Boolean aadhaarVerficationFlag) {
        this.aadhaarVerficationFlag = aadhaarVerficationFlag;
    }

    public String getPanNumber() {
        return panNumber;
    }

    public void setPanNumber(String panNumber) {
        this.panNumber = panNumber;
    }

    public String getFatherFirstName() {
        return fatherFirstName;
    }

    public void setFatherFirstName(String fatherFirstName) {
        this.fatherFirstName = fatherFirstName;
    }

    public String getFatherMiddleName() {
        return fatherMiddleName;
    }

    public void setFatherMiddleName(String fatherMiddleName) {
        this.fatherMiddleName = fatherMiddleName;
    }

    public String getFatherLastName() {
        return fatherLastName;
    }

    public void setFatherLastName(String fatherLastName) {
        this.fatherLastName = fatherLastName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Integer getLoanAmount() {
        return loanAmount;
    }

    public void setLoanAmount(Integer loanAmount) {
        this.loanAmount = loanAmount;
    }

    public Boolean getMobileVerificationFlag() {
        return mobileVerificationFlag;
    }

    public void setMobileVerificationFlag(Boolean mobileVerificationFlag) {
        this.mobileVerificationFlag = mobileVerificationFlag;
    }

    public Boolean getEmailVerificationFlag() {
        return emailVerificationFlag;
    }

    public void setEmailVerificationFlag(Boolean emailVerificationFlag) {
        this.emailVerificationFlag = emailVerificationFlag;
    }

    public String getiBankCustomer() {
        return iBankCustomer;
    }

    public void setiBankCustomer(String iBankCustomer) {
        this.iBankCustomer = iBankCustomer;
    }

    public String getCustomerType1() {
        return customerType1;
    }

    public void setCustomerType1(String customerType1) {
        this.customerType1 = customerType1;
    }

    public String getCustomerType2() {
        return customerType2;
    }

    public void setCustomerType2(String customerType2) {
        this.customerType2 = customerType2;
    }

    public Integer getCustomerVintageInMonths() {
        return customerVintageInMonths;
    }

    public void setCustomerVintageInMonths(Integer customerVintageInMonths) {
        this.customerVintageInMonths = customerVintageInMonths;
    }

    public Date getLastTransactionDate() {
        return lastTransactionDate;
    }

    public void setLastTransactionDate(Date lastTransactionDate) {
        this.lastTransactionDate = lastTransactionDate;
    }

    public Double getAverageMonthlyTransactionInLast6Months() {
        return averageMonthlyTransactionInLast6Months;
    }

    public void setAverageMonthlyTransactionInLast6Months(Double averageMonthlyTransactionInLast6Months) {
        this.averageMonthlyTransactionInLast6Months = averageMonthlyTransactionInLast6Months;
    }

    public Double getNormalisedPercentageOfTransactions() {
        return normalisedPercentageOfTransactions;
    }

    public void setNormalisedPercentageOfTransactions(Double normalisedPercentageOfTransactions) {
        this.normalisedPercentageOfTransactions = normalisedPercentageOfTransactions;
    }

    public Double getNormalisedRatio() {
        return normalisedRatio;
    }

    public void setNormalisedRatio(Double normalisedRatio) {
        this.normalisedRatio = normalisedRatio;
    }

    public Integer getCountOfPromoCodes() {
        return countOfPromoCodes;
    }

    public void setCountOfPromoCodes(Integer countOfPromoCodes) {
        this.countOfPromoCodes = countOfPromoCodes;
    }

    public Integer getCountOfRidesAvailed() {
        return countOfRidesAvailed;
    }

    public void setCountOfRidesAvailed(Integer countOfRidesAvailed) {
        this.countOfRidesAvailed = countOfRidesAvailed;
    }

    public Integer getCountOfPrepaidRecharges() {
        return countOfPrepaidRecharges;
    }

    public void setCountOfPrepaidRecharges(Integer countOfPrepaidRecharges) {
        this.countOfPrepaidRecharges = countOfPrepaidRecharges;
    }

    public Integer getCountOfMarketplace() {
        return countOfMarketplace;
    }

    public void setCountOfMarketplace(Integer countOfMarketplace) {
        this.countOfMarketplace = countOfMarketplace;
    }

    public String getVariable1() {
        return variable1;
    }

    public void setVariable1(String variable1) {
        this.variable1 = variable1;
    }

    public String getVariable2() {
        return variable2;
    }

    public void setVariable2(String variable2) {
        this.variable2 = variable2;
    }

    public String getVariable3() {
        return variable3;
    }

    public void setVariable3(String variable3) {
        this.variable3 = variable3;
    }

    public String getVariable4() {
        return variable4;
    }

    public void setVariable4(String variable4) {
        this.variable4 = variable4;
    }

    public String getVariable5() {
        return variable5;
    }

    public void setVariable5(String variable5) {
        this.variable5 = variable5;
    }

    public Double getVariable6() {
        return variable6;
    }

    public void setVariable6(Double variable6) {
        this.variable6 = variable6;
    }

    public Double getVariable7() {
        return variable7;
    }

    public void setVariable7(Double variable7) {
        this.variable7 = variable7;
    }

    public Double getVariable8() {
        return variable8;
    }

    public void setVariable8(Double variable8) {
        this.variable8 = variable8;
    }

    public Double getVariable9() {
        return variable9;
    }

    public void setVariable9(Double variable9) {
        this.variable9 = variable9;
    }

    public Double getVariable10() {
        return variable10;
    }

    public void setVariable10(Double variable10) {
        this.variable10 = variable10;
    }

    public boolean isKycCompleted() {
        return isKycCompleted;
    }

    public void setKycCompleted(boolean isKycCompleted) {
        this.isKycCompleted = isKycCompleted;
    }

    public AdditionalData getAdditionalData() {
        return additionalData;
    }

    public void setAdditionalData(AdditionalData additionalData) {
        this.additionalData = additionalData;
    }

    public Integer getFkUnderwritingBucket() {
        return fkUnderwritingBucket;
    }

    public void setFkUnderwritingBucket(Integer fkUnderwritingBucket) {
        this.fkUnderwritingBucket = fkUnderwritingBucket;
    }

    public BigDecimal getSuggestedCreditLimit() {
        return suggestedCreditLimit;
    }

    public void setSuggestedCreditLimit(BigDecimal suggestedCreditLimit) {
        this.suggestedCreditLimit = suggestedCreditLimit;
    }
}
