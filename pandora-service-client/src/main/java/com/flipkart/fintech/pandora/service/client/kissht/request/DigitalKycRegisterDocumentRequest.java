package com.flipkart.fintech.pandora.service.client.kissht.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Created by rishabhdhwaj.singh on 02/12/18.
 */

@JsonIgnoreProperties(ignoreUnknown = true)
public class DigitalKycRegisterDocumentRequest {

    @JsonProperty("user_reference_number")
    private String userReferenceNumber;

    @JsonProperty("document_category")
    private String kycDocCategory;

    @JsonProperty("face")
    private String kycDocFace;

    @JsonProperty("document_type")
    private String kycDocType;

    @JsonProperty("OTP_declared")
    private String declaredOtp;

    @JsonProperty("filename")
    private String fileName;

    public String getUserReferenceNumber() {
        return userReferenceNumber;
    }

    public void setUserReferenceNumber(String userReferenceNumber) {
        this.userReferenceNumber = userReferenceNumber;
    }

    public String getKycDocCategory() {
        return kycDocCategory;
    }

    public void setKycDocCategory(String kycDocCategory) {
        this.kycDocCategory = kycDocCategory;
    }

    public String getKycDocFace() {
        return kycDocFace;
    }

    public void setKycDocFace(String kycDocFace) {
        this.kycDocFace = kycDocFace;
    }

    public String getKycDocType() {
        return kycDocType;
    }

    public void setKycDocType(String kycDocType) {
        this.kycDocType = kycDocType;
    }

    public String getDeclaredOtp() {
        return declaredOtp;
    }

    public void setDeclaredOtp(String declaredOtp) {
        this.declaredOtp = declaredOtp;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }
}
