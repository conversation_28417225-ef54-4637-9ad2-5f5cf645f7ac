package com.flipkart.fintech.pandora.service.client.kissht.request;

/**
 * Created by aniruddha.sharma on 27/09/17.
 */
public class KisshtRequestHeaders {
    String siteKey;
    String timestamp;

    public String getSiteKey() {
        return siteKey;
    }

    public void setSiteKey(String siteKey) {
        this.siteKey = siteKey;
    }

    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    @Override
    public String toString() {
        return "{" +
                "\"sitekey\":\"" + siteKey + '\"' +
                ",\"timestamp\":\"" + timestamp + '\"' +
                "}";
    }
}
