package com.flipkart.fintech.pandora.service.client.kissht.request;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Created by aniruddha.sharma on 15/10/17.
 */
public class KycUpdateRequest {
    @JsonProperty("check_eligibility_data_id")
    private String checkEligibility;

    public String getCheckEligibility() {
        return checkEligibility;
    }

    public void setCheckEligibility(String checkEligibility) {
        this.checkEligibility = checkEligibility;
    }
}
