package com.flipkart.fintech.pandora.service.client.kissht.request;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Created by aniruddha.sharma on 26/12/17.
 */
public class RefundTransactionRequest {
    @JsonProperty("merchant_order_id")
    private String merchantOrderId;

    @JsonProperty("transaction_amount")
    private Double transactionAmount;

    public String getMerchantOrderId() {
        return merchantOrderId;
    }

    public void setMerchantOrderId(String merchantOrderId) {
        this.merchantOrderId = merchantOrderId;
    }

    public Double getTransactionAmount() {
        return transactionAmount;
    }

    public void setTransactionAmount(Double transactionAmount) {
        this.transactionAmount = transactionAmount;
    }
}
