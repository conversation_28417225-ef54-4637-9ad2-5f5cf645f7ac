package com.flipkart.fintech.pandora.service.client.kissht.request;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Created by aniruddha.sharma on 02/11/17.
 */
public class TransactionSaveRequest {
    @JsonProperty("kissht_user_id")
    private int accountNumber;

    @JsonProperty("otp")
    private String otp;

    @JsonProperty("kissht_transaction_id")
    private String kisshtTransactionId;

    public int getAccountNumber() {
        return accountNumber;
    }

    public void setAccountNumber(int accountNumber) {
        this.accountNumber = accountNumber;
    }

    public String getOtp() {
        return otp;
    }

    public void setOtp(String otp) {
        this.otp = otp;
    }

    public String getKisshtTransactionId() {
        return kisshtTransactionId;
    }

    public void setKisshtTransactionId(String kisshtTransactionId) {
        this.kisshtTransactionId = kisshtTransactionId;
    }

    @Override
    public String toString() {
        return "TransactionSaveRequest{" +
                "accountNumber=" + accountNumber +
                ", otp='" + otp + '\'' +
                ", kisshtTransactionId='" + kisshtTransactionId + '\'' +
                '}';
    }
}
