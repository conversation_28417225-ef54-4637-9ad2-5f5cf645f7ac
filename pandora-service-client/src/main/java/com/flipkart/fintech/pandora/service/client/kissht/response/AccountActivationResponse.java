package com.flipkart.fintech.pandora.service.client.kissht.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Created by aniruddha.sharma on 07/12/17.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class AccountActivationResponse {
    @JsonProperty("success")
    private boolean success;

    private String info;

    @JsonProperty("error_code")
    public int errorCode;

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getInfo() {
        return info;
    }

    public void setInfo(String info) {
        this.info = info;
    }

    public int getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(int errorCode) {
        this.errorCode = errorCode;
    }

    @Override
    public String toString() {
        return "AccountActivationResponse{" +
                "success=" + success +
                ", info='" + info + '\'' +
                ", errorCode=" + errorCode +
                '}';
    }
}
