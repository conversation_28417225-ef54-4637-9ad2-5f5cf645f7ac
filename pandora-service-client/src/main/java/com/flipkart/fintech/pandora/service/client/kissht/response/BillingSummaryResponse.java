package com.flipkart.fintech.pandora.service.client.kissht.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import javax.ws.rs.DefaultValue;
import java.math.BigDecimal;

/**
 * Created by aniruddha.sharma on 05/12/17.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class BillingSummaryResponse {

    @JsonProperty("success")
    @DefaultValue("false")
    private boolean success;

    @JsonProperty("due_amount")
    private BigDecimal dueAmount;

    @JsonProperty("due_date")
    private String dueDate;

    @JsonProperty("available_limit")
    private BigDecimal availableLimit;

    @JsonProperty("total_limit")
    private BigDecimal totalLimit;

    @JsonProperty("total_outstanding_amount")
    private BigDecimal totalOutstandingAmount;

    @JsonProperty("total_outstanding_amount_loans")
    private BigDecimal totalOutstandingAmountLoans;

    @JsonProperty("total_outstanding_amount_paylater")
    private BigDecimal totalOutstandingAmountPayLater;

    @JsonProperty("next_statement_generation_date")
    private String nextStatementGenerationDate;

    @JsonProperty("next_statement_generation_amount")
    private BigDecimal nextStatementGenerationAmount;

    @JsonProperty("last_statement_generation_date")
    private String lastStatementGenerationDate;

    @JsonProperty("latest_billed_amount")
    private BigDecimal latestBilledAmount;

    @JsonProperty("late_fees")
    private BigDecimal lateFees;

    @JsonProperty("unbilled_paylater_amount")
    private BigDecimal unbilledPaylaterAmount;

    @JsonProperty("error_code")
    public int errorCode;

    @JsonProperty("info")
    public String info;

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public BigDecimal getDueAmount() {
        return dueAmount;
    }

    public void setDueAmount(BigDecimal dueAmount) {
        this.dueAmount = dueAmount;
    }

    public String getDueDate() {
        return dueDate;
    }

    public void setDueDate(String dueDate) {
        this.dueDate = dueDate;
    }

    public BigDecimal getAvailableLimit() {
        return availableLimit;
    }

    public void setAvailableLimit(BigDecimal availableLimit) {
        this.availableLimit = availableLimit;
    }

    public BigDecimal getTotalLimit() {
        return totalLimit;
    }

    public void setTotalLimit(BigDecimal totalLimit) {
        this.totalLimit = totalLimit;
    }

    public BigDecimal getTotalOutstandingAmount() {
        return totalOutstandingAmount;
    }

    public void setTotalOutstandingAmount(BigDecimal totalOutstandingAmount) {
        this.totalOutstandingAmount = totalOutstandingAmount;
    }

    public BigDecimal getTotalOutstandingAmountLoans() {
        return totalOutstandingAmountLoans;
    }

    public void setTotalOutstandingAmountLoans(BigDecimal totalOutstandingAmountLoans) {
        this.totalOutstandingAmountLoans = totalOutstandingAmountLoans;
    }

    public BigDecimal getTotalOutstandingAmountPayLater() {
        return totalOutstandingAmountPayLater;
    }

    public void setTotalOutstandingAmountPayLater(BigDecimal totalOutstandingAmountPayLater) {
        this.totalOutstandingAmountPayLater = totalOutstandingAmountPayLater;
    }

    public String getNextStatementGenerationDate() {
        return nextStatementGenerationDate;
    }

    public void setNextStatementGenerationDate(String nextStatementGenerationDate) {
        this.nextStatementGenerationDate = nextStatementGenerationDate;
    }

    public BigDecimal getNextStatementGenerationAmount() {
        return nextStatementGenerationAmount;
    }

    public void setNextStatementGenerationAmount(BigDecimal nextStatementGenerationAmount) {
        this.nextStatementGenerationAmount = nextStatementGenerationAmount;
    }

    public String getLastStatementGenerationDate() {
        return lastStatementGenerationDate;
    }

    public void setLastStatementGenerationDate(String lastStatementGenerationDate) {
        this.lastStatementGenerationDate = lastStatementGenerationDate;
    }

    public BigDecimal getLatestBilledAmount() {
        return latestBilledAmount;
    }

    public void setLatestBilledAmount(BigDecimal latestBilledAmount) {
        this.latestBilledAmount = latestBilledAmount;
    }

    public BigDecimal getLateFees() {
        return lateFees;
    }

    public void setLateFees(BigDecimal lateFees) {
        this.lateFees = lateFees;
    }

    public int getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(int errorCode) {
        this.errorCode = errorCode;
    }

    public String getInfo() {
        return info;
    }

    public void setInfo(String info) {
        this.info = info;
    }

    public BigDecimal getUnbilledPaylaterAmount() {
        return unbilledPaylaterAmount;
    }

    public void setUnbilledPaylaterAmount(BigDecimal unbilledPaylaterAmount) {
        this.unbilledPaylaterAmount = unbilledPaylaterAmount;
    }

    @Override
    public String toString() {
        return "BillingSummaryResponse{" +
                "success=" + success +
                ", dueAmount=" + dueAmount +
                ", dueDate='" + dueDate + '\'' +
                ", availableLimit=" + availableLimit +
                ", totalLimit=" + totalLimit +
                ", totalOutstandingAmount=" + totalOutstandingAmount +
                ", totalOutstandingAmountLoans=" + totalOutstandingAmountLoans +
                ", totalOutstandingAmountPayLater=" + totalOutstandingAmountPayLater +
                ", nextStatementGenerationDate='" + nextStatementGenerationDate + '\'' +
                ", nextStatementGenerationAmount=" + nextStatementGenerationAmount +
                ", lastStatementGenerationDate='" + lastStatementGenerationDate + '\'' +
                ", latestBilledAmount=" + latestBilledAmount +
                ", lateFees=" + lateFees +
                ", unbilledPaylaterAmount=" + unbilledPaylaterAmount +
                ", errorCode=" + errorCode +
                ", info='" + info + '\'' +
                '}';
    }
}
