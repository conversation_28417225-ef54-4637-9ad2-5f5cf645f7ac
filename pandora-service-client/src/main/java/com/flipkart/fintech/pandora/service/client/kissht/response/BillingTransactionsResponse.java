package com.flipkart.fintech.pandora.service.client.kissht.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pandora.service.client.kissht.response.billing.BillingTransaction;

import javax.ws.rs.DefaultValue;
import java.util.List;

/**
 * Created by aniruddha.sharma on 06/12/17.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class BillingTransactionsResponse {
    @JsonProperty("success")
    @DefaultValue("false")
    private boolean success;

    @JsonProperty("startDate")
    private String startDate;

    @JsonProperty("endDate")
    private String endDate;

    @JsonProperty("transactions")
    private List<BillingTransaction> billingTransactionList;

    @JsonProperty("info")
    public String info;

    @JsonProperty("error_code")
    private int errorCode;

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public List<BillingTransaction> getBillingTransactionList() {
        return billingTransactionList;
    }

    public void setBillingTransactionList(List<BillingTransaction> billingTransactionList) {
        this.billingTransactionList = billingTransactionList;
    }

    public String getInfo() {
        return info;
    }

    public void setInfo(String info) {
        this.info = info;
    }

    public int getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(int errorCode) {
        this.errorCode = errorCode;
    }
}
