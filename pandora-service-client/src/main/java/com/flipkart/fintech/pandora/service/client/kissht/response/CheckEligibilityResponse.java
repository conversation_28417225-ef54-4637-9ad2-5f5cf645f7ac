package com.flipkart.fintech.pandora.service.client.kissht.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;


/**
 * Created by aniruddha.sharma on 12/09/17.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class CheckEligibilityResponse {
    @JsonProperty("is_existing_user")
    private boolean isExistingUser;

    @JsonProperty("kissht_user_id")
    private String kisshtUserId;

    @JsonProperty("customer_cibil_bucket")
    private int customerCibilBucket;

    @JsonProperty("is_eligible")
    private boolean isEligible;

    @JsonProperty("credit_limit")
    private Double creditLimit;

    @JsonProperty("success")
    private boolean success;

    @JsonProperty("error_code")
    private int errorCode;

    @JsonProperty("info")
    private String info;

    @JsonProperty("inelegible_reason_code")
    private int inelegibleReasonCode;

    @JsonProperty("cibil_score_bucket")
    private Integer cibilScoreBucket;

    public boolean isExistingUser() {
        return isExistingUser;
    }

    public void setExistingUser(boolean isExistingUser) {
        this.isExistingUser = isExistingUser;
    }

    public String getKisshtUserId() {
        return kisshtUserId;
    }

    public void setKisshtUserId(String kisshtUserId) {
        this.kisshtUserId = kisshtUserId;
    }

    public int getCustomerCibilBucket() {
        return customerCibilBucket;
    }

    public void setCustomerCibilBucket(int customerCibilBucket) {
        this.customerCibilBucket = customerCibilBucket;
    }

    public boolean isEligible() {
        return isEligible;
    }

    public void setEligible(boolean isEligible) {
        this.isEligible = isEligible;
    }

    public Double getCreditLimit() {
        return creditLimit;
    }

    public void setCreditLimit(Double creditLimit) {
        this.creditLimit = creditLimit;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public int getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(int errorCode) {
        this.errorCode = errorCode;
    }

    public String getInfo() {
        return info;
    }

    public void setInfo(String info) {
        this.info = info;
    }

    public int getInelegibleReasonCode() {
        return inelegibleReasonCode;
    }

    public void setInelegibleReasonCode(int inelegibleReasonCode) {
        this.inelegibleReasonCode = inelegibleReasonCode;
    }

    public Integer getCibilScoreBucket() {
        return cibilScoreBucket;
    }

    public void setCibilScoreBucket(Integer cibilScoreBucket) {
        this.cibilScoreBucket = cibilScoreBucket;
    }

    @Override
    public String toString() {
        return "CheckEligibilityResponse{" +
                "isExistingUser=" + isExistingUser +
                ", kisshtUserId='" + kisshtUserId + '\'' +
                ", customerCibilBucket=" + customerCibilBucket +
                ", isEligible=" + isEligible +
                ", creditLimit=" + creditLimit +
                ", success=" + success +
                ", errorCode=" + errorCode +
                ", info='" + info + '\'' +
                ", inelegibleReasonCode=" + inelegibleReasonCode +
                ", cibilScoreBucket='" + cibilScoreBucket + '\'' +
                '}';
    }
}
