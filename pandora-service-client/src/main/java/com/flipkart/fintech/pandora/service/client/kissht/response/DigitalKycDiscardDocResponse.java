package com.flipkart.fintech.pandora.service.client.kissht.response;

public class DigitalKycDiscardDocResponse {

  private boolean success;

  private String info;

  public boolean isSuccess() {
    return success;
  }

  public void setSuccess(boolean success) {
    this.success = success;
  }

  public String getInfo() {
    return info;
  }

  public void setInfo(String info) {
    this.info = info;
  }

  @Override
  public String toString() {
    return "DigitalKycDiscardDocResponse{" +
           "success=" + success +
           ", info='" + info + '\'' +
           '}';
  }
}
