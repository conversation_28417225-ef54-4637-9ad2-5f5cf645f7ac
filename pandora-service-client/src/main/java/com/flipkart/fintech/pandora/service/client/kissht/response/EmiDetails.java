package com.flipkart.fintech.pandora.service.client.kissht.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

/**
 * Created by aniruddha.sharma on 13/12/17.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class EmiDetails {

    private int tenure;

    private List<EmiInstallmentResponse> emiInstallmentResponseList;

    public int getTenure() {
        return tenure;
    }

    public void setTenure(int tenure) {
        this.tenure = tenure;
    }

    public List<EmiInstallmentResponse> getEmiInstallmentResponseList() {
        return emiInstallmentResponseList;
    }

    public void setEmiInstallmentResponseList(List<EmiInstallmentResponse> emiInstallmentResponseList) {
        this.emiInstallmentResponseList = emiInstallmentResponseList;
    }
}
