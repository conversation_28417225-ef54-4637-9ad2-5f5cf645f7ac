package com.flipkart.fintech.pandora.service.client.kissht.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Created by aniruddha.sharma on 06/12/17.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class StatementTransactionEmiDetailsResponse {
    @JsonProperty("current_installment")
    private int currentInstallment;

    @JsonProperty("total_installment")
    private int totalInstallment;

    @JsonProperty("principalAmount")
    private double principalAmount;

    @JsonProperty("interest")
    private EmiDetailsInterestResponse emiDetailsInterestResponse;

    public int getCurrentInstallment() {
        return currentInstallment;
    }

    public void setCurrentInstallment(int currentInstallment) {
        this.currentInstallment = currentInstallment;
    }

    public int getTotalInstallment() {
        return totalInstallment;
    }

    public void setTotalInstallment(int totalInstallment) {
        this.totalInstallment = totalInstallment;
    }

    public double getPrincipalAmount() {
        return principalAmount;
    }

    public void setPrincipalAmount(double principalAmount) {
        this.principalAmount = principalAmount;
    }

    public EmiDetailsInterestResponse getEmiDetailsInterestResponse() {
        return emiDetailsInterestResponse;
    }

    public void setEmiDetailsInterestResponse(EmiDetailsInterestResponse emiDetailsInterestResponse) {
        this.emiDetailsInterestResponse = emiDetailsInterestResponse;
    }

    @Override
    public String toString() {
        return "StatementTransactionEmiDetailsResponse{" +
                "currentInstallment=" + currentInstallment +
                ", totalInstallment=" + totalInstallment +
                ", principalAmount=" + principalAmount +
                ", emiDetailsInterestResponse=" + emiDetailsInterestResponse +
                '}';
    }
}
