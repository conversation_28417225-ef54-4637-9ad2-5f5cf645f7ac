package com.flipkart.fintech.pandora.service.client.kissht.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Created by aniruddha.sharma on 06/12/17.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class StatementTransactionResponse {

    private String transactionId;

    private String transactionDate;

    private Double transactionAmount;

    private String currency;

    private String transactionType;

    private String transactionSubType;

    @JsonProperty("emiDetail")
    private StatementTransactionEmiDetailsResponse statementTransactionEmiDetailsResponse;

    @JsonProperty("lateFees")
    private StatementTransactionLateFeesResponse statementTransactionLateFeesResponse;

    @JsonProperty("payBackDetails")
    private StatementTransactionPayBackDetailsResponse statementTransactionPayBackDetailsResponse;

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getTransactionDate() {
        return transactionDate;
    }

    public void setTransactionDate(String transactionDate) {
        this.transactionDate = transactionDate;
    }

    public Double getTransactionAmount() {
        return transactionAmount;
    }

    public void setTransactionAmount(Double transactionAmount) {
        this.transactionAmount = transactionAmount;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getTransactionType() {
        return transactionType;
    }

    public void setTransactionType(String transactionType) {
        this.transactionType = transactionType;
    }

    public String getTransactionSubType() {
        return transactionSubType;
    }

    public void setTransactionSubType(String transactionSubType) {
        this.transactionSubType = transactionSubType;
    }

    public StatementTransactionEmiDetailsResponse getStatementTransactionEmiDetailsResponse() {
        return statementTransactionEmiDetailsResponse;
    }

    public void setStatementTransactionEmiDetailsResponse(StatementTransactionEmiDetailsResponse statementTransactionEmiDetailsResponse) {
        this.statementTransactionEmiDetailsResponse = statementTransactionEmiDetailsResponse;
    }

    public StatementTransactionLateFeesResponse getStatementTransactionLateFeesResponse() {
        return statementTransactionLateFeesResponse;
    }

    public void setStatementTransactionLateFeesResponse(StatementTransactionLateFeesResponse
                                                                statementTransactionLateFeesResponse) {
        this.statementTransactionLateFeesResponse = statementTransactionLateFeesResponse;
    }

    public StatementTransactionPayBackDetailsResponse getStatementTransactionPayBackDetailsResponse() {
        return statementTransactionPayBackDetailsResponse;
    }

    public void setStatementTransactionPayBackDetailsResponse(StatementTransactionPayBackDetailsResponse statementTransactionPayBackDetailsResponse) {
        this.statementTransactionPayBackDetailsResponse = statementTransactionPayBackDetailsResponse;
    }
}
