package com.flipkart.fintech.pandora.service.client.kissht.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import javax.ws.rs.DefaultValue;

/**
 * Created by aniruddha.sharma on 03/11/17.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class TransactionFinishResponse {
    @JsonProperty("success")
    @DefaultValue("false")
    private boolean success;

    @JsonProperty("error_code")
    private int errorCode;

    @JsonProperty("info")
    private String info;

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public int getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(int errorCode) {
        this.errorCode = errorCode;
    }

    public String getInfo() {
        return info;
    }

    public void setInfo(String info) {
        this.info = info;
    }
}
