package com.flipkart.fintech.pandora.service.client.kissht.response.billing;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigDecimal;

/**
 * Created by aniruddha.sharma on 12/12/17.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LoanResponse {
    @JsonProperty("loan_reference_number")
    private String loanReferenceNumber;

    @JsonProperty("loan_amount")
    private BigDecimal loanAmount;

    @JsonProperty("loan_start_date")
    private String loanStartDate;

    @JsonProperty("loan_end_date")
    private String loanEndDate;

    @JsonProperty("transaction_amount")
    private BigDecimal transactionAmount;

    @JsonProperty("downpayment_amount")
    private BigDecimal downpaymentAmount;

    @JsonProperty("interest_rate")
    private BigDecimal interestRate;

    @JsonProperty("total_interest_amount")
    private BigDecimal totalInterestAmount;

    @JsonProperty("installment_amount")
    private BigDecimal installmentAmount;

    @JsonProperty("tenure")
    private int tenure;

    @JsonProperty("principal_outstanding_amount")
    private BigDecimal principalOutstandingAmount;

    @JsonProperty("total_principal_paid")
    private BigDecimal totalPrincipalPaid;

    @JsonProperty("total_interest_paid")
    private BigDecimal totalInterestPaid;

    @JsonProperty("number_of_installments_paid")
    private int numberOfInstallmentsPaid;

    @JsonProperty("loan_status")
    private String loanStatus;

    @JsonProperty("kissht_transaction_id")
    private String lenderTransactionId;

    @JsonProperty("loan_type")
    private String loanType;

    public String getLoanReferenceNumber() {
        return loanReferenceNumber;
    }

    public void setLoanReferenceNumber(String loanReferenceNumber) {
        this.loanReferenceNumber = loanReferenceNumber;
    }

    public BigDecimal getLoanAmount() {
        return loanAmount;
    }

    public void setLoanAmount(BigDecimal loanAmount) {
        this.loanAmount = loanAmount;
    }

    public String getLoanStartDate() {
        return loanStartDate;
    }

    public void setLoanStartDate(String loanStartDate) {
        this.loanStartDate = loanStartDate;
    }

    public String getLoanEndDate() {
        return loanEndDate;
    }

    public void setLoanEndDate(String loanEndDate) {
        this.loanEndDate = loanEndDate;
    }

    public BigDecimal getTransactionAmount() {
        return transactionAmount;
    }

    public void setTransactionAmount(BigDecimal transactionAmount) {
        this.transactionAmount = transactionAmount;
    }

    public BigDecimal getDownpaymentAmount() {
        return downpaymentAmount;
    }

    public void setDownpaymentAmount(BigDecimal downpaymentAmount) {
        this.downpaymentAmount = downpaymentAmount;
    }

    public BigDecimal getInterestRate() {
        return interestRate;
    }

    public void setInterestRate(BigDecimal interestRate) {
        this.interestRate = interestRate;
    }

    public BigDecimal getInstallmentAmount() {
        return installmentAmount;
    }

    public void setInstallmentAmount(BigDecimal installmentAmount) {
        this.installmentAmount = installmentAmount;
    }

    public int getTenure() {
        return tenure;
    }

    public void setTenure(int tenure) {
        this.tenure = tenure;
    }

    public BigDecimal getPrincipalOutstandingAmount() {
        return principalOutstandingAmount;
    }

    public void setPrincipalOutstandingAmount(BigDecimal principalOutstandingAmount) {
        this.principalOutstandingAmount = principalOutstandingAmount;
    }

    public BigDecimal getTotalPrincipalPaid() {
        return totalPrincipalPaid;
    }

    public void setTotalPrincipalPaid(BigDecimal totalPrincipalPaid) {
        this.totalPrincipalPaid = totalPrincipalPaid;
    }

    public BigDecimal getTotalInterestPaid() {
        return totalInterestPaid;
    }

    public void setTotalInterestPaid(BigDecimal totalInterestPaid) {
        this.totalInterestPaid = totalInterestPaid;
    }

    public int getNumberOfInstallmentsPaid() {
        return numberOfInstallmentsPaid;
    }

    public void setNumberOfInstallmentsPaid(int numberOfInstallmentsPaid) {
        this.numberOfInstallmentsPaid = numberOfInstallmentsPaid;
    }

    public String getLoanStatus() {
        return loanStatus;
    }

    public void setLoanStatus(String loanStatus) {
        this.loanStatus = loanStatus;
    }

    public String getLenderTransactionId() {
        return lenderTransactionId;
    }

    public void setLenderTransactionId(String lenderTransactionId) {
        this.lenderTransactionId = lenderTransactionId;
    }

    public String getLoanType() {
        return loanType;
    }

    public void setLoanType(String loanType) {
        this.loanType = loanType;
    }

    public BigDecimal getTotalInterestAmount() {
        return totalInterestAmount;
    }

    public void setTotalInterestAmount(BigDecimal totalInterestAmount) {
        this.totalInterestAmount = totalInterestAmount;
    }

    @Override
    public String toString() {
        return "LoanResponse{" +
                "loanReferenceNumber='" + loanReferenceNumber + '\'' +
                ", loanAmount=" + loanAmount +
                ", loanStartDate='" + loanStartDate + '\'' +
                ", loanEndDate='" + loanEndDate + '\'' +
                ", transactionAmount=" + transactionAmount +
                ", downpaymentAmount=" + downpaymentAmount +
                ", interestRate=" + interestRate +
                ", totalInterestAmount=" + totalInterestAmount +
                ", installmentAmount=" + installmentAmount +
                ", tenure=" + tenure +
                ", principalOutstandingAmount=" + principalOutstandingAmount +
                ", totalPrincipalPaid=" + totalPrincipalPaid +
                ", totalInterestPaid=" + totalInterestPaid +
                ", numberOfInstallmentsPaid=" + numberOfInstallmentsPaid +
                ", loanStatus='" + loanStatus + '\'' +
                ", lenderTransactionId='" + lenderTransactionId + '\'' +
                ", loanType='" + loanType + '\'' +
                '}';
    }
}
