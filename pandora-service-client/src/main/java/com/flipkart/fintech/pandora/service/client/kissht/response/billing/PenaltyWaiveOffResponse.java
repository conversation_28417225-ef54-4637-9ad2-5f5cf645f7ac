package com.flipkart.fintech.pandora.service.client.kissht.response.billing;

import com.fasterxml.jackson.annotation.JsonProperty;

import javax.ws.rs.DefaultValue;

/**
 * <AUTHOR>
 * @since 22/05/18.
 */
public class PenaltyWaiveOffResponse {
    @JsonProperty("success")
    @DefaultValue("false")
    private boolean success;

    @JsonProperty("error_code")
    private int errorCode;

    @JsonProperty("info")
    private String info;

    @JsonProperty("waived_reference_no")
    private String lenderPenaltyReverseRef;

    public int getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(int errorCode) {
        this.errorCode = errorCode;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getInfo() {
        return info;
    }

    public void setInfo(String info) {
        this.info = info;
    }

    public String getLenderPenaltyReverseRef() {
        return lenderPenaltyReverseRef;
    }

    public void setLenderPenaltyReverseRef(String lenderPenaltyReverseRef) {
        this.lenderPenaltyReverseRef = lenderPenaltyReverseRef;
    }

    @Override
    public String toString() {
        return "PenaltyWaiveOffResponse{" +
                "success=" + success +
                ", errorCode=" + errorCode +
                ", info='" + info + '\'' +
                ", lenderPenaltyReverseRef='" + lenderPenaltyReverseRef + '\'' +
                '}';
    }
}
