package com.flipkart.fintech.pandora.service.client.kissht.response.billing.types;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Created by aniruddha.sharma on 01/02/18.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EmiInstallment {

    @JsonProperty("kissht_transaction_id")
    private String lenderTransactionId;

    @JsonProperty("kissht_transaction_detail_reference_number")
    private String lenderTransactionDetailReferenceNumber;

    @JsonProperty("emi_installment_index")
    private Integer emiInstallmentIndex;

    @JsonProperty("loan_tenure")
    private Integer loanTenure;

    @JsonProperty("repayment_reference_number")
    private String repaymentReferenceNumber;

    public String getLenderTransactionId() {
        return lenderTransactionId;
    }

    public void setLenderTransactionId(String lenderTransactionId) {
        this.lenderTransactionId = lenderTransactionId;
    }

    public Integer getEmiInstallmentIndex() {
        return emiInstallmentIndex;
    }

    public void setEmiInstallmentIndex(Integer emiInstallmentIndex) {
        this.emiInstallmentIndex = emiInstallmentIndex;
    }

    public Integer getLoanTenure() {
        return loanTenure;
    }

    public void setLoanTenure(Integer loanTenure) {
        this.loanTenure = loanTenure;
    }

    public String getRepaymentReferenceNumber() {
        return repaymentReferenceNumber;
    }

    public void setRepaymentReferenceNumber(String repaymentReferenceNumber) {
        this.repaymentReferenceNumber = repaymentReferenceNumber;
    }

    public String getLenderTransactionDetailReferenceNumber() {
        return lenderTransactionDetailReferenceNumber;
    }

    public void setLenderTransactionDetailReferenceNumber(String lenderTransactionDetailReferenceNumber) {
        this.lenderTransactionDetailReferenceNumber = lenderTransactionDetailReferenceNumber;
    }

    @Override
    public String toString() {
        return "EmiInstallment{" +
                "lenderTransactionId='" + lenderTransactionId + '\'' +
                ", lenderTransactionDetailReferenceNumber='" + lenderTransactionDetailReferenceNumber + '\'' +
                ", emiInstallmentIndex=" + emiInstallmentIndex +
                ", loanTenure=" + loanTenure +
                ", repaymentReferenceNumber='" + repaymentReferenceNumber + '\'' +
                '}';
    }
}
