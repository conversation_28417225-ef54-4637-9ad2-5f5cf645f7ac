package com.flipkart.fintech.pandora.service.client.kissht.response.billing.types;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigDecimal;

/**
 * Created by aniruddha.sharma on 01/02/18.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class LateFees {

    @JsonProperty("late_fees_reference_number")
    private String lateFeesReferenceNumber;

    @JsonProperty("kissht_transaction_id")
    private String lenderTransactionId;

    @JsonProperty("late_fees_amount")
    private BigDecimal lateFeesAmount;

    @JsonProperty("late_fees_period")
    private String lateFeesPeriod;

    @JsonProperty("emi_installment_index")
    private Integer emiInstallmentIndex;

    @JsonProperty("loan_tenure")
    private Integer loanTenure;

    @JsonProperty("cgst_amount")
    private BigDecimal cgstAmount;

    @JsonProperty("sgst_amount")
    private BigDecimal sgstAmount;

    @JsonProperty("igst_amount")
    private BigDecimal igstAmount;

    @JsonProperty("base_amount")
    private BigDecimal baseAmount;

    public String getLateFeesReferenceNumber() {
        return lateFeesReferenceNumber;
    }

    public void setLateFeesReferenceNumber(String lateFeesReferenceNumber) {
        this.lateFeesReferenceNumber = lateFeesReferenceNumber;
    }

    public String getLenderTransactionId() {
        return lenderTransactionId;
    }

    public void setLenderTransactionId(String lenderTransactionId) {
        this.lenderTransactionId = lenderTransactionId;
    }

    public BigDecimal getLateFeesAmount() {
        return lateFeesAmount;
    }

    public void setLateFeesAmount(BigDecimal lateFeesAmount) {
        this.lateFeesAmount = lateFeesAmount;
    }

    public String getLateFeesPeriod() {
        return lateFeesPeriod;
    }

    public void setLateFeesPeriod(String lateFeesPeriod) {
        this.lateFeesPeriod = lateFeesPeriod;
    }

    public Integer getEmiInstallmentIndex() {
        return emiInstallmentIndex;
    }

    public void setEmiInstallmentIndex(Integer emiInstallmentIndex) {
        this.emiInstallmentIndex = emiInstallmentIndex;
    }

    public Integer getLoanTenure() {
        return loanTenure;
    }

    public void setLoanTenure(Integer loanTenure) {
        this.loanTenure = loanTenure;
    }

    public BigDecimal getCgstAmount() {
        return cgstAmount;
    }

    public void setCgstAmount(BigDecimal cgstAmount) {
        this.cgstAmount = cgstAmount;
    }

    public BigDecimal getSgstAmount() {
        return sgstAmount;
    }

    public void setSgstAmount(BigDecimal sgstAmount) {
        this.sgstAmount = sgstAmount;
    }

    public BigDecimal getIgstAmount() {
        return igstAmount;
    }

    public void setIgstAmount(BigDecimal igstAmount) {
        this.igstAmount = igstAmount;
    }

    public BigDecimal getBaseAmount() {
        return baseAmount;
    }

    public void setBaseAmount(BigDecimal baseAmount) {
        this.baseAmount = baseAmount;
    }
}
