package com.flipkart.fintech.pandora.service.client.kotak;

import com.flipkart.fintech.pandora.service.client.utils.AESEncryptionUtility;

import javax.crypto.spec.IvParameterSpec;
import java.security.*;
import java.util.Base64;

public class EncryptionUtility extends AESEncryptionUtility {

    public String encrypt(String message, String secretKey, String initializationVector) throws GeneralSecurityException {
        byte[] messageData = message.getBytes();
        byte[] ivParams = new byte[16];
        byte[] encodedMessage = new byte[messageData.length + 16];
        System.arraycopy(ivParams, 0, encodedMessage, 0, 16);
        System.arraycopy(messageData, 0, encodedMessage, 16, messageData.length);
        IvParameterSpec ivParameterSpec = new IvParameterSpec(ivParams);
        return encryptData(encodedMessage, ivParameterSpec, generateSecretKeySpec(secretKey));
    }

    public String decrypt(String message, String secretKey, String initializationVector) throws GeneralSecurityException {
        byte[] encryptedMessageData = message.getBytes();
        encryptedMessageData = Base64.getDecoder().decode(encryptedMessageData);
        byte[] decodedMessageData = new byte[encryptedMessageData.length - 16];
        System.arraycopy(encryptedMessageData, 16, decodedMessageData, 0, encryptedMessageData.length - 16);
        byte[] ivParams = new byte[16];
        System.arraycopy(encryptedMessageData,0, ivParams,0, ivParams.length);
        IvParameterSpec ivParameterSpec = new IvParameterSpec(ivParams);
        return decryptData(decodedMessageData, ivParameterSpec, generateSecretKeySpec(secretKey));
    }
}
