package com.flipkart.fintech.pandora.service.client.kotak.common;

import com.flipkart.fintech.pandora.service.client.kotak.constants.Constants;

import javax.ws.rs.core.HttpHeaders;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.MultivaluedHashMap;

public final class HeaderUtility {

    private HeaderUtility() {

    }

    public static MultivaluedHashMap<String, Object> getHeaders(String accessToken) {
        MultivaluedHashMap<String, Object> map = new MultivaluedHashMap<>();
        map.add(Constants.Headers.AUTHORIZATION, String.format(Constants.Headers.TOKEN_FORMAT,
                accessToken));
        map.add(HttpHeaders.CONTENT_TYPE, MediaType.TEXT_PLAIN);
        return map;
    }
}
