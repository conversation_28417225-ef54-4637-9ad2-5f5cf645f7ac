package com.flipkart.fintech.pandora.service.client.kotak.penny_drop;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.pandora.service.client.PandoraServiceClientException;
import com.flipkart.fintech.pandora.service.client.config.KotakClientConfiguration;
import com.flipkart.fintech.pandora.service.client.kotak.EncryptionUtility;
import com.flipkart.fintech.pandora.service.client.kotak.common.HttpRequestExecutor;
import com.flipkart.fintech.pandora.service.client.kotak.constants.Constants;
import com.flipkart.fintech.pandora.service.client.kotak.requests.PennyDropRequest;
import com.flipkart.fintech.pandora.service.client.kotak.responses.PennyDropResponse;
import com.google.inject.Inject;
import com.google.inject.name.Named;
import lombok.extern.slf4j.Slf4j;

import javax.ws.rs.client.Client;

@Slf4j
public class KotakPennyDropClient extends HttpRequestExecutor implements PennyDropClient {

    public static final String PENNY_DROP_FUNCTION = "PENNY_DROP_FUNCTION";

    @Inject
    public KotakPennyDropClient(@Named("kotak") Client client, KotakClientConfiguration kotakClientConfiguration,
                                ObjectMapper objectMapper, EncryptionUtility encryptionUtility) {
        super(client, kotakClientConfiguration, encryptionUtility, objectMapper);
    }

    @Override
    public PennyDropResponse pennyDrop(PennyDropRequest pennyDropRequest, String accessToken) {
        log.info("Penny Drop request for {}", pennyDropRequest.getUniqueIdentifier());
        PennyDropResponse pennyDropResponse = null;
        try {
            pennyDropResponse = this.executeHttpRequest(PENNY_DROP_FUNCTION, objectMapper.writeValueAsString(pennyDropRequest),
                    accessToken, Constants.Endpoints.PENNY_DROP, new TypeReference<PennyDropResponse>() {
                    });
        } catch (Exception e) {
            log.error("Error in penny drop for request id: {}", pennyDropRequest.getUniqueIdentifier(), e);
            throw new PandoraServiceClientException(e.getMessage());
        }
        return pennyDropResponse;
    }
}
