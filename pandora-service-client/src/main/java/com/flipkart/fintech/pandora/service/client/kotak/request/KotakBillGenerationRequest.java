package com.flipkart.fintech.pandora.service.client.kotak.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class KotakBillGenerationRequest {

    private String accountNumber;
    private String billDate;
    private String billDateFormat;
    private BigDecimal billAmount;

}
