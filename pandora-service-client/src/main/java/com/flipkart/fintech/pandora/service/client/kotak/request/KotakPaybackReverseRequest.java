package com.flipkart.fintech.pandora.service.client.kotak.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class KotakPaybackReverseRequest {

    private BigDecimal amount;
    private Long transactionTime;
    private String externalId;
    private String accountNumber;
}
