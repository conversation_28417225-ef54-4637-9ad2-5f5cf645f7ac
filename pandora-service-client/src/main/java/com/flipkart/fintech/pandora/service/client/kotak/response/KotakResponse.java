package com.flipkart.fintech.pandora.service.client.kotak.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.ws.rs.core.Response;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class KotakResponse {
    private Response.Status statusCode;
    private String endPoint;
    private String transactionId;
    private String transactionIdentifier;
    private String accountChargeIdentifer;
    private String billIdentifier;
    private String errorMessage;
}
