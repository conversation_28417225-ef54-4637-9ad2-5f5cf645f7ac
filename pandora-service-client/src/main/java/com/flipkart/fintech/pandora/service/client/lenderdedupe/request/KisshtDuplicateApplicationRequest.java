package com.flipkart.fintech.pandora.service.client.lenderdedupe.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Data;

@JsonIgnoreProperties(ignoreUnknown = true)
@AllArgsConstructor
@Data
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class KisshtDuplicateApplicationRequest extends LenderDuplicateApplicationRequest{
    private String mobileNumber;
    private String pan;
}
