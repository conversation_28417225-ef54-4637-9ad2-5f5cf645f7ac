package com.flipkart.fintech.pandora.service.client.lenderdedupe.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class MPokketDuplicateApplicationRequest extends LenderDuplicateApplicationRequest{
    @JsonProperty("email_id")
    private String emailId;
    @JsonProperty("mobile_number")
    private String mobileNumber;
}
