package com.flipkart.fintech.pandora.service.client.lenderdedupe.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class MpokketDedupeResponse {

    @JsonProperty("data")
    private Object data;

    @JsonProperty("message")
    private String message;

    @JsonProperty("status_code")
    private String statusCode;

    @JsonProperty("success")
    private boolean success;

    @JsonProperty("error_message")
    private List<String> errorMessage;
}
