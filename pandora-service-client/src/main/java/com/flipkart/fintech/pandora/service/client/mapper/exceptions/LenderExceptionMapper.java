package com.flipkart.fintech.pandora.service.client.mapper.exceptions;

import com.flipkart.fintech.pandora.api.model.pl.sandbox.dto.ApplicationStatus;
import com.flipkart.fintech.pandora.service.client.exceptions.LenderException;
import com.flipkart.fintech.pandora.service.client.mapper.sandbox.SandboxErrorMapper;
import com.flipkart.fintech.pandora.service.client.sandbox.v2.response.CreateApplicationResponse;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.ext.ExceptionMapper;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class LenderExceptionMapper implements ExceptionMapper<LenderException> {

  @Override
  public Response toResponse(LenderException e) {

    log.debug("Mapping LenderException to HTTP response: {}", e.getMessage());
    CreateApplicationResponse createApplicationResponse = new CreateApplicationResponse();
    createApplicationResponse.setError(e.getError());
    createApplicationResponse.setApplicationStatus(
        new ApplicationStatus(e.getError().getStatus(), e.getError().getReason()));
    return Response.status(SandboxErrorMapper.getHttpStatusCode(e.getError()))
        .type(MediaType.APPLICATION_JSON_TYPE)
        .entity(createApplicationResponse)
        .build();
  }
}
