package com.flipkart.fintech.pandora.service.client.mapper.sandbox;

import com.fasterxml.jackson.databind.JsonNode;
import com.flipkart.fintech.pandora.service.client.pl.response.ErrorResponse;
import org.apache.commons.lang3.StringUtils;

import static javax.ws.rs.core.Response.Status.INTERNAL_SERVER_ERROR;

public class SandboxErrorMapper implements ErrorMapper {

  public static int getHttpStatusCode(ErrorResponse error) {
    // TODO: Winterfell can only ack 500 status code. tracking https://github.fkinternal.com/Flipkart/sm-winterfell/issues/475
    return INTERNAL_SERVER_ERROR.getStatusCode();
  }

  static ErrorResponse classifyError(String message) {
    ErrorResponse.ErrorResponseBuilder errorResponse = ErrorResponse.builder().detail(message);
    if (isPanError(message))
      return errorResponse.code("ERR_CLI_PAN_INVALID").reason("Invalid PAN").build();
    if (isPincodeError(message))
      return errorResponse
          .code("ERR_SVR_UNSERVICEABLE_PINCODE")
          .reason("This pincode is not serviced yet")
          .build();
    if (isInvalidEmploymentType(message))
      return errorResponse
          .code("ERR_CLI_INVALID_EMPLOYMENT_TYPE")
          .reason("Invalid employment type")
          .build();
    if (isInvalidName(message))
      return errorResponse
          .code("ERR_CLI_INVALID_FIRST_NAME")
          .reason("Invalid first or last name")
          .build();
    if (isPanFailed(message))
      return errorResponse
          .code("ERR_SVR_VERIFY_PAN_FAILED")
          .reason("PAN verification failed at upstream. Retry.")
          .build();
    if (isAuthError(message))
      return errorResponse.code("ERR_CLI_AUTH_FAILED").reason("Invalid auth code").build();
    if (isTimestampError(message))
      return errorResponse
          .code("ERR_CLI_TS_IN_FUTURE")
          .reason("Timestamp is greater than current timestamp")
          .build();

    return errorResponse.code("ERR_SVR_UNKNOWN").reason(message).build();
  }

  private static boolean isTimestampError(String message) {
    return (message.contains("greater") || message.contains("future"))
        && (message.contains("timestamp") || message.contains("ts"));
  }

  private static boolean isAuthError(String message) {
    return message.contains("auth")
        && (message.contains("error") || message.contains("not") || message.contains("exception"));
  }

  private static boolean isPanFailed(String message) {
    return message.contains("pan") && (message.contains("failed") || message.contains("verif"));
  }

  private static boolean isInvalidName(String message) {
    return containsInvalid(message) && message.contains("name");
  }

  private static boolean isPanError(String message) {
    return containsInvalid(message) && message.contains("pan");
  }

  private static boolean isInvalidEmploymentType(String message) {
    return containsInvalid(message)
        && (message.contains("employment_type") || message.contains("employment type"));
  }

  private static boolean isPincodeError(String message) {
    return (message.contains("pincode") || message.contains("pin code"));
  }

  private static boolean containsInvalid(String message) {
    return message.contains("invalid") || message.contains("format");
  }

  @Override
  public ErrorResponse toObservedError(JsonNode jsonNode) {
    return toObservedError(getMessage(jsonNode));
  }

  @Override
  public ErrorResponse toObservedError(String string) {
    return SandboxErrorMapper.classifyError(string);
  }

  private static String getMessage(JsonNode jsonNode) {
    String message = jsonNode.findPath("journeyState").findPath("subState").asText();
    if (!StringUtils.isBlank(message)) return message.toLowerCase();
    message = jsonNode.findPath("Message").asText();
    if (!StringUtils.isBlank(message)) return message.toLowerCase();
    message = jsonNode.findPath("journeyState").findPath("subStatus").asText();
    if (!StringUtils.isBlank(message)) return message.toLowerCase();
    JsonNode node = jsonNode.findPath("error").findPath("details");
    if (node.isArray()) message = node.get(0).findPath("message").asText();
    return message.toLowerCase();
  }
}
