package com.flipkart.fintech.pandora.service.client.pl;

import com.flipkart.fintech.pandora.service.client.pl.request.*;
import com.flipkart.fintech.pandora.service.client.pl.response.*;

public interface IdfcBnplServiceClientV2 {
    IdfcSaleForwardResponse createBnplSaleForwardTransaction(BnplIdfcSaleForwardRequestV2 bnplSaleForwardRequest, String access_token);
    IdfcSaleForwardResponse createAdvanzSaleForwardTransaction(AdvanzIdfcSaleForwardRequestV2 saleForwardRequest, String access_token);
    BnplIdfcSaleReverseResponse createBnplSaleReverseTransaction(BnplIdfcSaleReverseRequestV2 bnplSaleReverseRequest, String access_token);
    EmiIdfcSaleReverseResponse createEmiSaleReverseTransaction(EmiIdfcSaleReverseRequestV2 emiSaleReverseRequest, String access_token);

    BnplIdfcPaybackForwardResponse createBnplPaybackForwardTransaction(BnplIdfcPaybackForwardRequestV2 bnplPaybackForwardRequest, String access_token);

    AdvanzIdfcPaybackForwardResponse createAdvanzPaybackForwardTransaction(AdvanzIdfcPaybackForwardRequestV2 advanzPaybackForwardRequest, String access_token);

    IdfcLoanClosureResponseV2 loanAutoClosure(LoanClosureIdfcRequestV2 loanClosureIdfcRequest, String accessToken);
}

