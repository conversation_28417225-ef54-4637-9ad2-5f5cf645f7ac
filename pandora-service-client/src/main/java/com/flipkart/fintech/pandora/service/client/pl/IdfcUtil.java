package com.flipkart.fintech.pandora.service.client.pl;

import com.flipkart.fintech.common.enums.Tenant;
import com.flipkart.fintech.filter.RequestContextThreadLocal;
import com.flipkart.fintech.pandora.service.client.utils.UniqueIdGenerator;
import com.google.inject.Inject;
import org.joda.time.DateTime;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

public class IdfcUtil {
    private IdfcConfiguration idfcConfiguration;

    @Inject
    public IdfcUtil(IdfcConfiguration idfcConfiguration) {
        this.idfcConfiguration = idfcConfiguration;
    }

    public static String getIdfcProductType(String loanType) {
        if(loanType != null) {
            if(LenderConstants.EMI.toString().equalsIgnoreCase(loanType)) {
                return LenderConstants.EMI;
            } else if(LenderConstants.PAY_LATER.toString().equalsIgnoreCase(loanType)) {
                return LenderConstants.BNPL;
            } else {
                return "";
            }
        }
        return "";
    }

    private String generateHash(String stringToEncrypt) throws NoSuchAlgorithmException {
        byte[]        bytes = stringToEncrypt.getBytes(StandardCharsets.UTF_8);
        MessageDigest m     = MessageDigest.getInstance("SHA-256");
        m.update(idfcConfiguration.getIdfcHashingSalt().getBytes());
        byte[] digest = m.digest(bytes);
        return new BigInteger(1, digest).toString(16);
    }

    public String generateIdfcRequestIdFromAccountId(String accountId) throws NoSuchAlgorithmException {
        String hash = generateHash(accountId);
        //Limiting characters in requestId for IDFC
        hash = hash.substring(0, Math.min(hash.length(), 15));
        return hash;
    }

    public List<String> generateIdfcRequestIdFromAccountIds(List<String> accountIds) throws NoSuchAlgorithmException {
        List<String> hashes = new ArrayList<>();
        for (String accId : accountIds) {
            String hash = generateHash(accId);
            //Limiting characters in requestId for IDFC
            hash = hash.substring(0, Math.min(hash.length(), 15));
            hashes.add(hash);
        }
        return hashes;
    }


    public String generateIdfcUPITxnId() {
        String upiTxnId = LenderConstants.UPI_TXN_ID_PREFIX;
        String uuid = UniqueIdGenerator.generateUUID();
        return upiTxnId.concat(uuid.substring(0, Math.min(uuid.length(), 24)));
    }

    public String generateIdfcIdempotentId(String accountId, String bankAccountNumber, String ifscCode) throws NoSuchAlgorithmException {

        String hash = generateHash(accountId + bankAccountNumber + ifscCode);
        //Limiting characters in requestId for IDFC
        hash = hash.substring(0, Math.min(hash.length(), 20));
        return hash;
    }

    public String generateRandomIdfcIdempotentId() throws NoSuchAlgorithmException {

        String hash = generateHash(UniqueIdGenerator.generateUUID());
        //Limiting characters in requestId for IDFC
        hash = hash.substring(0, Math.min(hash.length(), 20));
        return hash;
    }

    public static String getFormattedDatetime(DateTime dateTimeObject) {
        String           pattern          = "dd-MMM-yyyy";
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(pattern);
        if (dateTimeObject != null) {
            return simpleDateFormat.format(dateTimeObject.toDate());
        }
        return "";
    }

    public static String getFormattedDatetimeV2(DateTime dateTimeObject) {
        String           pattern          = "yyyy-MM-dd";
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(pattern);
        if (dateTimeObject != null) {
            return simpleDateFormat.format(dateTimeObject.toDate());
        }
        return "";
    }

    public static String getTransactionSourceSystem(String appId) {
        if (Tenant.FFB.equals(RequestContextThreadLocal.get().getTenantId())) {
            return LenderConstants.IDFC_FFB_SOURCE;
        }
        if(appId != null && appId.equals(LenderConstants.BBPS_APP_ID)) {
            return LenderConstants.BBPS_TRANSACTION_SOURCE_SYSTEM;
        }
        return LenderConstants.FLIPKART_TRANSACTION_SOURCE_SYSTEM;
    }

    public static String getSourceSystemForRemarks(String appId) {
        if(appId != null && appId.equals(LenderConstants.BBPS_APP_ID)) {
            return LenderConstants.BBPS_TRANSACTION_SOURCE_SYSTEM;
        }
        if(appId != null && appId.equals(LenderConstants.CLEARTRIP_APP_ID)) {
            return LenderConstants.CLEARTRIP_TRANSACTION_SOURCE_SYSTEM;
        }
        if(appId != null && appId.equals(LenderConstants.FK_TRAVEL_APP_ID)) {
            return LenderConstants.FK_TRAVEL_TRANSACTION_SOURCE_SYSTEM;
        }
        if (appId != null && !appId.equals(LenderConstants.FLIPKART_APP_ID)) {
            return LenderConstants.NON_FLIPKART_TRANSACTION_SOURCE_SYSTEM;
        }
        return LenderConstants.FLIPKART_TRANSACTION_SOURCE_SYSTEM;
    }

    public static String getChargeRequestType(String txnType){
        if (txnType.equalsIgnoreCase("debit")) {
            return LenderConstants.CHARGE_POST_TYPE;
        } else if (txnType.equalsIgnoreCase("credit")) {
            return LenderConstants.CHARGE_WAIVE_TYPE;
        }
        return "";
    }

    public static String getChargeRequestTypeV2(String txnType){
        if (txnType.equalsIgnoreCase("debit")) {
            return LenderConstants.CHARGE_POST_TYPE_V2;
        } else if (txnType.equalsIgnoreCase("credit")) {
            return LenderConstants.CHARGE_WAIVE_TYPE_V2;
        }
        return "";
    }

    public static String getChargeNarration(String txnType){
        if (txnType.equalsIgnoreCase("debit")) {
            return LenderConstants.CHARGE_POSTING_NARRATION;
        } else if (txnType.equalsIgnoreCase("credit")) {
            return LenderConstants.CHARGE_WAIVE_OFF_NARRATION;
        }
        return "";
    }

    public static String getCancellationRequestType(BigDecimal loanAmount, BigDecimal cancellationAmount) {
        if (loanAmount.compareTo(cancellationAmount) == 0) {
            return LenderConstants.FULL_REVERSAL_REQUEST_TYPE;
        }
        return LenderConstants.PARTIAL_REVERSAL_REQUEST_TYPE;

    }

    public static String getIdfcSourceSystem() {
        Tenant tenant = RequestContextThreadLocal.get().getTenantId();
        switch (tenant) {
            case FFB:
                return LenderConstants.IDFC_FFB_SOURCE;
            case FK_CONSUMER_CREDIT:
                return LenderConstants.IDFC_FLIPKART_SOURCE;
            default:
                return LenderConstants.NON_FLIPKART_TRANSACTION_SOURCE_SYSTEM;
        }
    }

    public static String getMinDuePaidFlag(BigDecimal paybackAmount, BigDecimal minDueAmount, BigDecimal totalPendingAmount, Boolean isOverduePayment){

        if ((minDueAmount != null || totalPendingAmount != null) && ((minDueAmount != null && paybackAmount.compareTo(minDueAmount) < 0) ||
                (totalPendingAmount != null && paybackAmount.compareTo(totalPendingAmount) < 0 && isOverduePayment != null && isOverduePayment))) {
            return LenderConstants.MIN_DUE_PAID_FALSE;
        }

        return LenderConstants.MIN_DUE_PAID_TRUE;


    }
}

