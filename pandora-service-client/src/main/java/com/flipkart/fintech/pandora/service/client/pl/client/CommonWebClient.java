package com.flipkart.fintech.pandora.service.client.pl.client;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.flipkart.fintech.pandora.service.client.auth.AccessTokenProvider;
import com.flipkart.fintech.pandora.service.client.auth.Scope;
import com.flipkart.fintech.pandora.service.client.config.ClientConfiguration;
import com.flipkart.fintech.pandora.service.client.exceptions.LenderException;
import com.flipkart.fintech.pandora.service.client.mapper.sandbox.ErrorMapper;
import com.flipkart.fintech.pandora.service.client.mapper.sandbox.SandboxErrorMapper;
import com.flipkart.fintech.pandora.service.client.pl.response.ErrorResponse;
import com.flipkart.fintech.pandora.service.client.sandbox.v2.response.CreateApplicationResponse;
import com.flipkart.fintech.pandora.service.client.utils.ObjectMapperUtil;
import com.google.inject.Inject;
import lombok.extern.slf4j.Slf4j;
import org.glassfish.jersey.client.ClientProperties;
import javax.ws.rs.ClientErrorException;
import javax.ws.rs.InternalServerErrorException;
import javax.ws.rs.ProcessingException;
import javax.ws.rs.client.Entity;
import javax.ws.rs.client.Invocation;
import javax.ws.rs.client.WebTarget;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.MultivaluedHashMap;
import javax.ws.rs.core.MultivaluedMap;
import javax.ws.rs.core.Response;
import java.net.SocketTimeoutException;
import java.util.Map;
import java.util.Objects;

import static com.flipkart.fintech.pandora.service.client.auth.Scope.LENDING_FIBE;
import static com.flipkart.fintech.pandora.service.client.auth.Scope.LENDING_MPOCKKET;

/**
 * <AUTHOR>
 * @date 08/12/23
 */
@Slf4j
public class CommonWebClient {

    protected final AccessTokenProvider accessTokenProvider;
    public static final String X_REQUEST_ID = "X-Request-ID";
    public static final String X_CLIENT_ID = "x-client-id";
    private final String MPOCKKET_CONFIG_PROPERTY = "mpockketConfiguration";
    protected final Map<Scope, ErrorMapper> scopeErrorMapperMap;

    @Inject
    public CommonWebClient(AccessTokenProvider accessTokenProvider, Map<Scope, ErrorMapper> scopeErrorMapperMap) {
        this.accessTokenProvider = accessTokenProvider;
        this.scopeErrorMapperMap = scopeErrorMapperMap;
    }

    public <T> T handleResponse(Response response, Class<T> successResponseType, Scope scope) throws InternalServerErrorException, LenderException {
        // Check if response is successful
        String responseBody = response.readEntity(String.class);
        log.info("Response received: {} {}", response.getStatus(),  responseBody);
        //TODO Remove the ABFL specific checks once PRECONDITION_FAILED Error handling is implemented by the lender
        if (response.getStatusInfo().getFamily() == Response.Status.Family.SUCCESSFUL ||
                (scope.equals(Scope.LENDING_ABFL) && response.getStatusInfo().getStatusCode() == Response.Status.PRECONDITION_FAILED.getStatusCode())) {
            JsonNode jsonNode;
            try {
                jsonNode = ObjectMapperUtil.get().readTree(responseBody);
            } catch (JsonProcessingException e) {
                log.error("Error parsing response body: {} {}", responseBody, e.getMessage());
                throw new InternalServerErrorException("Unable to parse the response received from the server");
            }
            if(jsonNode.isArray() && jsonNode.size() == 1) {
                return ObjectMapperUtil.get().convertValue(jsonNode.get(0), successResponseType);
            }
            return ObjectMapperUtil.get().convertValue(jsonNode, successResponseType);
        } else {
            log.error("Error while handling request from client, received status {}, reason {}, lender {}. Actual response {}", response.getStatusInfo().getStatusCode(), response.getStatusInfo().getReasonPhrase(), scope.name(), responseBody);
            if (successResponseType == CreateApplicationResponse.class ||
                    successResponseType == com.flipkart.fintech.pandora.service.client.sandbox.v1.response.CreateApplicationResponse.class) {
                throw new LenderException(ErrorResponse.get(responseBody, scopeErrorMapperMap.get(scope)));
            }
            if (response.getStatusInfo().getFamily() == Response.Status.Family.CLIENT_ERROR) {
                throw new ClientErrorException("Error while handling request from client for lender " + scope.name(), Response.Status.BAD_REQUEST);
            } else {
                throw new InternalServerErrorException("Unexpected response from the server for lender " + scope.name());
            }
        }
    }

    protected <T> String convertToString(T request) {
        try {
            return ObjectMapperUtil.get().writeValueAsString(request);
        } catch (Exception e) {
            throw new RuntimeException("Failed to convert object to string", e);
        }
    }

    public <T, E> E getPostResponse(T request, Class<E> responseClass, String path, WebTarget webTarget, Scope scope, String requestId) throws LenderException {
        return getPostResponse(request, responseClass, path, webTarget, scope, requestId, null);
    }

    private String formAccessToken(Scope scope, String path){
        String token = accessTokenProvider.getAccessToken(scope);
        if(Scope.valueOf(scope.name()) == LENDING_FIBE && path.contains("masked-dedupe")) return token;
        return "Bearer " + token;
    }

    private void invalidCacheIfUnAuthorized(int status, Scope scope) {
        if (status == Response.Status.UNAUTHORIZED.getStatusCode() || status == Response.Status.FORBIDDEN.getStatusCode()){
            accessTokenProvider.invalidateCacheForScope(scope);
        }
    }

    public <T, E> E getPostResponse(T request, Class<E> responseClass, String path, WebTarget webTarget, Scope scope, String requestId, ClientConfiguration clientConfiguration) throws LenderException {
        Response response = null;
        try {
            Invocation.Builder invocationBuilder = webTarget.path(path).request();
            invocationBuilder = invocationBuilder.headers(getHeaders(scope, path, requestId, clientConfiguration));
            if(Objects.nonNull(clientConfiguration) && Objects.nonNull(clientConfiguration.getReadTimeout())){
                invocationBuilder.property(ClientProperties.READ_TIMEOUT, clientConfiguration.getReadTimeout());
            }
            response = invocationBuilder.post(Entity.entity(convertToString(request), MediaType.APPLICATION_OCTET_STREAM));
            invalidCacheIfUnAuthorized(response.getStatus(), scope);
            return handleResponse(response, responseClass, scope);
        } catch (InternalServerErrorException | ClientErrorException e) {
            log.error("Internal Server Exception", e);
            throw e;
        } finally {
            if (response != null) {
                response.close();
            }
        }
    }

    public MultivaluedMap<String, Object> getHeaders(Scope scope, String path, String requestId, ClientConfiguration clientConfiguration){
        MultivaluedMap<String, Object> headers = new MultivaluedHashMap<>();
        if (Scope.valueOf(scope.name()) == LENDING_MPOCKKET) {return headers;}
        headers.add("Authorization", formAccessToken(scope, path));
        headers.add(X_REQUEST_ID, requestId);
        if(Objects.nonNull(clientConfiguration) && Objects.nonNull(clientConfiguration.getHeaders())){
            headers.putAll(clientConfiguration.getHeaders());
        }
        return headers;
    }
}
