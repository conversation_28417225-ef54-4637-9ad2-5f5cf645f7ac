package com.flipkart.fintech.pandora.service.client.pl.client;

import com.flipkart.fintech.pandora.service.client.cfa.ConfigHelper;
import com.flipkart.fintech.pandora.service.client.config.ClientConfiguration;
import com.flipkart.fintech.pandora.service.client.config.MpockketClientConfig;
import com.flipkart.fintech.pandora.service.client.exceptions.LenderException;
import com.flipkart.fintech.pandora.service.client.lenderdedupe.response.*;
import com.flipkart.fintech.pandora.service.client.mapper.sandbox.ErrorMapper;
import com.flipkart.fintech.pandora.service.client.sandbox.ApiParamModel;
import com.flipkart.fintech.pandora.service.client.sandbox.v1.request.CreateApplicationRequest;
import com.flipkart.fintech.pandora.service.client.sandbox.v1.request.GetApplicationRequest;
import com.flipkart.fintech.pandora.service.client.sandbox.v1.request.GetApplicationStatusRequest;
import com.flipkart.fintech.pandora.service.client.sandbox.v1.response.CreateApplicationResponse;
import com.flipkart.fintech.pandora.service.client.sandbox.v1.response.GetApplicationResponse;
import com.flipkart.fintech.pandora.api.model.response.sandbox.v1.GetApplicationStatusResponse;
import com.flipkart.fintech.pandora.service.client.sandbox.v1.utils.PlActions;
import com.flipkart.fintech.pandora.api.model.request.lenderdedupe.LenderDedupeRequest;
import com.flipkart.fintech.pandora.api.model.request.lenderdedupe.LenderDedupeResponse;
import com.flipkart.fintech.pandora.service.client.auth.AccessTokenProvider;
import com.flipkart.fintech.pandora.service.client.auth.Scope;
import com.flipkart.fintech.pandora.service.client.configuration.SandboxLendersPlConfiguration;
import com.flipkart.fintech.pandora.service.client.lenderdedupe.request.LenderDuplicateApplicationRequest;
import com.google.inject.Inject;
import com.google.inject.name.Named;
import lombok.extern.slf4j.Slf4j;
import org.glassfish.jersey.client.ClientProperties;
import org.modelmapper.ModelMapper;

import javax.ws.rs.ClientErrorException;
import javax.ws.rs.client.Client;
import javax.ws.rs.client.WebTarget;
import javax.ws.rs.core.MultivaluedHashMap;
import javax.ws.rs.core.MultivaluedMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 08/12/23
 */
@Slf4j
public class SmSandboxLendersPlClient extends CommonWebClient implements SandboxLendersPlClient {

    private final ApiParamModel apiParamModel;
    private final Map<Scope, Client> scopeClientMap;
    private final DedupeUtility dedupeUtility;
    @Inject
    public SmSandboxLendersPlClient(AccessTokenProvider accessTokenProvider, @Named("OAuthClientCommonMap") Map<Scope, Client> scopeClientMap, ApiParamModel apiParamModel, ModelMapper mapper, DedupeUtility dedupeUtility, @Named("ScopeToErrorMapperMap") Map<Scope, ErrorMapper> scopeErrorMapperMap) {
        super(accessTokenProvider, scopeErrorMapperMap);
        this.apiParamModel = apiParamModel;
        this.scopeClientMap = scopeClientMap;
        this.dedupeUtility = dedupeUtility;
    }


    @Override
    public CreateApplicationResponse getCreateApplicationResponse(CreateApplicationRequest request, Scope scope, String requestId) throws LenderException {
        Client client = scopeClientMap.get(scope);
        SandboxLendersPlConfiguration config = apiParamModel.getPlConfigurationForLender(scope);
        WebTarget webTarget = client.target(config.getHost());
        return getPostResponse(request, CreateApplicationResponse.class, config.getApiModel().get(PlActions.CREATE_APPLICATION).getEndpoint(),
                webTarget, scope, requestId);
    }

    @Override
    public GetApplicationResponse getApplicationResponse(GetApplicationRequest request, Scope scope, String requestId) throws LenderException {
        Client client = scopeClientMap.get(scope);
        SandboxLendersPlConfiguration config = apiParamModel.getPlConfigurationForLender(scope);
        WebTarget webTarget = client.target(config.getHost());

        return getPostResponse(request, GetApplicationResponse.class, config.getApiModel().get(PlActions.GET_APPLICATION).getEndpoint(),
                webTarget, scope, requestId);
    }

    @Override
    public GetApplicationStatusResponse getApplicationStatusResponse(GetApplicationStatusRequest request, Scope scope, String requestId) throws LenderException {
        Client client = scopeClientMap.get(scope);
        SandboxLendersPlConfiguration config = apiParamModel.getPlConfigurationForLender(scope);
        WebTarget webTarget = client.target(config.getHost());
        return getPostResponse(request, GetApplicationStatusResponse.class, config.getApiModel().get(PlActions.GET_APPLICATION_STATUS).getEndpoint(),
                webTarget, scope, requestId);
    }

    @Override
    public LenderDedupeResponse dedupeLenderApplication(LenderDedupeRequest request, Scope scope, String requestId) {
        LenderDuplicateApplicationRequest requestBody = dedupeUtility.createDedupeRequest(request);
        Client client = scopeClientMap.get(scope);
        SandboxLendersPlConfiguration config = apiParamModel.getPlConfigurationForLender(scope);
        WebTarget webTarget = client.target(config.getHost());
        Object response = null;
        String debugLog = String.format("DEDUPE_REQ_LENDER::PAN::%s,MOB::%s,LENDER::%s", request.getPan(), request.getMobileNumber(), request.getLender());
        try{
            if(Scope.valueOf(scope.name()) == Scope.LENDING_FIBE){
                response = getPostResponse(requestBody, FibeDedupeResponse.class, config.getApiModel().get(PlActions.CHECK_DEDUPE).getEndpoint(), webTarget, scope, requestId);
            }
            else if (Scope.valueOf(scope.name()) == Scope.LENDING_MONEYVIEW) {
                response = getPostResponse(requestBody, MoneyViewDedupeResponseV2.class, config.getApiModel().get(PlActions.CHECK_DEDUPE_V2).getEndpoint(), webTarget, scope, requestId);
            }
            else if (Scope.valueOf(scope.name()) == Scope.LENDING_MONEYVIEWOPENMKT) {
                response = getPostResponse(requestBody, MoneyViewDedupeResponseV2.class, config.getApiModel().get(PlActions.CHECK_DEDUPE_V2).getEndpoint(), webTarget, scope, requestId);
            }
            else if(Scope.valueOf(scope.name()) == Scope.LENDING_MONEYVIEWMFI){
                response = getPostResponse(requestBody, MoneyViewDedupeResponseV2.class, config.getApiModel().get(PlActions.CHECK_DEDUPE_V2).getEndpoint(), webTarget, scope, requestId);
            }
            else if (Scope.valueOf(scope.name()) == Scope.LENDING_SMARTCOIN) {
                response = getPostResponse(requestBody, SmartCoinDedupeResponse.class, config.getApiModel().get(PlActions.CHECK_DEDUPE).getEndpoint(), webTarget, scope, requestId);
            }
            else if (Scope.valueOf(scope.name()) == Scope.LENDING_DMI) {
                MultivaluedMap<String, Object> additionalHeaders = new MultivaluedHashMap<>();
                additionalHeaders.add(X_CLIENT_ID, apiParamModel.getPlConfigurationForLender(Scope.LENDING_DMI).getClientId());
                ClientConfiguration clientConfiguration = ClientConfiguration.builder().headers(additionalHeaders).build();
                response = getPostResponse(requestBody, DmiDedupeResponse.class, config.getApiModel().get(PlActions.CHECK_DEDUPE).getEndpoint(), webTarget, scope, requestId, clientConfiguration);
            }
            else if (Scope.valueOf(scope.name()) == Scope.LENDING_RING) {
                response = getPostResponse(requestBody, KisshtDedupeResponse.class, config.getApiModel().get(PlActions.CHECK_DEDUPE).getEndpoint(), webTarget, scope, requestId);
            }
            else if (Scope.valueOf(scope.name()) == Scope.LENDING_FINNABLE) {
                response = getPostResponse(requestBody, FinnableDedupeResponse.class, config.getApiModel().get(PlActions.CHECK_DEDUPE).getEndpoint(), webTarget, scope, requestId);
            }
            else if (Scope.valueOf(scope.name()) == Scope.LENDING_MPOCKKET) {
                log.info("Mpockket request received email, num: {}, {}", request.getEmail(), request.getMobileNumber());
                response = getPostResponse(requestBody, MpokketDedupeResponse.class, config.getApiModel().get(PlActions.CHECK_DEDUPE).getEndpoint(), webTarget, scope, requestId);
            }
            else {
                return null;
            }
        } catch (ClientErrorException e){
            LenderDedupeResponse errorResponse = new LenderDedupeResponse();
            if(Scope.valueOf(scope.name()) == Scope.LENDING_FIBE){

                errorResponse.setRequestSuccessful(true);
                errorResponse.setIsDuplicateApplication(true);
                debugLog += ",DUPLICATE ";
                log.info(debugLog);
                return errorResponse;
            }
            errorResponse.setRequestSuccessful(false);
            errorResponse.setMessage(e.getMessage());
            debugLog += ",FAIL ";
            log.info(debugLog);
            return errorResponse;
        } catch (Exception e){
            LenderDedupeResponse errorResponse = new LenderDedupeResponse();
            errorResponse.setRequestSuccessful(false);
            errorResponse.setMessage(e.getMessage());
            debugLog += ",FAIL ";
            log.info(debugLog);
            return errorResponse;
        }
        LenderDedupeResponse resp = dedupeUtility.parseResponseBasedOnLender(response, scope);
        debugLog += resp.getIsDuplicateApplication() ? ",DUPLICATE" : ",NOTDUPLICATE";
        log.info(debugLog);
        return resp;
    }

}
