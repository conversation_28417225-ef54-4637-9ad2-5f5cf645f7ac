package com.flipkart.fintech.pandora.service.client.pl.pennydrop;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.pandora.service.client.PandoraServiceClientException;
import com.flipkart.fintech.pandora.service.client.pl.request.IdfcFeeChargeRequest;
import com.flipkart.fintech.pandora.service.client.pl.request.pennyDrop.IdfcImpsPennyDropRequest;
import com.flipkart.fintech.pandora.service.client.pl.request.pennyDrop.IdfcUpiPennyDropRequest;
import com.flipkart.fintech.pandora.service.client.pl.request.pennyDrop.IdfcValidateUpiRequest;
import com.flipkart.fintech.pandora.service.client.pl.response.IdfcFeeChargeResponse;
import com.flipkart.fintech.pandora.service.client.pl.response.pennyDrop.IdfcImpsPennyDropResponse;
import com.flipkart.fintech.pandora.service.client.pl.response.pennyDrop.IdfcUpiPennyDropResponse;
import com.flipkart.fintech.pandora.service.client.pl.response.pennyDrop.IdfcValidateUpiResponse;
import com.google.inject.Inject;

import java.io.IOException;

public class IdfcMockClientImpl implements IdfcClient {

    private final ObjectMapper objectMapper;

    @Inject
    public IdfcMockClientImpl(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    @Override
    public IdfcImpsPennyDropResponse impsPennyDrop(IdfcImpsPennyDropRequest idfcImpsPennyDropRequest, String accessToken) {
        String response = "{\n" +
                "  \"reqId\": \"b2eryuiass\",\n" +
                "  \"status\": \"S\",\n" +
                "  \"transactionId\": \"************\",\n" +
                "  \"custBankAccNo\": \"***************\",\n" +
                "  \"customerName\": \"Bene Ac Holder\",\n" +
                "  \"errCode\": \"\",\n" +
                "  \"errMessage\": \"\"\n" +
                "}";
        IdfcImpsPennyDropResponse idfcImpsPennyDropResponse;
        try {
            idfcImpsPennyDropResponse = objectMapper.readValue(response, IdfcImpsPennyDropResponse.class);
        } catch (IOException e) {
            throw new PandoraServiceClientException(e);
        }
        return idfcImpsPennyDropResponse;
    }

    @Override
    public IdfcUpiPennyDropResponse upiPennyDrop(IdfcUpiPennyDropRequest idfcUpiPennyDropRequest, String accessToken) {
        String upiResponse = "{\n" +
                "\"reqId\": \"FLPKRT123456\",\n" +
                "\"TxnId\":\"IDFFKFLKRPT904a4b20f1a411eab4800055\",\n" +
                "\"TxnRefId\": \"************\",\n" +
                "\"CustRefId\": \"************\",\n" +
                "\"ResCode\": \"000\",\n" +
                "\"ResDesc\": \"Approved\",\n" +
                "\"TimeStamp\": \"2020-09-08T12:57:14+05:30\"\n" +
                "}";
        IdfcUpiPennyDropResponse idfcUpiPennyDropResponse = null;
        try {
            idfcUpiPennyDropResponse = objectMapper.readValue(upiResponse, IdfcUpiPennyDropResponse.class);
        } catch (JsonProcessingException e) {
            throw new PandoraServiceClientException(e);
        }
        return idfcUpiPennyDropResponse;
    }

    @Override
    public IdfcValidateUpiResponse validateUpiAddress(IdfcValidateUpiRequest idfcValidateUpiRequest, String accessToken) {
        String validateResponse = "{\n" +
                "\"reqId\": \"FLPKRT123456\",\n" +
                "\"TxnId\":\"IDFFKFLKRPT904a4b20f1a411eab4800050\",\n" +
                "\"VerifiedName\": \"Priyanka\",\n" +
                "\"TxnRefId\": \"025212604353\",\n" +
                "\"Type\": \"PERSON" +
                "\",\n" +
                "\"IFSC\": \"\",\n" +
                "\"AccType\": \"SAVINGS\"\n" +
                "}";
        IdfcValidateUpiResponse idfcValidateUpiResponse = null;
        try {
            idfcValidateUpiResponse = objectMapper.readValue(validateResponse, IdfcValidateUpiResponse.class);
            if ("test1@upi".equals(idfcValidateUpiRequest.getVirAddr())) {
                idfcValidateUpiResponse.setType("ENTITY");
            } else if ("test2@upi".equals(idfcValidateUpiRequest.getVirAddr())) {
                idfcValidateUpiResponse.setAccType("BANKWALLET");
            } else if ("test3@upi".equals(idfcValidateUpiRequest.getVirAddr())) {
                idfcValidateUpiResponse.setVerifiedName("Geet");
            }
        } catch (JsonProcessingException e) {
            throw new PandoraServiceClientException(e);
        }
        return idfcValidateUpiResponse;
    }


    @Override
    public IdfcFeeChargeResponse createIdfcFeeCharge(IdfcFeeChargeRequest idfcFeeChargeRequest, String accessToken) {
        return null;
    }
}
