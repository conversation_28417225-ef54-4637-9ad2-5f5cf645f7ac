package com.flipkart.fintech.pandora.service.client.pl.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class AdvanzIdfcPaybackForwardRequest {

    @JsonProperty("SourceName")
    private String sourceName;

    @JsonProperty("SourceRequestId")
    private String sourceRequestId;

    @JsonProperty("TransactionId")
    private String transactionId;

    @JsonProperty("TransactionDate")
    private String transactionDate;

    @JsonProperty("ChequeNo")
    private String chequeNo;

    @JsonProperty("ReceiptNo")
    private String receiptNo;

    @JsonProperty("TotalPaymentAmount")
    private String totalPaymentAmount;

    @JsonProperty("MinDue")
    private String minDue;

    @JsonProperty("MinDuePaid")
    private String minDuePaid;

    @JsonProperty("PaymentTransaction")
    private List<IdfcPaymentTransaction> paymentTransaction;

    @JsonProperty("ResponseStatus")
    private String responseStatus;

    @JsonProperty("ResponseId")
    private String responseId;

    @JsonProperty("ErrorMsg")
    private String errorMsg;

    @JsonIgnore
    private String transactionType;
}
