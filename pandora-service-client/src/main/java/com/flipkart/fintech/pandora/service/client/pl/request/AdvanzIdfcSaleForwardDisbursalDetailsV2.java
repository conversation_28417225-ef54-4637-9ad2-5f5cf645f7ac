package com.flipkart.fintech.pandora.service.client.pl.request;


import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class AdvanzIdfcSaleForwardDisbursalDetailsV2 {

    @JsonProperty("applicationid")
    private String applicationid;

    @JsonProperty("crnno")
    private Long crnno;

    @JsonProperty("disbursalamt")
    private Double disbursalamt;

    @JsonProperty("bankaccno")
    private String bankaccno;

    @JsonProperty("micrcode")
    private String micrcode;

    @JsonProperty("ifsccode")
    private String ifsccode;

    @JsonProperty("remarks")
    private String remarks;

    @JsonProperty("scheme")
    private Long scheme;

    @JsonProperty("rateemiflag")
    private String rateemiflag;

    @JsonProperty("interestrate")
    private Double interestrate;

    @JsonProperty("tenure")
    private Integer tenure;

    @JsonProperty("emi")
    private String emi;

    @JsonProperty("transactiondate")
    private String transactionDate;

    @JsonProperty("branchId")
    @JsonIgnore
    private String branchId;

    @JsonProperty("CollArea")
    @JsonIgnore
    private String collArea;

    @JsonProperty("orderId")
    @JsonIgnore
    private String orderId;

}
