package com.flipkart.fintech.pandora.service.client.pl.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;


@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class BnplIdfcPaybackForwardRequestV2 {

    @JsonProperty("LoanId")
    private String loanId;

    @JsonProperty("TransactionDate")
    private String transactionDate;

    @JsonProperty("reqId")
    private String reqId;

    @JsonProperty("SourceName")
    private String sourceName;

    @JsonProperty("POSAmount")
    private String POSAmount;

    @JsonProperty("LPPAmount")
    private String LPPAmount;

    @JsonProperty("TotalAmount")
    private String totalAmount;

    @JsonProperty("ChequeNo")
    private String chequeNo;

    @JsonProperty("ReceiptNo")
    private String receiptNo;

    @JsonProperty("TransactionType")
    private String transactionType;
}
