package com.flipkart.fintech.pandora.service.client.pl.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class GenerateOTPRequest {

    @JsonProperty("uid")
    private String uid;
    @JsonProperty("reqId")
    private String reqId;

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getReqId() {
        return reqId;
    }

    public void setReqId(String reqId) {
        this.reqId = reqId;
    }

    @Override
    public String toString() {
        return "GenerateOTPRequest{" +
                "uid='" + uid + '\'' +
                ", reqId='" + reqId + '\'' +
                '}';
    }
}
