package com.flipkart.fintech.pandora.service.client.pl.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class IdfcFeeChargeRequestV3 {
    @JsonProperty("entityCode")
    private String entityCode;

    @JsonProperty("entityReqId")
    private String entityReqId;

    @JsonProperty("loanId")
    private String loanId;

    @JsonProperty("txnDate")
    private String txnDate;

    @JsonProperty("requestType")
    private String requestType;

    @JsonProperty("chargeId")
    private String chargeId;

    @JsonProperty("chargeAmount")
    private String chargeAmount;

    @JsonProperty("chargeNarration")
    private String chargeNarration;

    @Override
    public String toString() {
        return "IdfcFeeChargeRequestV3{" +
                "entityCode='" + entityCode + '\'' +
                ", entityReqId='" + entityReqId + '\'' +
                ", loanId='" + loanId + '\'' +
                ", txnDate='" + txnDate + '\'' +
                ", requestType='" + requestType + '\'' +
                ", chargeId='" + chargeId + '\'' +
                ", chargeAmount='" + chargeAmount + '\'' +
                ", chargeNarration='" + chargeNarration + '\'' +
                '}';
    }
}
