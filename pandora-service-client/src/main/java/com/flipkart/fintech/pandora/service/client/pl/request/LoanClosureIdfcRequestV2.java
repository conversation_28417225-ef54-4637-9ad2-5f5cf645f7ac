package com.flipkart.fintech.pandora.service.client.pl.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@AllArgsConstructor
@NoArgsConstructor
public class LoanClosureIdfcRequestV2 {
    @JsonProperty("entityName")
    private String entityName;

    @JsonProperty("entityReqId")
    private String entityReqId;

    @JsonProperty("searchType")
    private String searchType;

    @JsonProperty("searchValue")
    private String searchValue;

    @JsonProperty("transactionDate")
    private String transactionDate;

    @JsonProperty("activityType")
    private String activityType;

    @JsonProperty("prepayPenaltyMatrix")
    private String prepayPenaltyMatrix;

    @JsonProperty("prepayPenalty")
    private String prepayPenalty;

}
