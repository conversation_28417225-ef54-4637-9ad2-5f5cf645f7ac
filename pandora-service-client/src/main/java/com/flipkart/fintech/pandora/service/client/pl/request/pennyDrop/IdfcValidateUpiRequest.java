package com.flipkart.fintech.pandora.service.client.pl.request.pennyDrop;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class IdfcValidateUpiRequest {

    private String reqId;
    @JsonProperty("VirAdd")
    private String virAddr;

}
