package com.flipkart.fintech.pandora.service.client.pl.requestV2;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(value = PropertyNamingStrategy.class)
public class CheckApprovalStatusRequestV2 {
    private String reqId;

    public String getReqId() {
        return reqId;
    }

    public void setReqId(String reqId) {
        this.reqId = reqId;
    }

    @Override
    public String toString() {
        return "CheckApprovalStatusRequest{" +
                "reqId='" + reqId + '\'' +
                '}';
    }
}
