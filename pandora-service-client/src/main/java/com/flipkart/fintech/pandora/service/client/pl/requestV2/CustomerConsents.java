package com.flipkart.fintech.pandora.service.client.pl.requestV2;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> - 24/11/22
 */
@Data
@NoArgsConstructor
public class CustomerConsents {

    @JsonProperty("panVerifyConsent")
    private String panVerifyConsent;

    @JsonProperty("panVerifyConsentTimestamp")
    private String panVerifyConsentTimestamp;

    @JsonProperty("bureauPullConsent")
    private String bureauPullConsent;

    @JsonProperty("bureauPullConsentTimestamp")
    private String bureauPullConsentTimestamp;

    @JsonProperty("UWDataSharingConsent")
    private String uwDataSharingConsent;

    @JsonProperty("UWDataSharingConsentTimestamp")
    private String uwDataSharingConsentTimestamp;

    @JsonProperty("KYCInitConsent")
    private String kycInitConsent;

    @JsonProperty("KYCInitConsentTimestamp")
    private String kycInitConsentTimestamp;

    @JsonProperty("KYCAcceptConsent")
    private String kycAcceptConsent;

    @JsonProperty("KYCAcceptConsentTimestamp")
    private String kycAcceptConsentTimestamp;

    @JsonProperty("thirdPartyDataSharingConsent")
    private String thirdPartyDataSharingConsent;

    @JsonProperty("thirdPartyDataSharingConsentTimestamp")
    private String thirdPartyDataSharingConsentTimestamp;

    @JsonProperty("OTPConsent")
    private String otpConsent;

    @JsonProperty("OTPConsentTimestamp")
    private String otpConsentTimestamp;

    @JsonProperty("eMandateConsent")
    private String eMandateConsent;

    @JsonProperty("eMandateConsentTimestamp")
    private String eMandateConsentTimestamp;

    @JsonProperty("TNCConsent")
    private String tncConsent;

    @JsonProperty("TNCConsentTimestamp")
    private String tncConsentTimestamp;

    public CustomerConsents(String timestamp) {
        this.bureauPullConsent = "Y";
        this.eMandateConsent = "Y";
        this.kycAcceptConsent = "Y";
        this.kycInitConsent = "Y";
        this.otpConsent = "Y";
        this.panVerifyConsent  = "Y";
        this.tncConsent = "Y";
        this.thirdPartyDataSharingConsent = "Y";
        this.uwDataSharingConsent = "Y";
        this.bureauPullConsentTimestamp = timestamp;
        this.eMandateConsentTimestamp = timestamp;
        this.kycAcceptConsentTimestamp = timestamp;
        this.kycInitConsentTimestamp = timestamp;
        this.otpConsentTimestamp = timestamp;
        this.panVerifyConsentTimestamp  = timestamp;
        this.tncConsentTimestamp = timestamp;
        this.thirdPartyDataSharingConsentTimestamp = timestamp;
        this.uwDataSharingConsentTimestamp = timestamp;
    }

    public void setConsentFields(String timestamp) {
        this.bureauPullConsent = "Y";
        this.eMandateConsent = "Y";
        this.kycAcceptConsent = "Y";
        this.kycInitConsent = "Y";
        this.otpConsent = "Y";
        this.panVerifyConsent  = "Y";
        this.tncConsent = "Y";
        this.thirdPartyDataSharingConsent = "Y";
        this.uwDataSharingConsent = "Y";
        this.bureauPullConsentTimestamp = timestamp;
        this.eMandateConsentTimestamp = timestamp;
        this.kycAcceptConsentTimestamp = timestamp;
        this.kycInitConsentTimestamp = timestamp;
        this.otpConsentTimestamp = timestamp;
        this.panVerifyConsentTimestamp  = timestamp;
        this.tncConsentTimestamp = timestamp;
        this.thirdPartyDataSharingConsentTimestamp = timestamp;
        this.uwDataSharingConsentTimestamp = timestamp;
    }
}
