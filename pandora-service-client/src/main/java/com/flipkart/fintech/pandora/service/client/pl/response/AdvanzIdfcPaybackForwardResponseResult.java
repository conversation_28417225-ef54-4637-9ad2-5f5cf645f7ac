package com.flipkart.fintech.pandora.service.client.pl.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;


@JsonIgnoreProperties(ignoreUnknown = true)
public class AdvanzIdfcPaybackForwardResponseResult {

    @JsonProperty("VAR_MANDATE_CHECK")
    private String VAR_MANDATE_CHECK;

    @JsonProperty("VAR_RESPONSE_STATUS")
    private String VAR_RESPONSE_STATUS;

    @JsonProperty("reqId")
    private String reqId;

    public String getVAR_MANDATE_CHECK() {
        return VAR_MANDATE_CHECK;
    }

    public String getVAR_RESPONSE_STATUS() {
        return VAR_RESPONSE_STATUS;
    }


    public String getReqId() {
        return reqId;
    }

    public void setVAR_MANDATE_CHECK(String VAR_MANDATE_CHECK) {
        this.VAR_MANDATE_CHECK = VAR_MANDATE_CHECK;
    }

    public void setVAR_RESPONSE_STATUS(String VAR_RESPONSE_STATUS) {
        this.VAR_RESPONSE_STATUS = VAR_RESPONSE_STATUS;
    }

    public void setReqId(String reqId) {
        this.reqId = reqId;
    }

    @Override
    public String toString() {
        return "AdvanzIdfcPaybackForwardResponseResult{" +
                "VAR_MANDATE_CHECK='" + VAR_MANDATE_CHECK + '\'' +
                ", VAR_RESPONSE_STATUS=" + VAR_RESPONSE_STATUS + '\'' +
                ", reqId=" + reqId +
                '}';
    }
}
