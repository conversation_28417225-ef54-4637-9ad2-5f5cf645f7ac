package com.flipkart.fintech.pandora.service.client.pl.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class IdfcErrorMessageHeader {
    @JsonProperty("rslt")
    private String result;

    @JsonProperty("error")
    private List<IdfcEkycError> idfcEkycErrorList;

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public List<IdfcEkycError> getIdfcEkycErrorList() {
        return idfcEkycErrorList;
    }

    public void setIdfcEkycErrorList(List<IdfcEkycError> idfcEkycErrorList) {
        this.idfcEkycErrorList = idfcEkycErrorList;
    }

    @Override
    public String toString() {
        return "IdfcErrorMessageHeader{" +
                "result='" + result + '\'' +
                ", idfcErrorServiceResponseList=" + idfcEkycErrorList +
                '}';
    }
}
