package com.flipkart.fintech.pandora.service.client.pl.response;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pandora.service.client.pl.LenderConstants;

@JsonIgnoreProperties(ignoreUnknown = true)
public class IdfcFeeChargeResponse {

    @JsonProperty("TRANSACTION_ID")
    private String transactionId;

    @JsonProperty("STATUS")
    private String status;

    @JsonProperty("ERROR_MSG")
    private String errorMsg;

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public boolean isSuccess(){
        if (this.getTransactionId()==null || this.getTransactionId().equals("0") || this.getTransactionId().equals("") || this.getTransactionId().equalsIgnoreCase("null") || !this.getStatus().equals(
            LenderConstants.SUCCESS_STATUS_TL)){
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "IdfcFeeChargeResponse{" +
                "transactionId='" + transactionId + '\'' +
                ", status='" + status + '\'' +
                ", errorMsg='" + errorMsg + '\'' +
                '}';
    }
}
