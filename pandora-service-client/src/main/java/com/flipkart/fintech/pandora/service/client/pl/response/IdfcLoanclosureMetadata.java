package com.flipkart.fintech.pandora.service.client.pl.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class IdfcLoanclosureMetadata {
    @JsonProperty("status")
    private String status;

    @JsonProperty("message")
    private String message;

    @JsonProperty("version")
    private String version;

    @JsonProperty("service")
    private String service;

    @JsonProperty("time")
    private String time;

    @JsonProperty("code")
    private String code;
}
