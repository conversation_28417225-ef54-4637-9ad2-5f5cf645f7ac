package com.flipkart.fintech.pandora.service.client.pl.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PanVerificationResponse {
    @JsonProperty("ResponseId")
    private String responseId;

    // Enum field. But currently kept as String to avoid breaking changes. Will be changed to Enum once all the values are shared.
    @JsonProperty("Status")
    private String status;
    @JsonProperty("Response")
    private PanUserInfo panUserInfo;
}
