package com.flipkart.fintech.pandora.service.client.pl.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class PaybackForwardResponseResourceData {
    @JsonProperty("acknowlId")
    private String acknowledgementId;

    @JsonProperty("entityReqId")
    private String entityReqId;
}
