package com.flipkart.fintech.pandora.service.client.pl.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.sensitive.annotation.SensitiveField;
import lombok.Data;

@Data
public class UserInfo {
    private String name;

    private String dob;

    @JsonProperty("e")
    @SensitiveField(keyName = "kycKey")
    private String email;

    @JsonProperty("m")
    @SensitiveField(keyName = "kycKey")
    private String phone;

    private String gender;
}
