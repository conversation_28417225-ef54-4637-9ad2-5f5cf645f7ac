package com.flipkart.fintech.pandora.service.client.pl.response.mpockket;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

@JsonIgnoreProperties(ignoreUnknown = true)
@Getter
@Setter
public class MpockketGetStatusResponse {
    private Map<String,String> data;
    private String message;
    @JsonProperty("status_code")
    private String statusCode;
    private String success;
    private MpockketLeadStatus leadStatus;
}
