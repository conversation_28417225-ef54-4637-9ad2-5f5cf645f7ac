package com.flipkart.fintech.pandora.service.client.plOnboarding;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.exception.ServiceErrorResponse;
import com.flipkart.fintech.exception.ServiceException;
import com.flipkart.fintech.pandora.service.client.plOnboarding.request.EncryptionRequest;
import com.flipkart.fintech.pandora.service.client.plOnboarding.response.LenderRes;
import com.flipkart.fintech.pandora.service.client.utils.PlEncryptionUtil;
import com.google.inject.Inject;
import com.google.inject.name.Named;
import org.apache.http.client.utils.URIBuilder;
import org.glassfish.jersey.client.ClientProperties;
import org.slf4j.LoggerFactory;
import java.net.URI;
import java.net.URLDecoder;
import java.util.Objects;
import javax.ws.rs.client.Client;
import javax.ws.rs.client.Entity;
import javax.ws.rs.client.Invocation;
import javax.ws.rs.client.WebTarget;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

public class GenericLenderClient {
    private static final org.slf4j.Logger logger = LoggerFactory.getLogger(GenericLenderClient.class);
    private static final ObjectMapper MAPPER = new ObjectMapper();
    private static final String UTF_8 = "UTF-8";
    private final Client client;

    @Inject
    public GenericLenderClient(@Named(PlClientConstants.PL_LENDER_CLIENT) Client client) {
        this.client = client;
    }

    public LenderRes fetchPostResponse(PlHttpRequest plHttpRequest) {
        Response response = null;
        try {

            URIBuilder uriBuilder = new URIBuilder(plHttpRequest.getUrl());
            if (Objects.nonNull(plHttpRequest.getParams())) {
                plHttpRequest.getParams().forEach(uriBuilder::setParameter);
            }
            URI uri = uriBuilder.build();
            WebTarget webTarget = client.target(URLDecoder.decode(uri.toString(), UTF_8));
            Invocation.Builder invocationBuilder = webTarget
                    .request(MediaType.APPLICATION_JSON)
                    .headers(plHttpRequest.getHeaders())
                    .property(ClientProperties.CONNECT_TIMEOUT, 40000)
                    .property(ClientProperties.READ_TIMEOUT, 40000);
            EncryptionRequest encryptionRequest = EncryptionRequest.builder()
                    .data(plHttpRequest.getLenderRequest())
                    .build();
            String payloadToEncrypt = MAPPER.writeValueAsString(encryptionRequest);
            logger.info("request object : {} ", payloadToEncrypt);
            logger.info("Lender request headers " + plHttpRequest.getLenderRequest().getClass() + ": {}", new ObjectMapper().writeValueAsString(plHttpRequest.getHeaders()));

            String encryptedPayload = PlEncryptionUtil.jweEncryptAndSign(plHttpRequest.getPublicKeyToEncrypt(),
                    plHttpRequest.getPrivateKeyToSign(), payloadToEncrypt);
            logger.info("PersonalLoans_API {} Invoked for Request Identifier {} EncryptedRequest: {}", plHttpRequest.getUrl(), plHttpRequest.getRequestIdentifier(),
                    encryptedPayload);
            response = invocationBuilder.post(Entity.entity(encryptedPayload, MediaType.TEXT_PLAIN));
            String encryptedResponse = response.readEntity(String.class);
            String decryptedResponse = PlEncryptionUtil.jweVerifyAndDecrypt(plHttpRequest.getPublicKeyToVerify(), plHttpRequest.getPrivateKeyToDecrypt(), encryptedResponse);

            logger.info("Lender response headers " + plHttpRequest.getLenderRequest().getClass() + ": {}", new ObjectMapper().writeValueAsString(response.getHeaders()));

            return LenderRes.builder()
                    .responseStatus(response.getStatus())
                    .context(decryptedResponse)
                    .build();

        } catch (Exception e) {
            logger.error("PersonalLoans_API error occurred for Request Identifier {}: {}", plHttpRequest.getRequestIdentifier(), e.getMessage());
            logger.error("PersonalLoans_API error stack trace: {}", e);

            throw new ServiceException(new ServiceErrorResponse(
                    Response.Status.INTERNAL_SERVER_ERROR, Response.Status.INTERNAL_SERVER_ERROR.getReasonPhrase(), e.getMessage()), e.getMessage());
        } finally {
            if (Objects.nonNull(response)) {
                response.close();
            }
        }
    }
}
