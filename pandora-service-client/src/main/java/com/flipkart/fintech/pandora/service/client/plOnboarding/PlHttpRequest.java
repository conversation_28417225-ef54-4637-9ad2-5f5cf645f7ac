package com.flipkart.fintech.pandora.service.client.plOnboarding;

import com.flipkart.fintech.pandora.service.client.plOnboarding.request.LenderRequest;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.ws.rs.core.MultivaluedMap;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.util.Map;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PlHttpRequest {
    private String url;

    private MultivaluedMap<String, Object> headers;

    private Map<String, String> params;

    private LenderRequest lenderRequest;

    private int connectTimeoutMs;

    private int readTimeoutMs;

    private RSAPublicKey publicKeyToEncrypt;

    private RSAPrivateKey privateKeyToSign;

    private RSAPublicKey publicKeyToVerify;

    private RSAPrivate<PERSON>ey privateKeyToDecrypt;

    private String requestIdentifier;
}
