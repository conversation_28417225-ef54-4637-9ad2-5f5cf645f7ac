package com.flipkart.fintech.pandora.service.client.plOnboarding.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.flipkart.fintech.pandora.api.model.request.plOnboarding.LoanPurpose;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
/**
 * Created by pritam.raj on 21/03/23.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class Consent {
    List<String> ipAddresses;
    String deviceId;
    Long currentTimeStamp;
    String deviceParams;
    String deviceInfo;
    private List<ConsentDocument> documentList;
}
