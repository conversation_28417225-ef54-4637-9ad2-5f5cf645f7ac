package com.flipkart.fintech.pandora.service.client.plOnboarding.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * Created by pritam.raj on 21/03/23.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public enum ConsentType {
    CONSENT_ACCEPT_OFFER,
    CONSENT_BUREAU_PULL,
    CONSENT_SUBMIT_OFFER,
    CONSENT_DATASHARING_BUREAUPULL,
    CONSENT_CONFIRM_HOUSEHOLD_INCOME,
    CONSENT_RECONFIRM_KFS;
}
