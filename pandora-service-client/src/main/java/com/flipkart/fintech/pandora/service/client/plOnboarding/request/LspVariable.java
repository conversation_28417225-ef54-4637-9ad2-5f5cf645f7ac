package com.flipkart.fintech.pandora.service.client.plOnboarding.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.flipkart.fintech.pandora.api.model.request.plOnboarding.LoanPurpose;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by pritam.raj on 21/03/23.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class LspVariable {

    private String userScore;

    private LoanPurpose loanPurpose;

    private String freeField10;

    private String freeField11;

    private String freeField12;

    private String freeField13;

    private String freeField14;

    private String freeField15;

    private String freeField6;

    private String freeField7;

    private String freeField8;

    private String freeField9;

    private String freeField16;

    private String freeField17;
}
