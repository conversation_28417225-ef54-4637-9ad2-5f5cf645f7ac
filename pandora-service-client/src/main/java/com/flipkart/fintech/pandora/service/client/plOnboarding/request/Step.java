package com.flipkart.fintech.pandora.service.client.plOnboarding.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * Created by pritam.raj on 03/04/23.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public enum Step {
    FK_TO_AXIS_REDIRECTION,
    ENACH,
    ETB_VERIFICATION,
    ETB_OTP,
    ETB_DEMOG_VERIFICATION,
    EKYC,
    ADDITIONAL_DETAILS,
    VKYC,
    LOAN_DISBURSAL;
}
