package com.flipkart.fintech.pandora.service.client.plOnboarding.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * Created by pritam.raj on 03/04/23.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public enum SubStep {
    FK_TO_AXIS_REDIRECTION,
    REGISTRATION,
    NPCI_REDIRECTION_NB,
    NPCI_REDIRECTION_DC,
    ACCOUNT_VERIFICATION,
    DEMOG_CONFIRM,
    ENACH_SETUP,
    OTP_VERIFICATION,
    AADHAAR_VERIFICATION,
    ADDI<PERSON>ON<PERSON>_DETAILS_SUBMIT,
    TNC_CALLOUT,
    AGENT_AVAILABILITY_CHECK,
    DEVICE_PERMISSIONS,
    DETAILS_SUBMIT,
    VKYC_SUCCESSFUL,
    OVERALL_VKYC,
    CONCURRENT_AUDITOR,
    LOAN_DISBURSAL;
}
