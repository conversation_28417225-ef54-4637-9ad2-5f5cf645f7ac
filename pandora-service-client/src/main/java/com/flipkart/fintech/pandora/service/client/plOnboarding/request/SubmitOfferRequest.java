package com.flipkart.fintech.pandora.service.client.plOnboarding.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Created by pritam.raj on 21/03/23.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class  SubmitOfferRequest implements LenderRequest {

    private String lspApplicationId;

    private LoanParameter loanParameters;

    private long requestTime;

    private Consent consents;

    private String callbackUrl;

    private Disbursalparam disbursalParams;
}
