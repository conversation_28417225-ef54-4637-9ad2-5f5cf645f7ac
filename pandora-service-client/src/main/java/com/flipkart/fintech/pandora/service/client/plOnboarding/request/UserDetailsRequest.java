package com.flipkart.fintech.pandora.service.client.plOnboarding.request;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.flipkart.fintech.pandora.api.model.common.Gender;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Pattern;

/**
 * Created by pritam.raj on 21/03/23.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserDetailsRequest {
    private String mobileNumber;

    @Pattern(regexp = "[A-Z]{3}P[A-Z]{1}[0-9]{4}[A-Z]{1}", message = "Invalid PAN")
    private String pan;
    @Pattern(regexp = "YYYY-MM-DD")
    private String dob;

    private String emailId;

    private Gender gender;

    private CommunicationAddress communicationAddress;

    private String firstName;

    private String lastName;
}
