package com.flipkart.fintech.pandora.service.client.plOnboarding.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.flipkart.fintech.pandora.service.client.plOnboarding.response.Status;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by pritam.raj on 03/04/23.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class WebHookRequest implements LenderRequest{

    private String lspApplicationId;

    private String lenderApplicationId;

    private Status applicationStatus;

    private Stage stage;

    private long stateTransitionTime;
}
