package com.flipkart.fintech.pandora.service.client.plOnboarding.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * Created by pritam.raj on 21/03/23.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ContingentChargesDetails {
    private BigDecimal bounceCharge;

    private BigDecimal gst;

    private BigDecimal BounceChargeTax;

    private BigDecimal totalBounceCharge;

    private PenalCharge penalCharges;

    private PenalCharge foreClosureCharges;

    private String duplicateStatementIssuance;

    private String duplicateAmortizationSchedule;

    private String duplicateInterestCertificate;

    private String cibilIssuanceCharges;

    private String chargesAgreementPhotocopy;

    private String swapCharges;

    private String duplicateNoDuesCertificateGeneration;
    private String latePaymentInterestDescription;
}
