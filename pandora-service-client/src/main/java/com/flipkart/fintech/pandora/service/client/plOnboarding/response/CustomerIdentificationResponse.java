package com.flipkart.fintech.pandora.service.client.plOnboarding.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class CustomerIdentificationResponse implements LenderResponse {
    private CustomerSegment customerSegment;

    private String customerSubSegment;

    private Status status;

    private String subStatus;

    @JsonProperty("applicationId")
    private String lenderApplicationId;

    private long applicationValidTill;
}
