package com.flipkart.fintech.pandora.service.client.plOnboarding.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * Created by pritam.raj on 21/03/23.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public enum CustomerSubSegment {
    PQ,
    NPQ,
    ETB_NPQ_KYC_NREQ,
    ETB_NPQ_KYC_REQ,
    ETB_LENDING_MIN_KYC,
    ETB_LENDING_FULL_KYC,
    LENDING_ETB;
}
