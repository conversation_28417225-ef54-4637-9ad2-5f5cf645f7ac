package com.flipkart.fintech.pandora.service.client.plOnboarding.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.flipkart.fintech.pandora.api.model.response.plOnboarding.EmploymentVerification;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by pritam.raj on 21/03/23.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class LoanApplicationStatusResponse implements LenderResponse{

    private Status applicationStatus;

    private String applicationSubStatus;

    private long validity;

    private IdentificationData identificationData;

    private Cdd cdd;

    private Kyc kyc;

    private Enach enach;

    private StatusLoanOfferResponse offerData;

    private EmploymentVerification employmentVerification;

    private WebNavigator navigator;

    private ProcessApplicationData processApplicationData;

    private long applicationValidTill;
}
