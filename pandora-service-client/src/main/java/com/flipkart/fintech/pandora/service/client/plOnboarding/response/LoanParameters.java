package com.flipkart.fintech.pandora.service.client.plOnboarding.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
/**
 * Created by pritam.raj on 21/03/23.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class LoanParameters {
    private BigDecimal loanAmount;

    private  BigDecimal interestCharged;

    private OtherCharges otherCharges;

    private RepaymentDetails repaymentDetails;

    private ContingentChargesDetails contingentChargesDetails;

    private InterestDetails interestDetails;

    private OtherDisclosures otherDisclosures;
}
