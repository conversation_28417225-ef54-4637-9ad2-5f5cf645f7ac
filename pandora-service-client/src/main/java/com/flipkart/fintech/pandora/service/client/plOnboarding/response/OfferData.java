package com.flipkart.fintech.pandora.service.client.plOnboarding.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * Created by pritam.raj on 21/03/23.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class OfferData {
    private String loanType;

    private String subProductType;

    private BigDecimal maxSanctionedAmt;

    private BigDecimal minSanctionedAmt;

    private BigDecimal repayingCapacityPerMonth;

    private ProcessingFee pf;

    private BigDecimal roi;

    private BigDecimal stampDuty;

    private Tenure tenure;
}
