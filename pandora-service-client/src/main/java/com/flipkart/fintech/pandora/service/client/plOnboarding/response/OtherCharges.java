package com.flipkart.fintech.pandora.service.client.plOnboarding.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * Created by pritam.raj on 21/03/23.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class OtherCharges {
    private BigDecimal processingFeeCharge;

    private BigDecimal processingFeeTax;

    private BigDecimal totalProcessingFee;

    private BigDecimal insurance;

    private BigDecimal stampDutyCharge;

    private BigDecimal stampDutyTax;

    private BigDecimal totalStampDuty;

    private BigDecimal netDisbursedAmount;

    private BigDecimal totalAmount;

    private BigDecimal totalCharges;

    private BigDecimal eapr;
}
