package com.flipkart.fintech.pandora.service.client.plOnboarding.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * Created by pritam.raj on 21/03/23.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public enum PenaltyPercentageOf {
    SANCTIONED_AMOUNT,
    DISBURSEMENT_AMOUNT,
    OUTSTANDING_DEMAND_VALUE,
    OUTSTANDING_PRINCIPAL;
}
