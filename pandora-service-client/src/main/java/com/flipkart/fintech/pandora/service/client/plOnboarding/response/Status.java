package com.flipkart.fintech.pandora.service.client.plOnboarding.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * Created by pritam.raj on 21/03/23.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public enum Status {
    SUCCESS,
    INPROGRESS,
    EXPIRED,
    PENDING,
    INITIATED,
    FAILURE,
    FAILED;
}
