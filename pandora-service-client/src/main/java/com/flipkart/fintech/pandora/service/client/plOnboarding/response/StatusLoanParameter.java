package com.flipkart.fintech.pandora.service.client.plOnboarding.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.flipkart.fintech.pandora.service.client.plOnboarding.request.ChargeBreakup;
import com.flipkart.fintech.pandora.service.client.plOnboarding.request.Tenure;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class StatusLoanParameter {

    private BigDecimal loanAmount;

    private BigDecimal roi;

    private List<ChargeBreakup> charges;

    private BigDecimal netDisbursalAmount;

    private BigDecimal emi;

    private Tenure tenure;
}
