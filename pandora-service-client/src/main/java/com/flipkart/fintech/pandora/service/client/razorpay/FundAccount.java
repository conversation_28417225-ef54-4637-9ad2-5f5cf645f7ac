package com.flipkart.fintech.pandora.service.client.razorpay;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class FundAccount {

    @NotNull
    private String id;

    @JsonProperty("contact_id")
    private String contactId;

    @JsonProperty("vpa")
    private VPA vpa;

    private Boolean active;

    private String entity;

    @JsonProperty("account_type")
    private String accountType;

    @JsonProperty("batch_id")
    private String batchId;

    @JsonIgnoreProperties("created_at")
    private Long createdAt;

    @JsonProperty("contact")
    private Contact contact;

}
