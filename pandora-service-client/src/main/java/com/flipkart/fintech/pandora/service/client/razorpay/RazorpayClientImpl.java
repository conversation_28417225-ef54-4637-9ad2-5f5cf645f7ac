package com.flipkart.fintech.pandora.service.client.razorpay;

import com.flipkart.fintech.pandora.service.client.PandoraServiceClientException;
import com.flipkart.fintech.pandora.service.client.razorpay.request.RazorpayCreateContactRequest;
import com.flipkart.fintech.pandora.service.client.razorpay.request.RazorpayCreateFundAccountRequest;
import com.flipkart.fintech.pandora.service.client.razorpay.request.RazorpayPayoutRequest;
import com.flipkart.fintech.pandora.service.client.razorpay.request.RazorpayValidateVPARequest;
import com.flipkart.fintech.pandora.service.client.razorpay.response.RazorpayCreateContactResponse;
import com.flipkart.fintech.pandora.service.client.razorpay.response.RazorpayCreateFundAccountResponse;
import com.flipkart.fintech.pandora.service.client.razorpay.response.RazorpayPayoutResponse;
import com.flipkart.fintech.pandora.service.client.razorpay.response.RazorpayValidateVPAResponse;
import com.google.inject.Inject;
import com.google.inject.name.Named;
import org.apache.commons.codec.binary.Base64;
import org.slf4j.LoggerFactory;

import javax.ws.rs.client.Client;
import javax.ws.rs.client.Entity;
import javax.ws.rs.client.Invocation;
import javax.ws.rs.client.WebTarget;
import javax.ws.rs.core.HttpHeaders;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.MultivaluedHashMap;
import javax.ws.rs.core.Response;

public class RazorpayClientImpl implements  RazorpayClient{

    private final static org.slf4j.Logger  logger = LoggerFactory.getLogger(RazorpayClientImpl.class);
    private final WebTarget webTarget;
    private final RazorpayConfiguration razorpayConfiguration;
    private static final String IDEMPOTENCY_KEY = "X-Payout-Idempotency";
    public static final String CREATE_CONTACT_PATH = "/v1/contacts";
    public static final String CREATE_FUND_ACCOUNT_PATH = "/v1/fund_accounts";
    public static final String VALIDATE_VPA_PATH = "/v1/fund_accounts/validations";
    public static final String PAYOUT_PATH = "/v1/payouts";
    public static final String VALIDATION_STATUS_PATH = "/v1/fund_accounts/validations/%s";



    @Inject
    public RazorpayClientImpl(RazorpayConfiguration razorpayConfiguration, @Named("razorpay") Client client) {
        this.webTarget = client.target(razorpayConfiguration.getUrl());
        this.razorpayConfiguration = razorpayConfiguration;
    }

    @Override
    public RazorpayCreateContactResponse createContact(RazorpayCreateContactRequest razorpayCreateContactRequest) {
        logger.info("Razorpay create contact request: {}", razorpayCreateContactRequest);
        Response response = null;
        RazorpayCreateContactResponse razorpayCreateContactResponse = null;
        try {
            Invocation.Builder invocationBuilder = webTarget.path(CREATE_CONTACT_PATH).request(MediaType.APPLICATION_JSON);
            invocationBuilder.headers(getHeaders());
            response = invocationBuilder.post(Entity.json(razorpayCreateContactRequest));
            if (response.getStatus() == 200 || response.getStatus() == 201) {
                razorpayCreateContactResponse = response.readEntity(RazorpayCreateContactResponse.class);
                logger.info("Razorpay create contact response : {}", razorpayCreateContactResponse);
            } else {
                String errorString = response.readEntity(String.class);
                logger.error("Error response from razorpay during create contact for request: {} response: {}",razorpayCreateContactRequest, errorString);
                throw new PandoraServiceClientException("Non 200 response from razorpay for create contact " + errorString);
            }

        }catch (Exception e){
            logger.error("Exception in getting response from razorpay for create contact for request : {} Exception {}: ", razorpayCreateContactRequest.toString(), e);
            throw new PandoraServiceClientException(e.getMessage());
        } finally {
            if (response != null) {
                response.close();
            }
        }

        return razorpayCreateContactResponse;
    }

    @Override
    public RazorpayCreateFundAccountResponse createFundAccount(RazorpayCreateFundAccountRequest razorpayCreateFundAccountRequest) {
        logger.info("Razorpay create fund account request: {}", razorpayCreateFundAccountRequest);
        Response response = null;
        RazorpayCreateFundAccountResponse razorpayCreateFundAccountResponse = null;
        try {
            Invocation.Builder invocationBuilder = webTarget.path(CREATE_FUND_ACCOUNT_PATH).request(MediaType.APPLICATION_JSON);
            invocationBuilder.headers(getHeaders());
            response = invocationBuilder.post(Entity.json(razorpayCreateFundAccountRequest));
            if (response.getStatus() == 200 || response.getStatus() == 201) {
                razorpayCreateFundAccountResponse = response.readEntity(RazorpayCreateFundAccountResponse.class);
                logger.info("Razorpay create fund account response : {}", razorpayCreateFundAccountResponse);
            } else {
                String errorString = response.readEntity(String.class);
                logger.error("Error response from razorpay during create fund account for request: {} response: {}",razorpayCreateFundAccountRequest, errorString);
                throw new PandoraServiceClientException("Non 200 response from razorpay for create fund account " + errorString);
            }

        }catch (Exception e){
            logger.error("Exception in getting response from razorpay for create fund account for request : {} Exception {}: ", razorpayCreateFundAccountRequest.toString(), e);
            throw new PandoraServiceClientException(e.getMessage());
        } finally {
            if (response != null) {
                response.close();
            }
        }

        return razorpayCreateFundAccountResponse;
    }

    @Override
    public RazorpayValidateVPAResponse validateVPA(RazorpayValidateVPARequest razorpayValidateVPARequest) {
        logger.info("Razorpay validate VPA request: {}", razorpayValidateVPARequest);
        razorpayValidateVPARequest.setAccountNumber(razorpayConfiguration.getAccountNumber());
        Response response = null;
        RazorpayValidateVPAResponse razorpayValidateVPAResponse = null;
        try {
            Invocation.Builder invocationBuilder = webTarget.path(VALIDATE_VPA_PATH).request(MediaType.APPLICATION_JSON);
            invocationBuilder.headers(getHeaders());
            response = invocationBuilder.post(Entity.json(razorpayValidateVPARequest));
            if (response.getStatus() == 200 || response.getStatus() == 201) {
                razorpayValidateVPAResponse = response.readEntity(RazorpayValidateVPAResponse.class);
                logger.info("Razorpay validate VPA response : {}", razorpayValidateVPAResponse);
            } else {
                String errorString = response.readEntity(String.class);
                logger.error("Error response from razorpay during validate VPA for request: {} response: {}",razorpayValidateVPARequest, errorString);
                throw new PandoraServiceClientException("Non 200 response from razorpay for validate VPA " + errorString);
            }

        }catch (Exception e){
            logger.error("Exception in getting response from razorpay for validate VPA for request : {} Exception {}: ", razorpayValidateVPARequest, e);
            throw new PandoraServiceClientException(e.getMessage());
        } finally {
            if (response != null) {
                response.close();
            }
        }

        return razorpayValidateVPAResponse;
    }

    @Override
    public RazorpayPayoutResponse processPayout(RazorpayPayoutRequest razorpayPayoutRequest) {
        logger.info("Razorpay payout request: {}", razorpayPayoutRequest);
        razorpayPayoutRequest.setAccountNumber(razorpayConfiguration.getAccountNumber());
        Response response = null;
        RazorpayPayoutResponse razorpayPayoutResponse = null;
        try {
            Invocation.Builder invocationBuilder = webTarget.path(PAYOUT_PATH).request(MediaType.APPLICATION_JSON);
            invocationBuilder.headers(getHeaders()).header(IDEMPOTENCY_KEY, razorpayPayoutRequest.getReferenceId().substring(4));
            response = invocationBuilder.post(Entity.json(razorpayPayoutRequest));
            if (response.getStatus() == 200 || response.getStatus() == 201) {
                razorpayPayoutResponse = response.readEntity(RazorpayPayoutResponse.class);
                logger.info("Razorpay payout response : {}", razorpayPayoutResponse);
            } else {
                String errorString = response.readEntity(String.class);
                logger.error("Error response from razorpay during payout for request: {} response: {}",razorpayPayoutRequest, errorString);
                throw new PandoraServiceClientException("Non 200 response from razorpay for payout " + errorString);
            }

        }catch (Exception e){
            logger.error("Exception in getting response from razorpay for payout for request : {} Exception {}: ", razorpayPayoutRequest.toString(), e);
            throw new PandoraServiceClientException(e.getMessage());
        } finally {
            if (response != null) {
                response.close();
            }
        }

        return razorpayPayoutResponse;

    }

    private String getAuthorizationKey(String username, String password) {
        String hash         = username + ":" + password;
        byte[] encodedBytes = Base64.encodeBase64(hash.getBytes());
        return new String(encodedBytes);
    }

    private MultivaluedHashMap<String, Object> getHeaders() {
        MultivaluedHashMap<String, Object> map = new MultivaluedHashMap<>();
        map.add(HttpHeaders.CONTENT_TYPE,MediaType.APPLICATION_JSON);
        map.add(HttpHeaders.AUTHORIZATION, "Basic " + getAuthorizationKey(razorpayConfiguration.getUsername(),razorpayConfiguration.getPassword()));
        return map;
    }

    @Override
    public RazorpayValidateVPAResponse getValidationStatus(String txnId){
        logger.info("Razorpay get validation status VPA request: {}", txnId);
        String path = String.format(VALIDATION_STATUS_PATH, txnId) ;
        Response response = null;
        RazorpayValidateVPAResponse razorpayValidateVPAResponse = null;
        try {
            Invocation.Builder invocationBuilder = webTarget.path(path).request(MediaType.APPLICATION_JSON);
            invocationBuilder.headers(getHeaders());
            response = invocationBuilder.get();
            if (response.getStatus() == 200 || response.getStatus() == 201) {
                razorpayValidateVPAResponse = response.readEntity(RazorpayValidateVPAResponse.class);
                logger.info("Razorpay validate VPA response : {}", razorpayValidateVPAResponse);
            } else {
                String errorString = response.readEntity(String.class);
                logger.error("Error response from razorpay during validate VPA for request: {} response: {}",txnId, errorString);
                throw new PandoraServiceClientException("Non 200 response from razorpay for validate VPA " + errorString);
            }

        }catch (Exception e){
            logger.error("Exception in getting response from razorpay for validate VPA for request : {} Exception {}: ", txnId, e);
            throw new PandoraServiceClientException(e.getMessage());
        } finally {
            if (response != null) {
                response.close();
            }
        }

        return razorpayValidateVPAResponse;
    }
}
