package com.flipkart.fintech.pandora.service.client.razorpay.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pandora.service.client.razorpay.FundAccount;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class RazorpayValidateVPARequest {

    @NotNull
    @JsonProperty("account_number")
    private String accountNumber;

    @NotNull
    @JsonProperty("fund_account")
    private FundAccount fundAccount;

}
