package com.flipkart.fintech.pandora.service.client.razorpay.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pandora.service.client.razorpay.VPA;
import lombok.Data;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class RazorpayCreateFundAccountResponse {

    private String id;

    @JsonProperty("contact_id")
    private String contactId;

    @JsonProperty("vpa")
    private VPA vpa;

    private boolean active;

    private String entity;

    @JsonProperty("account_type")
    private String accountType;

    @JsonProperty("batch_id")
    private String batchId;

    @JsonIgnoreProperties("created_at")
    private Long createdAt;

}
