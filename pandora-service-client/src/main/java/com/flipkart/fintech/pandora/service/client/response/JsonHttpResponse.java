package com.flipkart.fintech.pandora.service.client.response;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.http.HttpStatus;

import java.io.IOException;

/**
 * <AUTHOR>
 * @since 29/09/17.
 */
public class JsonHttpResponse {
    private static final ObjectMapper JSON_OBJECT_MAPPER = new ObjectMapper();

    private int statusCode;
    private byte[] rawResponseBody;

    public JsonHttpResponse(int statusCode) {
        this.statusCode = statusCode;
    }

    public int getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(int statusCode) {
        this.statusCode = statusCode;
    }

    public <T> T getEntity(TypeReference<T> typeReference) throws IOException {
        return JSON_OBJECT_MAPPER.readValue(rawResponseBody, typeReference);
    }

    public byte[] getRawResponseBody() {
        return rawResponseBody;
    }

    public void setRawResponseBody(byte[] rawResponseBody) throws IOException {
        this.rawResponseBody = rawResponseBody;
    }

    public boolean isSuccess() {
        return statusCode == HttpStatus.SC_OK;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this)
                .append("statusCode", statusCode)
                .append("rawResponseBody", rawResponseBody == null ? "" : new String(rawResponseBody))
                .toString();
    }
}