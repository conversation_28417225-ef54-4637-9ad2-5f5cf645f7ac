package com.flipkart.fintech.pandora.service.client.sandbox;

import com.flipkart.fintech.pandora.service.client.sandbox.v1.utils.ApiModel;
import com.flipkart.fintech.pandora.service.client.sandbox.v1.utils.PlActions;
import com.flipkart.fintech.pandora.service.client.auth.CommonLendersAuthenticationClient;
import com.flipkart.fintech.pandora.service.client.auth.Scope;
import com.flipkart.fintech.pandora.service.client.configuration.SandboxLendersPlConfiguration;
import com.flipkart.kloud.config.DynamicBucket;
import com.google.inject.Inject;
import com.google.inject.name.Named;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.flipkart.fintech.pandora.service.client.sandbox.v1.utils.SmeConstants.clientIdLenderScopeMap;

/**
 * <AUTHOR>
 * @date 27/11/23
 */
public class ApiParamModel {

    private final Map<String, SandboxLendersPlConfiguration> sandboxLendersPlConfigurationMap;
    private final DynamicBucket dynamicBucket;

    @Inject
    public ApiParamModel(@Named("SmallLendersPlConfigurationList") List<SandboxLendersPlConfiguration> sandboxLendersPlConfigurationList, DynamicBucket dynamicBucket) {
        this.sandboxLendersPlConfigurationMap = sandboxLendersPlConfigurationList.stream().collect(Collectors.toMap(SandboxLendersPlConfiguration::getScope, Function.identity()));
        this.dynamicBucket = dynamicBucket;
    }

    public SandboxLendersPlConfiguration getPlConfigurationForLender(Scope scope){
        return sandboxLendersPlConfigurationMap.get(scope.toString());
    }

    public Scope getScopeFromClientId(String clientId){
        return clientIdLenderScopeMap.get(clientId);
    }


    public String getHostForLender(Scope scope){
        SandboxLendersPlConfiguration config = this.getPlConfigurationForLender(scope);
        if(Objects.nonNull(config)){
            return config.getHost();
        } else {
            throw new NullPointerException("Could not find config for this lender.");
        }
    }

    public ApiModel getApiModelForLenderAndAction(Scope scope, PlActions action){
        SandboxLendersPlConfiguration config = this.getPlConfigurationForLender(scope);
        if(Objects.nonNull(config)){
            return config.getApiModel().get(action);
        } else {
            throw new NullPointerException("Could not find config for this lender.");
        }
    }


    public CommonLendersAuthenticationClient.OAuthTokenConfiguration getOAuthTokenConfiguration(Scope scope){
        SandboxLendersPlConfiguration config = getPlConfigurationForLender(scope);
        return CommonLendersAuthenticationClient.OAuthTokenConfiguration.builder()
                .username(config.getUsername())
                .password(config.getPassword())
                .authCode(config.getAuthCode())
                .clientId(config.getClientId())
                .tokenEndPoint(config.getTokenEndpoint())
                .host(config.getHost())
                .authCode(config.getAuthCode())
                .build();
    }

}
