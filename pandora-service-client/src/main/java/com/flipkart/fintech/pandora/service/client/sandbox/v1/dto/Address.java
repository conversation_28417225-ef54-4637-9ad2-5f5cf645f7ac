package com.flipkart.fintech.pandora.service.client.sandbox.v1.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 29/11/23
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Address {

    @JsonProperty(required = true)
    @NotNull
    private String pincode;

    private String addressLine1;

    private String addressLine2;
}