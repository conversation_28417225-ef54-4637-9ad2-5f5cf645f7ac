package com.flipkart.fintech.pandora.service.client.sandbox.v1.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.flipkart.fintech.pandora.service.client.sandbox.v1.dto.enums.TenureUnit;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 29/11/23
 */
@Data
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class Tenure {
    private int max;
    private int min;
    private TenureUnit unit;
}
