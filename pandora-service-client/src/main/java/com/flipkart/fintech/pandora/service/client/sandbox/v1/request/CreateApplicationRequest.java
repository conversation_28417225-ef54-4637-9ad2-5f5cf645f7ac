package com.flipkart.fintech.pandora.service.client.sandbox.v1.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.flipkart.fintech.pandora.service.client.sandbox.v1.dto.Consents;
import com.flipkart.fintech.pandora.service.client.sandbox.v1.dto.PreApprovedOfferDetails;
import com.flipkart.fintech.pandora.service.client.sandbox.v1.dto.UserInfo;
import com.flipkart.fintech.pandora.service.client.sandbox.v1.dto.enums.ApplicationType;
import com.flipkart.fintech.pandora.service.client.sandbox.v1.dto.enums.ProductType;
import com.flipkart.fintech.pandora.service.client.plOnboarding.request.LenderRequest;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 28/11/23
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CreateApplicationRequest implements LenderRequest {

    @NotNull
    private String lspApplicationId;

    @NotNull
    private String lspId;

    @NotNull
    private String lspRedirectUrl;

    @NotNull
    private ProductType productType;

    @NotNull
    private ApplicationType applicationType;

    @NotNull
    private List<Consents> consents;

    @NotNull
    private UserInfo userInfo;

    @NotNull
    private PreApprovedOfferDetails preApprovedOfferDetails;
}
