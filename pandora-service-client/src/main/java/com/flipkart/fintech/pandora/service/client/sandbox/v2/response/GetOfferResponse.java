package com.flipkart.fintech.pandora.service.client.sandbox.v2.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.flipkart.fintech.pandora.api.model.pl.sandbox.dto.ApplicationStatus;
import com.flipkart.fintech.pandora.api.model.pl.sandbox.dto.JourneyState;
import com.flipkart.fintech.pandora.api.model.pl.sandbox.dto.GeneratedOffer;
import lombok.*;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class GetOfferResponse {

    private String lenderApplicationId;
    private ApplicationStatus applicationStatus;
    private JourneyState journeyState;
    private GeneratedOffer generatedOffer;
}
