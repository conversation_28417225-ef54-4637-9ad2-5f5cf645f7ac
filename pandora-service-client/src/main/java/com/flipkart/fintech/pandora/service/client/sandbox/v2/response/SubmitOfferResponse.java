package com.flipkart.fintech.pandora.service.client.sandbox.v2.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.flipkart.fintech.pandora.api.model.pl.sandbox.dto.ApplicationStatus;
import com.flipkart.fintech.pandora.api.model.pl.sandbox.dto.JourneyState;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;


@Getter
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class SubmitOfferResponse {
    private String lenderApplicationId;
    private ApplicationStatus applicationStatus;
    private JourneyState journeyState;
}
