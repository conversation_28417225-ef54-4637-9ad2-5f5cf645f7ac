package com.flipkart.fintech.pandora.service.client.tokens;

import com.google.inject.Inject;
import com.google.inject.Singleton;

@Singleton
public class TokenCache {

  private final TokenProvider tokenProvider;
  private String bearerToken;
  private long expiryTime;

  @Inject
  public TokenCache(TokenProvider tokenProvider) {
    this.bearerToken = null;
    this.expiryTime = 0;
    this.tokenProvider = tokenProvider;
  }

  public synchronized String getToken() {
    if (bearerToken == null || System.currentTimeMillis() > expiryTime) {
      bearerToken = null;  // Invalidate the old token
      bearerToken = tokenProvider.fetchToken();  // Fetch a new token
      expiryTime = System.currentTimeMillis() + 30 * 60 * 1000 - 1000; // Reduce by a second to account for any potential drift
    }
    return bearerToken;
  }
}

