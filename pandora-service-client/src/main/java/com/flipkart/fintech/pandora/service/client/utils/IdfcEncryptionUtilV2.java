package com.flipkart.fintech.pandora.service.client.utils;

import com.flipkart.fintech.pandora.service.client.pl.IdfcConfiguration;
import lombok.extern.slf4j.Slf4j;
import javax.crypto.Cipher;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import javax.inject.Inject;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.Key;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;

@Slf4j
public class IdfcEncryptionUtilV2 {

    private final IdfcConfiguration idfcConfiguration;

    private final IvParameterSpec ivParameterSpec;

    private final Key aesKey;

    private final Cipher encryptionCipher;

    private final Cipher decryptionCipher;


    @Inject
    public IdfcEncryptionUtilV2(IdfcConfiguration idfcConfiguration) throws UnsupportedEncodingException, NoSuchPaddingException, NoSuchAlgorithmException, InvalidAlgorithmParameterException, InvalidKeyException {
        this.idfcConfiguration = idfcConfiguration;
        this.ivParameterSpec = new IvParameterSpec(new String(Base64.getDecoder().decode(idfcConfiguration.getInitializationVector())).getBytes(StandardCharsets.UTF_8));
        this.aesKey = new SecretKeySpec(idfcConfiguration.getIdfcEncryptionKeyV2().getBytes("UTF-8"), "AES");
        this.encryptionCipher = Cipher.getInstance("AES/CBC/PKCS5PADDING");
        this.encryptionCipher.init(Cipher.ENCRYPT_MODE, this.aesKey, this.ivParameterSpec);
        this.decryptionCipher = Cipher.getInstance("AES/CBC/PKCS5PADDING");
        this.decryptionCipher.init(Cipher.DECRYPT_MODE, this.aesKey, this.ivParameterSpec);
    }

    public String encryptUsingAes(String input) {
        String output = null;
        try {
            byte[] encrypted = encryptionCipher.doFinal(input.getBytes());
            output = Base64.getEncoder().encodeToString(encrypted);
        } catch (Exception e) {
            log.error("Error while encrypting");
        }
        return output;
    }

    public String decryptUsingAes(String input) {
        String output = null;
        try {
            byte[] decrypted = decryptionCipher.doFinal(Base64.getDecoder().decode(input));
            output = new String(decrypted);
        } catch (Exception e) {
            log.error("Error while decrypting");
        }
        return output;
    }

}