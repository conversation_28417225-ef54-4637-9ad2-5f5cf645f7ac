package com.flipkart.fintech.pandora;

import static org.junit.Assert.assertEquals;

import com.flipkart.fintech.pandora.service.client.digio.config.DigioConfiguration;
import com.flipkart.fintech.pandora.service.client.encryptor.AesEncryptorDecryptor;
import org.junit.Before;
import org.junit.Test;

public class AesEncryptorDecryptorTest {

    private static final String SECRET_KEY = "f7a4b3c92e8d4b7fa2391bc5d8e6a172";
    private AesEncryptorDecryptor aesEncryptorDecryptor;

    @Before
    public void setUp() {
        DigioConfiguration digioConfiguration = new DigioConfiguration();
        digioConfiguration.setEncryptionKey(SECRET_KEY);
        aesEncryptorDecryptor = new AesEncryptorDecryptor(digioConfiguration);
    }

    @Test
    public void testEncryptAndDecrypt() {
        String originalText = "HelloWorld@1234";
        String encryptedText = aesEncryptorDecryptor.encrypt(originalText);

        String decryptedText = aesEncryptorDecryptor.decrypt(encryptedText);

        assertEquals(originalText, decryptedText);
    }
}