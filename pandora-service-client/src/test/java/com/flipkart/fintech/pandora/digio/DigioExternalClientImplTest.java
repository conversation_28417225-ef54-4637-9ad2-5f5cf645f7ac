package com.flipkart.fintech.pandora.digio;

import com.flipkart.fintech.pandora.api.model.request.nach.kyc.pd.PennyDropKycRequest;
import com.flipkart.fintech.pandora.api.model.request.nach.kyc.rpd.ReversePennyDropKycRequest;
import com.flipkart.fintech.pandora.api.model.request.nach.mandate.CreateMandateRequest;
import com.flipkart.fintech.pandora.service.client.digio.DigioExternalClientImpl;
import com.flipkart.fintech.pandora.service.client.digio.NachEncryptionAndDecryption;
import com.flipkart.fintech.pandora.service.client.digio.config.DigioConfiguration;
import com.flipkart.fintech.pandora.service.client.digio.responses.common.AccessToken;
import com.flipkart.fintech.pandora.service.client.digio.responses.kyc.pd.PennyDropKycResponse;
import com.flipkart.fintech.pandora.service.client.digio.responses.kyc.rpd.KycRequestStatusResponse;
import com.flipkart.fintech.pandora.service.client.digio.responses.kyc.rpd.RPDKycResponse;
import com.flipkart.fintech.pandora.service.client.digio.responses.kyc.rpd.dto.KycRequestStatusResponseDTO;
import com.flipkart.fintech.pandora.service.client.digio.responses.kyc.rpd.dto.RPDKycResponseDTO;
import com.flipkart.fintech.pandora.service.client.digio.responses.mandate.MandateResponse;
import com.flipkart.fintech.pandora.service.client.digio.responses.mandate.dto.MandateResponseDTO;
import com.flipkart.fintech.pandora.service.client.digio.transformers.request.KycRequestTransformer;
import com.flipkart.fintech.pandora.service.client.digio.transformers.request.MandateRequestTransformer;
import com.flipkart.fintech.pandora.service.client.digio.transformers.response.*;
import org.junit.Ignore;
import org.junit.Test;
import org.mockito.Mock;
import javax.ws.rs.core.Response;

import org.mockito.MockitoAnnotations;
import org.modelmapper.ModelMapper;
import org.junit.Before;

import javax.ws.rs.client.*;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.testng.AssertJUnit.*;

public class DigioExternalClientImplTest {

    @Mock
    private NachEncryptionAndDecryption nachEncryptionAndDecryption;



    @Mock
    private WebTarget webTarget;

    private DigioExternalClientImpl digioExternalClient;
    @Mock
    private Invocation.Builder invocationBuilder;

    @Mock
    private Response response;



    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        ModelMapper mapper = new ModelMapper();
        DigioConfiguration digioConfiguration = new DigioConfiguration();
        KycRequestTransformer kycRequestTransformer = new KycRequestTransformer(mapper);
        RPDKycResponseTransformer rpdKycResponseTransformer = new RPDKycResponseTransformer(mapper);
        MandateRequestTransformer mandateRequestTransformer = new MandateRequestTransformer(mapper, digioConfiguration);
        MandateResponseTransformer mandateResponseTransformer = new MandateResponseTransformer(mapper);
        MandateResponseToDtoTransformer mandateResponseToDtoTransformer = new MandateResponseToDtoTransformer(mapper);
        RPDStatusTransformer RPDStatusTransformer = new RPDStatusTransformer(mapper);
        CancelRegisteredMandateResponseTransformer cancelRegisteredMandateResponseTransformer = new CancelRegisteredMandateResponseTransformer(mapper);
        digioExternalClient  = new DigioExternalClientImpl(webTarget, digioConfiguration,
                mandateRequestTransformer, mandateResponseTransformer, kycRequestTransformer,
                mandateResponseToDtoTransformer, RPDStatusTransformer, rpdKycResponseTransformer, cancelRegisteredMandateResponseTransformer, nachEncryptionAndDecryption);
    }

    @Test
    public void testInitiateReversePennyDrop_success() {
        ReversePennyDropKycRequest request = new ReversePennyDropKycRequest();
        request.setCustomerIdentifier("CID123");
        request.setFirstName("John");
        request.setLastName("Doe");
        request.setReferenceId("REF456");
        request.setTransactionId("TXN789");


        RPDKycResponse rpdKycResponse = getMockRPDKycResponse();

        when(nachEncryptionAndDecryption.decryptReversePennyDropKycRequest(request))
                .thenReturn(request);

        when(webTarget.path(anyString())).thenReturn(webTarget);
        when(webTarget.request()).thenReturn(invocationBuilder);
        when(invocationBuilder.post(any(Entity.class))).thenReturn(response);

        when(response.getStatus()).thenReturn(Response.Status.OK.getStatusCode());
        when(response.readEntity(RPDKycResponse.class)).thenReturn(rpdKycResponse);

        RPDKycResponseDTO result = digioExternalClient.initiateReversePennyDrop(request);
        assertEquals("rpd_abc123", result.getId());
        assertEquals("token_456", result.getGwtToken());
    }

    @Test
    public void testGetKycRequestStatus_success() {
        String requestId = "req_123";

        KycRequestStatusResponse kycStatusResponse = new KycRequestStatusResponse();
        kycStatusResponse.setId("req_123");
        kycStatusResponse.setCustomerIdentifier("CID123");
        kycStatusResponse.setCustomerName("John Doe");
        kycStatusResponse.setStatus("completed");
        kycStatusResponse.setAutoApproved(false);
        kycStatusResponse.setReminderRegistered(true);


        when(webTarget.path(anyString())).thenReturn(webTarget);
        when(webTarget.request()).thenReturn(invocationBuilder);
        when(invocationBuilder.post(isNull())).thenReturn(response);

        when(response.getStatus()).thenReturn(Response.Status.OK.getStatusCode());
        when(response.readEntity(KycRequestStatusResponse.class)).thenReturn(kycStatusResponse);
        when(nachEncryptionAndDecryption.encryptKycRequestStatusResponseDTO(any()))
                .thenAnswer(invocation -> invocation.getArgument(0));

        KycRequestStatusResponseDTO result = digioExternalClient.getKycRequestStatus(requestId);

        assertEquals("req_123", result.getId());
        assertEquals("CID123", result.getCustomerIdentifier());
        assertEquals("John Doe", result.getCustomerName());
        assertEquals("completed", result.getStatus());
        assertFalse(result.getAutoApproved());
        assertTrue(result.getReminderRegistered());
    }

    @Test
    public void testInitiatePennyDrop_success() {
        PennyDropKycRequest request = new PennyDropKycRequest();
        request.setBeneficiaryAccountNo("**********");
        request.setBeneficiaryIfsc("HDFC0001234");
        request.setBeneficiaryName("John Doe");
        request.setUniqueRequestId("REQ123");

        PennyDropKycResponse expectedResponse = new PennyDropKycResponse();
        expectedResponse.setId("penny_abc123");
        expectedResponse.setVerified(true);
        expectedResponse.setBeneficiaryNameWithBank("JOHN DOE");
        expectedResponse.setFuzzyMatchResult(true);
        expectedResponse.setFuzzyMatchScore(0.92);

        when(nachEncryptionAndDecryption.decryptPennyDropKycRequest(request))
                .thenReturn(request);

        when(webTarget.path(anyString())).thenReturn(webTarget);
        when(webTarget.request()).thenReturn(invocationBuilder);
        when(invocationBuilder.post(any(Entity.class))).thenReturn(response);

        when(response.getStatus()).thenReturn(Response.Status.OK.getStatusCode());
        when(response.readEntity(PennyDropKycResponse.class)).thenReturn(expectedResponse);

        PennyDropKycResponse result = digioExternalClient.initiatePennyDrop(request);

        assertNotNull(result);
        assertEquals("penny_abc123", result.getId());
        assertTrue(result.isVerified());
        assertEquals("JOHN DOE", result.getBeneficiaryNameWithBank());
        assertTrue(result.isFuzzyMatchResult());
        assertEquals(0.92, result.getFuzzyMatchScore(), 0.001);
    }

    @Test
    public void testInitiateMandate_apiMode_success() {
        CreateMandateRequest request = new CreateMandateRequest();
        request.setAuthMode("api");
        request.setMaximumAmount(5000.0);
        request.setFirstCollectionDate("2025-06-01");
        request.setFinalCollectionDate("2025-12-01");
        request.setFirstName("John");
        request.setLastName("Doe");
        request.setCustomerIdentifier("CID123");
        request.setCustomerAccountNumber("**********");
        request.setDestinationBankId("HDFC0001234");
        request.setDestinationBankName("HDFC Bank");


        MandateResponse mandateResponse = new MandateResponse();
        mandateResponse.setId("mandate123");
        mandateResponse.setMandateId("MID456");
        mandateResponse.setUmrn("UMNR123");


        when(nachEncryptionAndDecryption.decryptCreateMandateRequest(request)).thenReturn(request);
        when(webTarget.path(anyString())).thenReturn(webTarget);
        when(webTarget.request()).thenReturn(invocationBuilder);
        when(invocationBuilder.post(any(Entity.class))).thenReturn(response);
        when(response.getStatus()).thenReturn(Response.Status.OK.getStatusCode());
        when(response.readEntity(MandateResponse.class)).thenReturn(mandateResponse);
        when(nachEncryptionAndDecryption.encryptMandateResponseDTO(any())).thenAnswer(invocation -> invocation.getArgument(0));


        MandateResponseDTO result = digioExternalClient.initiateMandate(request);

        assertNotNull(result);
        assertEquals("mandate123", result.getId());
        assertEquals("MID456", result.getMandateId());
        assertEquals("UMNR123", result.getUmrn());
    }


    @Test
    @Ignore
    public void testInitiateMandate_upiMode_success() {
        CreateMandateRequest request = new CreateMandateRequest();
        request.setAuthMode("upi");
        request.setMaximumAmount(5000.0);
        request.setFirstCollectionDate("2025-06-01");
        request.setFinalCollectionDate("2025-12-01");
        request.setFirstName("John");
        request.setLastName("Doe");
        request.setCustomerIdentifier("CID123");
        request.setCustomerAccountNumber("**********");
        request.setDestinationBankId("HDFC0001234");
        request.setDestinationBankName("HDFC Bank");


        MandateResponse mandateResponse = new MandateResponse();
        mandateResponse.setId("mandate123");
        mandateResponse.setMandateId("MID456");
        mandateResponse.setUmrn("UMNR123");


        when(nachEncryptionAndDecryption.decryptCreateMandateRequest(request)).thenReturn(request);
        when(webTarget.path(anyString())).thenReturn(webTarget);
        when(webTarget.request()).thenReturn(invocationBuilder);
        when(invocationBuilder.post(any(Entity.class))).thenReturn(response);
        when(response.getStatus()).thenReturn(Response.Status.OK.getStatusCode());
        when(response.readEntity(MandateResponse.class)).thenReturn(mandateResponse);
        when(nachEncryptionAndDecryption.encryptMandateResponseDTO(any())).thenAnswer(invocation -> invocation.getArgument(0));


        MandateResponseDTO result = digioExternalClient.initiateMandate(request);

        assertNotNull(result);
        assertEquals("mandate123", result.getId());
        assertEquals("MID456", result.getMandateId());
        assertEquals("UMNR123", result.getUmrn());
    }



    private RPDKycResponse getMockRPDKycResponse() {
        RPDKycResponse rpdKycResponse = new RPDKycResponse();
        rpdKycResponse.setId("rpd_abc123");
        rpdKycResponse.setCreatedAt("2025-06-01 10:45:00");
        rpdKycResponse.setStatus("initiated");
        rpdKycResponse.setCustomerIdentifier("CID001");
        rpdKycResponse.setReferenceId("REF789");
        rpdKycResponse.setTransactionId("TXN456");
        rpdKycResponse.setCustomerName("John Doe");
        rpdKycResponse.setExpireInDays(7);
        rpdKycResponse.setAutoApproved(false);
        rpdKycResponse.setTemplateId("tpl_001");

        AccessToken accessToken = new AccessToken();
        accessToken.setEntityId("entity_123");
        accessToken.setId("token_456");

        rpdKycResponse.setAccessToken(accessToken);
        return rpdKycResponse;
    }
}
