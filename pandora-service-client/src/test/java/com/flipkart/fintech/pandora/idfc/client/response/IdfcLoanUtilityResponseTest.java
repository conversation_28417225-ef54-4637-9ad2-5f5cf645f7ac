package com.flipkart.fintech.pandora.idfc.client.response;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.flipkart.fintech.pandora.extensions.JsonFileParameterResolver;
import com.flipkart.fintech.pandora.test.annotations.JsonFile;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@ExtendWith(JsonFileParameterResolver.class)
public class IdfcLoanUtilityResponseTest {

  @Test
  public void create(@JsonFile("response/idfcLoanUtilityResponse.json") IdfcLoanUtilityResponse idfcLoanUtilityResponse) {
    assertNotNull(idfcLoanUtilityResponse);
    assertNotNull(idfcLoanUtilityResponse.getResourceData());
    assertEquals("N", idfcLoanUtilityResponse.getResourceData().get(0).getStatus());
    assertEquals("EOD_BOD NOT RUNNING", idfcLoanUtilityResponse.getResourceData().get(0).getDescription());
    assertNotNull(idfcLoanUtilityResponse.getMetaData());
    assertEquals("SUCCESS", idfcLoanUtilityResponse.getMetaData().getStatus());
    assertEquals("2023-11-26T15:33:26", idfcLoanUtilityResponse.getMetaData().getTime());
  }
}
