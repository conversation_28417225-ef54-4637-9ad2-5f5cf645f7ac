package com.flipkart.fintech.pandora.interceptor;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import com.flipkart.fintech.pandora.service.client.encryptor.DataEncryptorDecryptor;
import com.flipkart.fintech.pandora.service.client.interceptors.EncryptionInterceptor;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import javax.ws.rs.ext.ReaderInterceptorContext;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;

class EncryptionInterceptorTest {

  @Test
  void testAroundReadFrom_shouldReEncryptWhenErrorAndDataHasFiveSegments() throws IOException {
    String originalData = "seg1.seg2.seg3.seg4.seg5";
    String decryptedData = "decryptedPayload";

    ReaderInterceptorContext context = mock(ReaderInterceptorContext.class);
    when(context.getProperty("isError")).thenReturn(true);
    when(context.getProperty("encryptionDisabled")).thenReturn(false);
    when(context.getInputStream())
        .thenReturn(new ByteArrayInputStream(originalData.getBytes(StandardCharsets.UTF_8)));
    when(context.proceed()).thenReturn("proceeded");

    DataEncryptorDecryptor encryptor = mock(DataEncryptorDecryptor.class);
    when(encryptor.decrypt(originalData)).thenReturn(decryptedData);

    EncryptionInterceptor interceptor = new EncryptionInterceptor(encryptor);

    Object result = interceptor.aroundReadFrom(context);

    ArgumentCaptor<ByteArrayInputStream> captor =
        ArgumentCaptor.forClass(ByteArrayInputStream.class);
    verify(context).setInputStream(captor.capture());
    byte[] finalBytes;
    try (ByteArrayOutputStream buffer = new ByteArrayOutputStream()) {
      ByteArrayInputStream bais = captor.getValue();
      byte[] chunk = new byte[256];
      int length;
      while ((length = bais.read(chunk)) != -1) {
        buffer.write(chunk, 0, length);
      }
      finalBytes = buffer.toByteArray();
    }
    assertEquals(decryptedData, new String(finalBytes, StandardCharsets.UTF_8));
    assertEquals("proceeded", result);
  }

  @Test
  void testAroundReadFrom_shouldSkipWhenEncryptionDisabled() throws IOException {
    ReaderInterceptorContext context = mock(ReaderInterceptorContext.class);
    when(context.getProperty("encryptionDisabled")).thenReturn(true);
    when(context.proceed()).thenReturn("proceeded");

    DataEncryptorDecryptor encryptor = mock(DataEncryptorDecryptor.class);
    EncryptionInterceptor interceptor = new EncryptionInterceptor(encryptor);

    Object result = interceptor.aroundReadFrom(context);
    verify(context, never()).setInputStream(any());
    assertEquals("proceeded", result);
  }

  @Test
  void testAroundReadFrom_shouldDecryptWhenEnabledAndNotError() throws IOException {
    String originalData = "encryptedData";
    String decryptedData = "decryptedData";

    ReaderInterceptorContext context = mock(ReaderInterceptorContext.class);
    when(context.getProperty("isError")).thenReturn(false);
    when(context.getProperty("encryptionDisabled")).thenReturn(false);
    when(context.getInputStream())
        .thenReturn(new ByteArrayInputStream(originalData.getBytes(StandardCharsets.UTF_8)));
    when(context.proceed()).thenReturn("proceeded");

    DataEncryptorDecryptor encryptor = mock(DataEncryptorDecryptor.class);
    when(encryptor.decrypt(originalData)).thenReturn(decryptedData);

    EncryptionInterceptor interceptor = new EncryptionInterceptor(encryptor);

    Object result = interceptor.aroundReadFrom(context);

    ArgumentCaptor<ByteArrayInputStream> captor =
        ArgumentCaptor.forClass(ByteArrayInputStream.class);
    verify(context).setInputStream(captor.capture());
    byte[] inputBytes;
    try (ByteArrayOutputStream buffer = new ByteArrayOutputStream()) {
      ByteArrayInputStream stream = captor.getValue();
      byte[] data = new byte[1024];
      int bytesRead;
      while ((bytesRead = stream.read(data, 0, data.length)) != -1) {
        buffer.write(data, 0, bytesRead);
      }
      inputBytes = buffer.toByteArray();
    }
    assertEquals(decryptedData, new String(inputBytes, StandardCharsets.UTF_8));

    assertEquals("proceeded", result);
  }

  @Test
  void testAroundReadFrom_shouldProceedDirectlyWhenErrorAndNoFiveSegments() throws IOException {
    String originalData = "seg1.seg2.seg3.seg4";
    String decryptedData = "decryptedPayload";

    // Set up the context with isError true but data with only 4 segments.
    ReaderInterceptorContext context = mock(ReaderInterceptorContext.class);
    when(context.getProperty("isError")).thenReturn(true);
    when(context.getProperty("encryptionDisabled")).thenReturn(false);
    when(context.getInputStream())
        .thenReturn(new ByteArrayInputStream(originalData.getBytes(StandardCharsets.UTF_8)));
    when(context.proceed()).thenReturn("proceeded");

    // Set up the encryptor to return the decrypted payload.
    DataEncryptorDecryptor encryptor = mock(DataEncryptorDecryptor.class);
    when(encryptor.decrypt(originalData)).thenReturn(decryptedData);

    EncryptionInterceptor interceptor = new EncryptionInterceptor(encryptor);
    Object result = interceptor.aroundReadFrom(context);

    // Verify that no new input stream is set
    verify(context, never()).setInputStream(any());

    // Verify that proceed is called and its result is returned
    verify(context).proceed();
    assertEquals("proceeded", result);
  }
}
