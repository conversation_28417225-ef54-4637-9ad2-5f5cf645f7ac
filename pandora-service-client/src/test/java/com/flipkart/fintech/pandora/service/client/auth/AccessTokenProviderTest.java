package com.flipkart.fintech.pandora.service.client.auth;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

import java.util.concurrent.ExecutionException;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class AccessTokenProviderTest {
  
  @Mock
  AuthenticationClientFactory authenticationClientFactory;

  @Mock
  LenderAuthenticationClient lenderAuthenticationClient;

  @Test
  void getAccessTokenSuccess() throws ExecutionException {
    AccessTokenProvider accessTokenProvider = new AccessTokenProvider(authenticationClientFactory);

    // Mock the cache loader behavior
    Scope idfcScope = Scope.LENDING_IDFC;
    when(authenticationClientFactory.getLenderAuthenticationClient(idfcScope)).thenReturn(lenderAuthenticationClient);
    when(lenderAuthenticationClient.generateAccessToken()).thenReturn("token123");

    String token = accessTokenProvider.getAccessToken(idfcScope);
    assertEquals("token123", token, "Access token should match the expected value");
  }

  @Test
  @Disabled("This test is disabled because it is not possible to mock the behavior of the cache loader")
  void getAccessTokenThrowsException() throws ExecutionException {
    AccessTokenProvider accessTokenProvider = new AccessTokenProvider(authenticationClientFactory);

    // Simulate an ExecutionException
    Scope idfcScope = Scope.LENDING_IDFC;
    when(authenticationClientFactory.getLenderAuthenticationClient(idfcScope)).thenReturn(lenderAuthenticationClient);
    when(lenderAuthenticationClient.generateAccessToken()).thenThrow(ExecutionException.class);

    assertThrows(RuntimeException.class, () -> accessTokenProvider.getAccessToken(idfcScope), "Should throw RuntimeException on ExecutionException");
  }


}