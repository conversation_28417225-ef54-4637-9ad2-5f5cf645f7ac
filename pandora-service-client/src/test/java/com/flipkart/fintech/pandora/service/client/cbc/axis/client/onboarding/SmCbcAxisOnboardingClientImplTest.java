package com.flipkart.fintech.pandora.service.client.cbc.axis.client.onboarding;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.runners.MockitoJUnitRunner;

import static org.junit.jupiter.api.Assertions.*;

@RunWith(MockitoJUnitRunner.class)
class SmCbcAxisOnboardingClientImplTest {

    @BeforeEach
    void setUp() {
    }

    @Test
    void fetchApplicationStatus() {
    }

    @Test
    void fetchOfferDetails() {
    }

    @Test
    void fetchUserDetails() {
    }

    @Test
    void initiateChallenge() {
    }

    @Test
    void submitApplicationETC() {
    }

    @Test
    void submitApplicationNTB() {
    }

    @Test
    void submitApplicationETB() {
    }

    @Test
    void submitApplicationETBNoPA() {
    }

    @Test
    void fetchMaskedCardDetails() {
    }

    @Test
    void fetchAccountDetails() {
    }

    @Test
    void fetchKycStatus() {
    }

    @Test
    void fetchPanCkycDetails() {
    }

    @Test
    void initiatingKyc() {
    }
}