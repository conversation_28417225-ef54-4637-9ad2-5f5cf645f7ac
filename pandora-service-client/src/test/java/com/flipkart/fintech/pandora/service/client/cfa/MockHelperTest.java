package com.flipkart.fintech.pandora.service.client.cfa;

import static org.junit.jupiter.api.Assertions.*;

import com.flipkart.fintech.cryptex.config.DynamicBucketConfig;
import com.flipkart.fintech.pandora.service.client.cfa.MockHelper.MockedApi;
import com.flipkart.kloud.config.ConfigClient;
import com.flipkart.kloud.config.ConfigClientBuilder;
import com.flipkart.kloud.config.DynamicBucket;
import com.flipkart.kloud.config.error.ConfigServiceException;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

public class MockHelperTest {

  private MockHelper mockHelper;
  @BeforeEach
  public void setUp() throws Exception {
    ConfigClient configClient = new ConfigClientBuilder().build();
    DynamicBucket bucket = configClient.getDynamicBucket("sm-pandora-mock-preprod");
    mockHelper = new MockHelper(bucket);
  }

  private static DynamicBucket getDynamicBucket(DynamicBucketConfig mockBucketConfig)
      throws ConfigServiceException {
    ConfigClient configClient = new ConfigClientBuilder().build();
    return configClient.getDynamicBucket(mockBucketConfig.getBucketName());
  }

  @Test
  @Disabled
  public void test1() {
    Optional<MockedApi> api1 = mockHelper.findMockedApi("API1");
    assertTrue(api1.isPresent());
    MockedApi data = mockHelper.getData(api1.get().getDataKey(), MockedApi.class);
    assertEquals("API1", data.getApiName());
    assertEquals("K1", data.getDataKey());

  }
}