package com.flipkart.fintech.pandora.service.client.creditline;

import static org.junit.jupiter.api.Assertions.*;

import com.flipkart.fintech.pandora.service.client.hyperverge.requests.DigilockerAadhaarDetailsRequest;
import com.flipkart.fintech.pandora.service.client.hyperverge.requests.DigilockerConsentRequest;
import com.flipkart.fintech.pandora.service.client.hyperverge.responses.DigilockerAadhaarResponse;
import com.flipkart.fintech.pandora.service.client.hyperverge.responses.DigilockerAadhaarResult;
import com.flipkart.fintech.pandora.service.client.hyperverge.responses.DigilockerConsentResponse;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockserver.client.MockServerClient;
import org.mockserver.integration.ClientAndServer;
import org.mockserver.model.HttpRequest;
import org.mockserver.model.HttpResponse;

import javax.ws.rs.client.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.io.IOException;
import java.net.ServerSocket;

@ExtendWith(MockitoExtension.class)
public class HypervergeDigilockerTest {

    private int mockPort;
    private String baseUrl;
    private ClientAndServer mockServer;
    private Client client;
    private WebTarget webTarget;

    @Before
    public void startMockServer() throws IOException {
        mockPort = findFreePort();
        baseUrl = "http://localhost:" + mockPort;

        mockServer = ClientAndServer.startClientAndServer(mockPort);
        client = ClientBuilder.newClient();
        webTarget = client.target(baseUrl);
    }

    @After
    public void stopMockServer() {
        if (mockServer != null && mockServer.isRunning()) {
            mockServer.stop();
        }
    }

//    @Test
    public void testStartDigilocker_Success() {
        new MockServerClient("localhost", mockPort)
                .when(HttpRequest.request().withMethod("POST").withPath("/digilocker-consent"))
                .respond(HttpResponse.response()
                        .withStatusCode(200)
                        .withBody("{ \"status\": \"success\", \"statusCode\": \"200\" }")
                        .withContentType(org.mockserver.model.MediaType.APPLICATION_JSON));

        Response response = webTarget.path("/digilocker-consent")
                .request(MediaType.APPLICATION_JSON)
                .header("appId", "testAppId")
                .header("appKey", "testAppKey")
                .post(Entity.json(new DigilockerConsentRequest()));

        assertNotNull(response);
        assertEquals(200, response.getStatus());

        DigilockerConsentResponse result = response.readEntity(DigilockerConsentResponse.class);
        assertNotNull(result);
        assertEquals("success", result.getStatus());
        assertEquals("200", result.getStatusCode());
    }

//    @Test
    public void testFetchAadhaar_Success() {
        new MockServerClient("localhost", mockPort)
                .when(HttpRequest.request().withMethod("POST").withPath("/fetch-aadhaar"))
                .respond(HttpResponse.response()
                        .withStatusCode(200)
                        .withBody("{ \"status\": \"success\", \"statusCode\": \"200\", \"result\": { " +
                                "\"name\": \"John Doe\", " +
                                "\"dob\": \"1990-01-01\", " +
                                "\"address\": \"123 Street, City\", " +
                                "\"maskedAadhaarNumber\": \"XXXX-XXXX-1234\", " +
                                "\"photo\": \"base64encodedPhoto\", " +
                                "\"gender\": \"Male\", " +
                                "\"aadhaarFile\": \"base64encodedFile\" } }")
                        .withContentType(org.mockserver.model.MediaType.APPLICATION_JSON));

        DigilockerAadhaarDetailsRequest request = new DigilockerAadhaarDetailsRequest();
        request.setReferenceId("txn12345");
        request.setAadhaarFile("dummy_file_data");
        request.setRawAddressFields("dummy_address");

        Response response = webTarget.path("/fetch-aadhaar")
                .request(MediaType.APPLICATION_JSON)
                .header("appId", "testAppId")
                .header("appKey", "testAppKey")
                .header("transactionId", request.getReferenceId())
                .post(Entity.json(request));

        assertNotNull(response);
        assertEquals(200, response.getStatus());

        DigilockerAadhaarResponse result = response.readEntity(DigilockerAadhaarResponse.class);
        assertNotNull(result);
        assertEquals("success", result.getStatus());
        assertEquals("200", result.getStatusCode());

        DigilockerAadhaarResult resultData = result.getResult();
        assertNotNull(resultData);
        assertEquals("John Doe", resultData.getName());
        assertEquals("1990-01-01", resultData.getDob());
        assertEquals("123 Street, City", resultData.getAddress());
        assertEquals("XXXX-XXXX-1234", resultData.getMaskedAadhaarNumber());
        assertEquals("base64encodedPhoto", resultData.getPhoto());
        assertEquals("Male", resultData.getGender());
        assertEquals("base64encodedFile", resultData.getAadhaarFile());
    }

    private int findFreePort() throws IOException {
        try (ServerSocket socket = new ServerSocket(0)) {
            return socket.getLocalPort();
        }
    }
}
