package com.flipkart.fintech.pandora.service.client.creditline.yubi;

import centaur.client.shade.com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.pandora.api.model.pl.request.BankRequest;
import com.flipkart.fintech.pandora.api.model.pl.request.EsigningResponse;
import com.flipkart.fintech.pandora.api.model.pl.request.MandateRequest;
import com.flipkart.fintech.pandora.api.model.pl.response.BankResponse;
import com.flipkart.fintech.pandora.api.model.pl.response.CustomerDetailsResponse;
import com.flipkart.fintech.pandora.api.model.pl.response.DisbursementDetailsResponse;
import com.flipkart.fintech.pandora.api.model.pl.response.MandateResponse;
import com.flipkart.fintech.pandora.service.client.config.YubiConfiguration;
import com.flipkart.fintech.pandora.service.client.exceptions.LenderException;
import com.flipkart.fintech.pandora.service.client.exceptions.NetworkException;
import com.flipkart.fintech.pandora.yubi.request.*;
import com.flipkart.fintech.pandora.yubi.response.*;
import com.flipkart.fintech.pandora.yubi.response.kyc.UploadKYCResponse;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import javax.ws.rs.client.Entity;
import javax.ws.rs.client.Invocation;
import javax.ws.rs.client.WebTarget;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.io.IOException;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;


@RunWith(MockitoJUnitRunner.class)
public class YubiCreditLineClientImplTest {

    @Mock
    private WebTarget webTarget;

    @Mock
    private YubiConfiguration yubiConfiguration;

    @Mock
    private YubiConfiguration.Endpoints endpoints;

    @Mock
    private YubiConfiguration.Headers headers;

    @Mock
    private WebTarget pathTarget;

    @Mock
    private Invocation.Builder requestBuilder;

    @Mock
    private Response response;

    private YubiCreditLineClientImpl yubiCreditLineClient;
    private ObjectMapper objectMapper;

    @Before
    public void setUp() {
        objectMapper = new ObjectMapper();

        // Using lenient() to apublic void UnnecessaryStubbingException
        lenient().when(yubiConfiguration.getEndpoints()).thenReturn(endpoints);
        lenient().when(yubiConfiguration.getHeaders()).thenReturn(headers);
        lenient().when(headers.getApiKey()).thenReturn("test-api-key");
        lenient().when(headers.getClientId()).thenReturn("test-client-id");

        // Mock the builder pattern chain for HTTP requests
        lenient().when(webTarget.path(anyString())).thenReturn(pathTarget);
        lenient().when(pathTarget.request(MediaType.APPLICATION_JSON)).thenReturn(requestBuilder);
        lenient().when(requestBuilder.header(anyString(), anyString())).thenReturn(requestBuilder);

        // Register MultiPartFeature
        lenient().when(webTarget.register(any())).thenReturn(webTarget);

        yubiCreditLineClient = new YubiCreditLineClientImpl(webTarget, yubiConfiguration);
    }

    @Test
    public void createApplication_Success() throws LenderException, IOException {
        // Arrange
        CreateApplicationRequest request = new CreateApplicationRequest();
        CreateApplicationResponse expectedResponse = new CreateApplicationResponse();
        expectedResponse.setMessage("Success");

        String endpoint = "/create-application";
        when(endpoints.getCreateApplication()).thenReturn(endpoint);

        mockSuccessfulResponse(expectedResponse);

        // Act
        CreateApplicationResponse actualResponse = yubiCreditLineClient.createApplication(request);

        // Assert
        assertEquals("Success", actualResponse.getMessage());
        verifyHttpPostCall(endpoint);
    }

    @Test
    public void getOffer_Success() throws IOException, LenderException {
        // Arrange
        GetOfferRequest request = new GetOfferRequest();
        OfferResponse offerResponse = new OfferResponse();
        offerResponse.setPartnershipId("P123");

        String endpoint = "/get-offer";
        when(endpoints.getGetOffer()).thenReturn(endpoint);

        mockSuccessfulResponse(offerResponse);

        // Act
        OfferResponse actualResponse = yubiCreditLineClient.getOffer(request);

        // Assert
        assertEquals("P123", actualResponse.getPartnershipId());
        verifyHttpPostCall(endpoint);
    }

    @Test
    public void getOffer_Exception_ShouldThrowNetworkException() throws IOException {
        // Arrange
        GetOfferRequest request = new GetOfferRequest();
        String endpoint = "/get-offer";
        when(endpoints.getGetOffer()).thenReturn(endpoint);

        // Set up the exception with a message
        RuntimeException exception = new RuntimeException("Test exception");
        when(requestBuilder.post(any(Entity.class))).thenThrow(exception);

        // Act & Assert - expect PandoraClientException instead of NetworkException
        Exception thrownException = assertThrows(Exception.class,
                () -> yubiCreditLineClient.getOffer(request));

        // Verify it's the right type and has the expected message
        assertEquals("Unexpected error occurred from Yubi: Test exception", thrownException.getMessage());
    }

    @Test
    public void checkApprovalStatus_Success() throws LenderException, IOException {
        // Arrange
        LoanStatusRequest request = new LoanStatusRequest();
        LoanStatusResponse loanStatusResponse = new LoanStatusResponse();
        loanStatusResponse.setApplicationCreationStatus("SUCCESS");

        String endpoint = "/loan-status";
        when(endpoints.getLoanStatus()).thenReturn(endpoint);

        mockSuccessfulResponse(loanStatusResponse);

        // Act
        LoanStatusResponse actualResponse = yubiCreditLineClient.checkApprovalStatus(request);

        // Assert
        assertEquals("SUCCESS", actualResponse.getApplicationCreationStatus());
        verifyHttpPostCall(endpoint);
    }

    @Test
    public void confirmCommercial_Success() throws LenderException, IOException {
        // Arrange
        ConfirmCommercialRequest request = new ConfirmCommercialRequest();
        ConfirmCommercialResponse expectedResponse = new ConfirmCommercialResponse();
        expectedResponse.setClientApplicationId("SAMPLE_ID");

        String endpoint = "/confirm-commercial";
        when(endpoints.getConfirmCommercial()).thenReturn(endpoint);

        mockSuccessfulResponse(expectedResponse);

        // Act
        ConfirmCommercialResponse actualResponse = yubiCreditLineClient.confirmCommercial(request);

        // Assert
        assertEquals("SAMPLE_ID", actualResponse.getClientApplicationId());
        verifyHttpPostCall(endpoint);
    }

    @Test
    public void fetchDocument_Success() throws LenderException, IOException {
        // Arrange
        GetDocumentRequest request = new GetDocumentRequest();
        FetchDocumentResponse expectedResponse = new FetchDocumentResponse();
        expectedResponse.setClientApplicationId("APP123");

        String endpoint = "/fetch-document";
        when(endpoints.getFetchDocument()).thenReturn(endpoint);

        mockSuccessfulResponse(expectedResponse);

        // Act
        FetchDocumentResponse actualResponse = yubiCreditLineClient.fetchDocument(request);

        // Assert
        assertEquals("APP123", actualResponse.getClientApplicationId());
        verifyHttpPostCall(endpoint);
    }

    @Test
    public void generateDocument_Success() throws LenderException, IOException {
        // Arrange
        GenerateDocumentRequest request = new GenerateDocumentRequest();
        GenerateDocumentResponse expectedResponse = new GenerateDocumentResponse();
        expectedResponse.setClientApplicationId("cl_id_123");

        String endpoint = "/generate-document";
        when(endpoints.getGenerateDocument()).thenReturn(endpoint);

        mockSuccessfulResponse(expectedResponse);

        // Act
        GenerateDocumentResponse actualResponse = yubiCreditLineClient.generateDocument(request);

        // Assert
        assertEquals("cl_id_123", actualResponse.getClientApplicationId());
        verifyHttpPostCall(endpoint);
    }

    @Test
    public void getPaymentDetails_Success() throws LenderException, IOException {
        // Arrange
        LoanStatusRequest request = new LoanStatusRequest();
        RepaymentDetailsResponse expectedResponse = new RepaymentDetailsResponse();
        expectedResponse.setClientApplicationId("APP123");

        String endpoint = "/payment-details";
        when(endpoints.getPaymentDetails()).thenReturn(endpoint);

        mockSuccessfulResponse(expectedResponse);

        // Act
        RepaymentDetailsResponse actualResponse = yubiCreditLineClient.getPaymentDetails(request);

        // Assert
        assertEquals("APP123", actualResponse.getClientApplicationId());
        verifyHttpPostCall(endpoint);
    }

    @Test
    public void initiateEsign_Success() throws LenderException, IOException {
        // Arrange
        EsignRequest request = new EsignRequest();
        EsigningResponse expectedResponse = new EsigningResponse();
        expectedResponse.setEsigningStatus("Initiated");

        String endpoint = "/esign";
        when(endpoints.getEsign()).thenReturn(endpoint);

        mockSuccessfulResponse(expectedResponse);

        // Act
        EsignResponse actualResponse = yubiCreditLineClient.initiateEsign(request);

        // Assert
        assertEquals("Initiated", actualResponse.getEsigningStatus());
        verifyHttpPostCall(endpoint);
    }

    @Test
    public void fetchApplicationStatus_Success() throws LenderException, IOException {
        // Arrange
        LoanStatusRequest request = new LoanStatusRequest();
        ApplicationStatusResponse expectedResponse = new ApplicationStatusResponse();
        expectedResponse.setClientApplicationId("APP123");

        String endpoint = "/application-status";
        when(endpoints.getApplicationStatus()).thenReturn(endpoint);

        // Using lenient() for all stubs in this test
        lenient().when(requestBuilder.post(any(Entity.class))).thenReturn(response);
        lenient().when(response.readEntity(String.class)).thenReturn(objectMapper.writeValueAsString(expectedResponse));
        lenient().when(response.getStatus()).thenReturn(200);

        // Act
        ApplicationStatusResponse actualResponse = yubiCreditLineClient.fetchApplicationStatus(request);

        // Assert
        assertEquals("APP123", actualResponse.getClientApplicationId());
        verifyHttpPostCall(endpoint);
    }

    @Test
    public void uploadKYC_WithSelfie_Success() throws LenderException, IOException {
        // Arrange
        UploadKYCRequest request = new UploadKYCRequest();
        // No KYC trigger type for selfie
        UploadKYCResponse expectedResponse = new UploadKYCResponse();
        expectedResponse.setClientApplicationId("CL123");

        String endpoint = "/selfie";
        when(endpoints.getSelfie()).thenReturn(endpoint);

        mockSuccessfulResponse(expectedResponse);

        // Act
        UploadKYCResponse actualResponse = yubiCreditLineClient.uploadKYC(request,true);

        // Assert
        assertEquals("CL123", actualResponse.getClientApplicationId());
        verifyHttpPostCall(endpoint);
    }

    @Test
    public void uploadKYC_WithAadhaar_Success() throws LenderException, IOException {
        // Arrange
        UploadKYCRequest request = new UploadKYCRequest();
        request.setKycTriggerType("UPDATE"); // KYC trigger type for Aadhaar
        UploadKYCResponse expectedResponse = new UploadKYCResponse();
        expectedResponse.setClientApplicationId("CL123");

        String endpoint = "/aadhaar";
        when(endpoints.getAadhaarXml()).thenReturn(endpoint);

        mockSuccessfulResponse(expectedResponse);

        // Act
        UploadKYCResponse actualResponse = yubiCreditLineClient.uploadKYC(request,false);

        // Assert
        assertEquals("CL123", actualResponse.getClientApplicationId());
        verifyHttpPostCall(endpoint);
    }

    @Test
    public void getDisbursementDetails_Success() throws LenderException, IOException {
        // Arrange
        LoanStatusRequest request = new LoanStatusRequest();
        DisbursementDetailsResponse expectedResponse = new DisbursementDetailsResponse();
        expectedResponse.setClientApplicationId("CL123");

        String endpoint = "/disbursement-details";
        when(endpoints.getDisbursementDetail()).thenReturn(endpoint);

        mockSuccessfulResponse(expectedResponse);

        // Act
        DisbursementDetailResponse actualResponse = yubiCreditLineClient.getDisbursementDetails(request);

        // Assert
        assertEquals("CL123", actualResponse.getClientApplicationId());
        verifyHttpPostCall(endpoint);
    }

    @Test
    public void fetchCustomerDetails_Success() throws LenderException, IOException {
        // Arrange
        LoanStatusRequest request = new LoanStatusRequest();
        CustomerDetailsResponse expectedResponse = new CustomerDetailsResponse();
        expectedResponse.setClientCustomerId("CUST123");

        String endpoint = "/customer-details";
        when(endpoints.getCustomerDetail()).thenReturn(endpoint);

        mockSuccessfulResponse(expectedResponse);

        // Act
        CustomerDetailResponse actualResponse = yubiCreditLineClient.fetchCustomerDetails(request);

        // Assert
        assertEquals("CUST123", actualResponse.getClientCustomerId());
        verifyHttpPostCall(endpoint);
    }

    @Test
    public void updateMandateStatus_Success() throws LenderException, IOException {
        // Arrange
        MandateRequest request = new MandateRequest();
        MandateResponse expectedResponse = new MandateResponse();
        expectedResponse.setMandateRegistrationStatus("SUCCESS");

        String endpoint = "/update-mandate";
        when(endpoints.getUpdateMandate()).thenReturn(endpoint);

        mockSuccessfulResponse(expectedResponse);

        // Act
        MandateResponse actualResponse = yubiCreditLineClient.updateMandateStatus(request);

        // Assert
        assertEquals("SUCCESS", actualResponse.getMandateRegistrationStatus());
        verifyHttpPostCall(endpoint);
    }

    @Test
    public void updateBankStatus_Success() throws LenderException, IOException {
        // Arrange
        BankRequest request = new BankRequest();
        BankResponse expectedResponse = new BankResponse();
        expectedResponse.setAccountValidationStatus("PENDING");

        String endpoint = "/update-bank";
        when(endpoints.getUpdateBank()).thenReturn(endpoint);

        mockSuccessfulResponse(expectedResponse);

        // Act
        BankResponse actualResponse = yubiCreditLineClient.updateBankStatus(request);

        // Assert
        assertEquals("PENDING", actualResponse.getAccountValidationStatus());
        verifyHttpPostCall(endpoint);
    }

    @Test
    public void getPostResponse_NetworkException() {
        // Arrange
        CreateApplicationRequest request = new CreateApplicationRequest();
        String endpoint = "/create-application";
        when(endpoints.getCreateApplication()).thenReturn(endpoint);

        // Mock exception during HTTP call with a specific message
        RuntimeException exception = new RuntimeException("Network error");
        when(requestBuilder.post(any(Entity.class))).thenThrow(exception);

        // Act & Assert - expect PandoraClientException instead of NetworkException
        Exception thrownException = assertThrows(Exception.class,
                () -> yubiCreditLineClient.createApplication(request));

        // Verify it's the right message
        assertEquals("Unexpected error occurred from Yubi: Network error", thrownException.getMessage());
    }

    @Test
    public void generateSignedURL_Success() throws LenderException, IOException {
        // Arrange
        String base64Content = "dGVzdENvbnRlbnQ=";
        String extension = "pdf";
        String documentType = "LOAN_AGREEMENT";

        SignedURLResponse expectedResponse = new SignedURLResponse();
        expectedResponse.setPresignedUrl("https://example.com/signed-url");

        UploadFileResponse uploadFileResponse = new UploadFileResponse();
        uploadFileResponse.setFileId("FILE123");

        // Mock endpoints
        when(endpoints.getDocUpload()).thenReturn("/upload");
        when(endpoints.getGenerateSignedURL()).thenReturn("/generate-signed-url");

        // Create a separate mock for the request builder used in the file upload
        Invocation.Builder fileUploadRequestBuilder = mock(Invocation.Builder.class);
        Response fileUploadResponse = mock(Response.class);

        // Mock the request chain for file upload
        when(webTarget.path("/upload")).thenReturn(pathTarget);
        when(pathTarget.request()).thenReturn(fileUploadRequestBuilder);
        when(fileUploadRequestBuilder.header(anyString(), anyString())).thenReturn(fileUploadRequestBuilder);
        when(fileUploadRequestBuilder.post(any(Entity.class))).thenReturn(fileUploadResponse);
        when(fileUploadResponse.readEntity(String.class)).thenReturn(objectMapper.writeValueAsString(uploadFileResponse));

        // Mock the request chain for signed URL generation
        when(webTarget.path("/generate-signed-url")).thenReturn(pathTarget);
        when(pathTarget.request(MediaType.APPLICATION_JSON)).thenReturn(requestBuilder);
        when(requestBuilder.post(any(Entity.class))).thenReturn(response);
        when(response.readEntity(String.class)).thenReturn(objectMapper.writeValueAsString(expectedResponse));

        // Set status codes
        when(fileUploadResponse.getStatus()).thenReturn(200);
        when(response.getStatus()).thenReturn(200);

        // Act
        SignedURLResponse actualResponse = yubiCreditLineClient.generateSignedURL(base64Content, extension, documentType);

        // Assert
        assertEquals("https://example.com/signed-url", actualResponse.getPresignedUrl());

        // Verify the correct endpoints were called
        verify(webTarget).path("/upload");
        verify(webTarget).path("/generate-signed-url");

        // Verify headers were set
        verify(fileUploadRequestBuilder).header("x-api-key", "test-api-key");
        verify(fileUploadRequestBuilder).header("x-client-id", "test-client-id");

        // Verify posts were made
        verify(fileUploadRequestBuilder).post(any(Entity.class));
        verify(requestBuilder).post(any(Entity.class));
    }

    @Test
    public void generateSignedURL_UploadFailure() {
        // Arrange
        String base64Content = "invalid-base64";
        String extension = "pdf";
        String documentType = "LOAN_AGREEMENT";

        // Using lenient() for all stubs to apublic void UnnecessaryStubbingException
        lenient().when(endpoints.getDocUpload()).thenReturn("/upload");

        // Act & Assert
        NetworkException networkException = assertThrows(NetworkException.class,
                () -> yubiCreditLineClient.generateSignedURL(base64Content, extension, documentType));

        // Instead of checking the exact message, just verifying if it contains the expected text
        String errorMessage = networkException.getMessage();
        assertTrue(errorMessage.contains("Unexpected error occurred from Yubi:"));
    }

    @Test
    public void handleResponse_InvalidJson() throws IOException {
        // Arrange
        CreateApplicationRequest request = new CreateApplicationRequest();
        String endpoint = "/create-application";
        when(endpoints.getCreateApplication()).thenReturn(endpoint);

        when(requestBuilder.post(any(Entity.class))).thenReturn(response);
        when(response.readEntity(String.class)).thenReturn("invalid json");

        // Act & Assert
        assertThrows(RuntimeException.class, () -> yubiCreditLineClient.createApplication(request));
    }

    private <T> void mockSuccessfulResponse(T responseObject) throws IOException {
        when(requestBuilder.post(any(Entity.class))).thenReturn(response);
        when(response.readEntity(String.class)).thenReturn(objectMapper.writeValueAsString(responseObject));
        when(response.getStatus()).thenReturn(200);
    }

    private void verifyHttpPostCall(String endpoint) {
        verify(webTarget).path(endpoint);
        verify(pathTarget).request(MediaType.APPLICATION_JSON);
        verify(requestBuilder).header("x-api-key", "test-api-key");
        verify(requestBuilder).header("x-client-id", "test-client-id");
        verify(requestBuilder).post(any(Entity.class));
    }
}