package com.flipkart.fintech.pandora.service.client.creditsaison;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.pandora.api.model.creditsaison.request.LoanRequest;
import com.flipkart.fintech.pandora.service.client.encryptor.AESEncryptorTest;
import com.flipkart.fintech.pandora.service.client.hmac.HmacAuthentication;
import org.junit.Before;
import org.junit.Test;

import java.io.InputStream;

public class InitialOfferTest {

    private LoanRequest loanRequest;
    private HmacAuthentication hmacAuthentication;
    private  ObjectMapper objectMapper;

    @Before
    public void setUp() throws Exception {
        InputStream inputStream = AESEncryptorTest.class.getClassLoader().getResourceAsStream("credit_saison_initial_offer_request.json");
        loanRequest = new ObjectMapper().readValue(inputStream, LoanRequest.class);
        hmacAuthentication = new CreditSaisonAuthenticationImpl();
        objectMapper = new ObjectMapper();
    }

    @Test
    public void testCreditSaisonInitialOffer() throws JsonProcessingException {
        String request1 = objectMapper.writeValueAsString(loanRequest);
        request1 = request1.replace(",", ", ").replace(":", ": ");
        System.out.println(hmacAuthentication.generateAuthToken(loanRequest, request1));
    }

}