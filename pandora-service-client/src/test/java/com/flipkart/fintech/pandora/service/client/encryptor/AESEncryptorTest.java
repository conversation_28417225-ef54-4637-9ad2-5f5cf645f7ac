package com.flipkart.fintech.pandora.service.client.encryptor;

import static org.junit.jupiter.api.Assertions.*;

import java.util.Arrays;
import java.util.Base64;
import org.junit.jupiter.api.Test;

public class AESEncryptorTest {

  @Test
  void encrypt_should_return_proper_encrypted_payload() {
//    String originalPayload = "{\"key\":\"value\"}";
//    String initializationVector = "abcdefghijhellos";
//    byte[] ivBytes = initializationVector.getBytes();
//    String secretKey = "68656c6c6f736162636465666768696a";
//    String expectedEncryptedString = "0ZoWGMmGNJqMCmix0Ip//w==";
//    AESEncryptor aesEncryptor = new AESEncryptor(secretKey);
//    String encrypted = aesEncryptor.encrypt(originalPayload);
//    byte[] decode = Base64.getDecoder().decode(encrypted);
//    for(int i = 0; i < ivBytes.length; i++) {
//      System.out.print(ivBytes[i]);
//    }
//    System.out.println();
//    for(int i = 0; i < decode.length; i++) {
//      System.out.print(decode[i]);
//    }
//    System.out.println();
//    byte[] bytes = Arrays.copyOfRange(decode, ivBytes.length, decode.length);
//    for(int i = 0; i < bytes.length;i++) {
//      System.out.print(bytes[i]);
//    }
//    System.out.println();
//    byte[] bytes1 = expectedEncryptedString.getBytes();
//    for (int i = 0; i < bytes1.length; i++) {
//      System.out.print(bytes1[i]);
//    }
//    System.out.println();
//    assertArrayEquals(expectedEncryptedString.getBytes(), encrypted.getBytes());
  }

}