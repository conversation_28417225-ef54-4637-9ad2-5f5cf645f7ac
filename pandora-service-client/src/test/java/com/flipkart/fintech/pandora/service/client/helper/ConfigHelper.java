package com.flipkart.fintech.pandora.service.client.helper;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.pandora.service.client.config.KotakClientConfiguration;

import static io.dropwizard.testing.FixtureHelpers.fixture;

public class ConfigHelper {

    private final ObjectMapper objectMapper;
    private static final String CONFIG_PATH = "config/";

    public ConfigHelper() {
        this.objectMapper = new ObjectMapper();
    }

    public KotakClientConfiguration getKotakClientConfiguration() throws JsonProcessingException {
        String config = fixture(CONFIG_PATH + "kotak_client_configuration.json");
        return objectMapper.readValue(config, KotakClientConfiguration.class);
    }
}
