package com.flipkart.fintech.pandora.service.client.helper;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.pandora.service.client.kotak.requests.*;

import static io.dropwizard.testing.FixtureHelpers.fixture;

public class TestHelper {

    private final ObjectMapper objectMapper;
    private static final String REQUEST_PATH = "requests/";
    private static final String RESPONSE_PATH = "response/";

    public TestHelper() {
        this.objectMapper = new ObjectMapper();
    }

    public PanNumberVerificationRequest getPanNumberVerificationRequest() throws JsonProcessingException {
        String request = fixture(REQUEST_PATH + "pan_number_verification_request.json");
        return objectMapper.readValue(request, PanNumberVerificationRequest.class);
    }

    public CKYCSearchRequest getCKYCSearchRequest() throws JsonProcessingException {
        String request = fixture(REQUEST_PATH + "ckyc_search_request.json");
        return objectMapper.readValue(request, CKYCSearchRequest.class);
    }

    public AadhaarOTPGenerationRequest getAadhaarOTPGenerationRequest() throws JsonProcessingException {
        String request = fixture(REQUEST_PATH + "send_otp_request.json");
        return objectMapper.readValue(request, AadhaarOTPGenerationRequest.class);
    }

    public AadhaarOTPVerificationRequest getAadhaarOTPVerificationRequest() throws JsonProcessingException {
        String request = fixture(REQUEST_PATH + "verify_otp_request.json");
        return objectMapper.readValue(request, AadhaarOTPVerificationRequest.class);
    }

    public CKYCDownloadRequest getCKYCDownloadRequest() throws JsonProcessingException {
        String request = fixture(REQUEST_PATH + "ckyc_download_request.json");
        return objectMapper.readValue(request, CKYCDownloadRequest.class);
    }

    public PennyDropRequest getPennyDropRequest() throws JsonProcessingException {
        String request = fixture(REQUEST_PATH + "penny_drop_request.json");
        return objectMapper.readValue(request, PennyDropRequest.class);
    }

    public CheckEligibilityRequest getCheckEligibilityRequest() throws JsonProcessingException {
        String request = fixture(REQUEST_PATH + "check_eligibility_request.json");
        return objectMapper.readValue(request, CheckEligibilityRequest.class);
    }

    public BRECheckRequest getBRECheckRequest() throws JsonProcessingException {
        String request = fixture(REQUEST_PATH + "bre_check_request.json");
        return objectMapper.readValue(request, BRECheckRequest.class);
    }

    public CreateLoanRequest getCreateLoanRequest() throws JsonProcessingException {
        String request = fixture(REQUEST_PATH + "create_loan_request.json");
        return objectMapper.readValue(request, CreateLoanRequest.class);
    }
}
