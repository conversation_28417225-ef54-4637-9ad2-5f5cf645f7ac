package com.flipkart.fintech.pandora.service.client.hyperverge;

import com.flipkart.fintech.pandora.client.PandoraClientException;
import com.flipkart.fintech.pandora.service.client.hyperverge.requests.DigilockerAadhaarDetailsRequest;
import com.flipkart.fintech.pandora.service.client.hyperverge.requests.DigilockerConsentRequest;
import com.flipkart.fintech.pandora.service.client.hyperverge.responses.DigilockerAadhaarResponse;
import com.flipkart.fintech.pandora.service.client.hyperverge.responses.DigilockerConsentResponse;
import org.junit.Before;
import org.junit.Test;

import javax.ws.rs.client.Client;
import javax.ws.rs.client.Entity;
import javax.ws.rs.client.Invocation;
import javax.ws.rs.client.WebTarget;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

public class HyperVergeClientTest {

    private Client client;
    private WebTarget webTarget;
    private HyperVergeClient hyperVergeClient;

    @Before
    public void setUp() {
        client = mock(Client.class);
        webTarget = mock(WebTarget.class);
        when(client.target(anyString())).thenReturn(webTarget);
        hyperVergeClient = new HyperVergeClient(client, "appId", "appKey", "apiUrl", "urlPath");
    }

    @Test
    public void testStartDigilockerSuccess() {
        DigilockerConsentRequest request = mock(DigilockerConsentRequest.class);
        when(request.getReferenceId()).thenReturn("ref123");

        WebTarget pathTarget = mock(WebTarget.class);
        Invocation.Builder builder = mock(Invocation.Builder.class);
        Response response = mock(Response.class);

        when(webTarget.path(anyString())).thenReturn(pathTarget);
        when(pathTarget.request(MediaType.APPLICATION_JSON)).thenReturn(builder);
        when(builder.header(anyString(), any())).thenReturn(builder);
        when(builder.post(any(Entity.class))).thenReturn(response);
        when(response.getStatus()).thenReturn(200);
        when(response.readEntity(DigilockerConsentResponse.class)).thenReturn(new DigilockerConsentResponse());

        DigilockerConsentResponse result = hyperVergeClient.startDigilocker(request);
        assertNotNull(result);
        verify(response).close();
    }

    @Test
    public void testStartDigilockerFailure() {
        DigilockerConsentRequest request = mock(DigilockerConsentRequest.class);
        when(request.getReferenceId()).thenReturn("ref123");

        WebTarget pathTarget = mock(WebTarget.class);
        Invocation.Builder builder = mock(Invocation.Builder.class);
        Response response = mock(Response.class);

        when(webTarget.path(anyString())).thenReturn(pathTarget);
        when(pathTarget.request(MediaType.APPLICATION_JSON)).thenReturn(builder);
        when(builder.header(anyString(), any())).thenReturn(builder);
        when(builder.post(any(Entity.class))).thenReturn(response);
        when(response.getStatus()).thenReturn(400);

        assertThrows(RuntimeException.class, () -> hyperVergeClient.startDigilocker(request));
        verify(response).close();
    }

    @Test
    public void testFetchAadhaarSuccess() {
        DigilockerAadhaarDetailsRequest request = mock(DigilockerAadhaarDetailsRequest.class);
        when(request.getReferenceId()).thenReturn("ref456");

        WebTarget pathTarget = mock(WebTarget.class);
        Invocation.Builder builder = mock(Invocation.Builder.class);
        Response response = mock(Response.class);

        when(webTarget.path(anyString())).thenReturn(pathTarget);
        when(pathTarget.request(MediaType.APPLICATION_JSON)).thenReturn(builder);
        when(builder.header(anyString(), any())).thenReturn(builder);
        when(builder.post(any(Entity.class))).thenReturn(response);
        when(response.getStatus()).thenReturn(200);
        when(response.readEntity(DigilockerAadhaarResponse.class)).thenReturn(new DigilockerAadhaarResponse());

        DigilockerAadhaarResponse result = hyperVergeClient.fetchAadhaar(request);
        assertNotNull(result);
        verify(response).close();
    }

    @Test
    public void testFetchAadhaarFailure() {
        DigilockerAadhaarDetailsRequest request = mock(DigilockerAadhaarDetailsRequest.class);
        when(request.getReferenceId()).thenReturn("ref456");

        WebTarget pathTarget = mock(WebTarget.class);
        Invocation.Builder builder = mock(Invocation.Builder.class);
        Response response = mock(Response.class);

        when(webTarget.path(anyString())).thenReturn(pathTarget);
        when(pathTarget.request(MediaType.APPLICATION_JSON)).thenReturn(builder);
        when(builder.header(anyString(), any())).thenReturn(builder);
        when(builder.post(any(Entity.class))).thenReturn(response);
        when(response.getStatus()).thenReturn(400);

        assertThrows(RuntimeException.class, () -> hyperVergeClient.fetchAadhaar(request));
        verify(response).close();
    }
}