package com.flipkart.fintech.pandora.service.client.interceptors;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import javax.ws.rs.ext.ReaderInterceptorContext;
import org.junit.jupiter.api.Test;

class EncryptionInterceptorTest {

  @Test
  void test_encryption_gives_correct_bytes() throws IOException {
//    byte[] originalPayload = "{\"key\":\"value\"}".getBytes();
//    ReaderInterceptorContext context = mock(ReaderInterceptorContext.class);
//    when(context.getInputStream()).thenReturn(new ByteArrayInputStream(originalPayload));
//    EncryptionInterceptor interceptor = new EncryptionInterceptor();
//    interceptor.aroundReadFrom(context);
//    byte[] encryptedContent = new byte[originalPayload.length];
//    context.getInputStream().read(encryptedContent);
//    assertArrayEquals(originalToEncrypted(originalPayload), encryptedContent);
  }

}