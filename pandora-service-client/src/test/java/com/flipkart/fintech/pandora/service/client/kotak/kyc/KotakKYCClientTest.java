//package com.flipkart.fintech.pandora.service.client.kotak.kyc;
//
//import com.fasterxml.jackson.core.JsonProcessingException;
//import com.fasterxml.jackson.core.type.TypeReference;
//import com.fasterxml.jackson.databind.ObjectMapper;
//import com.flipkart.fintech.pandora.service.client.PandoraServiceClientException;
//import com.flipkart.fintech.pandora.service.client.config.KotakClientConfiguration;
//import com.flipkart.fintech.pandora.service.client.helper.ConfigHelper;
//import com.flipkart.fintech.pandora.service.client.helper.TestHelper;
//import com.flipkart.fintech.pandora.service.client.kotak.EncryptionUtility;
//import com.flipkart.fintech.pandora.service.client.kotak.common.KYCFunction;
//import com.flipkart.fintech.pandora.service.client.kotak.requests.*;
//import com.flipkart.fintech.pandora.service.client.kotak.responses.*;
//import org.junit.Assert;
//import org.junit.Before;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.Mock;
//import org.mockito.Mockito;
//import org.mockito.MockitoAnnotations;
//import org.mockito.runners.MockitoJUnitRunner;
//
//import javax.ws.rs.client.Client;
//import javax.ws.rs.client.Invocation;
//import javax.ws.rs.client.WebTarget;
//import javax.ws.rs.core.Response;
//import java.security.GeneralSecurityException;
//
//@RunWith(MockitoJUnitRunner.class)
//public class KotakKYCClientTest {
//
//    @Mock
//    private Client mockedClient;
//    @Mock
//    private EncryptionUtility encryptionUtility;
//    private KYCClient kycClient;
//    @Mock
//    private Response mockedResponse;
//    @Mock
//    private WebTarget mockedWebTarget;
//    @Mock
//    private Invocation.Builder mockedBuilder;
//    private TestHelper testHelper;
//    private ObjectMapper objectMapper;
//
//    @Before
//    public void setUp() throws JsonProcessingException, GeneralSecurityException {
//        MockitoAnnotations.initMocks(this);
//        ConfigHelper configHelper = new ConfigHelper();
//        testHelper = new TestHelper();
//        KotakClientConfiguration kotakClientConfiguration = configHelper.getKotakClientConfiguration();
//        objectMapper = new ObjectMapper();
//        Mockito.when(mockedClient.target(Mockito.anyString())).thenReturn(mockedWebTarget);
//        Mockito.when(mockedWebTarget.path(Mockito.anyString())).thenReturn(mockedWebTarget);
//        Mockito.when(mockedBuilder.headers(Mockito.any())).thenReturn(mockedBuilder);
//        Mockito.when(mockedWebTarget.request()).thenReturn(mockedBuilder);
//        Mockito.when(mockedBuilder.post(Mockito.any())).thenReturn(mockedResponse);
//        kycClient = new KotakKYCClient(mockedClient, kotakClientConfiguration, objectMapper, encryptionUtility);
//        Mockito.when(encryptionUtility.encrypt(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn("testEncryptedRequestString");
//        Mockito.when(mockedResponse.readEntity(String.class)).thenReturn("testEncryptedResponse");
//    }
//
//    @Test
//    public void testPanValidationExecutionSuccess() throws Exception {
//        Mockito.when(encryptionUtility.decrypt(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(
//                "{\"uniqueIdentifier\":\"9312347258554203200088\",\"status\":\"Success\",\"sfUniqueIdentifier\":\"SF0000000226\",\"preApproved\":\"Yes\",\"PANName\":\"ARUN KUMAR GOPU\",\"messageCode\":\"BCIF-07\",\"message\":\"Account type not equal to 1\",\"custType\":\"\",\"apiResult\":\"BCIF Found\"}");
//        Mockito.when(mockedResponse.getStatus()).thenReturn(200);
//        String accessToken = "testToken";
//        PanNumberVerificationRequest panNumberVerificationRequest = testHelper.getPanNumberVerificationRequest();
//        PanValidationResponse receivedPanValidationResponse = kycClient.executeKYCFunction(KYCFunction.VALIDATE_PAN,
//                panNumberVerificationRequest.getUniqueIdentifier(), objectMapper.writeValueAsString(panNumberVerificationRequest),
//                accessToken, new TypeReference<PanValidationResponse>() {
//                });
//        Assert.assertNotNull(receivedPanValidationResponse);
//    }
//
//    @Test(expected = PandoraServiceClientException.class)
//    public void testKYCFunctionFailureDueToNonSuccessResponse() throws Exception {
//        Mockito.when(encryptionUtility.decrypt(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn("{\"uniqueIdentifier\":\"9312347258554203200088\",\"status\":\"Success\",\"sfUniqueIdentifier\":\"SF0000000226\",\"preApproved\":\"Yes\",\"PANName\":\"TEST NAME\",\"messageCode\":\"BCIF-07\",\"message\":\"Account type not equal to 1\",\"custType\":\"\",\"apiResult\":\"BCIF Found\"}");
//        Mockito.when(encryptionUtility.encrypt(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn("testEncryptedRequestString");
//        Mockito.when(mockedResponse.getStatus()).thenReturn(403);
//        Mockito.when(mockedResponse.readEntity(String.class)).thenReturn("Invalid Access Token");
//        String accessToken = "testToken";
//        PanNumberVerificationRequest panNumberVerificationRequest = testHelper.getPanNumberVerificationRequest();
//        kycClient.executeKYCFunction(KYCFunction.VALIDATE_PAN, panNumberVerificationRequest.getUniqueIdentifier(),
//                objectMapper.writeValueAsString(panNumberVerificationRequest), accessToken, new TypeReference<PanValidationResponse>() {
//                });
//    }
//
//
//    @Test
//    public void testCKYCSearchExecutionSuccess() throws Exception {
//        CKYCSearchRequest ckycSearchRequest = testHelper.getCKYCSearchRequest();
//        Mockito.when(encryptionUtility.decrypt(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(
//                "{\"uniqueIdentifier\":\"9300000000000000000088\",\"status\":\"Success\",\"sfUniqueIdentifier\":\"SF0000008721\",\"apiResult\":\"A50Success\",\"messageCode\":\"KM-01\",\"message\":\"ProceedtoEKYC\"}");
//        Mockito.when(mockedResponse.getStatus()).thenReturn(200);
//        String accessToken = "testToken";
//        CKYCSearchResponse ckycSearchResponse = kycClient.executeKYCFunction(KYCFunction.CKYC_SEARCH, ckycSearchRequest.getUniqueIdentifier(),
//                objectMapper.writeValueAsString(ckycSearchRequest)
//                , accessToken, new TypeReference<CKYCSearchResponse>() {
//                });
//        Assert.assertNotNull(ckycSearchResponse);
//    }
//
//    @Test
//    public void testSendOTPSuccess() throws Exception {
//        AadhaarOTPGenerationRequest aadhaarOTPGenerationRequest = testHelper.getAadhaarOTPGenerationRequest();
//        Mockito.when(mockedResponse.getStatus()).thenReturn(200);
//        Mockito.when(encryptionUtility.decrypt(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(
//                "{\"uniqueIdentifier\":\"9300000000000000000088\",\"status\":\"Success\",\"apiResult\":\"EkycSendOTPSuccess\",\"ResCode\":\"OTP-01\",\"ResMsg\":\"ProceedtoEKYC\"}");
//        String accessToken = "testToken";
//        AadhaarOTPGenerationResponse aadhaarOTPGenerationResponse = kycClient.executeKYCFunction(KYCFunction.GENERATE_AADHAAR_OTP,
//                aadhaarOTPGenerationRequest.getUniqueIdentifier(), objectMapper.writeValueAsString(aadhaarOTPGenerationRequest)
//                , accessToken, new TypeReference<AadhaarOTPGenerationResponse>() {
//                });
//        Assert.assertNotNull(aadhaarOTPGenerationResponse);
//    }
//
//    @Test
//    public void testVerifyOTPSuccess() throws Exception {
//        AadhaarOTPVerificationRequest aadhaarOTPVerificationRequest = testHelper.getAadhaarOTPVerificationRequest();
//        Mockito.when(mockedResponse.getStatus()).thenReturn(200);
//        Mockito.when(encryptionUtility.decrypt(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn("{\"uniqueIdentifier\":\"9300000000000000000088\",\"status\":\"Success\",\"apiResult\":\"EkycCompletedSuccessfully\",\"responseCode\":\"EKYC-01\",\"responseMsg\":\"Success\",\"Name\":\"Mangesh\",\"Gender\":\"M\",\"District\":\"Mumbai\",\"Age\":\"20\",\"House\":\"ROOMNO.479MAHADANO.II-C-639\",\"Landmark\":\"NEARBABASAHEBAMBEDKARGROUND\",\"Locality\":\"DHARAVI\",\"Name\":\"SandeshSaduShinde\",\"PinCode\":\"400017\",\"State\":\"Maharashtra\",\"Street\":\"SHAHIDBHAGATSINGHNAGAR\"}");
//        String accessToken = "testToken";
//        AadhaarOTPVerificationResponse aadhaarOTPVerificationResponse = kycClient.executeKYCFunction(KYCFunction.VALIDATE_AADHAAR_OTP,
//                aadhaarOTPVerificationRequest.getUniqueIdentifier(), objectMapper.writeValueAsString(aadhaarOTPVerificationRequest),
//                accessToken, new TypeReference<AadhaarOTPVerificationResponse>() {
//                });
//        Assert.assertNotNull(aadhaarOTPVerificationResponse);
//    }
//
//    @Test
//    public void testCKYCDownloadSuccess() throws Exception {
//        CKYCDownloadRequest ckycDownloadRequest = testHelper.getCKYCDownloadRequest();
//        Mockito.when(mockedResponse.getStatus()).thenReturn(200);
//        Mockito.when(encryptionUtility.decrypt(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).
//                thenReturn("{\"uniqueIdentifier\":\"9300000000000000000088\",\"status\":\"Success\",\"apiResult\":\"Success\",\"responseCode\":\"CKYC-01\",\"responseMsg\":\"Success\",\"ckycName\":\"VIJAYSINGH\",\"ckycfatherName\":\"DinanatSingh\",\"ckycid\":\"60020459305457\",\"dob\":\"01-06-1963\",\"ckycgender\":\"M\",\"ckyccorAdd1\":\"QtrNo6AStreetNo87Sector\",\"ckyccorAdd2\":\"6BhilaiDurg\",\"ckyccorAdd3\":\"\",\"CKYCCorAddCity\":\"\",\"CKYCCorAddDistrict\":\"\",\"CKYCCorAddState\":\"\",\"CKYCCorAddCountry\":\"\",\"CKYCCorAddPin\":\"\"}");
//        String accessToken = "testToken";
//        CKYCDownloadResponse ckycDownloadResponse = kycClient.executeKYCFunction(KYCFunction.CKYC_DOWNLOAD, ckycDownloadRequest.getUniqueIdentifier(),
//                objectMapper.writeValueAsString(ckycDownloadRequest),
//                accessToken, new TypeReference<CKYCDownloadResponse>() {
//                });
//        Assert.assertNotNull(ckycDownloadResponse);
//    }
//}
