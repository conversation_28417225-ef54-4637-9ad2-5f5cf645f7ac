//package com.flipkart.fintech.pandora.service.client.kotak.loan_activation;
//
//import com.fasterxml.jackson.core.JsonProcessingException;
//import com.fasterxml.jackson.core.type.TypeReference;
//import com.fasterxml.jackson.databind.ObjectMapper;
//import com.flipkart.fintech.pandora.service.client.PandoraServiceClientException;
//import com.flipkart.fintech.pandora.service.client.config.KotakClientConfiguration;
//import com.flipkart.fintech.pandora.service.client.helper.ConfigHelper;
//import com.flipkart.fintech.pandora.service.client.helper.TestHelper;
//import com.flipkart.fintech.pandora.service.client.kotak.EncryptionUtility;
//import com.flipkart.fintech.pandora.service.client.kotak.common.LoanActivationFunction;
//import com.flipkart.fintech.pandora.service.client.kotak.requests.BRECheckRequest;
//import com.flipkart.fintech.pandora.service.client.kotak.requests.CheckEligibilityRequest;
//import com.flipkart.fintech.pandora.service.client.kotak.requests.CreateLoanRequest;
//import com.flipkart.fintech.pandora.service.client.kotak.responses.BRECheckResponse;
//import com.flipkart.fintech.pandora.service.client.kotak.responses.CheckEligibilityResponse;
//import com.flipkart.fintech.pandora.service.client.kotak.responses.LoanCreationResponse;
//import org.junit.Assert;
//import org.junit.Before;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.Mock;
//import org.mockito.Mockito;
//import org.mockito.MockitoAnnotations;
//import org.mockito.runners.MockitoJUnitRunner;
//
//import javax.ws.rs.client.Client;
//import javax.ws.rs.client.Invocation;
//import javax.ws.rs.client.WebTarget;
//import javax.ws.rs.core.Response;
//import java.security.GeneralSecurityException;
//
//@RunWith(MockitoJUnitRunner.class)
//public class LoanActivationClientTest {
//    @Mock
//    private Client mockedClient;
//    @Mock
//    private EncryptionUtility encryptionUtility;
//    private LoanActivationClient loanActivationClient;
//    @Mock
//    private Response mockedResponse;
//    @Mock
//    private WebTarget mockedWebTarget;
//    @Mock
//    private Invocation.Builder mockedBuilder;
//    private TestHelper testHelper;
//    private ObjectMapper objectMapper;
//
//    @Before
//    public void setUp() throws JsonProcessingException, GeneralSecurityException {
//        MockitoAnnotations.initMocks(this);
//        ConfigHelper configHelper = new ConfigHelper();
//        testHelper = new TestHelper();
//        KotakClientConfiguration kotakClientConfiguration = configHelper.getKotakClientConfiguration();
//        objectMapper = new ObjectMapper();
//        Mockito.when(mockedClient.target(Mockito.anyString())).thenReturn(mockedWebTarget);
//        Mockito.when(mockedWebTarget.path(Mockito.anyString())).thenReturn(mockedWebTarget);
//        Mockito.when(mockedBuilder.headers(Mockito.any())).thenReturn(mockedBuilder);
//        Mockito.when(mockedWebTarget.request()).thenReturn(mockedBuilder);
//        Mockito.when(mockedBuilder.post(Mockito.any())).thenReturn(mockedResponse);
//        loanActivationClient = new KotakLoanActivationClient(mockedClient, kotakClientConfiguration, objectMapper, encryptionUtility);
//        Mockito.when(encryptionUtility.encrypt(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn("testEncryptedRequestString");
//        Mockito.when(mockedResponse.readEntity(String.class)).thenReturn("testEncryptedResponse");
//    }
//
//    @Test
//    public void testCheckEligibilitySuccess() throws Exception {
//        CheckEligibilityRequest checkEligibilityRequest = testHelper.getCheckEligibilityRequest();
//        Mockito.when(mockedResponse.getStatus()).thenReturn(200);
//        Mockito.when(encryptionUtility.decrypt(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn("{\"uniqueIdentifier\":\"9300000000000000000088\",\"status\":\"Success\",\"apiResult\":\"NameMatchlessthanThreshold\",\"ResCode\":\"NAMEMATCH-01\",\"ResMsg\":\"NameMatchlessthanThreshold\",\"NameMatchScore\":\"56\"}");
//        String accessToken = "testToken";
//        CheckEligibilityResponse checkEligibilityResponse = loanActivationClient.executionLoanActivationFunction(LoanActivationFunction.CHECK_ELIGIBILITY,
//                checkEligibilityRequest.getUniqueIdentifier(), objectMapper.writeValueAsString(checkEligibilityRequest),
//                accessToken, new TypeReference<CheckEligibilityResponse>() {
//                });
//        Assert.assertNotNull(checkEligibilityResponse);
//    }
//
//    @Test
//    public void testBRECheckSuccess() throws Exception {
//        BRECheckRequest breCheckRequest = testHelper.getBRECheckRequest();
//        Mockito.when(mockedResponse.getStatus()).thenReturn(200);
//        Mockito.when(encryptionUtility.decrypt(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn("{\"uniqueIdentifier\":\"9300000000000000000088\",\"status\":\"Success\",\"apiResult\":\"Success\",\"responseCode\":\"BRE-01\",\"responseMsg\":\"BREApproved\",\"bureauscore\":\"750\",\"ApprovedLimit\":\"60000\",\"Reason\":\"APPROVEDASPERNTBPOLICY\"}");
//        String accessToken = "testToken";
//        BRECheckResponse breCheckResponse = loanActivationClient.executionLoanActivationFunction(LoanActivationFunction.BRE_CHECK,
//                breCheckRequest.getUniqueIdentifier(), objectMapper.writeValueAsString(breCheckRequest), accessToken,
//                new TypeReference<BRECheckResponse>() {
//                });
//        Assert.assertNotNull(breCheckResponse);
//    }
//
//    @Test
//    public void testCreateLoanSuccess() throws Exception {
//        CreateLoanRequest createLoanRequest = testHelper.getCreateLoanRequest();
//        Mockito.when(mockedResponse.getStatus()).thenReturn(200);
//        Mockito.when(encryptionUtility.decrypt(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn("{\"uniqueIdentifier\":\"9300000000000000000088\",\"status\":\"Success\",\"apiResult\":\"Success\",\"responseCode\":\"ETBPAPQ-01\",\"responseMsg\":\"ETBCustomerandPAPQlimitedupdated/insertedsuccessfully\",\"Reason\":\"ETBCustomerandPAPQlimitedupdated/insertedsuccessfully\",\"custType\":\"ETB\",\"CRN\":\"1090234567\",\"LMSIdentifier\":\"756735437234\"}");
//        String accessToken = "testToken";
//        LoanCreationResponse loanCreationResponse = loanActivationClient.executionLoanActivationFunction(LoanActivationFunction.LOAN_CREATION,
//                createLoanRequest.getUniqueIdentifier(), objectMapper.writeValueAsString(createLoanRequest), accessToken, new TypeReference<LoanCreationResponse>() {
//                });
//        Assert.assertNotNull(loanCreationResponse);
//    }
//
//    @Test(expected = PandoraServiceClientException.class)
//    public void testLoanActivationFailure() throws Exception {
//        CreateLoanRequest createLoanRequest = testHelper.getCreateLoanRequest();
//        Mockito.when(mockedResponse.getStatus()).thenReturn(403);
//        Mockito.when(encryptionUtility.decrypt(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn("{\"uniqueIdentifier\":\"9300000000000000000088\",\"status\":\"Success\",\"apiResult\":\"Success\",\"responseCode\":\"ETBPAPQ-01\",\"responseMsg\":\"ETBCustomerandPAPQlimitedupdated/insertedsuccessfully\",\"Reason\":\"ETBCustomerandPAPQlimitedupdated/insertedsuccessfully\",\"custType\":\"ETB\",\"CRN\":\"1090234567\",\"LMSIdentifier\":\"756735437234\"}");
//        String accessToken = "testToken";
//        Mockito.when(mockedResponse.readEntity(String.class)).thenReturn("Invalid Access Token");
//        loanActivationClient.executionLoanActivationFunction(LoanActivationFunction.LOAN_CREATION,
//                createLoanRequest.getUniqueIdentifier(), objectMapper.writeValueAsString(createLoanRequest), accessToken,
//                new TypeReference<LoanCreationResponse>() {
//                });
//    }
//}
