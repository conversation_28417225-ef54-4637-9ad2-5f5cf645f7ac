//package com.flipkart.fintech.pandora.service.client.kotak.penny_drop;
//
//import com.fasterxml.jackson.core.JsonProcessingException;
//import com.fasterxml.jackson.databind.ObjectMapper;
//import com.flipkart.fintech.pandora.service.client.PandoraServiceClientException;
//import com.flipkart.fintech.pandora.service.client.config.KotakClientConfiguration;
//import com.flipkart.fintech.pandora.service.client.helper.ConfigHelper;
//import com.flipkart.fintech.pandora.service.client.helper.TestHelper;
//import com.flipkart.fintech.pandora.service.client.kotak.EncryptionUtility;
//import com.flipkart.fintech.pandora.service.client.kotak.requests.PennyDropRequest;
//import com.flipkart.fintech.pandora.service.client.kotak.responses.PennyDropResponse;
//import org.junit.Assert;
//import org.junit.Before;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.Mock;
//import org.mockito.Mockito;
//import org.mockito.MockitoAnnotations;
//import org.mockito.runners.MockitoJUnitRunner;
//
//import javax.ws.rs.client.Client;
//import javax.ws.rs.client.Invocation;
//import javax.ws.rs.client.WebTarget;
//import javax.ws.rs.core.Response;
//import java.security.GeneralSecurityException;
//
//@RunWith(MockitoJUnitRunner.class)
//public class PennyDropClientTest {
//    @Mock
//    private Client mockedClient;
//    @Mock
//    private EncryptionUtility encryptionUtility;
//    private PennyDropClient pennyDropClient;
//    @Mock
//    private Response mockedResponse;
//    @Mock
//    private WebTarget mockedWebTarget;
//    @Mock
//    private Invocation.Builder mockedBuilder;
//    private TestHelper testHelper;
//
//    @Before
//    public void setUp() throws JsonProcessingException, GeneralSecurityException {
//        MockitoAnnotations.initMocks(this);
//        ConfigHelper configHelper = new ConfigHelper();
//        testHelper = new TestHelper();
//        KotakClientConfiguration kotakClientConfiguration = configHelper.getKotakClientConfiguration();
//        ObjectMapper objectMapper = new ObjectMapper();
//        Mockito.when(mockedClient.target(Mockito.anyString())).thenReturn(mockedWebTarget);
//        Mockito.when(mockedWebTarget.path(Mockito.anyString())).thenReturn(mockedWebTarget);
//        Mockito.when(mockedBuilder.headers(Mockito.any())).thenReturn(mockedBuilder);
//        Mockito.when(mockedWebTarget.request()).thenReturn(mockedBuilder);
//        Mockito.when(mockedBuilder.post(Mockito.any())).thenReturn(mockedResponse);
//        pennyDropClient = new KotakPennyDropClient(mockedClient, kotakClientConfiguration, objectMapper, encryptionUtility);
//        Mockito.when(encryptionUtility.encrypt(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn("testEncryptedRequestString");
//        Mockito.when(mockedResponse.readEntity(String.class)).thenReturn("testEncryptedResponse");
//    }
//
//    @Test
//    public void testPennyDropSuccess() throws Exception {
//        PennyDropRequest pennyDropRequest = testHelper.getPennyDropRequest();
//        Mockito.when(mockedResponse.getStatus()).thenReturn(200);
//        Mockito.when(encryptionUtility.decrypt(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn("{\"uniqueIdentifier\":\"9300000000000000000088\",\"status\":\"Success\",\"apiResult\":\"Success\",\"responseCode\":\"PENNYDROP-01\",\"responseMsg\":\"PennyDropandNameMatchSuccessful\",\"bankaccountname\":\"RahulAnilMishra\",\"bankaccounnameMatch\":\"78\",\"PennyDroptries\":2}");
//        String accessToken = "testToken";
//        PennyDropResponse pennyDropResponse = pennyDropClient.pennyDrop(pennyDropRequest, accessToken);
//        Assert.assertNotNull(pennyDropResponse);
//    }
//
//    @Test(expected = PandoraServiceClientException.class)
//    public void testPennyDropFailure() throws Exception {
//        PennyDropRequest pennyDropRequest = testHelper.getPennyDropRequest();
//        Mockito.when(mockedResponse.getStatus()).thenReturn(403);
//        Mockito.when(mockedResponse.readEntity(String.class)).thenReturn("Invalid access token");
//        Mockito.when(encryptionUtility.decrypt(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn("{\"uniqueIdentifier\":\"9300000000000000000088\",\"status\":\"Success\",\"apiResult\":\"Success\",\"responseCode\":\"PENNYDROP-01\",\"responseMsg\":\"PennyDropandNameMatchSuccessful\",\"bankaccountname\":\"RahulAnilMishra\",\"bankaccounnameMatch\":\"78\",\"PennyDroptries\":2}");
//        String accessToken = "testToken";
//        pennyDropClient.pennyDrop(pennyDropRequest, accessToken);
//    }
//}
