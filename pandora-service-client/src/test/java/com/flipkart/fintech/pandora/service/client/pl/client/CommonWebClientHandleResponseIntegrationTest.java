package com.flipkart.fintech.pandora.service.client.pl.client;

import com.flipkart.fintech.pandora.service.client.auth.AccessTokenProvider;
import com.flipkart.fintech.pandora.service.client.auth.Scope;
import com.flipkart.fintech.pandora.service.client.exceptions.LenderException;
import com.flipkart.fintech.pandora.service.client.mapper.sandbox.ErrorMapper;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import javax.ws.rs.ClientErrorException;
import javax.ws.rs.InternalServerErrorException;
import javax.ws.rs.core.Response;
import java.util.EnumMap;
import java.util.Map;

import static org.mockito.Mockito.*;
import static org.testng.Assert.*;

/**
 * Integration tests for CommonWebClient.handleResponse method.
 * These tests use the real ObjectMapperUtil instead of mocking it.
 */
public class CommonWebClientHandleResponseIntegrationTest {

    @Mock
    private AccessTokenProvider accessTokenProvider;

    @Mock
    private Response response;

    @Mock
    private Response.StatusType statusType;

    @Mock
    private ErrorMapper errorMapper;

    private CommonWebClient commonWebClient;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        Map<Scope, ErrorMapper> scopeErrorMapperMap = new EnumMap<>(Scope.class);
        scopeErrorMapperMap.put(Scope.LENDING_FIBE, errorMapper);
        commonWebClient = new CommonWebClient(accessTokenProvider, scopeErrorMapperMap);
    }

    @Test
    public void handleResponse_SuccessfulResponse_SingleObject() throws Exception {
        // Arrange
        String responseBody = "{\"key\":\"value\"}";

        when(response.readEntity(String.class)).thenReturn(responseBody);
        when(response.getStatus()).thenReturn(200);
        when(response.getStatusInfo()).thenReturn(statusType);
        when(statusType.getFamily()).thenReturn(Response.Status.Family.SUCCESSFUL);

        // Act
        TestResponse result = commonWebClient.handleResponse(response, TestResponse.class, Scope.LENDING_FIBE);

        // Assert
        assertNotNull(result);
        assertEquals("value", result.getKey());
        verify(response).readEntity(String.class);
        verify(response, atLeastOnce()).getStatusInfo();
    }

    @Test
    public void handleResponse_SuccessfulResponse_ArrayWithSingleElement() throws Exception {
        // Arrange
        String responseBody = "[{\"key\":\"value\"}]";

        when(response.readEntity(String.class)).thenReturn(responseBody);
        when(response.getStatus()).thenReturn(200);
        when(response.getStatusInfo()).thenReturn(statusType);
        when(statusType.getFamily()).thenReturn(Response.Status.Family.SUCCESSFUL);

        // Act
        TestResponse result = commonWebClient.handleResponse(response, TestResponse.class, Scope.LENDING_FIBE);

        // Assert
        assertNotNull(result);
        assertEquals("value", result.getKey());
        verify(response).readEntity(String.class);
        verify(response, atLeastOnce()).getStatusInfo();
    }

    @Test
    public void handleResponse_SuccessfulResponse_EmptyJsonObject() throws Exception {
        // Arrange
        String responseBody = "{}";

        when(response.readEntity(String.class)).thenReturn(responseBody);
        when(response.getStatus()).thenReturn(200);
        when(response.getStatusInfo()).thenReturn(statusType);
        when(statusType.getFamily()).thenReturn(Response.Status.Family.SUCCESSFUL);

        // Act
        TestResponse result = commonWebClient.handleResponse(response, TestResponse.class, Scope.LENDING_FIBE);

        // Assert
        assertNotNull(result);
        assertNull(result.getKey());
        verify(response).readEntity(String.class);
        verify(response, atLeastOnce()).getStatusInfo();
    }

    @Test
    public void handleResponse_SuccessfulResponse_InvalidJson() throws LenderException {
        // Arrange
        String responseBody = "invalid json";

        when(response.readEntity(String.class)).thenReturn(responseBody);
        when(response.getStatus()).thenReturn(200);
        when(response.getStatusInfo()).thenReturn(statusType);
        when(statusType.getFamily()).thenReturn(Response.Status.Family.SUCCESSFUL);

        // Act & Assert
        try {
            commonWebClient.handleResponse(response, TestResponse.class, Scope.LENDING_FIBE);
            fail("Expected InternalServerErrorException but no exception was thrown");
        } catch (InternalServerErrorException exception) {
            assertEquals("Unable to parse the response received from the server", exception.getMessage());
            verify(response).readEntity(String.class);
            verify(response, atLeastOnce()).getStatusInfo();
        }
    }

    @Test
    public void handleResponse_EmptyResponseBody() throws LenderException {
        // Arrange
        String responseBody = "";

        when(response.readEntity(String.class)).thenReturn(responseBody);
        when(response.getStatus()).thenReturn(200);
        when(response.getStatusInfo()).thenReturn(statusType);
        when(statusType.getFamily()).thenReturn(Response.Status.Family.SUCCESSFUL);

        // Act
        TestResponse result = commonWebClient.handleResponse(response, TestResponse.class, Scope.LENDING_FIBE);

        // Assert
        // The real ObjectMapperUtil returns null for empty strings
        assertNull(result);
        verify(response).readEntity(String.class);
        verify(response, atLeastOnce()).getStatusInfo();
    }

    @Test
    public void handleResponse_EmptyClientErrorResponse() throws LenderException {
        // Arrange
        String responseBody = "";

        when(response.readEntity(String.class)).thenReturn(responseBody);
        when(response.getStatus()).thenReturn(400);
        when(response.getStatusInfo()).thenReturn(statusType);
        when(statusType.getFamily()).thenReturn(Response.Status.Family.CLIENT_ERROR);
        when(statusType.getStatusCode()).thenReturn(400);
        when(statusType.getReasonPhrase()).thenReturn("Bad Request");

        // Act & Assert
        try {
            commonWebClient.handleResponse(response, TestResponse.class, Scope.LENDING_FIBE);
            fail("Expected ClientErrorException but no exception was thrown");
        } catch (ClientErrorException exception) {
            assertEquals("Error while handling request from client for lender LENDING_FIBE", exception.getMessage());
            assertEquals(Response.Status.BAD_REQUEST.getStatusCode(), exception.getResponse().getStatus());
            verify(response).readEntity(String.class);
            verify(response, atLeastOnce()).getStatusInfo();
        }
    }

    @Test
    public void handleResponse_EmptyServerErrorResponse() throws LenderException {
        // Arrange
        String responseBody = "";

        when(response.readEntity(String.class)).thenReturn(responseBody);
        when(response.getStatus()).thenReturn(500);
        when(response.getStatusInfo()).thenReturn(statusType);
        when(statusType.getFamily()).thenReturn(Response.Status.Family.SERVER_ERROR);
        when(statusType.getStatusCode()).thenReturn(500);
        when(statusType.getReasonPhrase()).thenReturn("Internal Server Error");

        // Act & Assert
        try {
            commonWebClient.handleResponse(response, TestResponse.class, Scope.LENDING_FIBE);
            fail("Expected InternalServerErrorException but no exception was thrown");
        } catch (InternalServerErrorException exception) {
            assertEquals("Unexpected response from the server for lender LENDING_FIBE", exception.getMessage());
            verify(response).readEntity(String.class);
            verify(response, atLeastOnce()).getStatusInfo();
        }
    }

    @Test
    public void handleResponse_ClientErrorResponse() throws LenderException {
        // Arrange
        String responseBody = "{\"error\":\"client error\"}";

        when(response.readEntity(String.class)).thenReturn(responseBody);
        when(response.getStatus()).thenReturn(400);
        when(response.getStatusInfo()).thenReturn(statusType);
        when(statusType.getFamily()).thenReturn(Response.Status.Family.CLIENT_ERROR);
        when(statusType.getStatusCode()).thenReturn(400);
        when(statusType.getReasonPhrase()).thenReturn("Bad Request");

        // Act & Assert
        try {
            commonWebClient.handleResponse(response, TestResponse.class, Scope.LENDING_FIBE);
            fail("Expected ClientErrorException but no exception was thrown");
        } catch (ClientErrorException exception) {
            assertEquals("Error while handling request from client for lender LENDING_FIBE", exception.getMessage());
            assertEquals(Response.Status.BAD_REQUEST.getStatusCode(), exception.getResponse().getStatus());
            verify(response).readEntity(String.class);
            verify(response, atLeastOnce()).getStatusInfo();
        }
    }

    @Test
    public void handleResponse_ServerErrorResponse() throws LenderException {
        // Arrange
        String responseBody = "{\"error\":\"server error\"}";

        when(response.readEntity(String.class)).thenReturn(responseBody);
        when(response.getStatus()).thenReturn(500);
        when(response.getStatusInfo()).thenReturn(statusType);
        when(statusType.getFamily()).thenReturn(Response.Status.Family.SERVER_ERROR);
        when(statusType.getStatusCode()).thenReturn(500);
        when(statusType.getReasonPhrase()).thenReturn("Internal Server Error");

        // Act & Assert
        try {
            commonWebClient.handleResponse(response, TestResponse.class, Scope.LENDING_FIBE);
            fail("Expected InternalServerErrorException but no exception was thrown");
        } catch (InternalServerErrorException exception) {
            assertEquals("Unexpected response from the server for lender LENDING_FIBE", exception.getMessage());
            verify(response).readEntity(String.class);
            verify(response, atLeastOnce()).getStatusInfo();
        }
    }

    // Simple test class for response conversion
    private static class TestResponse {
        private String key;

        public TestResponse() {}

        public TestResponse(String key) {
            this.key = key;
        }

        public String getKey() {
            return key;
        }

        public void setKey(String key) {
            this.key = key;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            TestResponse that = (TestResponse) o;
            if (key == null) {
                return that.key == null;
            }
            return key.equals(that.key);
        }
    }
}
