package com.flipkart.fintech.pandora.service.client.pl.client;

import static org.mockito.Mockito.*;
import static org.testng.Assert.*;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.pandora.service.client.auth.AccessTokenProvider;
import com.flipkart.fintech.pandora.service.client.auth.Scope;
import com.flipkart.fintech.pandora.service.client.exceptions.LenderException;
import com.flipkart.fintech.pandora.service.client.mapper.sandbox.ErrorMapper;
import com.flipkart.fintech.pandora.service.client.utils.ObjectMapperUtil;

import java.util.EnumMap;
import java.util.Map;
import javax.ws.rs.ClientErrorException;
import javax.ws.rs.InternalServerErrorException;
import javax.ws.rs.core.Response;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

public class CommonWebClientHandleResponseTest {

  @Mock private AccessTokenProvider accessTokenProvider;

  @Mock private Response response;

  @Mock private Response.StatusType statusType;

  @Mock private ErrorMapper errorMapper;

  @Mock private ObjectMapper objectMapper;

  @Mock private JsonNode jsonNode;

  private CommonWebClient commonWebClient;

  @Before
  public void setUp() {
    MockitoAnnotations.openMocks(this);
    Map<Scope, ErrorMapper> scopeErrorMapperMap = new EnumMap<>(Scope.class);
    scopeErrorMapperMap.put(Scope.LENDING_FIBE, errorMapper);
    commonWebClient = new CommonWebClient(accessTokenProvider, scopeErrorMapperMap);
  }

  @Test
  public void handleResponse_SuccessfulResponse_SingleObject() throws Exception {
    // Arrange
    String responseBody = "{\"key\":\"value\"}";
    TestResponse expectedResponse = new TestResponse("value");

    when(response.readEntity(String.class)).thenReturn(responseBody);
    when(response.getStatus()).thenReturn(200);
    when(response.getStatusInfo()).thenReturn(statusType);
    when(statusType.getFamily()).thenReturn(Response.Status.Family.SUCCESSFUL);

    try (MockedStatic<ObjectMapperUtil> mockedObjectMapperUtil =
        Mockito.mockStatic(ObjectMapperUtil.class)) {
      mockedObjectMapperUtil.when(ObjectMapperUtil::get).thenReturn(objectMapper);
      when(objectMapper.readTree(responseBody)).thenReturn(jsonNode);
      when(jsonNode.isArray()).thenReturn(false);
      when(objectMapper.convertValue(jsonNode, TestResponse.class)).thenReturn(expectedResponse);

      // Act
      TestResponse result =
          commonWebClient.handleResponse(response, TestResponse.class, Scope.LENDING_FIBE);

      // Assert
      assertEquals(expectedResponse, result);
      verify(response).readEntity(String.class);
      verify(response, atLeastOnce()).getStatusInfo();
      verify(objectMapper).readTree(responseBody);
      verify(objectMapper).convertValue(jsonNode, TestResponse.class);
      mockedObjectMapperUtil.verify(ObjectMapperUtil::get, times(2));
    }
  }

  @Test
  public void handleResponse_SuccessfulResponse_ArrayWithSingleElement() throws Exception {
    // Arrange
    String responseBody = "[{\"key\":\"value\"}]";
    TestResponse expectedResponse = new TestResponse("value");

    when(response.readEntity(String.class)).thenReturn(responseBody);
    when(response.getStatus()).thenReturn(200);
    when(response.getStatusInfo()).thenReturn(statusType);
    when(statusType.getFamily()).thenReturn(Response.Status.Family.SUCCESSFUL);

    try (MockedStatic<ObjectMapperUtil> mockedObjectMapperUtil =
        Mockito.mockStatic(ObjectMapperUtil.class)) {
      mockedObjectMapperUtil.when(ObjectMapperUtil::get).thenReturn(objectMapper);
      when(objectMapper.readTree(responseBody)).thenReturn(jsonNode);
      when(jsonNode.isArray()).thenReturn(true);
      when(jsonNode.size()).thenReturn(1);
      when(jsonNode.get(0)).thenReturn(jsonNode);
      when(objectMapper.convertValue(jsonNode, TestResponse.class)).thenReturn(expectedResponse);

      // Act
      TestResponse result =
          commonWebClient.handleResponse(response, TestResponse.class, Scope.LENDING_FIBE);

      // Assert
      assertEquals(expectedResponse, result);
      verify(response).readEntity(String.class);
      verify(response, atLeastOnce()).getStatusInfo();
      verify(objectMapper).readTree(responseBody);
      verify(jsonNode).get(0);
      verify(objectMapper).convertValue(jsonNode, TestResponse.class);
      mockedObjectMapperUtil.verify(() -> ObjectMapperUtil.get(), times(2));
    }
  }

  @Test
  public void handleResponse_SuccessfulResponse_InvalidJson()
      throws JsonProcessingException, LenderException {
    // Arrange
    String responseBody = "invalid json";

    when(response.readEntity(String.class)).thenReturn(responseBody);
    when(response.getStatus()).thenReturn(200);
    when(response.getStatusInfo()).thenReturn(statusType);
    when(statusType.getFamily()).thenReturn(Response.Status.Family.SUCCESSFUL);

    try (MockedStatic<ObjectMapperUtil> mockedObjectMapperUtil =
        Mockito.mockStatic(ObjectMapperUtil.class)) {
      mockedObjectMapperUtil.when(ObjectMapperUtil::get).thenReturn(objectMapper);
      when(objectMapper.readTree(responseBody))
          .thenThrow(new JsonProcessingException("Invalid JSON") {});

      // Act & Assert
      try {
        commonWebClient.handleResponse(response, TestResponse.class, Scope.LENDING_FIBE);
        fail("Expected InternalServerErrorException but no exception was thrown");
      } catch (InternalServerErrorException exception) {
        assertEquals(
            "Unable to parse the response received from the server", exception.getMessage());
        verify(response).readEntity(String.class);
        verify(response, atLeastOnce()).getStatusInfo();
        verify(objectMapper).readTree(responseBody);
        mockedObjectMapperUtil.verify(() -> ObjectMapperUtil.get(), times(1));
      }
    }
  }

  @Test
  public void handleResponse_SuccessfulResponse_EmptyJsonObject() throws Exception {
    // Arrange
    String responseBody = "{}";
    TestResponse expectedResponse = new TestResponse(null);

    when(response.readEntity(String.class)).thenReturn(responseBody);
    when(response.getStatus()).thenReturn(200);
    when(response.getStatusInfo()).thenReturn(statusType);
    when(statusType.getFamily()).thenReturn(Response.Status.Family.SUCCESSFUL);

    try (MockedStatic<ObjectMapperUtil> mockedObjectMapperUtil =
        Mockito.mockStatic(ObjectMapperUtil.class)) {
      mockedObjectMapperUtil.when(ObjectMapperUtil::get).thenReturn(objectMapper);
      when(objectMapper.readTree(responseBody)).thenReturn(jsonNode);
      when(jsonNode.isArray()).thenReturn(false);
      when(objectMapper.convertValue(jsonNode, TestResponse.class)).thenReturn(expectedResponse);

      // Act
      TestResponse result =
          commonWebClient.handleResponse(response, TestResponse.class, Scope.LENDING_FIBE);

      // Assert
      assertEquals(expectedResponse, result);
      verify(response).readEntity(String.class);
      verify(response, atLeastOnce()).getStatusInfo();
      verify(objectMapper).readTree(responseBody);
      verify(objectMapper).convertValue(jsonNode, TestResponse.class);
      mockedObjectMapperUtil.verify(() -> ObjectMapperUtil.get(), times(2));
    }
  }

  @Test
  public void handleResponse_EmptyResponseBody() throws LenderException, JsonProcessingException {
    // Arrange
    String responseBody = "";

    when(response.readEntity(String.class)).thenReturn(responseBody);
    when(response.getStatus()).thenReturn(200);
    when(response.getStatusInfo()).thenReturn(statusType);
    when(statusType.getFamily()).thenReturn(Response.Status.Family.SUCCESSFUL);

    try (MockedStatic<ObjectMapperUtil> mockedObjectMapperUtil =
        Mockito.mockStatic(ObjectMapperUtil.class)) {
      mockedObjectMapperUtil.when(ObjectMapperUtil::get).thenReturn(objectMapper);
      // Mock the behavior to return an empty JsonNode for empty strings
      when(objectMapper.readTree(responseBody)).thenReturn(jsonNode);
      when(jsonNode.isArray()).thenReturn(false);
      when(objectMapper.convertValue(jsonNode, TestResponse.class)).thenReturn(null);

      // Act
      TestResponse result = commonWebClient.handleResponse(response, TestResponse.class, Scope.LENDING_FIBE);

      // Assert
      assertNull(result);
      verify(response).readEntity(String.class);
      verify(response, atLeastOnce()).getStatusInfo();
      verify(objectMapper).readTree(responseBody);
      verify(jsonNode).isArray();
      verify(objectMapper).convertValue(jsonNode, TestResponse.class);
      mockedObjectMapperUtil.verify(() -> ObjectMapperUtil.get(), times(2));
    }
  }

  @Test
  public void handleResponse_ClientErrorResponse() throws LenderException {
    // Arrange
    String responseBody = "{\"error\":\"client error\"}";

    when(response.readEntity(String.class)).thenReturn(responseBody);
    when(response.getStatus()).thenReturn(400);
    when(response.getStatusInfo()).thenReturn(statusType);
    when(statusType.getFamily()).thenReturn(Response.Status.Family.CLIENT_ERROR);
    when(statusType.getStatusCode()).thenReturn(400);
    when(statusType.getReasonPhrase()).thenReturn("Bad Request");

    // Act & Assert
    try {
      commonWebClient.handleResponse(response, TestResponse.class, Scope.LENDING_FIBE);
      fail("Expected ClientErrorException but no exception was thrown");
    } catch (ClientErrorException exception) {
      assertEquals(
          "Error while handling request from client for lender LENDING_FIBE",
          exception.getMessage());
      assertEquals(
          Response.Status.BAD_REQUEST.getStatusCode(), exception.getResponse().getStatus());
      verify(response).readEntity(String.class);
      verify(response, atLeastOnce()).getStatusInfo();
    }
  }

  @Test
  public void handleResponse_EmptyClientErrorResponse() throws LenderException {
    // Arrange
    String responseBody = "";

    when(response.readEntity(String.class)).thenReturn(responseBody);
    when(response.getStatus()).thenReturn(400);
    when(response.getStatusInfo()).thenReturn(statusType);
    when(statusType.getFamily()).thenReturn(Response.Status.Family.CLIENT_ERROR);
    when(statusType.getStatusCode()).thenReturn(400);
    when(statusType.getReasonPhrase()).thenReturn("Bad Request");

    // Act & Assert
    try {
      commonWebClient.handleResponse(response, TestResponse.class, Scope.LENDING_FIBE);
      fail("Expected ClientErrorException but no exception was thrown");
    } catch (ClientErrorException exception) {
      assertEquals(
          "Error while handling request from client for lender LENDING_FIBE",
          exception.getMessage());
      assertEquals(
          Response.Status.BAD_REQUEST.getStatusCode(), exception.getResponse().getStatus());
      verify(response).readEntity(String.class);
      verify(response, atLeastOnce()).getStatusInfo();
      // No ObjectMapperUtil.get() calls in client error case
    }
  }

  @Test
  public void handleResponse_EmptyServerErrorResponse() throws LenderException {
    // Arrange
    String responseBody = "";

    when(response.readEntity(String.class)).thenReturn(responseBody);
    when(response.getStatus()).thenReturn(500);
    when(response.getStatusInfo()).thenReturn(statusType);
    when(statusType.getFamily()).thenReturn(Response.Status.Family.SERVER_ERROR);
    when(statusType.getStatusCode()).thenReturn(500);
    when(statusType.getReasonPhrase()).thenReturn("Internal Server Error");

    // Act & Assert
    try {
      commonWebClient.handleResponse(response, TestResponse.class, Scope.LENDING_FIBE);
      fail("Expected InternalServerErrorException but no exception was thrown");
    } catch (InternalServerErrorException exception) {
      assertEquals(
          "Unexpected response from the server for lender LENDING_FIBE", exception.getMessage());
      verify(response).readEntity(String.class);
      verify(response, atLeastOnce()).getStatusInfo();
      // No ObjectMapperUtil.get() calls in server error case
    }
  }

  @Test
  public void handleResponse_ServerErrorResponse() throws LenderException {
    // Arrange
    String responseBody = "{\"error\":\"server error\"}";

    when(response.readEntity(String.class)).thenReturn(responseBody);
    when(response.getStatus()).thenReturn(500);
    when(response.getStatusInfo()).thenReturn(statusType);
    when(statusType.getFamily()).thenReturn(Response.Status.Family.SERVER_ERROR);
    when(statusType.getStatusCode()).thenReturn(500);
    when(statusType.getReasonPhrase()).thenReturn("Internal Server Error");

    // Act & Assert
    try {
      commonWebClient.handleResponse(response, TestResponse.class, Scope.LENDING_FIBE);
      fail("Expected InternalServerErrorException but no exception was thrown");
    } catch (InternalServerErrorException exception) {
      assertEquals(
          "Unexpected response from the server for lender LENDING_FIBE", exception.getMessage());
      verify(response).readEntity(String.class);
      verify(response, atLeastOnce()).getStatusInfo();
      // No ObjectMapperUtil.get() calls in server error case
    }
  }

  // Simple test class for response conversion
  private static class TestResponse {
    private String key;

    public TestResponse() {}

    public TestResponse(String key) {
      this.key = key;
    }

    public String getKey() {
      return key;
    }

    public void setKey(String key) {
      this.key = key;
    }

    @Override
    public boolean equals(Object o) {
      if (this == o) return true;
      if (o == null || getClass() != o.getClass()) return false;
      TestResponse that = (TestResponse) o;
      if (key == null) {
        return that.key == null;
      }
      return key.equals(that.key);
    }
  }
}
