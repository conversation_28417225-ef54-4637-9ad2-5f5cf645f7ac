package com.flipkart.fintech.pandora.service.client.pl.client;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.pandora.api.model.response.uat.ApplicationResponse;
import com.flipkart.fintech.pandora.service.client.auth.AccessTokenProvider;
import com.flipkart.fintech.pandora.service.client.auth.Scope;
import com.flipkart.fintech.pandora.service.client.config.ClientConfiguration;
import com.flipkart.fintech.pandora.service.client.mapper.sandbox.ErrorMapper;
import com.flipkart.fintech.pandora.service.client.sandbox.v2.request.SubmitOfferRequest;
import com.flipkart.fintech.pandora.service.client.sandbox.v2.response.CreateApplicationResponse;
import com.flipkart.fintech.pandora.service.client.sandbox.v2.response.SubmitOfferResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.mockserver.client.MockServerClient;
import org.mockserver.integration.ClientAndServer;
import org.mockserver.model.MediaType;

import javax.ws.rs.ProcessingException;
import javax.ws.rs.client.Client;
import javax.ws.rs.client.ClientBuilder;
import javax.ws.rs.client.WebTarget;
import javax.ws.rs.core.MultivaluedHashMap;
import javax.ws.rs.core.MultivaluedMap;
import javax.ws.rs.core.Response;
import java.io.InputStream;
import java.net.SocketTimeoutException;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static com.flipkart.fintech.pandora.service.client.pl.client.CommonWebClient.X_REQUEST_ID;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockserver.model.HttpRequest.request;
import static org.mockserver.model.HttpResponse.response;


class CommonWebClientTest {

    private CommonWebClient commonWebClient;
    private ClientAndServer mockServer;
    private Client client;
    private AccessTokenProvider accessTokenProvider;
    private Map<Scope, ErrorMapper> errorMapper;

    @BeforeEach
    void setUp(){
        mockServer = ClientAndServer.startClientAndServer(1080);
        client = ClientBuilder.newClient();
        accessTokenProvider = Mockito.mock(AccessTokenProvider.class);
        commonWebClient = new CommonWebClient(accessTokenProvider, errorMapper);
    }


    @Test
    void testHandleResponseTimeout() throws JsonProcessingException {
        // Arrange: Set up a mock server that simulates a timeout (long delay)
        String body = "";

        new MockServerClient("localhost", 1080)
                .when(request().withPath("/timeout").withBody(body)
                        .withContentType(MediaType.APPLICATION_OCTET_STREAM))
                .respond(response().withDelay(TimeUnit.SECONDS, 2).withStatusCode(200).withBody(""));

        Mockito.when(accessTokenProvider.getAccessToken(Scope.LENDING_MPOCKKET)).thenReturn("dummy-token");

        ProcessingException exception = assertThrows(ProcessingException.class, () -> {
            WebTarget webTarget = client.target("http://localhost:1080/");
            ClientConfiguration clientConfiguration = ClientConfiguration.builder().readTimeout(10).build();
            SubmitOfferResponse response = commonWebClient.getPostResponse(SubmitOfferRequest.builder().build(), SubmitOfferResponse.class,"timeout", webTarget,
                    Scope.LENDING_MPOCKKET, "abc", clientConfiguration);
        });

        assertNotNull(exception.getCause(), "The cause should not be null.");
        assertTrue(exception.getCause() instanceof SocketTimeoutException,
                "Expected SocketTimeoutException but got: " + exception.getCause().getClass().getName());
    }


    @Test
    void getHeadersTest() {
        Mockito.when(accessTokenProvider.getAccessToken(Scope.LENDING_MONEYVIEW)).thenReturn("dummy-token");
        MultivaluedMap<String, Object> additionalHeaders = new MultivaluedHashMap<>();
        additionalHeaders.add("X-Client-Id", "calm");
        ClientConfiguration clientConfiguration = ClientConfiguration.builder().headers(additionalHeaders).readTimeout(2).build();
        MultivaluedMap<String, Object> headers = commonWebClient.getHeaders(Scope.LENDING_MONEYVIEW, "/", "abc", clientConfiguration);
        assertEquals("calm", headers.get("X-Client-Id").get(0));
        assertEquals("abc", headers.get(X_REQUEST_ID).get(0));
    }

    @Test
    void testHandleResponseWithPreconditionFailedAndABFLScope() throws Exception {
        String jsonResponse = "{"
                + "\"applicationStatus\": {"
                + "\"status\": \"REJECTED\","
                + "\"rejectReason\": \"BUSINESS_RULE_ERROR\""
                + "},"
                + "\"journeyState\": {"
                + "\"applicationState\": \"APPLICATION_CREATION\","
                + "\"ts\": 1746617086822466,"
                + "\"subState\": \"BUSINESS_RULE_ERROR and Customer already linked with another active journey\""
                + "}"
                + "}";

        Response mockResponse = Mockito.mock(Response.class);
        Response.StatusType mockStatusType = Mockito.mock(Response.StatusType.class);

        Mockito.when(mockResponse.getStatus()).thenReturn(412);
        Mockito.when(mockResponse.getStatusInfo()).thenReturn(mockStatusType);
        Mockito.when(mockStatusType.getFamily()).thenReturn(Response.Status.Family.OTHER);
        Mockito.when(mockStatusType.getStatusCode()).thenReturn(Response.Status.PRECONDITION_FAILED.getStatusCode());
        Mockito.when(mockResponse.readEntity(String.class)).thenReturn(jsonResponse);

        CreateApplicationResponse result = commonWebClient.handleResponse(mockResponse, CreateApplicationResponse.class, Scope.LENDING_ABFL);

        InputStream expectedResult = getClass().getClassLoader().getResourceAsStream("response/Abfl412ErrorResponseHandled.json");
        ObjectMapper objectMapper = new ObjectMapper();
        CreateApplicationResponse expectedCreateApplicationResponse = objectMapper.readValue(expectedResult, CreateApplicationResponse.class);
        assertEquals(result, expectedCreateApplicationResponse);
    }
}