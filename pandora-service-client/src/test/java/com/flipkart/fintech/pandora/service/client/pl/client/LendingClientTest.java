package com.flipkart.fintech.pandora.service.client.pl.client;

import org.junit.jupiter.api.Test;

public class LendingClientTest {
    @Test
    public void dedupeHash() {
        String testNum = "9739660088";
        String testEmail = "<EMAIL>";
        String secretSalt = "raeq2f2g7lu1uw8kyfmerkils7ldwaj4";
        String hashedNum = DedupeUtility.hashSHA256(secretSalt+testNum);
        String hashedEmail = DedupeUtility.hashSHA256(secretSalt+testEmail);
        System.out.println("hashedNum "+hashedNum + " for " + testNum);
        System.out.println("hashedEmail "+hashedEmail + " for " + testEmail);
    }
}
