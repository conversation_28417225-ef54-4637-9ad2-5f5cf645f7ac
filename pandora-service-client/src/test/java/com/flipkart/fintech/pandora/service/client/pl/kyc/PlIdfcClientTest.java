package com.flipkart.fintech.pandora.service.client.pl.kyc;

import com.flipkart.fintech.pandora.service.client.pl.client.IdfcPlClient;
import com.flipkart.fintech.pandora.service.client.pl.client.SmIdfcPlClient;
import javax.ws.rs.client.ClientBuilder;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class PlIdfcClientTest {

  private IdfcPlClient idfcClient;

  @BeforeEach
  void setUp() throws Exception {
    idfcClient = new SmIdfcPlClient(ClientBuilder.newClient(), "http://localhost:8080");
  }

  @Test
  void testSearchKYCAPI() throws Exception {

  }

}