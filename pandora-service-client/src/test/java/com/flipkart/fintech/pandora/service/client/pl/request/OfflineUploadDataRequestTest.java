//package com.flipkart.fintech.pandora.service.client.idfc.request;
//
//import com.fasterxml.jackson.databind.ObjectMapper;
//import org.junit.jupiter.api.Assertions;
//import org.junit.jupiter.api.Test;
//
//class OfflineUploadDataRequestTest {
//
//  @Test
//  void create() throws Exception {
//    String objectString = "{\"reqId\":\"PAYU401d427bd2f3451cb784ab56b5\",\"source\":\"PAYSENSE\",\"loanId\":\"61209112\",\"kycMode\":\"OKYC\",\"documents\":[{\"type\":\"KYC\",\"subType\":\"liveliness\",\"source\":\"PAYSENSE\",\"documentName\":\"kycselfie1596802231609_2_5ERZOAz\",\"fileExtension\":\"jpg\",\"documentContent\":\"/9j/4AAQSkZJRgABAQAAAQABAAD/4gIosD//2Q==\"}],\"data\":[{\"type\":\"KYC\",\"subType\":\"livelinessResponse\",\"source\":\"HYPERVERGE\",\"content\":\"\"},{\"type\":\"KYC\",\"subType\":\"photoMatchResponse\",\"source\":\"HYPERVERGE\",\"content\":\"\"}]}";
//    ObjectMapper objectMapper = new ObjectMapper();
//    OfflineUploadDataRequest offlineUploadDataRequest = objectMapper.readValue(objectString,
//        OfflineUploadDataRequest.class);
//    Assertions.assertNotNull(offlineUploadDataRequest);
//  }
//
//}
