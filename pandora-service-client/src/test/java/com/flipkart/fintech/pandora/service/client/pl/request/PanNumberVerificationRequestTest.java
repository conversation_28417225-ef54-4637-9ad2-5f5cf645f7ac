package com.flipkart.fintech.pandora.service.client.pl.request;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.Test;

class PanNumberVerificationRequestTest {

    @Test
    void create() {
        PanNumberVerificationRequest panVerificationRequest = new PanNumberVerificationRequest();
        panVerificationRequest.setPanNumber("**********");
        panVerificationRequest.setReqId("abcdefgh");
        assertNotNull(panVerificationRequest);
        assertThat(panVerificationRequest.getPanNumber(), equalTo("**********"));
    }

}