//package com.flipkart.fintech.pandora.service.client.idfc.request.ckyc;
//
//import static org.junit.jupiter.api.Assertions.*;
//
//import com.fasterxml.jackson.databind.ObjectMapper;
//import org.junit.jupiter.api.Test;
//
//class DownloadCkycRequestBodyTest {
//
//  @Test
//  void create() throws Exception {
//    String ckycRequestBodyString = "{\"RequestId\":\"akxyzabcefghijkmopqrstuv\",\"Source\":\"PARTNER\",\"Details\":[{\"TransactionId\":\"4565881\",\"RecordIdentifier\":\"P5IUYY9635\",\"ApplicationFormNo\":\"1\",\"BranchCode\":\"11\",\"CKYCNumber\":\"60000240829238\",\"DOB\":\"1956-06-12\",\"MobileNumber\":\"6088908888\",\"Pincode\":\"400001\",\"BirthYear\":\"1956\",\"dedupe_flag\":\"N\"}]}";
//    ObjectMapper objectMapper = new ObjectMapper();
//    DownloadCkycRequestBody downloadCkycRequestBody = objectMapper.readValue(ckycRequestBodyString, DownloadCkycRequestBody.class);
//    assertNotNull(downloadCkycRequestBody);
//  }
//
//}