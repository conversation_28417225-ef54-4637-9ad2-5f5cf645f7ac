package com.flipkart.fintech.pandora.service.client.pl.request.ckyc;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.Test;

class DownloadCkycRequestDetailTest {

  @Test
  void create() throws Exception {
    DownloadCkycRequestDetail downloadCkycRequestDetail = DownloadCkycRequestDetail.builder()
        .transactionId("4565881")
        .ckycNumber("60000240829238")
        .dob("1956-06-12")
        .mobileNumber("6088908888")
        .build();
    assertNotNull(downloadCkycRequestDetail);
    assertThat(downloadCkycRequestDetail.getTransactionId(), equalTo("4565881"));
    assertThat(downloadCkycRequestDetail.getCkycNumber(), equalTo("60000240829238"));
    assertThat(downloadCkycRequestDetail.getDob(), equalTo("1956-06-12"));
    assertThat(downloadCkycRequestDetail.getMobileNumber(), equalTo("6088908888"));
  }

}