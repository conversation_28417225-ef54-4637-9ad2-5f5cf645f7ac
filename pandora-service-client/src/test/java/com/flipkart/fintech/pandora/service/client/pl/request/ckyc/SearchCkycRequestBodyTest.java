package com.flipkart.fintech.pandora.service.client.pl.request.ckyc;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.junit.jupiter.api.Assertions.*;

import java.util.Collections;
import org.junit.jupiter.api.Test;

class SearchCkycRequestBodyTest {

    @Test
    void create() throws Exception {
        SearchCkycRequestBody searchCkycRequestBody = getSearchCkycRequestBody();
        assertNotNull(searchCkycRequestBody);
        assertThat(searchCkycRequestBody.getDetails().size(), equalTo(1));
        assertThat(searchCkycRequestBody.getDetails().get(0), equalTo(getSearchCkycRequestDetail()));
    }

    private static SearchCkycRequestBody getSearchCkycRequestBody() {
        SearchCkycRequestBody searchCkycRequestBody = new SearchCkycRequestBody();
        searchCkycRequestBody.setDetails(Collections.singletonList(getSearchCkycRequestDetail()));
        return searchCkycRequestBody;
    }

    private static SearchCkycRequestDetail getSearchCkycRequestDetail() {
        SearchCkycRequestDetail searchCkycRequestDetail = new SearchCkycRequestDetail();
        searchCkycRequestDetail.setTransactionId("1234567890");
        searchCkycRequestDetail.setInputIdType("Success");
        searchCkycRequestDetail.setInputIdNo("Success");
        return searchCkycRequestDetail;
    }

}