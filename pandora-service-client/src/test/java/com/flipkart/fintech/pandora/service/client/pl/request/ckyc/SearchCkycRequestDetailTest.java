package com.flipkart.fintech.pandora.service.client.pl.request.ckyc;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.Test;

class SearchCkycRequestDetailTest {

    @Test
    void create() throws Exception {
        SearchCkycRequestDetail searchCkycRequestDetail = getSearchCkycRequestDetail();
        assertNotNull(searchCkycRequestDetail);
        assertEquals(searchCkycRequestDetail.getTransactionId(), "1234567890");
        assertEquals(searchCkycRequestDetail.getInputIdType(), "Success");
        assertEquals(searchCkycRequestDetail.getInputIdNo(), "Success");
    }

    private static SearchCkycRequestDetail getSearchCkycRequestDetail() {
        SearchCkycRequestDetail searchCkycRequestDetail = new SearchCkycRequestDetail();
        searchCkycRequestDetail.setTransactionId("1234567890");
        searchCkycRequestDetail.setInputIdType("Success");
        searchCkycRequestDetail.setInputIdNo("Success");
        return searchCkycRequestDetail;
    }

}