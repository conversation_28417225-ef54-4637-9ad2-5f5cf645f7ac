package com.flipkart.fintech.pandora.service.client.pl.response;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.Test;

class IdfcErrorTest {

    @Test
    void create() throws Exception {
        IdfcError idfcError = getIdfcError();
        assertNotNull(idfcError);
        assertEquals(idfcError.getErrorCode(), "1234567890");
        assertEquals(idfcError.getErrorType(), "Success");
        assertEquals(idfcError.getErrorDescription(), "Success");
        assertEquals(idfcError.getCorrelationId(), "1234567890");
        assertEquals(idfcError.getDateTime(), "2019-01-01");
    }

    private static IdfcError getIdfcError() {
        IdfcError idfcError = new IdfcError();
        idfcError.setErrorCode("1234567890");
        idfcError.setErrorType("Success");
        idfcError.setErrorDescription("Success");
        idfcError.setCorrelationId("1234567890");
        idfcError.setDateTime("2019-01-01");
        return idfcError;
    }

}