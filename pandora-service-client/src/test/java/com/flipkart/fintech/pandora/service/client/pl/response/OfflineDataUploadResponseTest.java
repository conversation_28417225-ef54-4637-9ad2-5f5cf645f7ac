package com.flipkart.fintech.pandora.service.client.pl.response;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;

class OfflineDataUploadResponseTest {

  @Test
  void create() throws Exception {
    String objectString = "{\"reqId\":\"PARTNER_01\",\"source\":\"PARTNER\",\"status\":\"success\",\"errorCode\":\"\",\"errorDescription\":\"\"}";
    ObjectMapper objectMapper = new ObjectMapper();
    OfflineDataUploadResponse offlineDataUploadResponse = objectMapper.readValue(objectString,
        OfflineDataUploadResponse.class);

    assertNotNull(offlineDataUploadResponse);
    assertThat(offlineDataUploadResponse.getRequestId(), equalTo("PARTNER_01"));
    assertThat(offlineDataUploadResponse.getSource(), equalTo("PARTNER"));
    assertThat(offlineDataUploadResponse.getStatus(), equalTo("success"));
    assertThat(offlineDataUploadResponse.getErrorCode(), equalTo(""));
    assertThat(offlineDataUploadResponse.getErrorDescription(), equalTo(""));
  }

}
