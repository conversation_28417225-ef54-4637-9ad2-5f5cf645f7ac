package com.flipkart.fintech.pandora.service.client.pl.response;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.Test;

class PanNumberVerificationResponseTest {

    @Test
    void create() throws Exception {
        PanNumberVerificationResponse panNumberVerificationResponse = getPanNumberVerificationResponse();
        assertNotNull(panNumberVerificationResponse);
        assertEquals(panNumberVerificationResponse.getReqId(), "1234567890");
        assertEquals(panNumberVerificationResponse.getPanVerificationResponse(), getPanVerificationResponse());
    }

    private static PanNumberVerificationResponse getPanNumberVerificationResponse() {
        PanNumberVerificationResponse panNumberVerificationResponse = new PanNumberVerificationResponse();
        panNumberVerificationResponse.setReqId("1234567890");
        panNumberVerificationResponse.setPanVerificationResponse(getPanVerificationResponse());
        return panNumberVerificationResponse;
    }

    private static PanVerificationResponse getPanVerificationResponse() {
        PanVerificationResponse panVerificationResponse = new PanVerificationResponse();
        panVerificationResponse.setResponseId("1234567890");
        panVerificationResponse.setStatus("Success");
        panVerificationResponse.setPanUserInfo(getPanUserInfo());
        return panVerificationResponse;
    }

    private static PanUserInfo getPanUserInfo() {
        PanUserInfo panUserInfo = new PanUserInfo();
        panUserInfo.setPanNumber("**********");
        panUserInfo.setFirstName("Raja");
        panUserInfo.setMiddleName("Raja");
        panUserInfo.setLastName("Cholan");
        panUserInfo.setPanTitle("Mr");
        panUserInfo.setPanStatus("Existing and Valid PAN");
        panUserInfo.setPnStatus("E");
        panUserInfo.setReturnCode("1");
        panUserInfo.setLastUpdatedDate("2019-01-01");
        panUserInfo.setNamePrintedOnPanCard("Raja Cholan");
        panUserInfo.setTypeOfHolder("Individual");
        return panUserInfo;
    }

}