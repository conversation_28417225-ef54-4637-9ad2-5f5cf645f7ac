package com.flipkart.fintech.pandora.service.client.pl.response;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.Test;

class PanUserInfoTest {

    @Test
    void create() throws Exception {
        PanUserInfo panUserInfo = getPanUserInfo();
        assertNotNull(panUserInfo);
        assertThat(panUserInfo.getPanNumber(), equalTo("**********"));
        assertThat(panUserInfo.getFirstName(), equalTo("Raja"));
        assertThat(panUserInfo.getMiddleName(), equalTo("Raja"));
        assertThat(panUserInfo.getLastName(), equalTo("Cholan"));
        assertThat(panUserInfo.getPanTitle(), equalTo("Mr"));
        assertThat(panUserInfo.getPanStatus(), equalTo("Existing and Valid PAN"));
        assertThat(panUserInfo.getPnStatus(), equalTo("E"));
        assertThat(panUserInfo.getReturnCode(), equalTo("1"));
        assertThat(panUserInfo.getLastUpdatedDate(), equalTo("2019-01-01"));
        assertThat(panUserInfo.getNamePrintedOnPanCard(), equalTo("Raja Cholan"));
        assertThat(panUserInfo.getTypeOfHolder(), equalTo("Individual"));
        
    }

    private static PanUserInfo getPanUserInfo() {
        PanUserInfo panUserInfo = new PanUserInfo();
        panUserInfo.setPanNumber("**********");
        panUserInfo.setFirstName("Raja");
        panUserInfo.setMiddleName("Raja");
        panUserInfo.setLastName("Cholan");
        panUserInfo.setPanTitle("Mr");
        panUserInfo.setPanStatus("Existing and Valid PAN");
        panUserInfo.setPnStatus("E");
        panUserInfo.setReturnCode("1");
        panUserInfo.setLastUpdatedDate("2019-01-01");
        panUserInfo.setNamePrintedOnPanCard("Raja Cholan");
        panUserInfo.setTypeOfHolder("Individual");
        return panUserInfo;
    }

}