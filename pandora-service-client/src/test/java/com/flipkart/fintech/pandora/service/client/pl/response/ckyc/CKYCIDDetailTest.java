package com.flipkart.fintech.pandora.service.client.pl.response.ckyc;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.junit.jupiter.api.Assertions.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;

class CKYCIDDetailTest {

  @Test
  void create() throws Exception {
    String DetailString = "{\"CKYCAvailableIDType\":\"C\",\"CKYCAvailableIDTypeStatus\":\"01\",\"CKYCIDRemarks\":\"\"}";
    ObjectMapper objectMapper = new ObjectMapper();
    CKYCIDDetail cKYCIDDetail = objectMapper.readValue(DetailString, CKYCIDDetail.class);
    assertNotNull(cKYCIDDetail);
    assertThat(cKYCIDDetail.getCKYCAvailableIDType(), equalTo("C"));
    assertThat(cKYCIDDetail.getCKYCAvailableIDTypeStatus(), equalTo("01"));
    assertThat(cKYCIDDetail.getCKYCIDRemarks(), equalTo(""));
  }

}