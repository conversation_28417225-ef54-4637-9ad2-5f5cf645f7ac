package com.flipkart.fintech.pandora.service.client.pl.response.ckyc;

import static org.junit.jupiter.api.Assertions.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;

class SearchCkycResponseDetailTest {

  @Test
  void create() throws Exception {
    String DetailString = "{\"TransactionId\":\"FFF12\",\"TransactionStatus\":\"CKYCSuccess\",\"TransactionRejectionCode\":\"\",\"TransactionRejectionDescription\":\"\",\"BranchCode\":\"\",\"RecordIdentifier\":\"\",\"ApplicationFormNo\":\"\",\"CKYCAvailable\":\"Yes\",\"CKYCAccType\":\"Normal\",\"CKYCID\":\"10017881161918\",\"CKYCName\":\"MrAbcdAADDD\",\"CKYCAge\":\"25\",\"CKYCFatherName\":\"MrFatherrDDD\",\"CKYCPhotoImageType\":\"jpg\",\"CKYCKYCDate\":\"07-03-2017\",\"CKYCGenDate\":\"07-03-2017\",\"CKYCPhoto\":\"/9j/4AAQSkZJRgABAQEAYABgAAD//gA7Q1JFQVRPUjogZ2QtanBlZyB2MS\",\"CKYCRequestId\":\"11490260\",\"CKYCRequestDate\":\"10-04-2017\",\"CKYCUpdatedDate\":\"08-03-2017\",\"CKYCRemarks\":\"07-03-2017\",\"CKYCIDDetails\":[{\"CKYCAvailableIDType\":\"C\",\"CKYCAvailableIDTypeStatus\":\"01\",\"CKYCIDRemarks\":\"\"},{\"CKYCAvailableIDType\":\"D\",\"CKYCAvailableIDTypeStatus\":\"03\",\"CKYCIDRemarks\":\"\"}],\"CKYCPhotoBytes\":\"Ue0elaJkEVAqQAZpwUZ6UARhSamiiOelOAHpVuJR6VMmOw1\"}";
    ObjectMapper objectMapper = new ObjectMapper();
    SearchCkycResponseDetail searchCkycResponseDetail = objectMapper.readValue(DetailString, SearchCkycResponseDetail.class);
    assertNotNull(searchCkycResponseDetail);
  }

}