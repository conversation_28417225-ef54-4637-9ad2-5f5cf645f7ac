//package com.flipkart.fintech.pandora.service.client.plOnboarding;
//
//import static org.junit.jupiter.api.Assertions.*;
//
//import com.flipkart.fintech.pandora.service.client.plOnboarding.response.LenderRes;
//import javax.ws.rs.client.Client;
//import javax.ws.rs.client.ClientBuilder;
//import org.junit.jupiter.api.Test;
//
//class GenericLenderClientTest {
//
//    // This class is for testing the GenericLenderClient class.
//
//    // This method is for testing the fetchPostResponse method.
//    @Test
//    void testFetchPostResponse() {
//        Client client = ClientBuilder.newClient();
//        GenericLenderClient genericLenderClient = new GenericLenderClient(client);
//        PlHttpRequest plHttpRequest = new PlHttpRequest();
//        LenderRes result = genericLenderClient.fetchPostResponse(plHttpRequest);
//        assertNull(result);
//    }
//
//}