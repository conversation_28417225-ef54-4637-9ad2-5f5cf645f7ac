package com.flipkart.fintech.pandora.service.client.plOnboarding.response;

import static org.junit.jupiter.api.Assertions.*;

import com.flipkart.fintech.pandora.extensions.JsonFileParameterResolver;
import com.flipkart.fintech.pandora.test.annotations.JsonFile;
import java.math.BigDecimal;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@ExtendWith(JsonFileParameterResolver.class)
class GetKfsResponseTest {

  private static final String LATE_PAYMENT_INTEREST_DESCRIPTION = "8.0% p.a. above applicable interest rate on the overdue amount (subject to the aggregate not exceeding 24% p.a . above the applicable interest rate on the overdue amount). + GST as applicable";
  
  @Test
  void createNew(@JsonFile("GetNewKfsResponse.json") GetKfsResponse getKfsResponse) {
    assertNotNull(getKfsResponse);
    assertEquals("SUCCESS", getKfsResponse.getStatus());
    assertEquals(200, getKfsResponse.getStatusCode().intValue());
    assertNotNull(getKfsResponse.getResponse().getContingentChargesDetails().getLatePaymentInterestDescription());
    assertEquals(LATE_PAYMENT_INTEREST_DESCRIPTION, getKfsResponse.getResponse().getContingentChargesDetails().getLatePaymentInterestDescription());
  }

  @Test
  void createOld(@JsonFile("GetOldKfsResponse.json") GetKfsResponse getKfsResponse) {
    assertNotNull(getKfsResponse);
    assertEquals("SUCCESS", getKfsResponse.getStatus());
    assertEquals(200, getKfsResponse.getStatusCode().intValue());
    assertNull(getKfsResponse.getResponse().getContingentChargesDetails().getLatePaymentInterestDescription());
    assertNotNull(getKfsResponse.getResponse().getContingentChargesDetails().getPenalCharges());
    assertEquals(new BigDecimal(24), getKfsResponse.getResponse().getContingentChargesDetails().getPenalCharges().getSlabPenalty().getSlabs().get(0).getPenalty().getValue());
  }
}