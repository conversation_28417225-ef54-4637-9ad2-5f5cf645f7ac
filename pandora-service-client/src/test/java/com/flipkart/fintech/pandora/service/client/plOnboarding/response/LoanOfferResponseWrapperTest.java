package com.flipkart.fintech.pandora.service.client.plOnboarding.response;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.pandora.api.model.pl.request.PanVerificationRequest;
import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.io.InputStream;

import static org.junit.jupiter.api.Assertions.*;

class LoanOfferResponseWrapperTest {

    @Test
    void create() {
        try {
            InputStream inputStream = LoanOfferResponseWrapperTest.class.getClassLoader().getResourceAsStream("data.json");
            LoanOfferResponseWrapper responseWrapper = new ObjectMapper().readValue(inputStream, LoanOfferResponseWrapper.class);

            assertNotNull(responseWrapper);
        } catch (IOException e) {
            throw new RuntimeException("Failed to read JSON file", e);
        }
    }

}