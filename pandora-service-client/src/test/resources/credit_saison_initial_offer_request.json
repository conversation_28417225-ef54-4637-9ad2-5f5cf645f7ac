{"linkedIndividuals": [{"applicantType": "LinkedIndividual", "contacts": [{"countryCode": "+91", "notify": "true", "priority": 5, "type": "phone", "typeCode": "MOBILE", "value": "+919295390873"}, {"countryCode": null, "notify": null, "priority": 5, "type": "email", "typeCode": "PERSONAL", "value": "abhis<PERSON><PERSON><PERSON><EMAIL>"}], "individual": {"dob": "1992-11-11", "firstName": "<PERSON><PERSON><PERSON>", "middleName": "", "lastName": "<PERSON>"}, "kyc": [{"issuedCountry": "IN", "kycType": "panCard", "kycValue": "**********"}], "addresses": [{"type": "CURRES", "city": "Mumbai", "state": "MH", "country": "IN", "pinCode": "400003"}], "misc": {"income": 1000.0}, "employmentStatus": "salaried", "employerName": null}], "customerConsents": [{"consentFor": "PII", "consentMode": "clickwrap", "consentChannel": "app", "consentTime": "2024-04-17", "requestID": "SupMoney123", "consentIdentifier": {"ip": "************,           *************", "mac": null, "rmn": "+919900955357"}}], "loan": {"loanProduct": "SPM"}, "partnerId": "SP", "partnerLoanId": "APP2404171714064109176356727"}