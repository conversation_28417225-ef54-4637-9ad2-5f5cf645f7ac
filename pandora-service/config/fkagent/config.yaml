imports:
  - org/glassfish/jersey/server/ContainerRequest
  - org/glassfish/jersey/server/ContainerResponse
  - org/glassfish/jersey/message/internal/OutboundJaxrsResponse
  - org/glassfish/jersey/internal/util/collection/Value
  - javax/servlet/http/HttpServletRequest
  - javax/servlet/http/HttpServletResponse

  # For for dropwizard 1.X
  - org/glassfish/jersey/servlet/ServletContainer

  - java/lang/Object
  - java/lang/Class
  - java/lang/Boolean
  - java/lang/String
  - java/net/URI
  - java/util/List

  #   jersey http client
  - org/glassfish/jersey/client/ClientRuntime
  - org/glassfish/jersey/client/ClientRequest
  - org/glassfish/jersey/client/ClientResponse

metrics:

  ServletContainer.service(LURI;LURI;LHttpServletRequest;LHttpServletResponse;)LValue;: #for dropwizard 1.X
    - type: Timed
      name: http.server
      doc: Measuring http resource latencies
      labels: ['$__perf:$2.getHeader("X-PERF-TEST")']

    - type: ExceptionCounted
      name: http.server
      doc: Measuring http resource latencies
      labels: ['statusCode:$3.status', '$__perf:$2.getHeader("X-PERF-TEST")']
      exception: '$3.status:[4-5][0-9][0-9]'

  #   jersey http client
  ClientRuntime.invoke(LClientRequest;)LClientResponse;:
    - type: Timed
      name: http.client
      doc: Http methods call latencies
      labels: ['IP_address:$0.uri.host', 'HTTPMethod:$0.method', 'statusCode:$RESPONSE.status']
      exception: '$RESPONSE.status:[4-5][0-9][0-9]'

  com.netflix.hystrix.HystrixCommand.execute()Ljava/lang/Object;:
    - type: Timed
      name: com.netflix.hystrix.HystrixCommand.execute
      doc: Measuring latencies for method execute in com.netflix.hystrix.HystrixCommand
      labels: []

  com.netflix.hystrix.HystrixCommand.queue()Ljava/util/concurrent/Future;:
    - type: Timed
      name: com.netflix.hystrix.HystrixCommand.queue
      doc: Measuring latencies for method queue in com.netflix.hystrix.HystrixCommand
      labels: []

  com.flipkart.fintech.pandora.service.client.pl.client.CommonWebClient.getPostResponse(Ljava/lang/Object;Ljava/lang/Class;Ljava/lang/String;Ljavax/ws/rs/client/WebTarget;Lcom/flipkart/fintech/pandora/service/client/auth/Scope;Ljava/lang/String;)Ljava/lang/Object;:
    - type: Timed
      name: com.flipkart.fintech.pandora.service.client.pl.client.CommonWebClient.getPostResponse
      doc: Measuring latencies for method getPostResponse in com.flipkart.fintech.pandora.service.client.pl.client.CommonWebClient
      labels: ['lender:$4']

  com.flipkart.fintech.pandora.service.client.pl.client.CommonWebClient.getPostResponse(Ljava/lang/Object;Ljava/lang/Class;Ljava/lang/String;Ljavax/ws/rs/client/WebTarget;Lcom/flipkart/fintech/pandora/service/client/auth/Scope;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/Object;:
    - type: Timed
      name: com.flipkart.fintech.pandora.service.client.pl.client.CommonWebClient.getPostResponse
      doc: Measuring latencies for method getPostResponse in com.flipkart.fintech.pandora.service.client.pl.client.CommonWebClient
      labels: ['lender:$4']


scope: #choose 1 of below
  ServletContainer.service(LURI;LURI;LHttpServletRequest;LHttpServletResponse;)LValue; #For dropwizard 1.X

system:
  jvm:
    - gc
    - memory
    - threads