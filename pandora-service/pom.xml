<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>pandora</artifactId>
        <groupId>com.flipkart.fintech</groupId>
        <version>1.5.16-SM</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>pandora-service</artifactId>

    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
        <dropwizard.version>2.0.4</dropwizard.version>
        <hubspot.version>*******</hubspot.version>
        <guice.version>4.1.0</guice.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <ardour.version>1.0.83</ardour.version>
        <boomerang.version>1.1-J8</boomerang.version>
        <card.client>1.1.2-JAVA-8</card.client>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.flipkart.kloud.config</groupId>
            <artifactId>client-java</artifactId>
            <version>1.5.9</version>
        </dependency>
        <dependency>
            <groupId>org.elasticsearch.client</groupId>
            <artifactId>elasticsearch-rest-high-level-client</artifactId>
            <version>7.8.0</version>
        </dependency>
        <dependency>
            <groupId>com.flipkart.usercluster.chowkidaar</groupId>
            <artifactId>model</artifactId>
            <version>1.0.3</version>
            <exclusions>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-databind</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.flipkart.usercluster.chowkidaar</groupId>
            <artifactId>chowkidaar-client</artifactId>
            <version>1.1.7-SM</version>
        </dependency>
        <dependency>
            <groupId>org.eclipse.jetty</groupId>
            <artifactId>jetty-servlet</artifactId>
            <version>9.4.37.v20210219</version>
        </dependency>
        <dependency>
            <groupId>net.minidev</groupId>
            <artifactId>json-smart</artifactId>
            <version>2.3.1</version>
        </dependency>
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-codec-http</artifactId>
            <version>4.1.44.Final</version>
        </dependency>
        <dependency>
            <groupId>org.eclipse.jetty</groupId>
            <artifactId>jetty-server</artifactId>
            <version>9.4.30.v20200611</version>
        </dependency>
        <dependency>
            <groupId>org.eclipse.jetty</groupId>
            <artifactId>jetty-util</artifactId>
            <version>9.4.37.v20210219</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
            <version>2.13.5</version>
        </dependency>
        <dependency>
            <groupId>com.flipkart.instrumentation</groupId>
            <artifactId>fk-agent-client</artifactId>
            <version>2.1</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.22</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>me.xdrop</groupId>
            <artifactId>fuzzywuzzy</artifactId>
            <version>1.4.0</version>
        </dependency>
        <dependency>
            <groupId>io.dropwizard</groupId>
            <artifactId>dropwizard-core</artifactId>
            <version>${dropwizard.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.eclipse.jetty</groupId>
                    <artifactId>jetty-http</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.glassfish.hk2.external</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.eclipse.jetty</groupId>
                    <artifactId>jetty-servlet</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.flipkart.fintech</groupId>
            <artifactId>fintech-common-exception</artifactId>
            <version>1.0.22</version>
            <exclusions>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-databind</artifactId>
                </exclusion>
            </exclusions>
        </dependency>


        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-codec</artifactId>
            <version>4.1.66.Final</version>
        </dependency>
        <dependency>
            <groupId>com.flipkart.fpg</groupId>
            <artifactId>helios-proxy-lib</artifactId>
            <version>1.0.3</version>
            <exclusions>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty-codec</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>metrics-core</artifactId>
                    <groupId>io.dropwizard.metrics</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jetty-server</artifactId>
                    <groupId>org.eclipse.jetty</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jetty-http</artifactId>
                    <groupId>org.eclipse.jetty</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>io.dropwizard</groupId>
            <artifactId>dropwizard-client</artifactId>
            <version>${dropwizard.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.glassfish.hk2.external</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>io.dropwizard</groupId>
            <artifactId>dropwizard-metrics</artifactId>
            <version>${dropwizard.version}</version>
        </dependency>
        <dependency>
            <groupId>org.javassist</groupId>
            <artifactId>javassist</artifactId>
            <version>3.20.0-GA</version>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>32.1.3-jre</version>
        </dependency>

        <dependency>
            <groupId>com.hubspot.dropwizard</groupId>
            <artifactId>dropwizard-guice</artifactId>
            <version>${hubspot.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.javassist</groupId>
                    <artifactId>javassist</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.code.findbugs</groupId>
                    <artifactId>annotations</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.glassfish.jersey.core</groupId>
                    <artifactId>jersey-server</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.guava</groupId>
                    <artifactId>guava</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.googlecode.libphonenumber</groupId>
            <artifactId>libphonenumber</artifactId>
            <version>8.2.0</version>
        </dependency>
        <dependency>
            <groupId>org.glassfish.jersey.media</groupId>
            <artifactId>jersey-media-multipart</artifactId>
            <version>2.30.1</version>
        </dependency>
        <dependency>
            <groupId>com.smoketurner</groupId>
            <artifactId>dropwizard-swagger</artifactId>
            <version>1.1.2-1</version>
            <exclusions>
                <exclusion>
                    <groupId>org.glassfish.hk2.external</groupId>
                    <artifactId>aopalliance-repackaged</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.glassfish.hk2.external</groupId>
                    <artifactId>javax.inject</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.glassfish.jersey.media</groupId>
                    <artifactId>jersey-media-multipart</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.palominolabs.metrics</groupId>
            <artifactId>metrics-guice</artifactId>
            <version>3.1.3</version>
            <exclusions>
                <exclusion>
                    <groupId>io.dropwizard.metrics</groupId>
                    <artifactId>metrics-annotation</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.flipkart.fintech</groupId>
            <artifactId>pandora-api</artifactId>
            <version>${project.parent.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-annotations</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.validation</groupId>
                    <artifactId>validation-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>joda-time</groupId>
                    <artifactId>joda-time</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.ws.rs</groupId>
                    <artifactId>javax.ws.rs-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.flipkart.fintech</groupId>
            <artifactId>pandora-client</artifactId>
            <version>${project.parent.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-annotations</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.validation</groupId>
                    <artifactId>validation-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>joda-time</groupId>
                    <artifactId>joda-time</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.ws.rs</groupId>
                    <artifactId>javax.ws.rs-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.github.rholder</groupId>
            <artifactId>guava-retrying</artifactId>
            <version>2.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.flipkart.fintech</groupId>
            <artifactId>pandora-service-client</artifactId>
            <version>${project.parent.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>io.dropwizard</groupId>
                    <artifactId>dropwizard-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-lang3</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.httpcomponents</groupId>
                    <artifactId>httpcore</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.httpcomponents</groupId>
                    <artifactId>httpclient</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.validation</groupId>
                    <artifactId>validation-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.ws.rs</groupId>
                    <artifactId>javax.ws.rs-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.inject</groupId>
                    <artifactId>guice</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.inject</groupId>
                    <artifactId>javax.inject</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.netflix.hystrix</groupId>
                    <artifactId>hystrix-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.reactivex</groupId>
                    <artifactId>rxjava</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>joda-time</groupId>
                    <artifactId>joda-time</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-annotations</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-databind</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.ws.rs</groupId>
                    <artifactId>javax.ws.rs-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.validation</groupId>
                    <artifactId>validation-api</artifactId>
                </exclusion>

                <exclusion>
                    <groupId>io.dropwizard</groupId>
                    <artifactId>dropwizard-jersey</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.projectlombok</groupId>
                    <artifactId>lombok</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.inject.extensions</groupId>
                    <artifactId>guice-multibindings</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.eclipse.jetty</groupId>
                    <artifactId>jetty-server</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>javax.annotation</groupId>
            <artifactId>javax.annotation-api</artifactId>
            <version>1.2</version>
        </dependency>

        <dependency>
            <groupId>org.apache.tika</groupId>
            <artifactId>tika-core</artifactId>
            <version>1.20</version>
        </dependency>

        <dependency>
            <groupId>joda-time</groupId>
            <artifactId>joda-time</artifactId>
            <version>2.9.7</version>
        </dependency>

        <dependency>
            <groupId>com.flipkart.fintech</groupId>
            <artifactId>ardour-client</artifactId>
            <version>${ardour.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.projectlombok</groupId>
                    <artifactId>lombok</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.flipkart.affordability</groupId>
                    <artifactId>affordability-service-clients</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.flipkart.affordability</groupId>
            <artifactId>affordability-service-clients</artifactId>
            <version>5.0.3-SM</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.avro</groupId>
                    <artifactId>avro</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.avro</groupId>
                    <artifactId>avro-maven-plugin</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>


        <dependency>
            <groupId>com.flipkart.affordability</groupId>
            <artifactId>bnpl-collections-client</artifactId>
            <version>1.4.12</version>
            <exclusions>
                <exclusion>
                    <groupId>org.projectlombok</groupId>
                    <artifactId>lombok</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.google.inject</groupId>
            <artifactId>guice</artifactId>
            <version>${guice.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>javax.inject</groupId>
                    <artifactId>javax.inject</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>aopalliance</groupId>
                    <artifactId>aopalliance</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.google.inject.extensions</groupId>
            <artifactId>guice-assistedinject</artifactId>
            <version>4.1.0</version>
            <exclusions>
                <exclusion>
                    <groupId>com.google.inject</groupId>
                    <artifactId>guice</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.glassfish.hk2</groupId>
            <artifactId>hk2-api</artifactId>
            <version>2.5.0-b32</version>
            <exclusions>
                <exclusion>
                    <groupId>org.glassfish.hk2</groupId>
                    <artifactId>hk2-utils</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.glassfish.hk2.external</groupId>
                    <artifactId>aopalliance-repackaged</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.flipkart.affordability</groupId>
            <artifactId>bnpl-khaata-client</artifactId>
            <version>1.1.43</version>
            <exclusions>
                <exclusion>
                    <groupId>com.flipkart.affordability</groupId>
                    <artifactId>bnpl-tijori-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.flipkart.affordability</groupId>
                    <artifactId>affordability-service-clients</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
            <version>4.1</version>
        </dependency>

        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.4</version>
        </dependency>
        <dependency>
            <groupId>com.flipkart.fintech</groupId>
            <artifactId>fintech-common-security</artifactId>
            <version>1.1.4-SM-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>io.dropwizard.metrics</groupId>
                    <artifactId>metrics-annotation</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.javassist</groupId>
                    <artifactId>javassist</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.glassfish.jersey.core</groupId>
                    <artifactId>jersey-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.dropwizard</groupId>
                    <artifactId>dropwizard-lifecycle</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.glassfish.jersey.core</groupId>
            <artifactId>jersey-common</artifactId>
            <version>2.30.1</version>
            <exclusions>
                <exclusion>
                    <groupId>org.glassfish.jersey.bundles.repackaged</groupId>
                    <artifactId>jersey-guava</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.glassfish.hk2.external</groupId>
                    <artifactId>javax.inject</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.glassfish.hk2</groupId>
                    <artifactId>osgi-resource-locator</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.glassfish.hk2.external</groupId>
                    <artifactId>aopalliance-repackaged</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.flipkart.fintech</groupId>
            <artifactId>fintech-cryptex-bundle</artifactId>
            <version>1.1.24-SM</version>
            <exclusions>
                <exclusion>
                    <groupId>io.dropwizard</groupId>
                    <artifactId>dropwizard-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.flipkart.kloud.authn</groupId>
                    <artifactId>client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- v********** is required for AuthN v3 migration -->
        <dependency>
            <groupId>com.flipkart.kloud.authn</groupId>
            <artifactId>client</artifactId>
            <version>**********</version>
        </dependency>
        <dependency>
            <groupId>aopalliance</groupId>
            <artifactId>aopalliance</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.13</version>
            <exclusions>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-codec</groupId>
                    <artifactId>commons-codec</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.glassfish.hk2</groupId>
                    <artifactId>osgi-resource-locator</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.glassfish.jersey.bundles.repackaged</groupId>
            <artifactId>jersey-guava</artifactId>
            <version>2.25.1</version>
        </dependency>

        <dependency>
            <groupId>com.flipkart.fintech</groupId>
            <artifactId>pinaka-client</artifactId>
            <version>${pinaka.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.flipkart.affordability</groupId>
                    <artifactId>affordability-service-clients</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.flipkart.fintech</groupId>
            <artifactId>card-gateway-client</artifactId>
            <version>${card.gateway.client.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.flipkart.affordability</groupId>
                    <artifactId>affordability-service-clients</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.flipkart.fintech</groupId>
            <artifactId>fintech-logger</artifactId>
            <version>1.0.0</version>
            <exclusions>
                <exclusion>
                    <groupId>org.aspectj</groupId>
                    <artifactId>aspectjrt</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjrt</artifactId>
            <version>1.8.6</version>
        </dependency>
        <dependency>
            <groupId>com.netflix.hystrix</groupId>
            <artifactId>hystrix-javanica</artifactId>
            <version>1.5-RC</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-lang3</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.guava</groupId>
                    <artifactId>guava</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.netflix.hystrix</groupId>
                    <artifactId>hystrix-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-collections</groupId>
                    <artifactId>commons-collections</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.dropwizard</groupId>
            <artifactId>dropwizard-testing</artifactId>
            <version>2.0.4</version>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>com.fasterxml.jackson.jaxrs</groupId>
                    <artifactId>jackson-jaxrs-json-provider</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.netflix.hystrix</groupId>
            <artifactId>hystrix-metrics-event-stream</artifactId>
            <version>1.5.18</version>
            <exclusions>
                <exclusion>
                    <groupId>com.fasterxml.jackson.module</groupId>
                    <artifactId>jackson-module-afterburner</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.netflix.hystrix</groupId>
                    <artifactId>hystrix-javanica</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-annotationse</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-databind</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-text</artifactId>
            <version>1.6</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.netflix.hystrix</groupId>
            <artifactId>hystrix-codahale-metrics-publisher</artifactId>
            <version>1.5.18</version>
        </dependency>
        <dependency>
            <groupId>org.modelmapper</groupId>
            <artifactId>modelmapper</artifactId>
            <version>2.3.0</version>
        </dependency>
        <dependency>
            <groupId>com.ffb.cse</groupId>
            <artifactId>credit_service_clients</artifactId>
            <version>1.1.3</version>
            <exclusions>
                <exclusion>
                    <groupId>org.projectlombok</groupId>
                    <artifactId>lombok</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.flipkart.fintech</groupId>
                    <artifactId>pinaka-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.flipkart.fintech</groupId>
            <artifactId>fintech-common-headers</artifactId>
            <version>1.0.124</version>
            <exclusions>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-classic</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.projectlombok</groupId>
                    <artifactId>lombok</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.flipkart.fintech</groupId>
            <artifactId>fintech-common-filters</artifactId>
            <version>1.0.1</version>
        </dependency>
        <dependency>
            <groupId>com.flipkart.affordability</groupId>
            <artifactId>bnpl-tijori-client</artifactId>
            <version>2.0.136</version>
            <exclusions>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-annotations</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.dropwizard</groupId>
                    <artifactId>dropwizard-jersey</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.validation</groupId>
                    <artifactId>validation-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.glassfish.jersey.media</groupId>
                    <artifactId>jersey-media-json-jackson</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.inject.extensions</groupId>
                    <artifactId>guice-multibindings</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.flipkart.fintech</groupId>
                    <artifactId>skyler-core-sdk</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.flipkart.fintech</groupId>
                    <artifactId>skyler-api-models</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.json</groupId>
                    <artifactId>json</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.flipkart.fintech</groupId>
            <artifactId>skyler-api-models</artifactId>
            <version>1.8.27</version>
            <exclusions>
                <exclusion>
                    <artifactId>byte-buddy</artifactId>
                    <groupId>net.bytebuddy</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.auth0</groupId>
            <artifactId>java-jwt</artifactId>
            <version>3.3.0</version>
        </dependency>
        <dependency>
            <groupId>com.flipkart.affordability</groupId>
            <artifactId>fintech-phoenix-client</artifactId>
            <version>1.0.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.flipkart.kloud.authn</groupId>
            <artifactId>dropwizard-relying-party</artifactId>
            <version>**********</version>
            <exclusions>
                <exclusion>
                    <groupId>io.dropwizard</groupId>
                    <artifactId>dropwizard-auth</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.flipkart.kloud.authn</groupId>
            <artifactId>commons</artifactId>
            <version>**********</version>
        </dependency>




        <dependency>
            <groupId>com.flipkart.fintech</groupId>
            <artifactId>fintech-common-rotator</artifactId>
            <version>1.0.32</version>
        </dependency>
        <dependency>
            <groupId>commons-collections</groupId>
            <artifactId>commons-collections</artifactId>
            <version>3.2.2</version>
        </dependency>
        <dependency>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java</artifactId>
            <version>3.25.2</version>
        </dependency>
        <dependency>
            <groupId>org.eclipse.jetty</groupId>
            <artifactId>jetty-http</artifactId>
            <version>9.4.30.v20200611</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.testng</groupId>
            <artifactId>testng</artifactId>
            <version>6.9.8</version>
        </dependency>
        <dependency>
            <groupId>com.flipkart</groupId>
            <artifactId>ablibrary</artifactId>
            <version>3.8.0</version>
            <exclusions>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-databind</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.flipkart.fintech</groupId>
            <artifactId>profile-api</artifactId>
            <version>3.3.19-SM</version>
        </dependency>

        <dependency>
            <groupId>com.flipkart.fintech</groupId>
            <artifactId>winterfell-client</artifactId>
            <version>1.0.55-SM</version>
        </dependency>
        <dependency>
            <groupId>com.flipkart.affordability</groupId>
            <artifactId>bnpl-shylock-model</artifactId>
            <version>1.0.43</version>
        </dependency>
        <dependency>
            <groupId>com.flipkart.affordability</groupId>
            <artifactId>bnpl-shylock-client</artifactId>
            <version>1.0.40</version>
        </dependency>
        <dependency>
            <groupId>io.github.millij</groupId>
            <artifactId>poi-object-mapper</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.netflix.hystrix</groupId>
            <artifactId>hystrix-core</artifactId>
            <version>1.5.9</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.flipkart.fintech</groupId>
            <artifactId>user-data-client</artifactId>
            <version>1.0.0</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sumo.dataplatform</groupId>
            <artifactId>event-publisher</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sumo.dataplatform</groupId>
            <artifactId>schema-registry</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.avro</groupId>
            <artifactId>avro</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-databind</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.avro</groupId>
            <artifactId>avro-maven-plugin</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-databind</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sumo.boomerang</groupId>
            <artifactId>boomerang-client</artifactId>
            <version>${boomerang.version}</version>
        </dependency>
        <dependency>
            <groupId>com.sumo.boomerang</groupId>
            <artifactId>boomerang-models</artifactId>
            <version>${boomerang.version}</version>
        </dependency>
        <dependency>
            <groupId>com.sumo.bff.card</groupId>
            <artifactId>card-service-client</artifactId>
            <version>${card.client}</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <!--  Plugin to add additional test source and test-resource  -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <version>3.1.0</version>
                <executions>
                    <execution>
                        <id>add-integration-test-source</id>
                        <phase>generate-test-sources</phase>
                        <goals>
                            <goal>add-test-source</goal>
                        </goals>
                        <configuration>
                            <sources>
                                <source>src/integration-test/java</source>
                            </sources>
                        </configuration>
                    </execution>
                    <execution>
                        <id>add-integration-test-resource</id>
                        <phase>generate-test-resources</phase>
                        <goals>
                            <goal>add-test-resource</goal>
                        </goals>
                        <configuration>
                            <resources>
                                <resource>
                                    <directory>src/integration-test/resources</directory>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <version>3.0.0-M1</version>
                <executions>
                    <execution>
                        <id>enforce-no-snapshots</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <requireReleaseDeps>
                                    <message>No Snapshots Allowed!</message>
                                    <excludes>
                                        <exclude>org.apache.maven:maven-core</exclude>
                                        <exclude>org.apache.maven.plugins:*</exclude>
                                    </excludes>
                                </requireReleaseDeps>
                            </rules>
                            <fail>false</fail>
                        </configuration>
                    </execution>
                    <execution>
                        <id>no-duplicate-declared-dependencies</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <banDuplicatePomDependencyVersions/>
                            </rules>
                            <fail>false</fail>
                        </configuration>
                    </execution>
                    <execution>
                        <id>dependency-convergence</id>
                        <configuration>
                            <rules>
                                <dependencyConvergence>
                                    <uniqueVersions>true</uniqueVersions>
                                </dependencyConvergence>
                            </rules>
                            <fail>false</fail>
                        </configuration>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <!--<plugin>-->
            <!--<groupId>org.owasp</groupId>-->
            <!--<artifactId>dependency-check-maven</artifactId>-->
            <!--<version>1.4.5</version>-->
            <!--<executions>-->
            <!--<execution>-->
            <!--<goals>-->
            <!--<goal>check</goal>-->
            <!--</goals>-->
            <!--</execution>-->
            <!--</executions>-->
            <!--</plugin>-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <version>2.4.2</version>
                <configuration>
                    <createDependencyReducedPom>true</createDependencyReducedPom>
                    <filters>
                        <filter>
                            <artifact>*:*</artifact>
                            <excludes>
                                <exclude>META-INF/*.SF</exclude>
                                <exclude>META-INF/*.DSA</exclude>
                                <exclude>META-INF/*.RSA</exclude>
                            </excludes>
                        </filter>
                    </filters>
                </configuration>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                        <configuration>
                            <transformers>
                                <transformer
                                        implementation="org.apache.maven.plugins.shade.resource.ServicesResourceTransformer"/>
                                <transformer
                                        implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer">
                                    <mainClass>
                                        com.flipkart.fintech.pandora.service.application.PandoraApplication
                                    </mainClass>
                                </transformer>
                            </transformers>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.22.0</version>
                <configuration>
                    <skipTests>${skipTests}</skipTests>
                    <argLine>${surefireArgLine} -Djacoco.excludes=**/IdfcTransformer.class:**/IdfcTransformer$*.class</argLine>
                    <excludes>
                        <exclude>**/IT*.java</exclude>
                        <exclude>**/IdfcTransformer.java</exclude>
                        <exclude>**/IdfcTransformer$*.java</exclude>
                    </excludes>
                </configuration>
                <executions>
                    <execution>
                        <id>integration-tests</id>
                        <phase>integration-test</phase>
                        <goals>
                            <goal>test</goal>
                        </goals>
                        <configuration>
                            <skipTests>${skipCT}</skipTests>
                            <suiteXmlFiles>
                                <suiteXmlFile>src/integration-test/resources/testng.xml</suiteXmlFile>
                            </suiteXmlFiles>
                            <argLine>${testArgLine}  -DaopType=GUICE</argLine>
                            <excludes>
                                <exclude>**/TEST*.java</exclude>
                                <exclude>com/flipkart/pandora/service/exception/**/*</exclude>
                                <exclude>com/flipkart/pandora/service/external/**/*</exclude>
                                <exclude>com/flipkart/pandora/service/scheduler/**/*</exclude>
                                <exclude>com/flipkart/pandora/service/redis/**/*</exclude>
                                <exclude>com/flipkart/pandora/service/router/**/*</exclude>
                                <exclude>com/flipkart/pandora/service/resources/internal/**/*</exclude>
                                <exclude>com/flipkart/pandora/service/resources/web/ffb/**/*</exclude>
                                <exclude>com/flipkart/pandora/service/resources/web/smefin/**/*</exclude>
                                <exclude>com/flipkart/pandora/service/resources/web/v2/**/*</exclude>
                                <exclude>com/flipkart/pandora/service/resources/web/AesResource.class</exclude>
                                <exclude>com/flipkart/pandora/service/resources/web/AuthResource.class</exclude>
                                <exclude>com/flipkart/pandora/service/resources/web/BillingResource.class</exclude>
                                <exclude>com/flipkart/pandora/service/resources/web/DigitalKycResource.class</exclude>
                                <exclude>com/flipkart/pandora/service/resources/web/IncomeAssessmentResource.class</exclude>
                                <exclude>com/flipkart/pandora/service/resources/web/LenderActionResource.class</exclude>
                                <exclude>com/flipkart/pandora/service/resources/web/DebuggingResource.class</exclude>
                                <exclude>com/flipkart/pandora/service/resources/web/NudgingResource.class</exclude>
                                <exclude>com/flipkart/pandora/service/resources/web/PaybackResource.class</exclude>
                                <exclude>com/flipkart/pandora/service/resources/web/QuessResource.class</exclude>
                                <exclude>com/flipkart/pandora/service/resources/web/TransactionResource.class</exclude>
                                <exclude>com/flipkart/pandora/service/resources/web/UbonaResource.class</exclude>
                                <exclude>com/flipkart/pandora/service/resources/web/UpiResource.class</exclude>

                                <exclude>com/flipkart/pandora/service/core/billing/**/*</exclude>
                                <exclude>com/flipkart/pandora/service/core/callback/**/*</exclude>
                                <exclude>com/flipkart/pandora/service/core/digitalkyc/**/*</exclude>
                                <exclude>com/flipkart/pandora/service/core/ebc/**/*</exclude>
                                <exclude>com/flipkart/pandora/service/core/ffb/**/*</exclude>
                                <exclude>com/flipkart/pandora/service/core/flowcontrol/**/*</exclude>
                                <exclude>com/flipkart/pandora/service/core/health/**/*</exclude>
                                <exclude>com/flipkart/pandora/service/core/incomeassessment/**/*</exclude>
                                <exclude>com/flipkart/pandora/service/core/lenderraction/**/*</exclude>
                                <exclude>com/flipkart/pandora/service/core/lifeCycleManagement/**/*</exclude>
                                <exclude>com/flipkart/pandora/service/core/payback/**/*</exclude>
                                <exclude>com/flipkart/pandora/service/core/quess/**/*</exclude>
                                <exclude>com/flipkart/pandora/service/core/security/**/*</exclude>
                                <exclude>com/flipkart/pandora/service/core/smefin/**/*</exclude>
                                <exclude>com/flipkart/pandora/service/core/teleSales/**/*</exclude>
                                <exclude>com/flipkart/pandora/service/core/transaction/**/*</exclude>
                                <exclude>com/flipkart/pandora/service/core/ubona/**/*</exclude>
                                <exclude>com/flipkart/pandora/service/core/upi/**/*</exclude>
                                <exclude>com/flipkart/pandora/service/core/UserOnboarding/**/*</exclude>
                            </excludes>
                            <includes>
                                <include>**/*IT*.java</include>
                            </includes>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.3</version>
                <configuration>
                    <excludes>
                        <exclude>**/IdfcTransformer.class</exclude>
                        <exclude>**/IdfcTransformer$*.class</exclude>
                    </excludes>
                </configuration>

                <executions>
                    <execution>
                        <id>pre-unit-test</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                        <configuration>
                            <destFile>${project.build.directory}/coverage-reports/jacoco-ut.exec</destFile>
                            <propertyName>surefireArgLine</propertyName>
                        </configuration>
                    </execution>
                    <execution>
                        <id>post-unit-test</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                        <configuration>
                            <excludes>
                                <exclude>com/flipkart/fintech/pandora/service/**/*IT.class</exclude>
                                <exclude>**/IdfcTransformer.class</exclude>
                                <exclude>**/IdfcTransformer$*.class</exclude>
                            </excludes>
                            <dataFile>${project.build.directory}/coverage-reports/jacoco-ut.exec</dataFile>
                            <outputDirectory>${project.reporting.outputDirectory}/jacoco-ut</outputDirectory>
                        </configuration>
                    </execution>
                    <execution>
                        <id>pre-integration-prepare</id>
                        <phase>pre-integration-test</phase>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                        <configuration>
                            <destFile>${project.build.directory}/coverage-reports/jacoco-it.exec</destFile>
                            <propertyName>testArgLine</propertyName>
                        </configuration>
                    </execution>
                    <execution>
                        <id>post-integration-test</id>
                        <phase>post-integration-test</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                        <configuration>
                            <excludes>
                                <exclude>com/flipkart/fintech/pandora/service/**/*Test.class</exclude>
                                <exclude>com/flipkart/fintech/pandora/service/exception/**/*</exclude>
                                <exclude>com/flipkart/fintech/pandora/service/external/**/*</exclude>
                                <exclude>**/IdfcTransformer.class</exclude>
                                <exclude>**/IdfcTransformer$*.class</exclude>
                            </excludes>
                            <dataFile>${project.build.directory}/coverage-reports/jacoco-it.exec</dataFile>
                            <outputDirectory>${project.reporting.outputDirectory}/jacoco-it</outputDirectory>
                        </configuration>
                    </execution>
                    <execution>
                        <id>merge-results</id>
                        <phase>verify</phase>
                        <goals>
                            <goal>merge</goal>
                        </goals>
                        <configuration>
                            <fileSets>
                                <fileSet>
                                    <directory>${project.build.directory}/coverage-reports</directory>
                                    <includes>
                                        <include>*.exec</include>
                                    </includes>
                                </fileSet>
                            </fileSets>
                            <destFile>${project.build.directory}/coverage-reports/aggregate.exec</destFile>
                        </configuration>
                    </execution>
                    <execution>
                        <id>post-merge-report</id>
                        <phase>verify</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                        <configuration>
                            <dataFile>${project.build.directory}/coverage-reports/aggregate.exec</dataFile>
                            <outputDirectory>${project.reporting.outputDirectory}/jacoco-aggregate</outputDirectory>
                            <excludes>
                                <exclude>com/flipkart/fintech/pandora/service/transformer/IdfcTransformer.class</exclude>
                                <exclude>**/com/flipkart/fintech/pandora/service/transformer/IdfcTransformer.class</exclude>
                                <exclude>**/*IdfcTransformer*</exclude>
                                <exclude>**/*IdfcTransformer*.class</exclude>
                                <exclude>**/*IdfcTransformer*.java</exclude>
                            </excludes>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>exclude-idfc-transformer</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.jacoco</groupId>
                        <artifactId>jacoco-maven-plugin</artifactId>
                        <version>0.8.3</version>
                        <configuration>
                            <excludes>
                                <exclude>**/IdfcTransformer.class</exclude>
                                <exclude>**/IdfcTransformer$*.class</exclude>
                            </excludes>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

</project>