package com.flipkart.fintech.pandora.service;

import com.flipkart.fintech.pandora.api.model.response.ckyc.SearchCkycResponse;
import com.flipkart.fintech.pandora.api.model.response.ckyc.DownloadCkycResponse;
import com.flipkart.fintech.pandora.service.common.Assertions;
import lombok.extern.slf4j.Slf4j;

import javax.ws.rs.core.Response;

import org.testng.annotations.Test;

@Slf4j
public class CkycResourceIT {

    @Test(description = "search ckyc failure", priority = 1)
    public void searchCkycFail() {
        Response response = com.flipkart.fintech.pandora.service.common.TestHelper.searchCkycFail(null, null);
        SearchCkycResponse searchCkycResponse = response.readEntity(SearchCkycResponse.class);
        Assertions.verifySearchCkycFail(response, searchCkycResponse);
    }

    @Test(description = "search ckyc success", priority = 2)
    public void searchCkycSuccess() {
        Response response = com.flipkart.fintech.pandora.service.common.TestHelper.searchCkyc("ACC100", "**********");
        SearchCkycResponse searchCkycResponse = response.readEntity(SearchCkycResponse.class);
        Assertions.verifySearchCkycSuccess(response, searchCkycResponse);
    }

    @Test(description = "search ckyc reject", priority = 3)
    public void searchCkycReject() {
        Response response = com.flipkart.fintech.pandora.service.common.TestHelper.searchCkyc("ACC101", "**********");
        SearchCkycResponse searchCkycResponse = response.readEntity(SearchCkycResponse.class);
        Assertions.verifySearchCkycReject(response, searchCkycResponse);
    }

//    @Test(description = "dowload ckyc success", priority = 4)
//    public void dowloadCkycSuccess() {
//        Response response = com.flipkart.fintech.pandora.service.common.TestHelper.downloadCkyc("IDFC", "ACC100", "CKYC123", "password@123");
//        com.flipkart.fintech.pandora.idfc.client.response.DownloadCkycResponse downloadCkycResponse = response.readEntity(com.flipkart.fintech.pandora.idfc.client.response.DownloadCkycResponse.class);
//        Assertions.verifyDowloadCkycSuccess(response, downloadCkycResponse);
//    }
//
//    @Test(description = "dowload ckyc reject", priority = 5)
//    public void dowloadCkycReject() {
//        Response response = com.flipkart.fintech.pandora.service.common.TestHelper.downloadCkyc("IDFC", "ACC101", "CKYC123", "password@123");
//        com.flipkart.fintech.pandora.idfc.client.response.DownloadCkycResponse downloadCkycResponse = response.readEntity(com.flipkart.fintech.pandora.idfc.client.response.DownloadCkycResponse.class);
//        Assertions.verifyDowloadCkycReject(response, downloadCkycResponse);
//    }     

    @Test(description = "dowload ckyc failure", priority = 6)
    public void dowloadCkycFail() {
        Response response = com.flipkart.fintech.pandora.service.common.TestHelper.downloadCkycFail("IDFC", null, "CKYC123", "password@123");
        DownloadCkycResponse downloadCkycResponse = response.readEntity(DownloadCkycResponse.class);
        Assertions.verifyDowloadCkyFail(response, downloadCkycResponse);
    }

}
