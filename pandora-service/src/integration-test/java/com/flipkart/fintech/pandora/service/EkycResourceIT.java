package com.flipkart.fintech.pandora.service;

import com.flipkart.fintech.pandora.api.model.response.ekyc.EkycSendOtpResponse;
import com.flipkart.fintech.pandora.api.model.response.ekyc.EkycVerifyOtpResponse;
import com.flipkart.fintech.pandora.api.model.response.ekyc.KycDetailsResponse;
import com.flipkart.fintech.pandora.service.common.Assertions;
import lombok.extern.slf4j.Slf4j;
import javax.ws.rs.core.Response;
import org.testng.annotations.Test;

@Slf4j
public class EkycResourceIT {

    @Test(description = "send document success", priority = 8)
    public void sendDocument() {
        Response response = com.flipkart.fintech.pandora.service.common.TestHelper.sendDocument("AADHAAR_XML", "lDtezAOarUyFBDg3HaMAgA==",  "ACC14125653191935118");
        KycDetailsResponse kycDetailsResponse = response.readEntity(KycDetailsResponse.class);
        Assertions.sendDocumentSuccess(response, kycDetailsResponse);
    }

    @Test(description = "send document failure", priority = 9)
    public void sendDocumentFail() {
        Response response = com.flipkart.fintech.pandora.service.common.TestHelper.sendDocumentFail("AADHAAR_XML", "lDtezAOarUyFBDg3HaMAgA==",  "ACC14125653191935118");
        KycDetailsResponse kycDetailsResponse = response.readEntity(KycDetailsResponse.class);
        Assertions.sendDocumentFail(response, kycDetailsResponse);
    }

}
