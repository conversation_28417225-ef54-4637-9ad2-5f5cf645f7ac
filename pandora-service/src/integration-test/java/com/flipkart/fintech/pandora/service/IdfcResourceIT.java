package com.flipkart.fintech.pandora.service;

import com.flipkart.fintech.pandora.service.common.Assertions;
import lombok.extern.slf4j.Slf4j;
import javax.ws.rs.core.Response;
import org.testng.annotations.Test;

@Slf4j
public class IdfcResourceIT {
    @Test(description = "send otp success", priority = 1)
    public void checkLoan() {
        Response response = com.flipkart.fintech.pandora.service.common.TestHelper.checkLoan("ACCADBFFC4807BF4882A375297F4D6AFF6DQ", "5676789098786756");
        Assertions.verifyCheckLoan(response);
    }
}
