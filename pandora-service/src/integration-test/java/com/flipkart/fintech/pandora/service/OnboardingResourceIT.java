package com.flipkart.fintech.pandora.service;

import com.flipkart.fintech.pandora.service.common.Assertions;
import lombok.extern.slf4j.Slf4j;
import javax.ws.rs.core.Response;
import org.testng.annotations.Test;
import com.flipkart.fintech.pandora.api.model.response.onboarding.UserResponse;
import com.flipkart.fintech.pandora.api.model.response.onboarding.PanNumberResponse;

@Slf4j
public class OnboardingResourceIT {

    @Test(description = "check eligibility request success", priority = 1)
    public void checkEligibilityRequest() {
        Response response = com.flipkart.fintech.pandora.service.common.TestHelper.checkEligibilityRequest("FK", 100000.50, "ABCD", "ACCADBFFC4807BF4882A375297F4D6AFF6DQ");
        UserResponse userResponse = response.readEntity(UserResponse.class);
        Assertions.verifyCheckEligibilityRequest(response, userResponse);
    }

    @Test(description = "check eligibility request failure", priority = 2)
    public void checkEligibilityRequestFail() {
        Response response = com.flipkart.fintech.pandora.service.common.TestHelper.checkEligibilityRequestFail("FK", null, "ABCD", "ACCADBFFC4807BF4882A375297F4D6AFF6DQ");
        UserResponse userResponse = response.readEntity(UserResponse.class);
        Assertions.verifyCheckEligibilityRequestFail(response, userResponse);
    }

    @Test(description = "create loan success", priority = 3)
    public void createLoan() {
        Response response = com.flipkart.fintech.pandora.service.common.TestHelper.createLoan("FK", 100000.50, "ABCD", "ACCADBFFC4807BF4882A375297F4D6AFF6DQ");
        UserResponse userResponse = response.readEntity(UserResponse.class);
        Assertions.verifyCreateLoan(response, userResponse);
    }

    @Test(description = "create loan failure", priority = 4)
    public void createLoanFail() {
        Response response = com.flipkart.fintech.pandora.service.common.TestHelper.createLoanFail("FK", null, "ABCD", "ACCADBFFC4807BF4882A375297F4D6AFF6DQ");
        UserResponse userResponse = response.readEntity(UserResponse.class);
        Assertions.verifyCreateLoanFail(response, userResponse);
    }

    @Test(description = "verify pan success")
    public void verifyPan() {
        Response response = com.flipkart.fintech.pandora.service.common.TestHelper.verifyPan("OdxluHbvYEq/LcPTRnlpxg==", "ACC0827A364374B4376BABF40C3AB1E3AF5X");
        PanNumberResponse panNumberResponse = response.readEntity(PanNumberResponse.class);
        Assertions.verifyPan(response, panNumberResponse);
    }

    @Test(description = "verify pan fail")
    public void verifyPanFail() {
        Response response = com.flipkart.fintech.pandora.service.common.TestHelper.verifyPan("PANFAILghagci979", null);
        PanNumberResponse panNumberResponse = response.readEntity(PanNumberResponse.class);
        Assertions.verifyPanFail(response, panNumberResponse);
    }

    @Test(description = "verify pan fail")
    public void verifyInvalidPan() {
        Response response = com.flipkart.fintech.pandora.service.common.TestHelper.verifyPan("PANFAILsjbdj7979", "ACC0827A364374B4376BABF40C3AB1E3AF5X");
        PanNumberResponse panNumberResponse = response.readEntity(PanNumberResponse.class);
        Assertions.verifyPanFail(response, panNumberResponse);
    }


}
