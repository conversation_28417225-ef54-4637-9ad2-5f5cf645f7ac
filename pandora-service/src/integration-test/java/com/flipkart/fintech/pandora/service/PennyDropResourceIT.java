package com.flipkart.fintech.pandora.service;

import com.flipkart.fintech.pandora.api.model.response.pennyDrop.PennyDropResponse;
import com.flipkart.fintech.pandora.service.common.Assertions;
import lombok.extern.slf4j.Slf4j;
import javax.ws.rs.core.Response;
import org.testng.annotations.Test;

@Slf4j
public class PennyDropResourceIT {
    @Test(description = "penny drop IMPS success", priority = 1)
    public void pennyDropImps() {
        Response response = com.flipkart.fintech.pandora.service.common.TestHelper.pennyDropImps("ACC2DEDB3BC47054F818FAA8E680072E011R", "ACC1234", "CAN-1234", "true");
        PennyDropResponse pennyDropResponse = response.readEntity(PennyDropResponse.class);
        Assertions.verifyPennyDropImpsResponse(response, pennyDropResponse);
    }

    @Test(description = "penny drop IMPS failure", priority = 2)
    public void pennyDropImpsFail() {
        Response response = com.flipkart.fintech.pandora.service.common.TestHelper.pennyDropImpsFail("ACC2DEDB3BC47054F818FAA8E680072E011R", "ACC1234", "CAN-1234", "true");
        PennyDropResponse pennyDropResponse = response.readEntity(PennyDropResponse.class);
        Assertions.verifyPennyDropImpsResponseFail(response, pennyDropResponse);
    }

    @Test(description = "penny drop UPI success", priority = 3)
    public void pennyDropUpi() {
        Response response = com.flipkart.fintech.pandora.service.common.TestHelper.pennyDropUpi("ACC2DEDB3BC47054F818FAA8E680072E011R", "ACC1234", "CAN-1234", "true");
        PennyDropResponse pennyDropResponse = response.readEntity(PennyDropResponse.class);
        Assertions.verifyPennyDropUpiResponse(response, pennyDropResponse);
    }

    @Test(description = "penny drop UPI failure", priority = 4)
    public void pennyDropUpiFail() {
        Response response = com.flipkart.fintech.pandora.service.common.TestHelper.pennyDropUpiFail("ACC2DEDB3BC47054F818FAA8E680072E011R", "ACC1234", "CAN-1234", "true");
        PennyDropResponse pennyDropResponse = response.readEntity(PennyDropResponse.class);
        Assertions.verifyPennyDropUpiResponseFail(response, pennyDropResponse);
    }
    
}
