package com.flipkart.fintech.pandora.service;

import com.flipkart.fintech.pandora.service.common.ApplicationServer;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.AfterSuite;
import org.testng.annotations.BeforeSuite;

@Slf4j
public class ServerIT {

    @BeforeSuite
    public void startServer() throws Exception {
        log.info("Starting pandora application server...");
        ApplicationServer.SERVER.before();
    }

    @AfterSuite
    public void stopServer() {
        log.info("Stopping pandora application server...");
        ApplicationServer.SERVER.after();
    }
}
