package com.flipkart.fintech.pandora.service.common;

import com.flipkart.fintech.pandora.api.model.response.onboarding.PanNumberResponse;
import com.flipkart.fintech.pandora.api.model.response.pennyDrop.PennyDropResponse;
import lombok.extern.slf4j.Slf4j;
import com.flipkart.fintech.pandora.api.model.response.ckyc.SearchCkycResponse;
import com.flipkart.fintech.pandora.api.model.response.ckyc.DownloadCkycResponse;
import com.flipkart.fintech.pandora.api.model.response.ekyc.EkycSendOtpResponse;
import com.flipkart.fintech.pandora.api.model.response.ekyc.EkycVerifyOtpResponse;
import com.flipkart.fintech.pandora.api.model.response.ekyc.KycDetailsResponse;
import com.flipkart.fintech.pandora.api.model.response.onboarding.UserResponse;
import com.flipkart.fintech.pandora.api.model.common.STATUS;
import javax.ws.rs.core.Response;
import org.testng.Assert;

@Slf4j
public class Assertions {

    private Assertions() {
    }

    public static void verifySearchCkycSuccess(Response response, SearchCkycResponse searchCkycResponse) {
        log.info("verifying search Ckyc success...");
        verifyResponseStatusOK(response);
        verifySearchCkycSuccessResponse(searchCkycResponse);
    }

    public static void verifySearchCkycReject(Response response, SearchCkycResponse searchCkycResponse) {
        log.info("verifying search Ckyc reject...");
        verifyResponseStatusOK(response);
        verifySearchCkycRejectResponse(searchCkycResponse);
    }

    public static void verifySearchCkycFail(Response response, SearchCkycResponse searchCkycResponse) {
        log.info("verifying search Ckyc failure ...");
        verifyResponseStatusServerError(response);
        verifyResponseCKYCRejected(searchCkycResponse);
    }

    public static void verifyDowloadCkycSuccess(Response response, DownloadCkycResponse downloadCkycResponse) {
        log.info("verifying download Ckyc success ...");
        verifyResponseStatusServerError(response);
        verifyDowloadCkycSuccessResponse(downloadCkycResponse);
    }

    public static void verifyDowloadCkycReject(Response response, DownloadCkycResponse downloadCkycResponse) {
        log.info("verifying download Ckyc reject ...");
        verifyResponseStatusServerError(response);
        verifyDowloadCkycRejectResponse(downloadCkycResponse);
    }


    public static void verifyDowloadCkyFail(Response response, DownloadCkycResponse downloadCkycResponse) {
        log.info("verifying download Ckyc failure...");
        verifyResponseStatusServerError(response);
        verifydownloadCkycResponseError(downloadCkycResponse);
    }

    public static void verifySendOtp(Response response, EkycSendOtpResponse ekycSendOtpResponse) {
        log.info("send otp ekyc ...");
        verifyResponseStatusOK(response);
        verifySendOtpResponse(ekycSendOtpResponse);
    }

    public static void verifySendOtpFail(Response response, EkycSendOtpResponse ekycSendOtpResponse) {
        log.info("send otp ekyc failure...");
        verifyResponseStatusServerError(response);
        verifySendOtpResponseError(ekycSendOtpResponse);
    }

    public static void verifyOtpSuccess(Response response, EkycVerifyOtpResponse ekycVerifyOtpResponse) {
        log.info("verify otp ekyc success...");
        verifyResponseStatusOK(response);
        verifyOtpResponse(ekycVerifyOtpResponse);
    }

    public static void verifyOtpFail(Response response, EkycVerifyOtpResponse ekycVerifyOtpResponse) {
        log.info("verify otp ekyc failure...");
        verifyResponseStatusServerError(response);
        verifyOtpResponseError(ekycVerifyOtpResponse);
    }

    public static void sendDocumentSuccess(Response response, KycDetailsResponse kycDetailsResponse) {
        log.info("send document ekyc success...");
        verifyResponseStatusOK(response);
        verifySendDocumentResponse(kycDetailsResponse);
    }

    public static void sendDocumentFail(Response response, KycDetailsResponse kycDetailsResponse) {
        log.info("send document ekyc failure...");
        verifyResponseStatusServerError(response);
        verifySendDocumentResponseError(kycDetailsResponse);
    }
    public static void verifyCheckEligibilityRequest(Response response, UserResponse userResponse) {
        log.info("check eligibility request success...");
        verifyResponseStatusOK(response);
        verifyCheckEligibility(userResponse);
    }

    public static void verifyCheckEligibilityRequestFail(Response response, UserResponse userResponse) {
        log.info("check eligibility request failure...");
        verifyResponseStatusNotFound(response);
        verifyCheckEligibilityResponseError(userResponse);
    }

    public static void verifyCreateLoan(Response response, UserResponse userResponse) {
        log.info("create loan success...");
        verifyResponseStatusOK(response);
        verifyCreateLoanResponse(userResponse);
    }

    public static void verifyCreateLoanFail(Response response, UserResponse userResponse) {
        log.info("create loan failure...");
        verifyResponseStatusNotFound(response);
        verifyCreateLoanResponseError(userResponse);
    }

    public static void verifyPan(Response response, PanNumberResponse panNumberResponse) {
        log.info("verify pan success...");
        verifyResponseStatusOK(response);
        verifyPanResponse(panNumberResponse);
    }

    public static void verifyPanFail(Response response, PanNumberResponse panNumberResponse) {
        log.info("verify pan failure...");
        verifyResponseStatusServerError(response);
        verifyPanResponseError(panNumberResponse);
    }

    public static void verifyPennyDropImpsResponse(Response response, PennyDropResponse pennyDropResponse) {
        log.info("verify penny drop IMPS success...");
        verifyResponseStatusOK(response);
        verifyPennyDropImpsResponse(pennyDropResponse);
    }

    public static void verifyPennyDropImpsResponseFail(Response response, PennyDropResponse pennyDropResponse) {
        log.info("verify penny drop IMPS failure...");
        verifyResponseStatusServerError(response);
        verifyPennyDropImpsResponseError(pennyDropResponse);
    }

    public static void verifyPennyDropUpiResponse(Response response, PennyDropResponse pennyDropResponse) {
        log.info("verify penny drop IMPS success...");
        verifyResponseStatusOK(response);
        verifyPennyDropUpiResponse(pennyDropResponse);
    }

    public static void verifyPennyDropUpiResponseFail(Response response, PennyDropResponse pennyDropResponse) {
        log.info("verify penny drop IMPS failure...");
        verifyResponseStatusServerError(response);
        verifyPennyDropUpiResponseError(pennyDropResponse);
    }

    public static void verifyCheckLoan(Response response) {
        log.info("verify penny drop IMPS failure...");
        verifyResponseStatusOK(response);
        verifyCheckLoanResponse(response);
    }

    public static void verifyResponseStatusOK(Response response) {
        Assert.assertEquals(response.getStatus(), Response.Status.OK.getStatusCode());
    }

    public static void verifyResponseStatusServerError(Response response) {
        Assert.assertEquals(response.getStatus(), Response.Status.INTERNAL_SERVER_ERROR.getStatusCode());
    }

    public static void verifyResponseStatus429(Response response) {
        Assert.assertEquals(response.getStatus(), 429);
    }

    public static void verifyResponseStatusNotFound(Response response) {
        Assert.assertEquals(response.getStatus(), Response.Status.NOT_FOUND.getStatusCode());
    }

    public static <T> void verifySearchCkycSuccessResponse(SearchCkycResponse searchCkycResponse) {
        Assert.assertEquals(searchCkycResponse.getTransactionStatus(), "CKYCSuccess");
    }

    public static <T> void verifySearchCkycRejectResponse(SearchCkycResponse searchCkycResponse) {
        Assert.assertEquals(searchCkycResponse.getTransactionStatus(), "CKYCReject");
    }

    public static <T> void verifyResponseCKYCRejected(SearchCkycResponse searchCkycResponse) {
        Assert.assertEquals(searchCkycResponse.getTransactionStatus(), null);
        Assert.assertNull(searchCkycResponse.getCkycId());
    }

    public static <T> void verifyDowloadCkycSuccessResponse(DownloadCkycResponse downloadCkycResponse) {
        Assert.assertEquals(downloadCkycResponse.getStatus(), "CKYCSuccess");
        Assert.assertNotNull(downloadCkycResponse.getUserData());
    }

    public static <T> void verifyDowloadCkycRejectResponse(DownloadCkycResponse downloadCkycResponse) {
        Assert.assertEquals(downloadCkycResponse.getStatus(), "CKYCReject");
        Assert.assertNotNull(downloadCkycResponse.getUserData());
    }

    public static <T> void verifySendOtpResponse(EkycSendOtpResponse ekycSendOtpResponse) {
        Assert.assertEquals(ekycSendOtpResponse.getStatus(), STATUS.SUCCESS);
        Assert.assertNotNull(ekycSendOtpResponse.getTransactionReference());
    }

    public static <T> void verifyOtpResponse(EkycVerifyOtpResponse ekycVerifyOtpResponse) {
        Assert.assertEquals(ekycVerifyOtpResponse.getStatus(), STATUS.SUCCESS);
        Assert.assertNotNull(ekycVerifyOtpResponse.getTransactionReference());
    }

    public static <T> void verifySendDocumentResponse(KycDetailsResponse kycDetailsResponse) {
        Assert.assertEquals(kycDetailsResponse.getStatus(), "SUCCESS");
        Assert.assertNotNull(kycDetailsResponse.getData());
    }

    public static <T> void verifyCheckEligibility(UserResponse userResponse) {
        Assert.assertEquals(userResponse.getStatus(),  STATUS.SUCCESS);
    }

    public static <T> void verifyCreateLoanResponse(UserResponse userResponse) {
        Assert.assertEquals(userResponse.getStatus(),  STATUS.SUCCESS);
    }
    public static <T> void verifyPanResponse(PanNumberResponse panNumberResponse) {
        Assert.assertEquals(panNumberResponse.getStatus(),  STATUS.SUCCESS);
    }

    public static <T> void verifyPennyDropImpsResponse(PennyDropResponse pennyDropResponse) {
        Assert.assertEquals(pennyDropResponse.getStatus(), "SUCCESS");
        Assert.assertNotNull(pennyDropResponse.getTransactionId());
    }

    public static <T> void verifyPennyDropUpiResponse(PennyDropResponse pennyDropResponse) {
        Assert.assertEquals(pennyDropResponse.getStatus(), "SUCCESS");
        Assert.assertNotNull(pennyDropResponse.getTransactionId());
    }

    public static <T> void verifyCheckLoanResponse(Response response) {
        Assert.assertEquals(response.getStatus(), 200);
    }

    public static <T> void verifydownloadCkycResponseError(DownloadCkycResponse downloadCkycResponse) {
        Assert.assertEquals(downloadCkycResponse.getStatus(), null);
    }

    public static <T> void verifySendOtpResponseError(EkycSendOtpResponse ekycSendOtpResponse) {
        Assert.assertEquals(ekycSendOtpResponse.getStatus(), null);
    }

    public static <T> void verifyOtpResponseError(EkycVerifyOtpResponse ekycSendOtpResponse) {
        Assert.assertEquals(ekycSendOtpResponse.getStatus(), null);
    }

    public static <T> void verifySendDocumentResponseError(KycDetailsResponse kycDetailsResponse) {
        Assert.assertEquals(kycDetailsResponse.getStatus(), null);
    }

    public static <T> void verifyCheckEligibilityResponseError(UserResponse userResponse) {
        Assert.assertEquals(userResponse.getStatus(), null);
    }

    public static <T> void verifyCreateLoanResponseError(UserResponse userResponse) {
        Assert.assertEquals(userResponse.getStatus(), null);
    }

    public static <T> void verifyPanResponseError(PanNumberResponse panNumberResponse) {
        Assert.assertEquals(panNumberResponse.getStatus(), null);
    }

    public static <T> void verifyPennyDropImpsResponseError(PennyDropResponse pennyDropResponse) {
        Assert.assertEquals(pennyDropResponse.getStatus(), null);
    }

    public static <T> void verifyPennyDropUpiResponseError(PennyDropResponse pennyDropResponse) {
        Assert.assertEquals(pennyDropResponse.getStatus(), null);
    }

    public static void verifySendOtpFailCode995(Response response, EkycSendOtpResponse ekycSendOtpResponse) {
        log.info("send otp ekyc failure with aadhaar code 995...");
        verifyResponseStatusOK(response);
        verifySendOtpResponseErrorInvalidAadhaar(ekycSendOtpResponse);
    }

    public static void verifySendOtpFailCode996(Response response, EkycSendOtpResponse ekycSendOtpResponse) {
        log.info("send otp ekyc failure with aadhaar code 996...");
        verifyResponseStatusOK(response);
        verifySendOtpResponseErrorInvalidAadhaar(ekycSendOtpResponse);
    }

    public static void verifySendOtpFailCode997(Response response, EkycSendOtpResponse ekycSendOtpResponse) {
        log.info("send otp ekyc failure with aadhaar code 997...");
        verifyResponseStatusOK(response);
        verifySendOtpResponseErrorInvalidAadhaar(ekycSendOtpResponse);
    }

    public static void verifySendOtpFailCode998(Response response, EkycSendOtpResponse ekycSendOtpResponse) {
        log.info("send otp ekyc failure with aadhaar code 998...");
        verifyResponseStatusOK(response);
        verifySendOtpResponseErrorInvalidAadhaar(ekycSendOtpResponse);
    }

    public static void verifySendOtpFailCode950(Response response, EkycSendOtpResponse ekycSendOtpResponse) {
        log.info("send otp ekyc failure with aadhaar code 950...");
        verifyResponseStatusOK(response);
        verifySendOtpResponseErrorTooManyAttempts(ekycSendOtpResponse);
    }

    public static void verifySendOtpFailCode952(Response response, EkycSendOtpResponse ekycSendOtpResponse) {
        log.info("send otp ekyc failure with aadhaar code 952...");
        verifyResponseStatusOK(response);
        verifySendOtpResponseErrorTooManyAttempts(ekycSendOtpResponse);
    }

    public static void verifySendOtpFailCode110(Response response, EkycSendOtpResponse ekycSendOtpResponse) {
        log.info("send otp ekyc failure with aadhaar code 110...");
        verifyResponseStatusOK(response);
        verifySendOtpResponseErrorEmailMobileNotLinked(ekycSendOtpResponse);
    }

    public static void verifySendOtpFailCode111(Response response, EkycSendOtpResponse ekycSendOtpResponse) {
        log.info("send otp ekyc failure with aadhaar code 111...");
        verifyResponseStatusOK(response);
        verifySendOtpResponseErrorEmailMobileNotLinked(ekycSendOtpResponse);
    }

    public static void verifySendOtpFailCode112(Response response, EkycSendOtpResponse ekycSendOtpResponse) {
        log.info("send otp ekyc failure with aadhaar code 112...");
        verifyResponseStatusOK(response);
        verifySendOtpResponseErrorEmailMobileNotLinked(ekycSendOtpResponse);
    }

    public static void verifySendOtpFailCode113(Response response, EkycSendOtpResponse ekycSendOtpResponse) {
        log.info("send otp ekyc failure with aadhaar code 113...");
        verifyResponseStatusOK(response);
        verifySendOtpResponseErrorEmailMobileNotLinked(ekycSendOtpResponse);
    }

    public static void verifySendOtpFailCode114(Response response, EkycSendOtpResponse ekycSendOtpResponse) {
        log.info("send otp ekyc failure with aadhaar code 114...");
        verifyResponseStatusOK(response);
        verifySendOtpResponseErrorEmailMobileNotLinked(ekycSendOtpResponse);
    }

    public static void verifySendOtpFailCode115(Response response, EkycSendOtpResponse ekycSendOtpResponse) {
        log.info("send otp ekyc failure with aadhaar code 115...");
        verifyResponseStatusOK(response);
        verifySendOtpResponseErrorEmailMobileNotLinked(ekycSendOtpResponse);
    }

    public static void verifySendOtpFailRandomCode(Response response, EkycSendOtpResponse ekycSendOtpResponse) {
        log.info("send otp ekyc failure with unmapped aadhaar code ...");
        verifyResponseStatusOK(response);
        verifySendOtpResponseErrorRandomCode(ekycSendOtpResponse);
    }

    public static <T> void verifySendOtpResponseErrorInvalidAadhaar(EkycSendOtpResponse ekycSendOtpResponse) {
        Assert.assertEquals(ekycSendOtpResponse.getStatus(), STATUS.INVALID_AADHAAR);
    }

    public static <T> void verifySendOtpResponseErrorTooManyAttempts(EkycSendOtpResponse ekycSendOtpResponse) {
        Assert.assertEquals(ekycSendOtpResponse.getStatus(), STATUS.TOO_MANY_ATTEMPTS);
    }

    public static <T> void verifySendOtpResponseErrorEmailMobileNotLinked(EkycSendOtpResponse ekycSendOtpResponse) {
        Assert.assertEquals(ekycSendOtpResponse.getStatus(), STATUS.AADHAAR_EMAIL_MOBILE_NOT_LINKED);
    }

    public static <T> void verifySendOtpResponseErrorRandomCode(EkycSendOtpResponse ekycSendOtpResponse) {
        Assert.assertEquals(ekycSendOtpResponse.getStatus(), STATUS.AADHAAR_API_FAILED);
    }

    public static void verifyOtpFailCode1(Response response, EkycVerifyOtpResponse ekycVerifyOtpResponse) {
        log.info("verify otp ekyc failure...");
        verifyResponseStatusOK(response);
        verifyOtpResponseErrorFailCode1(ekycVerifyOtpResponse);
    }

    public static void verifyOtpFailCode2(Response response, EkycVerifyOtpResponse ekycVerifyOtpResponse) {
        log.info("verify otp ekyc failure...");
        verifyResponseStatusOK(response);
        verifyOtpResponseErrorFailCode2(ekycVerifyOtpResponse);
    }

    public static void verifyOtpFailCode3(Response response, EkycVerifyOtpResponse ekycVerifyOtpResponse) {
        log.info("verify otp ekyc failure...");
        verifyResponseStatusOK(response);
        verifyOtpResponseErrorFailCode3(ekycVerifyOtpResponse);
    }

    public static void verifyOtpFailCode4(Response response, EkycVerifyOtpResponse ekycVerifyOtpResponse) {
        log.info("verify otp ekyc failure...");
        verifyResponseStatusOK(response);
        verifyOtpResponseErrorFailCode4(ekycVerifyOtpResponse);
    }

    public static void verifyOtpFailCode5(Response response, EkycVerifyOtpResponse ekycVerifyOtpResponse) {
        log.info("verify otp ekyc failure...");
        verifyResponseStatusOK(response);
        verifyOtpResponseErrorFailCode5(ekycVerifyOtpResponse);
    }

    public static void verifyOtpFailInvalidotp(Response response, EkycVerifyOtpResponse ekycVerifyOtpResponse) {
        log.info("verify otp ekyc failure...");
        verifyResponseStatusOK(response);
        verifyOtpResponseErrorInvalidOtp(ekycVerifyOtpResponse);
    }

    public static <T> void verifyOtpResponseErrorFailCode1(EkycVerifyOtpResponse ekycSendOtpResponse) {
        Assert.assertEquals(ekycSendOtpResponse.getStatus(), STATUS.INVALID_PIN);
    }

    public static <T> void verifyOtpResponseErrorFailCode2(EkycVerifyOtpResponse ekycSendOtpResponse) {
        Assert.assertEquals(ekycSendOtpResponse.getStatus(), STATUS.INVALID_AGE);
    }

    public static <T> void verifyOtpResponseErrorFailCode3(EkycVerifyOtpResponse ekycSendOtpResponse) {
        Assert.assertEquals(ekycSendOtpResponse.getStatus(), STATUS.INVALID_CKYC_ACCTYPE_03);
    }

    public static <T> void verifyOtpResponseErrorFailCode4(EkycVerifyOtpResponse ekycSendOtpResponse) {
        Assert.assertEquals(ekycSendOtpResponse.getStatus(), STATUS.INVALID_CKYC_ACCTYPE_04);
    }

    public static <T> void verifyOtpResponseErrorFailCode5(EkycVerifyOtpResponse ekycSendOtpResponse) {
        Assert.assertEquals(ekycSendOtpResponse.getStatus(), STATUS.NAME_MISMATCH_PAN_AADHAAR);
    }

    public static <T> void verifyOtpResponseErrorInvalidOtp(EkycVerifyOtpResponse ekycSendOtpResponse) {
        Assert.assertEquals(ekycSendOtpResponse.getStatus(), STATUS.INCORRECT_OTP);
    }

}
