package com.flipkart.fintech.pandora.service.common;

import com.flipkart.fintech.pandora.api.model.request.EncryptionKeyData;
import com.flipkart.fintech.pandora.api.model.request.onboarding.CompressedUserRequest;
import com.flipkart.fintech.pandora.api.model.request.onboarding.PanNumberRequest;
import com.flipkart.fintech.pandora.api.model.request.onboarding.UserRequest;
import org.glassfish.jersey.client.JerseyClientBuilder;
import javax.ws.rs.client.Client;
import javax.ws.rs.client.Entity;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.MultivaluedHashMap;
import javax.ws.rs.core.MultivaluedMap;
import javax.ws.rs.core.Response;
import com.flipkart.fintech.pandora.api.model.request.ckyc.DownloadCkycRequest;
import com.flipkart.fintech.pandora.api.model.request.ckyc.SearchCkycRequest;
import com.flipkart.fintech.pandora.api.model.common.AuthenticationType;
import com.flipkart.fintech.pandora.api.model.request.ekyc.EkycSendOtpRequest;
import com.flipkart.fintech.pandora.api.model.request.ekyc.EkycVerifyOtpRequest;
import com.flipkart.fintech.pandora.api.model.request.ekyc.SendKycDocumentRequest;
import com.flipkart.fintech.pandora.api.model.request.pennyDrop.PennyDropRequest;
import com.flipkart.fintech.pandora.api.model.PennyDropMethod;

public class TestHelper {
    private static Client client = new JerseyClientBuilder().build();

    private TestHelper() {
    }

    private static final class URL {
        public static final String search_ckyc = "http://localhost:%d/pandora/1/ckyc/IDFC/search";
        public static final String download_ckyc = "http://localhost:%d/pandora/1/ckyc/IDFC/download";
        public static final String send_document = "http://localhost:%d/pandora/1/IDFC/ekyc/send-document";
        public static final String send_otp = "http://localhost:%d/pandora/1/IDFC/ekyc/send_otp";
        public static final String verify_otp = "http://localhost:%d/pandora/1/IDFC/ekyc/verify_otp";
        public static final String check_loan = "http://localhost:%d/pandora/idfc/loan/details";
        public static final String penny_drop = "http://localhost:%d/pandora/1/penny-drop/IDFC";
        public static final String create_loan = "http://localhost:%d/pandora/1/IDFC/create-loan";
        public static final String check_eligibility = "http://localhost:%d/pandora/1/IDFC/checkEligibilityRequest";
        public static final String pan_verification = "http://localhost:%d/pandora/1/IDFC/verifyPan";
    }

    //private static final int localPort = ApplicationServer.SERVER.getLocalPort();
    private static final int localPort = 8214;

    private static final String base64EncodedDocumentData = "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";

    public static Response searchCkyc(String accountId, String panNumber) {
        SearchCkycRequest searchCkycRequest = new SearchCkycRequest();
        searchCkycRequest.setAccountId(accountId);
        searchCkycRequest.setPanNumber(panNumber);
        return client.target(String.format(URL.search_ckyc, localPort))
                .request().headers(getHeaders()).post(Entity.entity(searchCkycRequest, MediaType.APPLICATION_JSON));
    }

    public static Response searchCkycFail(String accountId, String panNumber) {
        SearchCkycRequest searchCkycRequest = new SearchCkycRequest();
        searchCkycRequest.setAccountId(accountId);
        searchCkycRequest.setPanNumber(panNumber);
        return client.target(String.format(URL.search_ckyc, localPort))
                .request().headers(getHeaders()).post(Entity.entity(searchCkycRequest, MediaType.APPLICATION_JSON));
    }

    public static Response downloadCkyc(String financialProviderRefId, String userRefId, String ckycId, String password) {
        DownloadCkycRequest downloadCkycRequest = new DownloadCkycRequest();
        downloadCkycRequest.setFinancialProviderRefId(financialProviderRefId);
        downloadCkycRequest.setUserRefId(userRefId);
        downloadCkycRequest.setCkycId(ckycId);
        downloadCkycRequest.setPassword(password);
        downloadCkycRequest.setPasswordType(AuthenticationType.PHONE_NUMBER);
        return client.target(String.format(URL.download_ckyc, localPort))
                .request().headers(getHeaders()).post(Entity.entity(downloadCkycRequest, MediaType.APPLICATION_JSON));
    }

    public static Response downloadCkycFail(String financialProviderRefId, String userRefId, String ckycId, String password) {
        DownloadCkycRequest downloadCkycRequest = new DownloadCkycRequest();
        downloadCkycRequest.setFinancialProviderRefId(financialProviderRefId);
        downloadCkycRequest.setUserRefId(userRefId);
        downloadCkycRequest.setCkycId(ckycId);
        downloadCkycRequest.setPassword(password);
        downloadCkycRequest.setPasswordType(AuthenticationType.PHONE_NUMBER);
        return client.target(String.format(URL.download_ckyc, localPort))
                .request().headers(getHeaders()).post(Entity.entity(downloadCkycRequest, MediaType.APPLICATION_JSON));
    }

    private static MultivaluedMap<String, Object> getHeaders() {
        MultivaluedMap<String, Object> headers = new MultivaluedHashMap<>();
        headers.add("Content-Type", Constants.CONTENT_TYPE);
        headers.add("X-Tenant-Id", Constants.FK_CONSUMER_CREDIT);
        return headers;
    }

    private static MultivaluedMap<String, Object> getHeaders(String requestId) {
        MultivaluedMap<String, Object> headers = new MultivaluedHashMap<>();
        headers.add("Content-Type", Constants.CONTENT_TYPE);
        headers.add("X-Tenant-Id", Constants.FK_CONSUMER_CREDIT);
        return headers;
    }

    public static Response sendOtp(String externalRefId, String aadhaarNumber, String externalRefKey) {
        EkycSendOtpRequest ekycSendOtpRequest = new EkycSendOtpRequest();
        ekycSendOtpRequest.setExternalRefId(externalRefId);
        ekycSendOtpRequest.setAadhaarNumber(aadhaarNumber);
        ekycSendOtpRequest.setExternalRefKey(externalRefKey);
        EncryptionKeyData encryptionKeyData = new  EncryptionKeyData();
        encryptionKeyData.setEncKey("hGTFVf/bmyGm6tO+aDXAMhYc6yu4fWo9rht+AUeEv2hNByemg9yAULadfGixvmi4afE/m21iY09QIk33wRgLmQTNcalIv0fsjfN8aGZq+JwcHWkkuuWUOIW9zLJI9DASPgrX/p/UmiI4X9ReGP0QLhtqUQrTWcoepMZ2oCgrJoJQu9CMiAJWDMMs3E1wwbnwXNrDb/7bPrik8Kfu7nzNGjSibE+NspoOQdnzmt3kWpR6qWHt/Oe/ljQ90tCOUsHVNN3Eg45NCL/W6gqB4nnw1qKAQI3qVqJNYWydUEygDmZAnatT2TXBRkg0OuJI3zJ0dsnWELcuI9vJSYFDiyr2wg==");
        encryptionKeyData.setEncKeyRef("1:f67d4584-f76d-4435-b065-5f7553539d30");
        ekycSendOtpRequest.setEncKeyData(encryptionKeyData);
        return client.target(String.format(URL.send_otp, localPort))
                .request().headers(getHeaders()).post(Entity.entity(ekycSendOtpRequest, MediaType.APPLICATION_JSON));
    }

    public static Response sendOtpFail(String externalRefId, String aadhaarNumber, String externalRefKey) {
        EkycSendOtpRequest ekycSendOtpRequest = new EkycSendOtpRequest();
        ekycSendOtpRequest.setExternalRefId(externalRefId);
        ekycSendOtpRequest.setAadhaarNumber(aadhaarNumber);
        ekycSendOtpRequest.setExternalRefKey(externalRefKey);
        EncryptionKeyData encryptionKeyData = new  EncryptionKeyData();
        encryptionKeyData.setEncKey("af8FyW7cc00rpmF6cj6BBzoMrblRyj6U/ZIFVa8O6KjSinKgsxpH6Z0Klyd87QWNLN/ce1jgam5P2PUQ04gvMI4Dc8bYJNFvB73jnpYBvbZNzWREYtSE4heJRb1mY6VVevNrSJo3qnNIApZWiZbXX50uj1+AnIN/bFcXxmJiF0tIHQNQgRmH1VanZvuxkInUMj6jf5NcnDE7m2l8f8Bj5HqnsY1v3VsoZ2dwM4yZeTrc7p3lw0VwdlovKy6pZVWU4Lmc6vQysp1EsHQb165bemXWq4m6+mRLGJxKkRLxSdhHyKwL6eRfc8vhtANlRPf0uo2J1O9m0D4KOAeu2U2vmQ==");
        encryptionKeyData.setEncKeyRef("1:310b67a0-e678-4c14-a66e-95a5c70c8d6e");
        ekycSendOtpRequest.setEncKeyData(encryptionKeyData);
        return client.target(String.format(URL.send_otp, localPort))
                .request().headers(getHeaders()).post(Entity.entity(ekycSendOtpRequest, MediaType.APPLICATION_JSON));
    }

    public static Response verifyOtp(String otp, String externalRefId, String aadhaarNumber, String externalRefKey) {
        EkycVerifyOtpRequest ekycVerifyOtpRequest = new EkycVerifyOtpRequest();
        ekycVerifyOtpRequest.setAadhaarNumber(aadhaarNumber);
        ekycVerifyOtpRequest.setOtp(otp);
        ekycVerifyOtpRequest.setExternalRefKey(externalRefKey);
        ekycVerifyOtpRequest.setExternalRefId(externalRefId);
        ekycVerifyOtpRequest.setTransactionReference("");
        ekycVerifyOtpRequest.setCkycFlow(false);
        EncryptionKeyData encryptionKeyData = new  EncryptionKeyData();
        encryptionKeyData.setEncKey("af8FyW7cc00rpmF6cj6BBzoMrblRyj6U/ZIFVa8O6KjSinKgsxpH6Z0Klyd87QWNLN/ce1jgam5P2PUQ04gvMI4Dc8bYJNFvB73jnpYBvbZNzWREYtSE4heJRb1mY6VVevNrSJo3qnNIApZWiZbXX50uj1+AnIN/bFcXxmJiF0tIHQNQgRmH1VanZvuxkInUMj6jf5NcnDE7m2l8f8Bj5HqnsY1v3VsoZ2dwM4yZeTrc7p3lw0VwdlovKy6pZVWU4Lmc6vQysp1EsHQb165bemXWq4m6+mRLGJxKkRLxSdhHyKwL6eRfc8vhtANlRPf0uo2J1O9m0D4KOAeu2U2vmQ==");
        encryptionKeyData.setEncKeyRef("1:310b67a0-e678-4c14-a66e-95a5c70c8d6e");
        ekycVerifyOtpRequest.setEncKeyData(encryptionKeyData);
        return client.target(String.format(URL.verify_otp, localPort))
                .request().headers(getHeaders()).post(Entity.entity(ekycVerifyOtpRequest, MediaType.APPLICATION_JSON));
    }

    public static Response verifyOtpFail(String otp, String externalRefId, String aadhaarNumber, String externalRefKey) {
        EkycVerifyOtpRequest ekycVerifyOtpRequest = new EkycVerifyOtpRequest();
        ekycVerifyOtpRequest.setAadhaarNumber(aadhaarNumber);
        ekycVerifyOtpRequest.setOtp(otp);
        ekycVerifyOtpRequest.setExternalRefKey(externalRefKey);
        ekycVerifyOtpRequest.setExternalRefId(externalRefId);
        ekycVerifyOtpRequest.setTransactionReference("");
        ekycVerifyOtpRequest.setCkycFlow(false);
        EncryptionKeyData encryptionKeyData = new  EncryptionKeyData();
        encryptionKeyData.setEncKey("af8FyW7cc00rpmF6cj6BBzoMrblRyj6U/ZIFVa8O6KjSinKgsxpH6Z0Klyd87QWNLN/ce1jgam5P2PUQ04gvMI4Dc8bYJNFvB73jnpYBvbZNzWREYtSE4heJRb1mY6VVevNrSJo3qnNIApZWiZbXX50uj1+AnIN/bFcXxmJiF0tIHQNQgRmH1VanZvuxkInUMj6jf5NcnDE7m2l8f8Bj5HqnsY1v3VsoZ2dwM4yZeTrc7p3lw0VwdlovKy6pZVWU4Lmc6vQysp1EsHQb165bemXWq4m6+mRLGJxKkRLxSdhHyKwL6eRfc8vhtANlRPf0uo2J1O9m0D4KOAeu2U2vmQ==");
        encryptionKeyData.setEncKeyRef("1:310b67a0-e678-4c14-a66e-95a5c70c8d6e");
        ekycVerifyOtpRequest.setEncKeyData(encryptionKeyData);
        return client.target(String.format(URL.verify_otp, localPort))
                .request().headers(getHeaders()).post(Entity.entity(ekycVerifyOtpRequest, MediaType.APPLICATION_JSON));
    }

    public static Response sendDocument(String documentType, String documentPassword, String financialProviderRefId) {
        SendKycDocumentRequest sendKycDocumentRequest = new SendKycDocumentRequest();
        sendKycDocumentRequest.setDocumentType(documentType);
        sendKycDocumentRequest.setDocumentPassword(documentPassword);
        sendKycDocumentRequest.setBase64EncodedDocumentData(base64EncodedDocumentData);
        sendKycDocumentRequest.setFinancialProviderRefId(financialProviderRefId);
        EncryptionKeyData encryptionKeyData = new  EncryptionKeyData();
        encryptionKeyData.setEncKey("hGTFVf/bmyGm6tO+aDXAMhYc6yu4fWo9rht+AUeEv2hNByemg9yAULadfGixvmi4afE/m21iY09QIk33wRgLmQTNcalIv0fsjfN8aGZq+JwcHWkkuuWUOIW9zLJI9DASPgrX/p/UmiI4X9ReGP0QLhtqUQrTWcoepMZ2oCgrJoJQu9CMiAJWDMMs3E1wwbnwXNrDb/7bPrik8Kfu7nzNGjSibE+NspoOQdnzmt3kWpR6qWHt/Oe/ljQ90tCOUsHVNN3Eg45NCL/W6gqB4nnw1qKAQI3qVqJNYWydUEygDmZAnatT2TXBRkg0OuJI3zJ0dsnWELcuI9vJSYFDiyr2wg==");
        encryptionKeyData.setEncKeyRef("1:f67d4584-f76d-4435-b065-5f7553539d30");
        sendKycDocumentRequest.setEncryptionKeyData(encryptionKeyData);
        return client.target(String.format(URL.send_document, localPort))
                .request().headers(getHeaders()).post(Entity.entity(sendKycDocumentRequest, MediaType.APPLICATION_JSON));
    }

    public static Response sendDocumentFail(String documentType, String documentPassword, String financialProviderRefId) {
        SendKycDocumentRequest sendKycDocumentRequest = new SendKycDocumentRequest();
        sendKycDocumentRequest.setDocumentType(documentType);
        sendKycDocumentRequest.setDocumentPassword(documentPassword);
        sendKycDocumentRequest.setBase64EncodedDocumentData(base64EncodedDocumentData);
        sendKycDocumentRequest.setFinancialProviderRefId(financialProviderRefId);
        EncryptionKeyData encryptionKeyData = new  EncryptionKeyData();
        encryptionKeyData.setEncKey("hGTFVf/bmyGm6tO+aDXAMhYc6yu4fWo9rht+AUeEv2hNByemg9yAULadfGixvmi4afE/m21iY09QIk33wRgLmQTNcalIv0fsjfN8aGZq+JwcHWkkuuWUOIW9zLJI9DASPgrX/p/UmiI4X9ReGP0QLhtqUQrTWcoepMZ2oCgrJoJQu9CMiAJWDMMs3E1wwbnwXNrDb/7bPrik8Kfu7nzNGjSibE+NspoOQdnzmt3kWpR6qWHt/Oe/ljQ90tCOUsHVNN3Eg45NCL/W6gqB4nnw1qKAQI3qVqJNYWydUEygDmZAnatT2TXBRkg0OuJI3zJ0dsnWELcuI9vJSYFDiyr2wg==");
        encryptionKeyData.setEncKeyRef("1:f67d4584-f76d-4435-b065-5f7553539d30");
        sendKycDocumentRequest.setEncryptionKeyData(encryptionKeyData);
        return client.target(String.format(URL.send_document, localPort))
                .request().headers(getHeaders()).post(Entity.entity(sendKycDocumentRequest, MediaType.APPLICATION_JSON));
    }

    public static Response checkEligibilityRequest(String businessCategory, Double loanAmount, String controlFlowToken, String externalReferenceId) {
        UserRequest userRequest = null;
        CompressedUserRequest compressedUserRequest = (CompressedUserRequest) userRequest;
        compressedUserRequest = new CompressedUserRequest();
        compressedUserRequest.setBusinessCategory(businessCategory);
        compressedUserRequest.setType(UserRequest.RequestType.COMPRESSED);
        compressedUserRequest.setLoanAmount(loanAmount);
        compressedUserRequest.setControlFlowToken(controlFlowToken);
        compressedUserRequest.setExternalReferenceId(externalReferenceId);
        return client.target(String.format(URL.check_eligibility, localPort))
                .request().headers(getHeaders()).post(Entity.entity(compressedUserRequest, MediaType.APPLICATION_JSON));
    }

    public static Response checkEligibilityRequestFail(String businessCategory, Double loanAmount, String controlFlowToken, String externalReferenceId) {
        UserRequest userRequest = null;
        CompressedUserRequest compressedUserRequest = (CompressedUserRequest) userRequest;
        compressedUserRequest = new CompressedUserRequest();
        compressedUserRequest.setBusinessCategory(businessCategory);
        compressedUserRequest.setType(UserRequest.RequestType.COMPRESSED);
        compressedUserRequest.setLoanAmount(loanAmount);
        compressedUserRequest.setControlFlowToken(controlFlowToken);
        compressedUserRequest.setExternalReferenceId(externalReferenceId);
        return client.target(String.format(URL.check_eligibility, localPort))
                .request().headers(getHeaders()).post(Entity.entity(compressedUserRequest, MediaType.APPLICATION_JSON));
    }

    public static Response createLoan(String businessCategory, Double loanAmount, String controlFlowToken, String externalReferenceId) {
        UserRequest userRequest = null;
        CompressedUserRequest compressedUserRequest = (CompressedUserRequest) userRequest;
        compressedUserRequest = new CompressedUserRequest();
        compressedUserRequest.setBusinessCategory(businessCategory);
        compressedUserRequest.setType(UserRequest.RequestType.COMPRESSED);
        compressedUserRequest.setLoanAmount(loanAmount);
        compressedUserRequest.setControlFlowToken(controlFlowToken);
        compressedUserRequest.setExternalReferenceId(externalReferenceId);
        return client.target(String.format(URL.create_loan, localPort))
                .request().headers(getHeaders()).post(Entity.entity(compressedUserRequest, MediaType.APPLICATION_JSON));
    }

    public static Response createLoanFail(String businessCategory, Double loanAmount, String controlFlowToken, String externalReferenceId) {
        UserRequest userRequest = null;
        CompressedUserRequest compressedUserRequest = (CompressedUserRequest) userRequest;
        compressedUserRequest = new CompressedUserRequest();
        compressedUserRequest.setBusinessCategory(businessCategory);
        compressedUserRequest.setType(UserRequest.RequestType.COMPRESSED);
        compressedUserRequest.setLoanAmount(loanAmount);
        compressedUserRequest.setControlFlowToken(controlFlowToken);
        compressedUserRequest.setExternalReferenceId(externalReferenceId);
        return client.target(String.format(URL.create_loan, localPort))
                .request().headers(getHeaders()).post(Entity.entity(compressedUserRequest, MediaType.APPLICATION_JSON));
    }

    public static Response verifyPan(String pan, String accId) {
        PanNumberRequest panNumberRequest = new PanNumberRequest();
        panNumberRequest.setPan(pan);
        panNumberRequest.setAccId(accId);
        return client.target(String.format(URL.pan_verification, localPort))
                .request().headers(getHeaders()).post(Entity.entity(panNumberRequest, MediaType.APPLICATION_JSON));
    }

    public static Response pennyDropImps(String merchantUserId, String bankAccountNumber, String ifscCode, String upiHandle) {
        PennyDropRequest pennyDropRequest = new PennyDropRequest();
        pennyDropRequest.setMerchantUserId(merchantUserId);
        pennyDropRequest.setPennyDropMethod(PennyDropMethod.IMPS);
        EncryptionKeyData encryptionKeyData = new  EncryptionKeyData();
        encryptionKeyData.setEncKey("af8FyW7cc00rpmF6cj6BBzoMrblRyj6U/ZIFVa8O6KjSinKgsxpH6Z0Klyd87QWNLN/ce1jgam5P2PUQ04gvMI4Dc8bYJNFvB73jnpYBvbZNzWREYtSE4heJRb1mY6VVevNrSJo3qnNIApZWiZbXX50uj1+AnIN/bFcXxmJiF0tIHQNQgRmH1VanZvuxkInUMj6jf5NcnDE7m2l8f8Bj5HqnsY1v3VsoZ2dwM4yZeTrc7p3lw0VwdlovKy6pZVWU4Lmc6vQysp1EsHQb165bemXWq4m6+mRLGJxKkRLxSdhHyKwL6eRfc8vhtANlRPf0uo2J1O9m0D4KOAeu2U2vmQ==");
        encryptionKeyData.setEncKeyRef("1:310b67a0-e678-4c14-a66e-95a5c70c8d6e");
        pennyDropRequest.setEncryptionKeyData(encryptionKeyData);
        pennyDropRequest.setBankAccountNumber(bankAccountNumber);
        pennyDropRequest.setIfscCode(ifscCode);
        pennyDropRequest.setUpiHandle(upiHandle);
        return client.target(String.format(URL.penny_drop, localPort))
                .request().headers(getHeaders()).post(Entity.entity(pennyDropRequest, MediaType.APPLICATION_JSON));
    }

    public static Response pennyDropImpsFail(String merchantUserId, String bankAccountNumber, String ifscCode, String upiHandle) {
        PennyDropRequest pennyDropRequest = new PennyDropRequest();
        pennyDropRequest.setMerchantUserId(merchantUserId);
        pennyDropRequest.setPennyDropMethod(PennyDropMethod.IMPS);
        EncryptionKeyData encryptionKeyData = new  EncryptionKeyData();
        encryptionKeyData.setEncKey("af8FyW7cc00rpmF6cj6BBzoMrblRyj6U/ZIFVa8O6KjSinKgsxpH6Z0Klyd87QWNLN/ce1jgam5P2PUQ04gvMI4Dc8bYJNFvB73jnpYBvbZNzWREYtSE4heJRb1mY6VVevNrSJo3qnNIApZWiZbXX50uj1+AnIN/bFcXxmJiF0tIHQNQgRmH1VanZvuxkInUMj6jf5NcnDE7m2l8f8Bj5HqnsY1v3VsoZ2dwM4yZeTrc7p3lw0VwdlovKy6pZVWU4Lmc6vQysp1EsHQb165bemXWq4m6+mRLGJxKkRLxSdhHyKwL6eRfc8vhtANlRPf0uo2J1O9m0D4KOAeu2U2vmQ==");
        encryptionKeyData.setEncKeyRef("1:310b67a0-e678-4c14-a66e-95a5c70c8d6e");
        pennyDropRequest.setEncryptionKeyData(encryptionKeyData);
        pennyDropRequest.setBankAccountNumber(bankAccountNumber);
        pennyDropRequest.setIfscCode(ifscCode);
        pennyDropRequest.setUpiHandle(upiHandle);
        return client.target(String.format(URL.penny_drop, localPort))
                .request().headers(getHeaders()).post(Entity.entity(pennyDropRequest, MediaType.APPLICATION_JSON));
    }

    public static Response pennyDropUpi(String merchantUserId, String bankAccountNumber, String ifscCode, String upiHandle) {
        PennyDropRequest pennyDropRequest = new PennyDropRequest();
        pennyDropRequest.setMerchantUserId(merchantUserId);
        pennyDropRequest.setPennyDropMethod(PennyDropMethod.UPI);
        EncryptionKeyData encryptionKeyData = new  EncryptionKeyData();
        encryptionKeyData.setEncKey("af8FyW7cc00rpmF6cj6BBzoMrblRyj6U/ZIFVa8O6KjSinKgsxpH6Z0Klyd87QWNLN/ce1jgam5P2PUQ04gvMI4Dc8bYJNFvB73jnpYBvbZNzWREYtSE4heJRb1mY6VVevNrSJo3qnNIApZWiZbXX50uj1+AnIN/bFcXxmJiF0tIHQNQgRmH1VanZvuxkInUMj6jf5NcnDE7m2l8f8Bj5HqnsY1v3VsoZ2dwM4yZeTrc7p3lw0VwdlovKy6pZVWU4Lmc6vQysp1EsHQb165bemXWq4m6+mRLGJxKkRLxSdhHyKwL6eRfc8vhtANlRPf0uo2J1O9m0D4KOAeu2U2vmQ==");
        encryptionKeyData.setEncKeyRef("1:310b67a0-e678-4c14-a66e-95a5c70c8d6e");
        pennyDropRequest.setEncryptionKeyData(encryptionKeyData);
        pennyDropRequest.setBankAccountNumber(bankAccountNumber);
        pennyDropRequest.setIfscCode(ifscCode);
        pennyDropRequest.setUpiHandle(upiHandle);
        return client.target(String.format(URL.penny_drop, localPort))
                .request().headers(getHeaders()).post(Entity.entity(pennyDropRequest, MediaType.APPLICATION_JSON));
    }

    public static Response pennyDropUpiFail(String merchantUserId, String bankAccountNumber, String ifscCode, String upiHandle) {
        PennyDropRequest pennyDropRequest = new PennyDropRequest();
        pennyDropRequest.setMerchantUserId(merchantUserId);
        pennyDropRequest.setPennyDropMethod(PennyDropMethod.UPI);
        EncryptionKeyData encryptionKeyData = new  EncryptionKeyData();
        encryptionKeyData.setEncKey("af8FyW7cc00rpmF6cj6BBzoMrblRyj6U/ZIFVa8O6KjSinKgsxpH6Z0Klyd87QWNLN/ce1jgam5P2PUQ04gvMI4Dc8bYJNFvB73jnpYBvbZNzWREYtSE4heJRb1mY6VVevNrSJo3qnNIApZWiZbXX50uj1+AnIN/bFcXxmJiF0tIHQNQgRmH1VanZvuxkInUMj6jf5NcnDE7m2l8f8Bj5HqnsY1v3VsoZ2dwM4yZeTrc7p3lw0VwdlovKy6pZVWU4Lmc6vQysp1EsHQb165bemXWq4m6+mRLGJxKkRLxSdhHyKwL6eRfc8vhtANlRPf0uo2J1O9m0D4KOAeu2U2vmQ==");
        encryptionKeyData.setEncKeyRef("1:310b67a0-e678-4c14-a66e-95a5c70c8d6e");
        pennyDropRequest.setEncryptionKeyData(encryptionKeyData);
        pennyDropRequest.setBankAccountNumber(bankAccountNumber);
        pennyDropRequest.setIfscCode(ifscCode);
        pennyDropRequest.setUpiHandle(upiHandle);
        return client.target(String.format(URL.penny_drop, localPort))
                .request().headers(getHeaders()).post(Entity.entity(pennyDropRequest, MediaType.APPLICATION_JSON));
    }

    public static Response checkLoan(String appId, String crnNo) {
        Response response = client.target(String.format(URL.check_loan, localPort)).queryParam("appId", appId).queryParam("crnNo", crnNo)
                .request().headers(getHeaders()).get();
        return response;
    }

}
