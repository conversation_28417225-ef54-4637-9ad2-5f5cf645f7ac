server:
  type: default
  applicationContextPath: /pandora
  adminContextPath: /admin
  applicationConnectors:
    - type: http
      port: 8214
  adminConnectors:
    - type: http
      port: 8215
  requestLog:
    # Note:
    # 1. We do not specify a logFormat because the default log format is Common Log Format (CLF), which is good
    # 2. If we specify a logFormat, note that the format specifiers for access logs are different
    # 3. Access log accepts ONLY ONE appender - we cannot get access logs on both console and file simultaneously
    appenders:
      - type: file
        currentLogFilename: /var/log/pandora/access.log
        threshold: ALL
        archive: true
        archivedLogFilenamePattern: /var/log/pandora/access-%i.log.gz
        archivedFileCount: 5
        maxFileSize: 20MB
        timeZone: IST

jerseyClientConfig:
  maxThreads: 200
  cookiesEnabled: true
  gzipEnabledForRequests: false

logging:
  level: INFO
  appenders:
    - type: file
      threshold: ALL
      logFormat: "[%d{dd-MM-yyyy HH:mm:ss.SSS}] [%X{X-Request-ID}] %highlight([%level]) [%logger{36}] - %msg%n"
      currentLogFilename: /var/log/pandora/pandora.log
      archive: true
      archivedLogFilenamePattern: /var/log/pandora/pandora-%i.log.gz
      archivedFileCount: 5
      maxFileSize: 20MB
      timeZone: IST
    - type: file
      threshold: ERROR
      logFormat: "[%d{dd-MM-yyyy HH:mm:ss.SSS}] [%X{X-Request-ID}] %highlight([%level]) [%logger{36}] - %msg%n"
      currentLogFilename: /var/log/pandora/pandora-error.log
      archive: true
      archivedLogFilenamePattern: /var/log/pandora/pandora-error-%i.log.gz
      archivedFileCount: 5
      maxFileSize: 20MB
      timeZone: IST
    - type: console
      threshold: INFO
      logFormat: "[%d{dd-MM-yyyy HH:mm:ss.SSS}] [%X{X-Request-ID}] %highlight([%level]) [%logger{36}] - %msg %replace(%exception){'\n',' | '} %n"
      timeZone: IST
      target: stdout

healthCheckName: PandoraHealth

deployEnv: PROD

swagger:
  title: Pandora Service
  description: APIs for Pandora Service
  contact: <EMAIL>
  resourcePackage: com.flipkart.fintech.pandora.service.resources.internal,com.flipkart.fintech.pandora.service.resources.web

kisshtServiceClientConfigEfa:
  url:  https://app.kissht.com/efa
  client: kisshtNbfc
  siteKey: cfDbausU8UC3MXqsZKbILuGGpoLhl742HGrBoFTX
  secretKey: XD1I13C7H4QPTRVPFZLRFRQUAPQNC3GQ
  reverseProxyKey: kissht_pandora


rateLimitingConfig:
  [
    { "limiterKey": "IMPS_PENNY_DROP", "timeoutInMs":10000 },
    { "limiterKey": "PAN_SUBMIT", "timeoutInMs":10000 },
    { "limiterKey": "UPI_PENNY_DROP", "timeoutInMs":10000 },
    { "limiterKey": "GENERATE_OTP", "timeoutInMs":10000 },
    { "limiterKey": "CKYC_SEARCH", "timeoutInMs":10000 },
    { "limiterKey": "CKYC_DOWNLOAD", "timeoutInMs":10000 },
    { "limiterKey": "SEND_DOCUMENT", "timeoutInMs":10000 },
    { "limiterKey": "CHECK_LOAN_STATUS", "timeoutInMs":10000 },
    { "limiterKey": "CHECK_ELIGIBILITY", "timeoutInMs": 10000 },
    { "limiterKey": "CREATE_LOAN", "timeoutInMs": 10000 },
    { "limiterKey": "UPDATE_LOAN", "timeoutInMs": 10000 },
    { "limiterKey": "VERIFY_OTP", "timeoutInMs": 10000 }
  ]

citiEfaConfiguration:
  url:  https://in.sandbox.apib2b.citi.com
  client: 451e41a2-c8e5-4895-83c2-336e6ffc0254
  secretKey: tH7gC8iX3vE7sA1bY4iK0vU2hC2bW6mH4oP4mU3mL1kF1hL0gT
  enabled: false
  cachedAccessTokenTTL: 30
  trustStorePath:  "src/integration-test/resources/local/citi/client-truststore.jks"
  trustStorePass: password
  keyStorePath: "src/integration-test/resources/local/citi/client-keystore.jks"
  keyStorePass: password
  proxyUri: **********:443
  gstRate: 18
  apiPath:
    clientAccessTokenPath: "/gcbin/api/clientCredentials/oauth2/token/in/fkt"
    accessTokenPath: "/gcbin/api/whiteLabel/oauth2/token/in/fkt"
    controlFlowTokenPath: "/gcbin/api/partner/v1/apac/onboarding/products/unsecured/applications/%s/mfa/statuses"
    accountSummaryPath: "/gcbin/api/v1/accounts/%s"
    billingTransactionsPath: "/gcbin/api/v1/accounts/%s/transactions"
    createApplicationPath: "/gcbin/api/v1/apac/onboarding/products/unsecured/applications"
    updateApplicationPath: "/gcbin/api/v1/apac/onboarding/products/unsecured/applications/%s"
    checkEligibilityPath: "/gcbin/api/v1/apac/onboarding/products/unsecured/applications/%s/inPrincipleApprovals"
    activateAccountPath: "/gcbin/api/v1/apac/onboarding/products/unsecured/applications/%s/submission"
    applicationEnquiryPath: "/gcbin/api/v1/apac/onboarding/products/unsecured/applications/%s/status"
    getCustomerAccountsPath: "/gcbin/api/v1/accounts"
    fetchAccountAccessCodePath: "/gcbin/api/partner/v1/accounts/%s/accessCodes"
    consentEnquiryPath: "/gcbin/api/v1/accounts/%s/consents"
    sendOtpPath: "/gcbin/api/v1/in/customers/identity/otp"
    verifyOtpPath: "/gcbin/api/v1/in/customers/identity/kyc"
    updateMfaPath: "/gcbin/api/partner/v1/mfa/statuses"

axisCbcConfiguration:
  hmacKey: hmacKey
  hmacAlgorithm: HmacSHA256
  isNfrApiEnabled: true
  url: https://saksham.axisbank.co.in
  client: 6697d0e6-74df-4766-83d8-67977f2a1d50
  secretKey: rC4kW4hW6pO0rQ7nS8dW4yU7nH7sB0dN6fH7oA0bO6hN3pW3tY
  trustStorePath: "src/integration-test/resources/axis/prod-client-truststore.jks"
  trustStorePass: password
  keyStorePath: "src/integration-test/resources/axis/prod-client-keystore.jks"
  keyStorePass: password
  proxyUri: https://saksham.axisbank.co.in:443
  encryptionKey: ********************************
  channelId: FK
  encryptAxisLogs: true
  axisCbcApiConfigurationsMap:
    updatePlusMembership:
      apiPath: "/gateway/api/insta-credit-card/sc/v1/update-reward-profile"
      serviceRequestId: "API.API.GET.REWARD"
      serviceRequestVersion: "1.0"
    checkCohortEligibility:
      apiPath: "/gateway/api/v1/insta-credit-card/check-cohort-eligibility"
      serviceRequestId : "CheckCardEligibility"
      serviceRequestVersion: "1.0"
    demoCardConsent:
      apiPath: "/gateway/api/v1/insta-credit-card/cust-dtl-consent-otp"
      serviceRequestId: "OTPGen"
      serviceRequestVersion: "1.0"
    demoCardConsentForEtbCc:
      apiPath: "/gateway/api/v1/insta-credit-card-upgrade/cust-dtl-consent-otp"
      serviceRequestId: "OTPGen"
      serviceRequestVersion: "1.0"
    demoCardConsentForEtbNpa:
      apiPath: "/gateway/api/v1/insta-credit-card-npa/cust-dtl-consent-otp"
      serviceRequestId: "OTPGen"
      serviceRequestVersion: "1.0"
    fetchCustomerDemographics:
      apiPath: "/gateway/api/v1/insta-credit-card/fetch-cust-details"
      serviceRequestId: "FetchDemog"
      serviceRequestVersion: "1.0"
    fetchCustomerDemographicsForEtbCc:
      apiPath: "/gateway/api/v1/insta-credit-card-upgrade/fetch-cust-details"
      serviceRequestId: "FetchDemog"
      serviceRequestVersion: "1.0"
    fetchCustomerDemographicsForEtbNpa:
      apiPath: "/gateway/api/v1/insta-credit-card-npa/fetch-cust-details"
      serviceRequestId: "FetchDemog"
      serviceRequestVersion: "1.0"
    applyCardConsent :
      apiPath: "/gateway/api/v1/insta-credit-card/apply-consent-otp"
      serviceRequestId: "OTPGen"
      serviceRequestVersion: "1.0"
    applyCardConsentForEtbCc:
      apiPath: "/gateway/api/v1/insta-credit-card-upgrade/apply-consent-otp"
      serviceRequestId: "OTPGen"
      serviceRequestVersion: "1.0"
    applyCardConsentForEtbNpa:
      apiPath: "/gateway/api/v1/insta-credit-card-npa/apply-consent-otp"
      serviceRequestId: "OTPGen"
      serviceRequestVersion: "1.0"
    applyCardConsentForNtb:
      apiPath: "/gateway/api/v1/insta-credit-card-ntb/apply-consent-otp"
      serviceRequestId: "OTPGen"
      serviceRequestVersion: "1.0"
    processNewCardCase:
      apiPath: "/gateway/api/v1/insta-credit-card/new-card-application"
      serviceRequestId: "ProcessNewCard"
      serviceRequestVersion: "1.0"
    processUpgradeCard:
      apiPath: "/gateway/api/v1/insta-credit-card-upgrade/new-card-application"
      serviceRequestId: "ProcessNewCard"
      serviceRequestVersion: "1.0"
    processUpgradeCardV2:
      apiPath: "/gateway/api/v2/insta-credit-card-upgrade/new-card-application"
      serviceRequestId: "ProcessNewCardCase"
      serviceRequestVersion: "1.0"
    processNewCardCaseForEtbNpa:
      apiPath: "/gateway/api/v1/insta-credit-card-npa/new-card-application"
      serviceRequestId: "ProcessNewCardCase"
      serviceRequestVersion: "1.0"
    processNewCardCaseForNtb:
      apiPath: "/gateway/api/v1/insta-credit-card-ntb/new-card-application"
      serviceRequestId: "ProcessNewCardCase"
      serviceRequestVersion: "1.0"
    checkCardUpgradeStatus:
      apiPath: "/gateway/api/v1/insta-credit-card-upgrade/check-card-upgrade-status"
      serviceRequestId: "FetchCardDetails"
      serviceRequestVersion: "1.0"
    checkCaseStatus:
      apiPath: "/gateway/api/v2/insta-credit-card/check-case-status"
      serviceRequestId: "FetchCardDetails"
      serviceRequestVersion: "1.0"
    checkCaseStatusV3:
      apiPath: "/gateway/api/v3/insta-credit-card/check-case-status"
      serviceRequestId: "FetchCardDetails"
      serviceRequestVersion: "1.0"
    lcmGenerateAuthOtp:
      apiPath: "/gateway/api/v1/insta-credit-card/lcm/gen-auth-otp"
      serviceRequestId: "AX.ICC.LCM.AUTH"
      serviceRequestVersion: "1.0"
    lcmValidateAuthOtp:
      apiPath: "/gateway/api/v1/insta-credit-card/lcm/validate-auth-otp"
      serviceRequestId: "AX.ICC.LCM.AUTH.VAL"
      serviceRequestVersion: "1.0"
    checkCaseStatusMobile:
      apiPath: "/gateway/api/v1/insta-credit-card/check-case-status-mobile"
      serviceRequestId: "CheckCaseStatus"
      serviceRequestVersion: "1.0"
    checkCaseStatusMobileForEtbNpa:
      apiPath: "/gateway/api/v1/insta-credit-card-npa/check-case-status-mobile"
      serviceRequestId: "CheckCaseStatus"
      serviceRequestVersion: "1.0"
    checkCaseStatusMobileForNtb:
      apiPath: "/gateway/api/v1/insta-credit-card-ntb/check-case-status-mobile"
      serviceRequestId: "CheckCaseStatus"
      serviceRequestVersion: "1.0"
    kycSchedule:
      apiPath: "/gateway/api/v1/insta-credit-card/kyc-scheduler"
      serviceRequestId: "ProcessNewCardCase"
      serviceRequestVersion: "1.0"
    getStatementGenerationDate:
      apiPath: "/gateway/api/v2/insta-credit-card/lcm/gen-statement-dates"
      serviceRequestId: "AX.ICC.LCM.CARDDET"
      serviceRequestVersion: "1.0"
    getCashBackSummary:
      apiPath: "/gateway/api/v2/insta-credit-card/lcm/fetch-all-cashback"
      serviceRequestId: "AX.ICC.LCM.CARDDET"
      serviceRequestVersion: "1.0"
    getStatementSummary:
      apiPath: "/gateway/api/v2/insta-credit-card/lcm/fetch-last-stmt-concise"
      serviceRequestId: "AX.ICC.LCM.CARDDET"
      serviceRequestVersion: "1.0"
    getAccountSummary:
      apiPath: "/gateway/api/v1/insta-credit-card/lcm/account-summary-details"
      serviceRequestId: "AccountSummary"
      serviceRequestVersion: "1.0"
    lcmSendEmail:
      apiPath: "/gateway/api/v1/insta-credit-card/lcm/send-email"
      serviceRequestId: "AX.ICC.LCM.SENDEML"
      serviceRequestVersion: "1.0"
    handShakeVideoKyc:
      apiPath: "/gateway/api/v1/insta-credit-card/vkyc/handshake"
      serviceRequestId: "AX.ICC.VKYC.AUTH"
      serviceRequestVersion: "1.0"
    blockCard:
      apiPath: "/gateway/api/v1/insta-credit-card/lcm/block-card"
      serviceRequestId: "AX.ICC.LCM.CARDDET"
      serviceRequestVersion: "1.0"
    getCreditCardSettings:
      apiPath: "/gateway/api/v2/insta-credit-card/lcm/manage-usage-limit/inquiry"
      serviceRequestId: "ACPInquiry"
      serviceRequestVersion: "1.0"
    updateCreditCardSettings:
      apiPath: "/gateway/api/v1/insta-credit-card/lcm/manage-usage-limit/update"
      serviceRequestId: "ACPUpdate"
      serviceRequestVersion: "1.0"
    checkCreditLimitEligibility:
      apiPath: "/gateway/api/v2/insta-credit-card/lcm/check-credit-limit-eligibility"
      serviceRequestId: "checkCreditLimitEligibility"
      serviceRequestVersion: "1.0"
    getCreditLimitIncrementSettings:
      apiPath: "/gateway/api/v1/insta-credit-card/lcm/credit-limit-increment"
      serviceRequestId: "creditLimitInc"
      serviceRequestVersion: "1.0"
    getTransactionSummary:
      apiPath: "/gateway/api/v2/insta-credit-card/lcm/transaction-summary"
      serviceRequestId: "TransactionSummary"
      serviceRequestVersion: "1.0"
    convertTxnToInstalment:
      apiPath: "/gateway/api/v1/insta-credit-card/lcm/convert-txn-to-instalment"
      serviceRequestId: "convertTxn"
      serviceRequestVersion: "1.0"
    validateDeliveryCode:
      apiPath: "/gateway/api/v2/insta-credit-card/lcm/delivery-code-validate"
      serviceRequestId: "ABC.PQR.XYZ.007"
      serviceRequestVersion: "1.0"
    initiateApplication:
      apiPath: "/gateway/api/income-assessment/v1/initiate-application"
      serviceRequestId: "Init-App"
      serviceRequestVersion: "1.0"
    generateToken:
      apiPath: "/gateway/api/income-assessment/v1/generate-auth-code"
      serviceRequestId: "Generate-Auth-Code"
      serviceRequestVersion: "1.0"
    getStatus:
      apiPath: "/gateway/api/income-assessment/v1/get-status"
      serviceRequestId: "Status"
      serviceRequestVersion: "1.0"
  testConfigsMap:
    mockAxisResponses: N
    replacePhoneNumber: N
    mockPhoneNumber: 919999999999
    mockNudgeResponse: true
    mockCremoScore:

kisshtServiceClientConfigBnpl:
  url:  http://***********
  client: kisshtNbfc
  siteKey: bb5CKNVso77Y7P7Y9VdQNnG236DEHD6p8CcmTeWQ
  secretKey: RSFKTKIL6POCB5L1C3UIE1O0BDYNA26Q

epaylaterClientConfig:
  url: http://***********
  apiKey: secret_18e54ebc-bcc3-11e8-8be2-4fc0f616d9e6

gibraltarConfig:
  maxPublicKeyCount: 100
  keySpaceName: onboarding
  clientName: efa-onboarding-client
  trustStorePath: "src/integration-test/resources/gibraltar/prod-client-truststore.jks"
  trustStorePass: password
  keyStorePath: "src/integration-test/resources/gibraltar/prod-client-keystore.jks"
  keyStorePass: password
  certificateAlias: gibraltarSelfSigned
  url: https://*************:8443,https://***********:8443
  generateKeysOnStartUp: false
  connectionTimeoutInMs: 150
  socketTimeoutInMs: 150
  httpRequestRetryCount: 5

userServiceClientConfig:
  usUrl: http://************:35200
  usClientId: affordability
  mockApiLatencyInMilliSec:
    userProfile: 10

loginServiceClientConfig:
  loginServiceUrl: http://***********:35100
  loginServiceClientId: affordability

khaataClientConfig:
  url: http://***********:8980
  clientName: pandora

bucketingConfig:
  enabled: true
  accountVintage: [90,180,360]
  daysSinceOrder: [215]
  ordersFulfilled: [3,5]
  gmvTillDate: [10000,20000]
  percentageReturned: [25,33]
  ordersAtCommonAddress: [2,3]

ubonaServiceClientConfig:
  url: https://enterprise.ubona.com

redis:
  serverAddress: localhost:6379
  maxTotal: 100
  maxIdle: 10
  minIdle: 1
  password: dummy

ardourClientConfig:
  url: http://**********:80
  client: secret_18e54ebc-bcc3-11e8-8be2-4fc0f616d9e6

awsS3ClientConfig:
  awsS3ConfigMap:
    kissht:
      accessKey: ********************
      secretKey: mWsXTB+EFFC29M2Jbu8lVxdLZATsb7lcwtBhYqHs
      awsProxyHost: **********
      awsProxyPort: 80
      region: AP_SOUTH_1

pinakaClientConfig:
  url: http://************:9091
  client: pandora-client
  merchant: mp_flipkart

cardGatewayClientConfig:
  url: http://sm-card-gateway-dev1-playground.sm-card-gateway-playground.fkcloud.in
  client: pandora-client
  merchant: mp_flipkart

ubonaConfiguration:
  reverseProxyKey: ubona
  aesKey: B7F13DE0F479ACEA259B3A2FF53CB94C
  aesIv: 712EE5763C687550
  aesSalt: 3D08463B8B40DF1D
  shouldEncrypt: true
  blockSize: 16

collectionsClientConfiguration:
  scheme: http
  host: **********
  port: 80
  exchangeName: fintech-collections

ibConfiguration:
  userSignupUrl: https://esbpr.ivlfinance.com/esb/ivl/0/process/ivlfin/a_user/v1
  uploadDocumentsUrl: https://esbpr.ivlfinance.com/esb/ivl/0/process/ivlfin/a_cardputresultdocumenttocrm/v1
  creditAssessmentUrl: https://esbpr.ivlfinance.com/esb/ivl/0/process/ivlfin/a_carddemographic/v1
  cardCaptureValidationUrl: https://esbpr.ivlfinance.com/esb/ivl/0/process/ivlfin/a_cardcapturevalidatedata/v1
  cardDetailsUrl: https://esbpr.ivlfinance.com/esb/ivl/0/process/ivlfin/a_cardgetdetails/v1
  panNameServiceUrl: https://esbpr.ivlfinance.com/esb/ivl/0/process/ivlfin/a_cardpanname/v1
  cardMandateUrl: https://esbpr.ivlfinance.com/esb/ivl/0/process/ivlfin/a_cardemandateresult/v1
  source: Flipkart
  enableWizard: false
  testUsers: ["A1","A2","ACC68257761FC00450C8AAEFDFD8361E6A0X","ACC2A5AA7EDAEC4414EA6B2EDE8E2C7A781I","ACCFE741BBF1F944C9CB376E1BF87EAF340W","ACC9778B47182D2434BB64FBDAA95F87411C","ACC07A17819EC1D44C981193DEBBF043400R","ACCB6FF2FF2D325482C9F5341CA50841626O","ACC14052326530718101","ACC14057582966055708","ACC14017297826421172","ACC5846BC71224D416DA690D8090E99F390N","ACC432BDCE121DC4D30B4DA7CAFC7EBB9C9S","ACCE60889472DC44267B9BFE9484054657BI","ACC648D1DD78C3040A686B0160F9909EFAFO","ACLWBSGA95GFWF51CLBS21W5EPYGWZBR","ACPLAJLVWTU75OMLR3QGV2XZA4761Y9U","CCABD2D37CCCC84F37B1F870993ADA6FE6X","ACC2A5AA7EDAEC4414EA6B2EDE8E2C7A781I","ACC14208208785381226","ACC7495ACE0B9F84BB9AC63D0DA60AD679AC","ACQH6JT64FW13ZSZAW6YD9QMXVXEQBNQ","ACC8D9F9B36546043F79E60EAB4ADFCE9D3Z","ACC13553664458368763","ACC05D577873443449FA9E165A662D03B961","ACCABD2D37CCCC84F37B1F870993ADA6FE6X","ACC1E6E824DC8A9420CA54AB3BC4A2586842","ACT27W3Q1XZN7MIO9YVPMHVOOZKHGUHK","AC9W4EAP7LBS7PIJYJQFRWETMVDB19NM","ACC83381E9EE2104E18B00FE41B5366F1AFK","AC2QKHBR41R08QH9T4D4VYQWG4RZZSSB","ACC4C5070D22EBD4F26A017B71167444CFCW","ACC62A1D1BCC03648AE93B7A62A4D8CA284J","ACCD134CC390E5B47EABAADA4F19EFFA6C8V","ACC4304E3E4E26B4878AF931EF89E9B8DAEC","ACC798D3CC5AC4447869C1B1DD645A7128AZ","ACCA17D4EA4D70F43388C6EC80E73A69180R","ACC13832240149214783","ACCEE79114ED745438BB1AD7D813193798FD"]

idfcConfiguration:
  proxyUri: http://************:8216
  pennyDropUri: http://************:8216
  clientId: fpkt-9c2e-4d94-994b-53557430ea32
  clientIdV2: ea5ec20b-6e7b-4a6c-8256-3771b9285092
  clientSecret: ********************************
  idfcEncryptionKey: Idfc4321Uidai987
  grantType: "client_credentials"
  idfcHashingSalt: 1DFcHasH1nGs@1t-pR0D
  accessTokenTimeoutInMinutes: 10
  bnplScheme: 53521
  emiScheme: 73010
  fsupScheme: 73012
  convFeeChargeId: 702455
  usageFeeChargeId: 702678
  documentUploadConfiguration:
    documentUploadUrl: https://proapi2.capitalfirst.com:65018/prodapigateway/65018/v2/bpm_uploadDocuments
    userId: eyJkYXRhIjoidmF1bHQ6djE6KzhTT3YxcW1qZ2JRczRLenJpRTJFR2xScEtZMEw3Y2R2MzlTUFJTdG9wZkQwSm89Iiwia2V5SWQiOiJjZmctc3ZjOjppbi1jaGVubmFpLTEtcHJvZDpwYW5kb3JhLXByb2QifQ==
    environment: PRODUCTION
    password: eyJkYXRhIjoidmF1bHQ6djE6VUlHamlJMW1oWjVsY0xOcFgxb05qU2x1TmtYVzZOYUFLcVA0OUNEbUpVRDJwdWt6YTJIVUx3MVRlbzZrVDhkQTNpMEZGUjcyMWpnc2x3RWFnUT09Iiwia2V5SWQiOiJjZmctc3ZjOjppbi1jaGVubmFpLTEtcHJvZDpwYW5kb3JhLXByb2QifQ==
  impsPennyDropUrl: /prod1/api/1.0/sys/impsFT
  upiPennyDropUrl: /api/v1/idfc/upi/preAuth
  validateUpiUrl: /api/v1/idfc/upi/validateVA
  checkLoanStatusUrl: https://muleuat.capitalfirst.com:8443
  idfcPaybackReversalEnabledTenants: ["FFB","FK_CONSUMER_CREDIT"]
  idfcEncryptionKeyV2: FLPKRT9E40A858FKTB0AD4DTGEEB5DF1
  proxyUriV2: https://apiext.uat.idfcfirstbank.com
  initializationVector: Kl4lNVAqKzFuNUAxXjF+Kw==

indiaBullsClientConfig:
  protocol: https
  hostName: esbpr.ivlfinance.com/esb/ivl/0/process/ivlfin
  portNo:
  balanceEnquiryPath:
  dashboardApiPath: /a_carddashboard/v1
  agreementApiPath: /a_onlinesoa/v1
  tijoriUrl: http://***********:80

pandoraHystrixProperties:
  commandExecutionProperties:
    hystrix.command.AxisIncomeInitiate.execution.isolation.thread.timeoutInMilliseconds: 14000
    hystrix.command.AxisIncomeGenerateToken.execution.isolation.thread.timeoutInMilliseconds: 14000
    hystrix.command.AxisIncomeGetStatus.execution.isolation.thread.timeoutInMilliseconds: 14000
    hystrix.command.IndiaBullsCardMandate.execution.isolation.thread.timeoutInMilliseconds: 10000
    hystrix.command.IndiaBullsDashboard.execution.isolation.thread.timeoutInMilliseconds: 10000
    hystrix.command.IndiaBullsUserSignup.execution.isolation.thread.timeoutInMilliseconds: 10000
    hystrix.command.IndiaBullsUploadDocuments.execution.isolation.thread.timeoutInMilliseconds: 10000
    hystrix.command.IndiaBullsCreditAssessment.execution.isolation.thread.timeoutInMilliseconds: 30000
    hystrix.command.IndiaBullsCardCaptureValidation.execution.isolation.thread.timeoutInMilliseconds: 10000
    hystrix.command.IndiaBullsCardDetails.execution.isolation.thread.timeoutInMilliseconds: 10000
    hystrix.command.IndiaBullsPanNameService.execution.isolation.thread.timeoutInMilliseconds: 10000
    hystrix.command.AxisCbcCheckCohortEligibility.execution.isolation.thread.timeoutInMilliseconds: 14000
    hystrix.command.AxisCbcDemoCardConsent.execution.isolation.thread.timeoutInMilliseconds: 14000
    hystrix.command.AxisCbcDemoCardConsentEtbNpa.execution.isolation.thread.timeoutInMilliseconds: 14000
    hystrix.command.AxisCbcDemoCardConsentEtbCc.execution.isolation.thread.timeoutInMilliseconds: 14000
    hystrix.command.AxisCbcApplyCardConsent.execution.isolation.thread.timeoutInMilliseconds: 14000
    hystrix.command.AxisCbcApplyCardConsentEtbCc.execution.isolation.thread.timeoutInMilliseconds: 14000
    hystrix.command.AxisCbcApplyCardConsentNtb.execution.isolation.thread.timeoutInMilliseconds: 14000
    hystrix.command.AxisCbcApplyCardConsentEtbNpa.execution.isolation.thread.timeoutInMilliseconds: 14000
    hystrix.command.AxisCbcFetchCustDemogs.execution.isolation.thread.timeoutInMilliseconds: 14000
    hystrix.command.AxisCbcFetchCustDemogsEtbNpa.execution.isolation.thread.timeoutInMilliseconds: 14000
    hystrix.command.AxisCbcFetchCustDemogsEtbCc.execution.isolation.thread.timeoutInMilliseconds: 14000
    hystrix.command.AxisCbcProcessNewCard.execution.isolation.thread.timeoutInMilliseconds: 20000
    hystrix.command.AxisCbcProcessNewCardNtb.execution.isolation.thread.timeoutInMilliseconds: 20000
    hystrix.command.AxisCbcProcessNewCardEtbNpa.execution.isolation.thread.timeoutInMilliseconds: 20000
    hystrix.command.AxisCbcProcessUpgradeCard.execution.isolation.thread.timeoutInMilliseconds: 20000
    hystrix.command.AxisCbcCheckCaseStatus.execution.isolation.thread.timeoutInMilliseconds: 20000
    hystrix.command.AxisCbcCheckCardUpgradeStatus.execution.isolation.thread.timeoutInMilliseconds: 2000
    hystrix.command.AxisCbcLcmAuthOtpGen.execution.isolation.thread.timeoutInMilliseconds: 14000
    hystrix.command.AxisCbcLcmAuthOtpVal.execution.isolation.thread.timeoutInMilliseconds: 14000
    hystrix.command.AxisCbcLcmSendEmail.execution.isolation.thread.timeoutInMilliseconds: 14000
    hystrix.command.AxisVKYC.execution.isolation.thread.timeoutInMilliseconds: 14000
    hystrix.command.AxisCbcAccountSummary.execution.isolation.thread.timeoutInMilliseconds: 14000
    hystrix.command.AxisCbcFetchStatement.execution.isolation.thread.timeoutInMilliseconds: 14000
    hystrix.command.AxisCbcStatementDates.execution.isolation.thread.timeoutInMilliseconds: 14000
    hystrix.command.AxisCbcLcmBlockCard.execution.isolation.thread.timeoutInMilliseconds: 14000
    hystrix.command.AxisCbcLifeTimeCashBack.execution.isolation.thread.timeoutInMilliseconds: 14000
    hystrix.command.AxisCbcLcmGetCreditCardSettings.execution.isolation.thread.timeoutInMilliseconds: 14000
    hystrix.command.AxisCbcLcmUpdateCreditCardSettings.execution.isolation.thread.timeoutInMilliseconds: 14000
    hystrix.command.AxisCbcCheckCaseStatusMobile.execution.isolation.thread.timeoutInMilliseconds: 2000
    hystrix.command.AxisCbcCheckCaseStatusMobileEtbNpa.execution.isolation.thread.timeoutInMilliseconds: 2000
    hystrix.command.AxisCbcCheckCaseStatusMobileNtb.execution.isolation.thread.timeoutInMilliseconds: 2000
    hystrix.command.IdfcPanVerification.execution.isolation.thread.timeoutInMilliseconds: 15000
    hystrix.command.IdfcAccessTokenKey.execution.isolation.thread.timeoutInMilliseconds: 5000
    hystrix.command.IdfcGenerateOtpKey.execution.isolation.thread.timeoutInMilliseconds: 30000
    hystrix.command.IdfcVerifyOtpKey.execution.isolation.thread.timeoutInMilliseconds: 30000
    hystrix.command.IdfcImpsPennyDropKey.execution.isolation.thread.timeoutInMilliseconds: 10000
    hystrix.command.IdfcUpiPennyDropKey.execution.isolation.thread.timeoutInMilliseconds: 15000
    hystrix.command.IdfcValidateUpiPennyDropKey.execution.isolation.thread.timeoutInMilliseconds: 15000
    hystrix.command.IdfcCheckEligibilityKey.execution.isolation.thread.timeoutInMilliseconds: 30000
    hystrix.command.IdfcCreateLoanKey.execution.isolation.thread.timeoutInMilliseconds: 20000
    hystrix.command.IdfcUpdateLoanKey.execution.isolation.thread.timeoutInMilliseconds: 20000
    hystrix.command.IdfcCheckLoanStatusKey.execution.isolation.thread.timeoutInMilliseconds: 2000
    hystrix.command.IdfcSaleForwardKey.execution.isolation.thread.timeoutInMilliseconds: 20000
    hystrix.command.IdfcSaleReverseKey.execution.isolation.thread.timeoutInMilliseconds: 20000
    hystrix.command.IdfcPaybackForwardKey.execution.isolation.thread.timeoutInMilliseconds: 20000
    hystrix.command.IdfcSearchCkycKey.execution.isolation.thread.timeoutInMilliseconds: 10000
    hystrix.command.IdfcDownloadCkycKey.execution.isolation.thread.timeoutInMilliseconds: 10000
    hystrix.command.TeleSales.execution.isolation.thread.timeoutInMilliseconds: 14000
    hystrix.command.IdfcFfbUploadDocumentKey.execution.isolation.thread.timeoutInMilliseconds: 15000
    hystrix.command.IdfcSendKycDocument.execution.isolation.thread.timeoutInMilliseconds: 5000
    hystrix.command.IdfcEbcAuthTokenKey.execution.isolation.thread.timeoutInMilliseconds: 10000
    hystrix.command.UserServiceGenerateOtp.execution.isolation.thread.timeoutInMilliseconds: 10000

  commandCircuitBreakerProperties:
    hystrix.command.AxisIncomeInitiate.circuitBreaker.forceClosed: false
    hystrix.command.AxisIncomeGenerateToken.circuitBreaker.forceClosed: false
    hystrix.command.AxisIncomeGetStatus.circuitBreaker.forceClosed: false
    hystrix.command.IndiaBullsDashboard.circuitBreaker.forceClosed: false
    hystrix.command.IndiaBullsCardMandate.circuitBreaker.forceClosed: false
    hystrix.command.IndiaBullsUserSignup.circuitBreaker.forceClosed: false
    hystrix.command.IndiaBullsUploadDocuments.circuitBreaker.forceClosed: false
    hystrix.command.IndiaBullsCreditAssessment.circuitBreaker.forceClosed: false
    hystrix.command.IndiaBullsCardCaptureValidation.circuitBreaker.forceClosed: false
    hystrix.command.IndiaBullsCardDetails.circuitBreaker.forceClosed: false
    hystrix.command.IndiaBullsPanNameService.circuitBreaker.forceClosed: false
    hystrix.command.AxisCbcCheckCohortEligibility.circuitBreaker.forceClosed: false
    hystrix.command.AxisCbcDemoCardConsent.circuitBreaker.forceClosed: false
    hystrix.command.AxisCbcDemoCardConsentEtbNpa.circuitBreaker.forceClosed: false
    hystrix.command.AxisCbcDemoCardConsentEtbCc.circuitBreaker.forceClosed: false
    hystrix.command.AxisCbcApplyCardConsent.circuitBreaker.forceClosed: false
    hystrix.command.AxisCbcApplyCardConsentEtbCc.circuitBreaker.forceClosed: false
    hystrix.command.AxisCbcApplyCardConsentNtb.circuitBreaker.forceClosed: false
    hystrix.command.AxisCbcApplyCardConsentEtbNpa.circuitBreaker.forceClosed: false
    hystrix.command.AxisCbcFetchCustDemogs.circuitBreaker.forceClosed: false
    hystrix.command.AxisCbcFetchCustDemogsEtbNpa.circuitBreaker.forceClosed: false
    hystrix.command.AxisCbcFetchCustDemogsEtbCc.circuitBreaker.forceClosed: false
    hystrix.command.AxisCbcProcessNewCard.circuitBreaker.forceClosed: false
    hystrix.command.AxisCbcProcessUpgradeCard.circuitBreaker.forceClosed: false
    hystrix.command.AxisCbcProcessNewCardNtb.circuitBreaker.forceClosed: false
    hystrix.command.AxisCbcProcessNewCardEtbNpa.circuitBreaker.forceClosed: false
    hystrix.command.AxisCbcCheckCaseStatus.circuitBreaker.forceClosed: false
    hystrix.command.AxisCbcCheckCardUpgradeStatus.circuitBreaker.forceClosed: false
    hystrix.command.AxisCbcLcmAuthOtpGen.circuitBreaker.forceClosed: false
    hystrix.command.AxisCbcLcmAuthOtpVal.circuitBreaker.forceClosed: false
    hystrix.command.AxisCbcLcmSendEmail.circuitBreaker.forceClosed: false
    hystrix.command.AxisCbcAccountSummary.circuitBreaker.forceClosed: false
    hystrix.command.AxisCbcLcmBlockCard.circuitBreaker.forceClosed: false
    hystrix.command.AxisCbcLcmGetCreditCardSettings.circuitBreaker.forceClosed: false
    hystrix.command.AxisCbcLcmUpdateCreditCardSettings.circuitBreaker.forceClosed: false
    hystrix.command.AxisCbcCheckCaseStatusMobile.circuitBreaker.forceClosed: false
    hystrix.command.AxisCbcCheckCaseStatusMobileEtbNpa.circuitBreaker.forceClosed: false
    hystrix.command.AxisCbcCheckCaseStatusMobileNtb.circuitBreaker.forceClosed: false
    hystrix.command.AxisCbcFetchStatement.circuitBreaker.forceClosed: false
    hystrix.command.AxisCbcStatementDates.circuitBreaker.forceClosed: false
    hystrix.command.AxisCbcLifeTimeCashBack.circuitBreaker.forceClosed: false
    hystrix.command.AxisVKYC.circuitBreaker.forceClosed: false
    hystrix.command.IdfcPanVerification.circuitBreaker.forceClosed: false
    hystrix.command.IdfcAccessTokenKey.circuitBreaker.forceClosed: false
    hystrix.command.IdfcGenerateOtpKey.circuitBreaker.forceClosed: false
    hystrix.command.IdfcVerifyOtpKey.circuitBreaker.forceClosed: false
    hystrix.command.IdfcImpsPennyDropKey.circuitBreaker.forceClosed: false
    hystrix.command.IdfcValidateUpiPennyDropKey.circuitBreaker.forceClosed: false
    hystrix.command.IdfcUpiPennyDropKey.circuitBreaker.forceClosed: false
    hystrix.command.IdfcCheckEligibilityKey.circuitBreaker.forceClosed: false
    hystrix.command.IdfcCreateLoanKey.circuitBreaker.forceClosed: false
    hystrix.command.IdfcUpdateLoanKey.circuitBreaker.forceClosed: false
    hystrix.command.IdfcCheckLoanStatusKey.circuitBreaker.forceClosed: false
    hystrix.command.IdfcSearchCkycKey.circuitBreaker.forceClosed: false
    hystrix.command.IdfcDownloadCkycKey.circuitBreaker.forceClosed: false
    hystrix.command.IdfcSaleForwardKey.circuitBreaker.forceClosed: false
    hystrix.command.IdfcSaleReverseKey.circuitBreaker.forceClosed: false
    hystrix.command.IdfcPaybackForwardKey.circuitBreaker.forceClosed: false
    hystrix.command.TeleSales.circuitBreaker.forceClosed: false
    hystrix.command:IdfcFfbUploadDocumentKey.circuitBreaker.forceClosed: false
    hystrix.command:IdfcSendKycDocument.circuitBreaker.forceClosed: false
    hystrix.command:IdfcEbcAuthTokenKey.circuitBreaker.forceClosed: false
    hystrix.command.UserServiceGenerateOtp.circuitBreaker.forceClosed: false

  threadPoolProperties:
    hystrix.threadpool.AxisIncomeInitiate.coreSize: 10
    hystrix.threadpool.AxisIncomeInitiate.maximumSize: 10
    hystrix.threadpool.AxisIncomeInitiate.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.AxisIncomeGenerateToken.coreSize: 10
    hystrix.threadpool.AxisIncomeGenerateToken.maximumSize: 10
    hystrix.threadpool.AxisIncomeGenerateToken.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.AxisIncomeGetStatus.coreSize: 10
    hystrix.threadpool.AxisIncomeGetStatus.maximumSize: 10
    hystrix.threadpool.AxisIncomeGetStatus.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.IndiaBullsCardMandate.coreSize: 35
    hystrix.threadpool.IndiaBullsCardMandate.maximumSize: 35
    hystrix.threadpool.IndiaBullsCardMandate.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.IndiaBullsDashboard.coreSize: 20
    hystrix.threadpool.IndiaBullsDashboard.maximumSize: 20
    hystrix.threadpool.IndiaBullsDashboard.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.IndiaBullsUserSignup.coreSize: 80
    hystrix.threadpool.IndiaBullsUserSignup.maximumSize: 80
    hystrix.threadpool.IndiaBullsUserSignup.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.IndiaBullsUploadDocuments.coreSize: 150
    hystrix.threadpool.IndiaBullsUploadDocuments.maximumSize: 150
    hystrix.threadpool.IndiaBullsUploadDocuments.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.IndiaBullsCreditAssessment.coreSize: 100
    hystrix.threadpool.IndiaBullsCreditAssessment.maximumSize: 100
    hystrix.threadpool.IndiaBullsCreditAssessment.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.IndiaBullsCardCaptureValidation.coreSize: 50
    hystrix.threadpool.IndiaBullsCardCaptureValidation.maximumSize: 50
    hystrix.threadpool.IndiaBullsCardCaptureValidation.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.IndiaBullsCardDetails.coreSize: 35
    hystrix.threadpool.IndiaBullsCardDetails.maximumSize: 35
    hystrix.threadpool.IndiaBullsCardDetails.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.IndiaBullsPanNameService.coreSize: 35
    hystrix.threadpool.IndiaBullsPanNameService.maximumSize: 35
    hystrix.threadpool.IndiaBullsPanNameService.allowMaximumSizeToDivergeFromCoreSize:   false
    hystrix.threadpool.AxisCbcCheckCohortEligibility.coreSize: 100
    hystrix.threadpool.AxisCbcCheckCohortEligibility.maximumSize: 100
    hystrix.threadpool.AxisCbcCheckCohortEligibility.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.AxisCbcDemoCardConsent.coreSize: 100
    hystrix.threadpool.AxisCbcDemoCardConsent.maximumSize: 100
    hystrix.threadpool.AxisCbcDemoCardConsent.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.AxisCbcDemoCardConsentEtbNpa.coreSize: 100
    hystrix.threadpool.AxisCbcDemoCardConsentEtbNpa.maximumSize: 100
    hystrix.threadpool.AxisCbcDemoCardConsentEtbNpa.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.AxisCbcDemoCardConsentEtbCc.coreSize: 100
    hystrix.threadpool.AxisCbcDemoCardConsentEtbCc.maximumSize: 100
    hystrix.threadpool.AxisCbcDemoCardConsentEtbCc.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.AxisCbcApplyCardConsent.coreSize: 50
    hystrix.threadpool.AxisCbcApplyCardConsent.maximumSize: 50
    hystrix.threadpool.AxisCbcApplyCardConsent.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.AxisCbcApplyCardConsentNtb.coreSize: 50
    hystrix.threadpool.AxisCbcApplyCardConsentNtb.maximumSize: 50
    hystrix.threadpool.AxisCbcApplyCardConsentNtb.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.AxisCbcApplyCardConsentEtbNpa.coreSize: 50
    hystrix.threadpool.AxisCbcApplyCardConsentEtbNpa.maximumSize: 50
    hystrix.threadpool.AxisCbcApplyCardConsentEtbNpa.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.AxisCbcApplyCardConsentEtbCc.coreSize: 50
    hystrix.threadpool.AxisCbcApplyCardConsentEtbCc.maximumSize: 50
    hystrix.threadpool.AxisCbcApplyCardConsentEtbCc.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.AxisCbcFetchCustDemogs.coreSize: 100
    hystrix.threadpool.AxisCbcFetchCustDemogs.maximumSize: 100
    hystrix.threadpool.AxisCbcFetchCustDemogs.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.AxisCbcFetchCustDemogsEtbCc.coreSize: 100
    hystrix.threadpool.AxisCbcFetchCustDemogsEtbCc.maximumSize: 100
    hystrix.threadpool.AxisCbcFetchCustDemogsEtbCc.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.AxisCbcProcessNewCard.coreSize: 50
    hystrix.threadpool.AxisCbcProcessNewCard.maximumSize: 50
    hystrix.threadpool.AxisCbcProcessNewCard.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.AxisCbcProcessNewCardNtb.coreSize: 50
    hystrix.threadpool.AxisCbcProcessNewCardNtb.maximumSize: 50
    hystrix.threadpool.AxisCbcProcessNewCardNtb.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.AxisCbcProcessNewCardEtbNpa.coreSize: 50
    hystrix.threadpool.AxisCbcProcessNewCardEtbNpa.maximumSize: 50
    hystrix.threadpool.AxisCbcProcessNewCardEtbNpa.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.AxisCbcProcessUpgradeCard.coreSize: 50
    hystrix.threadpool.AxisCbcProcessUpgradeCard.maximumSize: 50
    hystrix.threadpool.AxisCbcProcessUpgradeCard.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.AxisCbcCheckCaseStatus.coreSize: 50
    hystrix.threadpool.AxisCbcCheckCaseStatus.maximumSize: 50
    hystrix.threadpool.AxisCbcCheckCaseStatus.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.AxisCbcCheckCardUpgradeStatus.coreSize: 50
    hystrix.threadpool.AxisCbcCheckCardUpgradeStatus.maximumSize: 50
    hystrix.threadpool.AxisCbcCheckCardUpgradeStatus.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.AxisCbcLcmAuthOtpGen.coreSize: 50
    hystrix.threadpool.AxisCbcLcmAuthOtpGen.maximumSize: 50
    hystrix.threadpool.AxisCbcLcmAuthOtpGen.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.AxisCbcLcmAuthOtpVal.coreSize: 50
    hystrix.threadpool.AxisCbcLcmAuthOtpVal.maximumSize: 50
    hystrix.threadpool.AxisCbcLcmAuthOtpVal.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.AxisCbcCheckCaseStatusMobile.coreSize: 100
    hystrix.threadpool.AxisCbcCheckCaseStatusMobile.maximumSize: 100
    hystrix.threadpool.AxisCbcCheckCaseStatusMobile.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.AxisCbcCheckCaseStatusMobileEtbNpa.coreSize: 100
    hystrix.threadpool.AxisCbcCheckCaseStatusMobileEtbNpa.maximumSize: 100
    hystrix.threadpool.AxisCbcCheckCaseStatusMobileEtbNpa.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.AxisCbcCheckCaseStatusMobileNtb.coreSize: 100
    hystrix.threadpool.AxisCbcCheckCaseStatusMobileNtb.maximumSize: 100
    hystrix.threadpool.AxisCbcCheckCaseStatusMobileNtb.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.IdfcPanVerification.coreSize: 100
    hystrix.threadpool.IdfcPanVerification.maximumSize: 100
    hystrix.threadpool.IdfcPanVerification.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.IdfcAccessTokenKey.coreSize: 50
    hystrix.threadpool.IdfcAccessTokenKey.maximumSize: 50
    hystrix.threadpool.IdfcAccessTokenKey.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.IdfcGenerateOtpKey.coreSize: 100
    hystrix.threadpool.IdfcGenerateOtpKey.maximumSize: 100
    hystrix.threadpool.IdfcGenerateOtpKey.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.IdfcVerifyOtpKey.coreSize: 100
    hystrix.threadpool.IdfcVerifyOtpKey.maximumSize: 100
    hystrix.threadpool.IdfcVerifyOtpKey.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.IdfcImpsPennyDropKey.coreSize: 100
    hystrix.threadpool.IdfcImpsPennyDropKey.maximumSize: 100
    hystrix.threadpool.IdfcImpsPennyDropKey.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.IdfcValidateUpiPennyDropKey.coreSize: 100
    hystrix.threadpool.IdfcValidateUpiPennyDropKey.maximumSize: 100
    hystrix.threadpool.IdfcValidateUpiPennyDropKey.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.IdfcUpiPennyDropKey.coreSize: 100
    hystrix.threadpool.IdfcUpiPennyDropKey.maximumSize: 100
    hystrix.threadpool.IdfcUpiPennyDropKey.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.IdfcCheckEligibilityKey.coreSize: 50
    hystrix.threadpool.IdfcCheckEligibilityKey.maximumSize: 50
    hystrix.threadpool.IdfcCheckEligibilityKey.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.IdfcCreateLoanKey.coreSize: 50
    hystrix.threadpool.IdfcCreateLoanKey.maximumSize: 50
    hystrix.threadpool.IdfcCreateLoanKey.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.IdfcUpdateLoanKey.coreSize: 50
    hystrix.threadpool.IdfcUpdateLoanKey.maximumSize: 50
    hystrix.threadpool.IdfcUpdateLoanKey.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.IdfcCheckLoanStatusKey.coreSize: 50
    hystrix.threadpool.IdfcCheckLoanStatusKey.maximumSize: 50
    hystrix.threadpool.IdfcCheckLoanStatusKey.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.IdfcSearchCkycKey.coreSize: 50
    hystrix.threadpool.IdfcSearchCkycKey.maximumSize: 50
    hystrix.threadpool.IdfcSearchCkycKey.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.IdfcDownloadCkycKey.coreSize: 50
    hystrix.threadpool.IdfcDownloadCkycKey.maximumSize: 50
    hystrix.threadpool.IdfcDownloadCkycKey.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.IdfcSaleForwardKey.coreSize: 50
    hystrix.threadpool.IdfcSaleForwardKey.maximumSize: 50
    hystrix.threadpool.IdfcSaleForwardKey.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.IdfcSaleReverseKey.coreSize: 50
    hystrix.threadpool.IdfcSaleReverseKey.maximumSize: 50
    hystrix.threadpool.IdfcSaleReverseKey.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.IdfcPaybackForwardKey.coreSize: 50
    hystrix.threadpool.IdfcPaybackForwardKey.maximumSize: 50
    hystrix.threadpool.IdfcPaybackForwardKey.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.TeleSales.coreSize: 50
    hystrix.threadpool.TeleSales.maximumSize: 50
    hystrix.threadpool.TeleSales.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.AxisCbcFetchStatement.coreSize: 50
    hystrix.threadpool.AxisCbcFetchStatement.maximumSize: 50
    hystrix.threadpool.AxisCbcFetchStatement.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.AxisCbcStatementDates.coreSize: 50
    hystrix.threadpool.AxisCbcStatementDates.maximumSize: 50
    hystrix.threadpool.AxisCbcStatementDates.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.AxisCbcLifeTimeCashBack.coreSize: 50
    hystrix.threadpool.AxisCbcLifeTimeCashBack.maximumSize: 50
    hystrix.threadpool.AxisCbcLifeTimeCashBack.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.AxisVKYC.coreSize: 50
    hystrix.threadpool.AxisVKYC.maximumSize: 50
    hystrix.threadpool.AxisVKYC.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.IdfcFfbUploadDocumentKey.coreSize: 50
    hystrix.threadpool.IdfcFfbUploadDocumentKey.maximumSize: 50
    hystrix.threadpool.IdfcFfbUploadDocumentKey.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.IdfcSendKycDocument.coreSize: 10
    hystrix.threadpool.IdfcSendKycDocument.maximumSize: 10
    hystrix.threadpool.IdfcSendKycDocument.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.IdfcEbcAuthTokenKey.coreSize: 10
    hystrix.threadpool.IdfcEbcAuthTokenKey.maximumSize: 10
    hystrix.threadpool.IdfcEbcAuthTokenKey.allowMaximumSizeToDivergeFromCoreSize: false
    hystrix.threadpool.UserServiceGenerateOtp.coreSize: 10000
    hystrix.threadpool.UserServiceGenerateOtp.maximumSize: 10000
    hystrix.threadpool.UserServiceGenerateOtp.allowMaximumSizeToDivergeFromCoreSize: false

lenderErrorConfig:
  errorMapping:
    INDIA_BULLS : ["Failure:FAILURE","E:VALID_PAN","N:INVALID_PAN","PAN_FETCH_EXCEPTION:FAILED_PAN_RESPONSE","APPLY_NOW_FETCH_EXCEPTION:FAILURE"]


heliosProxyConfig:
  maxConnections: 1024
  maxConnectionsPerHost: 1024
  pooledConnectionIdleTimeoutInMS: 6000
  executorThreadsCount: 32
  executionIsolationSemaphoreMaxConcurrentRequests: 1024
  executionTimeoutInMS: 10000
  hystrixCommandKey: PandoraServiceHystrixCommand
  hystrixGroupKey: PandoraServiceHystrixGroup
  rewriteProtocal: http
  rewriteHostName: 127.0.0.1
  rewritePortNumber: 8214
  enableProxy: false


teleSalesConfiguration:
  createUrl: https://tpclient.teleperformancedibs.com/FlipKartHulk/RestServiceImpl.svc/pushData
  updateUrl: https://tpclient.teleperformancedibs.com/FlipKartHulk/RestServiceImpl.svc/UpdateData
  aesKey: 31A5A90F993901F784C4563D3E59EA4F
  aesIv: 528AE4EC7AAD236C
  aesSalt: 3289138030CBC0BD
  authKey: FlipKartAuthkey
  authValue: ZiPfoxlkowlQ4Ek

authConfig:
  authEnabled: true
  authnUrl: "https://authn.ch.flipkart.com"
  clientId: "pandora-service"
  clientSecret: "3sJSka1Vms1xtSwxcpKLbxITgyUpMB9mhpP7NhDTaIqz7lvJ"
  authIgnoreUrls: "(?!(/1/ubona/cancelCall|/1/ubona/makeCall).*$).*$"
  saveRequest: false
  loginUrl: null

cryptexBundleConfiguration:
  cryptexBundleEnabled: false
  authNClientConfig:
    url: http://***********
    clientId: pandora-service
    clientSecret: 3sJSka1Vms1xtSwxcpKLbxITgyUpMB9mhpP7NhDTaIqz7lvJ

  cryptexClientConfig:
    endpoint: http://***********
    maxConnections: 5
    connectTimeOut: 1500
    readTimeOut: 1500

  dynamicBucketConfig:
    bucketName: pandora-local
    enableLocalDynamicBucket: true

documentPropertyTypeList:
  - name: CF_DocumentName
    type: DOCUMENT_NAME
  - name: DocumentTitle
    type: DOCUMENT_TITLE
  - name: CF_DocumentTypeName
    type: DOCUMENT_TYPE
  - name: CF_FinnOneLoanNumber
    type: LOAN_NUMBER

idfcFfbAuthConfiguration:
  proxyUri: http://*************:80
  grantType: "client_credentials"
  clientId: "kflpkrt-1a35-46fb-96b5-5f3929ba7381"
  clientSecret: "kflpkrt-85a9-40a1-b132-4faf2c70baa6"

multiTenantOAuthClientConfig:
  cachedAccessTokenTTL : 30
  oAuthUrl: http://***********:80
  oAuthServiceClientConfigMap:
    FK_CONSUMER_CREDIT:
      oAuthClientID: c2c95e0075c04b2e9b83e1bc8a09f57e
      oAuthClientSecret: FVMWGlJ22AoWfQ+ithjYn+M0tjr8xgakuHl06J5A+DELtVtc
      cachedAccessTokenTTL : 30
      oAuthUrl: http://***********:80

fintechUserServiceTenantMap:
  FK_CONSUMER_CREDIT: FLIPKART
  FFB: FFB

businessCategorySchemeIdMap:
  grocery: 58901
  fashion: 58829

quessConfiguration:
  channelId: "FK"
  encryptionKey: "53EACE72CD83D6B60754C2F3959168EA"
  url: https://saksham.axisbank.co.in
  mockEnabled: false
  client: 6697d0e6-74df-4766-83d8-67977f2a1d50
  secretKey: rC4kW4hW6pO0rQ7nS8dW4yU7nH7sB0dN6fH7oA0bO6hN3pW3tY
  username: fkuser
  password: jmEWx)ia5JQVlh(
  contextMap:
    login_token:
      maximumSize: 10000
      duration: 3600
      durationUnit: SECONDS
  axisCbcApiConfigurationsMap:
    userLogin:
      apiPath: "gateway/api/kyc/v1/user-login"
      serviceRequestId: "QUESS"
      serviceRequestVersion: "1.0"
    slotCapacity:
      apiPath: "gateway/api/kyc/v1/slot-capacity"
      serviceRequestId: "QUESS"
      serviceRequestVersion: "1.0"
    scheduleCapacity:
      apiPath: "gateway/api/kyc/v1/reserve-slot"
      serviceRequestId: "QUESS"
      serviceRequestVersion: "1.0"
    getSchedule:
      apiPath: "gateway/api/kyc/v1/get-schedule-details"
      serviceRequestId: "QUESS"
      serviceRequestVersion: "1.0"
    updateSchedule:
      apiPath: "gateway/api/kyc/v1/update-slot"
      serviceRequestId: "QUESS"
      serviceRequestVersion: "1.0"
    cancelSchedule:
      apiPath: "gateway/api/kyc/v1/cancel-slot"
      serviceRequestId: "QUESS"
      serviceRequestVersion: "1.0"

encryptionKeys:
  kycKey: eyJkYXRhIjoidmF1bHQ6djE6MUJTdm8xSU5TVkVwM2hrbk42Um1yU29aaDBnU3pRK0cyNWtFNFA4ZVJQby9LYS9lWXdTZko3QmVMREJFc2UyTWk5bGFHUFNVTm5YV1JiN2siLCJrZXlJZCI6ImNmZy1zdmM6OmluLWNoZW5uYWktMS1wcm9kOnBhbmRvcmEtcHJvZCJ9
  phoenixEncryptionKey: eyJkYXRhIjoidmF1bHQ6djE6Z3gvd2xyUTlpczArTGo1MTJ6RkY1QmNqdzFOMGdZOTJhVi9NSlJueXpFZStSZWpFK0haY3d0N2pZUXZ3TnBWRHltbkVjd3NFUGlZTnZ4dlQiLCJrZXlJZCI6ImNmZy1zdmM6OmluLWNoZW5uYWktMS1wcm9kOnBhbmRvcmEtcHJvZCJ9
  cbcKey: 59AC6C2B95DEFC3EC76C56CF232AF829

ebcClientConfig:
  encryptionSecretKey: eyJkYXRhIjoidmF1bHQ6djE6YXM1NmwvdHZLUjF1dDh6UFYrNFNUNWxxdHdDbGovNEV4MXd0cElmdVFQbW1Na3NlZTBNR0ZJMVdoSFE9Iiwia2V5SWQiOiJjZmctc3ZjOjppbi1jaGVubmFpLTEtcHJvZDpwYW5kb3JhLXByb2QifQ==
  fkEncryptionKey: eyJkYXRhIjoidmF1bHQ6djE6S3F6TkoxSW1xQ0JCQ1djd2RVMnlMbXp1NWU4TDhxZExhemJlMi8xNmdsTWhPdnVXdWRRZWhUOTU4VndwVmM1M0FZWi9SRWFqMnZiSlJhMnIiLCJrZXlJZCI6ImNmZy1zdmM6OmluLWNoZW5uYWktMS1wcm9kOnBhbmRvcmEtcHJvZCJ9
  ebcTokenUriHost: https://proapi2.capitalfirst.com:65515
  ebcAuthPassword: eyJkYXRhIjoidmF1bHQ6djE6RStJZWtWS2FoNFA3elVWOW96MVMxbW1kMmdZckd0WXJkUFQvcWp3YWNadTE2SjNsU1grT0ZXUmg4Zz09Iiwia2V5SWQiOiJjZmctc3ZjOjppbi1jaGVubmFpLTEtcHJvZDpwYW5kb3JhLXByb2QifQ==
  ebcAuthUsername: eyJkYXRhIjoidmF1bHQ6djE6SXFEL1REeGpOeWp6Z3d2QjA2Sk1pWFptU2FOY0diTDJKaGNIb3dJMU0xSGV2TkJERXNNNiIsImtleUlkIjoiY2ZnLXN2Yzo6aW4tY2hlbm5haS0xLXByb2Q6cGFuZG9yYS1wcm9kIn0=
  environment: PROD

datafeedConfiguration:
  url: http://************
  clientId: "fintech-datafeed-service"

phoenixClientConfig:
  url: http://***********
  clientId: pandora

rotation:
  enableRotator: true
  defaultRotationStatus: true

hystrixModuleConfig:
  hystrixPropertiesFileName: hystrix.properties
  resourcesFilePath: "src/integration-test/resources/"

idfcFetchTokenDelayInMinutes: 0

fsupTransactionFlag: true

gibraltarClientConfig:
  host: https://************
  keySpaceName: onboarding
  clientName: efa-onboarding-client
  targetClientId: gibraltar-prod
  maxPublicKeyCount: 100

tijoriClientConfig:
  endpoint: http://***********:80
  clientId: pandora
  authClientId: tijori-service

razorpayConfiguration:
  url: https://api.razorpay.com
  username: eyJkYXRhIjoidmF1bHQ6djE6cWswdVg4QjJ6ME5GdElPSjM3VDVoRnE2Y2dUZEk1K21LTWRvL2Z2Ny8rbjV6UDNZTDdGQmQ5ZGE1bmZjY0FmYUFmWjkiLCJrZXlJZCI6ImNmZy1zdmM6OmluLWh5ZGVyYWJhZC0xLXByZXByb2Q6cGFuZG9yYS1wcmVwcm9kIn0=
  password: eyJkYXRhIjoidmF1bHQ6djE6M1JsQXE2OS9XdnhqdXdReW9wR29kM3VtUGtJTWFTckpvRXVXbHFQZ2hoaXRmMmo1OEk1eWFmTkdjZVJKZmIzL1VVWXVDZz09Iiwia2V5SWQiOiJjZmctc3ZjOjppbi1oeWRlcmFiYWQtMS1wcmVwcm9kOnBhbmRvcmEtcHJlcHJvZCJ9
  accountNumber: eyJkYXRhIjoidmF1bHQ6djE6dW95L0tNRnhKUm9jRjJXUkZuN09TeThSMUdoMVJiQTRURkFZUnBOQ05nUGpTbGJ5c0hibzZzZnlLajA9Iiwia2V5SWQiOiJjZmctc3ZjOjppbi1oeWRlcmFiYWQtMS1wcmVwcm9kOnBhbmRvcmEtcHJlcHJvZCJ9

collectionsConfiguration:
  url: http://**********:7980/bnpl-collections

abConfiguration:
  endpoint: ***********:80
  layerList: "Affordability"
  clientId: "Mapi.affordability"
  testAccounts: []
  unlayeredAbEnabled: true
  clientSecretKey: fintech-pandora-prod-1e51dca49a4943b4b344fb90eeed5f5b

winterfellClientConfig:
  host: http://localhost/fintech-winterfell
  clientId: pandora
  connectionTimeout: 30000
  readTimeout: 10000

