package com.flipkart.fintech.pandora.service.application;

import com.codahale.metrics.MetricRegistry;

public class PandoraMetricRegistry {
    private static MetricRegistry metricRegistry;

    private PandoraMetricRegistry(MetricRegistry metricRegistry) {
        this.metricRegistry = metricRegistry;
    }


    public static synchronized MetricRegistry getMetricRegistry() {
        if (metricRegistry == null) {
            // if instance is null, initialize
            metricRegistry = new MetricRegistry();
        }
        return metricRegistry;
    }
}

