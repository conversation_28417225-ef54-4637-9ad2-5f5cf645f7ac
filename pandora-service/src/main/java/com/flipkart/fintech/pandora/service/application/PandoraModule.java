package com.flipkart.fintech.pandora.service.application;

import com.codahale.metrics.MetricRegistry;
import com.codahale.metrics.health.HealthCheck;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.module.paramnames.ParameterNamesModule;
import com.flipkart.abservice.models.exceptions.ABInitializationException;
import com.flipkart.abservice.resources.ABService;
import com.flipkart.abservice.store.ConfigStore;
import com.flipkart.abservice.store.impl.ABApiConfigStore;
import com.flipkart.affordability.bnpl.tijori.api.enums.Lender;
import com.flipkart.affordability.bnpl.tijori.client.StdTijoriClientConfig;
import com.flipkart.affordability.clients.loginservice.LoginServiceClient;
import com.flipkart.affordability.clients.loginservice.LoginServiceClientImpl;
import com.flipkart.affordability.clients.oauth.MultiTenantOAuthClientImpl;
import com.flipkart.affordability.clients.oauth.OAuthClient;
import com.flipkart.affordability.clients.userservice.USClient;
import com.flipkart.affordability.clients.userservice.USClientImpl;
import com.flipkart.affordability.collections.client.CollectionsClientConfiguration;
import com.flipkart.affordability.collections.client.sync.SyncCollectionsClient;
import com.flipkart.affordability.collections.client.sync.SyncCollectionsClientImpl;
import com.flipkart.affordability.config.BigfootConfiguration;
import com.flipkart.affordability.config.LoginServiceClientConfig;
import com.flipkart.affordability.config.MultiTenantOAuthClientConfig;
import com.flipkart.affordability.config.UserServiceClientConfig;
import com.flipkart.affordability.khaata.client.KhaataClient;
import com.flipkart.affordability.khaata.client.KhaataClientConfig;
import com.flipkart.affordability.khaata.client.KhaataClientImpl;
import com.flipkart.affordability.phoenix.client.PhoenixClientConfig;
import com.flipkart.fintech.ardour.client.ArdourClientConfig;
import com.flipkart.fintech.ardour.client.ArdourClientImplV2;
import com.flipkart.fintech.ardour.client.ArdourClientV2;
import com.flipkart.fintech.common.enums.Tenant;
import com.flipkart.fintech.cryptex.LocalDynamicBucket;
import com.flipkart.fintech.cryptex.config.CryptexBundleConfiguration;
import com.flipkart.fintech.cryptex.config.DynamicBucketConfig;
import com.flipkart.fintech.pandora.api.model.cbc.request.common.ValidateDetailsRequestType;
import com.flipkart.fintech.pandora.api.model.common.FinancialProvider;
import com.flipkart.fintech.pandora.api.model.pl.response.VkycStatus.VkycStatusEnum;
import com.flipkart.fintech.pandora.service.annotations.RateLimit;
import com.flipkart.fintech.pandora.service.application.configuration.*;
import com.flipkart.fintech.pandora.service.client.PandoraServiceClientModule;
import com.flipkart.fintech.pandora.service.client.auth.*;
import com.flipkart.fintech.pandora.service.client.auth.Scope;
import com.flipkart.fintech.pandora.service.client.cbc.axis.SmAxisCbcConfiguration;
import com.flipkart.fintech.pandora.service.client.cbc.kotak.SmKotakCbcConfiguration;
import com.flipkart.fintech.pandora.service.client.cbc.upswing.config.UpswingConfig;
import com.flipkart.fintech.pandora.service.client.cbc.upswing.helper.UpswingInMemoryCache;
import com.flipkart.fintech.pandora.service.client.cbc.upswing.helper.UpswingInMemoryCacheImpl;
import com.flipkart.fintech.pandora.service.client.cbc.upswing.services.UpswingServiceClient;
import com.flipkart.fintech.pandora.service.client.cbc.upswing.services.UpswingServiceClientImpl;
import com.flipkart.fintech.pandora.service.client.cfa.ConfigHelper;
import com.flipkart.fintech.pandora.service.client.cfa.MockHelper;
import com.flipkart.fintech.pandora.service.client.collections.IgsClient;
import com.flipkart.fintech.pandora.service.client.collections.IgsClientImpl;
import com.flipkart.fintech.pandora.service.client.collections.config.CollectionsConfiguration;
import com.flipkart.fintech.pandora.service.client.config.GupshupWebhookConfig;
import com.flipkart.fintech.pandora.service.client.config.KotakClientConfiguration;
import com.flipkart.fintech.pandora.service.client.config.MpockketClientConfig;
import com.flipkart.fintech.pandora.service.client.config.YubiConfiguration;
import com.flipkart.fintech.pandora.service.client.creditline.yubi.YubiCreditLineClient;
import com.flipkart.fintech.pandora.service.client.creditline.yubi.YubiCreditLineClientImpl;
import com.flipkart.fintech.pandora.service.client.creditsaison.CreditSaisonAuthenticationImpl;
import com.flipkart.fintech.pandora.service.client.creditsaison.configs.CreditSaisonConfiguration;
import com.flipkart.fintech.pandora.service.client.datafeed.config.DatafeedConfiguration;
import com.flipkart.fintech.pandora.service.client.digio.config.DigioConfiguration;
import com.flipkart.fintech.pandora.service.client.epaylater.EpaylaterClientConfig;
import com.flipkart.fintech.pandora.service.client.epaylater.EpaylaterServiceClient;
import com.flipkart.fintech.pandora.service.client.epaylater.EpaylaterServiceClientImpl;
import com.flipkart.fintech.pandora.service.client.experian.configs.ExperianConfiguration;
import com.flipkart.fintech.pandora.service.client.fixera.config.FixeraConfig;
import com.flipkart.fintech.pandora.service.client.hmac.HmacAuthentication;
import com.flipkart.fintech.pandora.service.client.indiabulls.IndiaBullsClientConfig;
import com.flipkart.fintech.pandora.service.client.kissht.KisshtBnplServiceClient;
import com.flipkart.fintech.pandora.service.client.kissht.KisshtBnplServiceClientImpl;
import com.flipkart.fintech.pandora.service.client.kissht.KisshtServiceClientConfigBnpl;
import com.flipkart.fintech.pandora.service.client.kissht.KisshtServiceClientConfigEfa;
import com.flipkart.fintech.pandora.service.client.kissht.client.billing.KisshtBillingServiceClient;
import com.flipkart.fintech.pandora.service.client.kissht.client.billing.KisshtBillingServiceClientImpl;
import com.flipkart.fintech.pandora.service.client.kissht.client.onboarding.KisshtOnboardingServiceClient;
import com.flipkart.fintech.pandora.service.client.kissht.client.onboarding.KisshtOnboardingServiceClientImpl;
import com.flipkart.fintech.pandora.service.client.kissht.client.payback.KisshtPaybackServiceClient;
import com.flipkart.fintech.pandora.service.client.kissht.client.payback.KisshtPaybackServiceClientImpl;
import com.flipkart.fintech.pandora.service.client.kissht.client.transaction.KisshtTransactionServiceClient;
import com.flipkart.fintech.pandora.service.client.kissht.client.transaction.KisshtTransactionServiceClientImpl;
import com.flipkart.fintech.pandora.service.client.kotak.KotakAdvanzServiceClient;
import com.flipkart.fintech.pandora.service.client.kotak.KotakAdvanzServiceClientImpl;
import com.flipkart.fintech.pandora.service.client.kotak.KotakConfiguration;
import com.flipkart.fintech.pandora.service.client.pl.*;
import com.flipkart.fintech.pandora.service.client.pl.client.SandboxLendersPlV2Client;
import com.flipkart.fintech.pandora.service.client.pl.client.SmSandboxLendersPlV2Client;
import com.flipkart.fintech.pandora.service.client.pl.ebc.EbcClientConfig;
import com.flipkart.fintech.pandora.service.client.plOnboarding.PlClientConstants;
import com.flipkart.fintech.pandora.service.client.razorpay.RazorpayConfiguration;
import com.flipkart.fintech.pandora.service.commons.bigfootIngestor.BigfootEntityConfig;
import com.flipkart.fintech.pandora.service.core.UserOnboarding.*;
import com.flipkart.fintech.pandora.service.core.auth.IdfcTokenScheduler;
import com.flipkart.fintech.pandora.service.core.billing.IBillingManagerFactory;
import com.flipkart.fintech.pandora.service.core.billing.v2.IBillingManager;
import com.flipkart.fintech.pandora.service.core.billing.v2.IndiaBullsBillingManager;
import com.flipkart.fintech.pandora.service.core.callbackevents.CallbackEventService;
import com.flipkart.fintech.pandora.service.core.callbackevents.CreditSaisonCallbackEventService;
import com.flipkart.fintech.pandora.service.core.cbc.cardProvider.AxisCbcActionExecutor;
import com.flipkart.fintech.pandora.service.core.cbc.cardProvider.CbcActionExecutor;
import com.flipkart.fintech.pandora.service.core.cbc.fieldValidators.FieldValidator;
import com.flipkart.fintech.pandora.service.core.cbc.fieldValidators.axis.AxisDisplayNameFieldValidator;
import com.flipkart.fintech.pandora.service.core.cbc.manager.*;
import com.flipkart.fintech.pandora.service.core.cbc.manager.impl.*;
import com.flipkart.fintech.pandora.service.core.cg.CgManager;
import com.flipkart.fintech.pandora.service.core.cg.CgManagerImp;
import com.flipkart.fintech.pandora.service.core.debuggingtool.DebuggingService;
import com.flipkart.fintech.pandora.service.core.debuggingtool.DebuggingServiceImpl;
import com.flipkart.fintech.pandora.service.core.ebc.DecryptionService;
import com.flipkart.fintech.pandora.service.core.ebc.DecryptionServiceImpl;
import com.flipkart.fintech.pandora.service.core.ebc.EbcService;
import com.flipkart.fintech.pandora.service.core.ebc.EbcServiceImpl;
import com.flipkart.fintech.pandora.service.core.incomeassessment.*;
import com.flipkart.fintech.pandora.service.core.lenderaction.IdfcLenderAction;
import com.flipkart.fintech.pandora.service.core.lenderaction.KotakLenderAction;
import com.flipkart.fintech.pandora.service.core.lenderaction.LenderActionService;
import com.flipkart.fintech.pandora.service.core.lenders.IdfcUserActionExecutor;
import com.flipkart.fintech.pandora.service.core.lenders.LenderUserActionExecutor;
import com.flipkart.fintech.pandora.service.core.lenders.YubiUserActionExecutor;
import com.flipkart.fintech.pandora.service.core.payback.DirectPaybackService;
import com.flipkart.fintech.pandora.service.core.payback.DirectPaybackServiceImpl;
import com.flipkart.fintech.pandora.service.core.plUserOnboarding.*;
import com.flipkart.fintech.pandora.service.core.plUserOnboarding.adapters.ILenderOperationAdapter;
import com.flipkart.fintech.pandora.service.core.plUserOnboarding.adapters.LenderAdapterFactory;
import com.flipkart.fintech.pandora.service.core.plUserOnboarding.annotations.LenderConfiguration;
import com.flipkart.fintech.pandora.service.core.quess.*;
import com.flipkart.fintech.pandora.service.core.smefin.PayoutDataMockServiceImpl;
import com.flipkart.fintech.pandora.service.core.smefin.PayoutDataService;
import com.flipkart.fintech.pandora.service.core.smefin.PayoutDataServiceImpl;
import com.flipkart.fintech.pandora.service.core.transaction.IDFCTransactionManagerV2;
import com.flipkart.fintech.pandora.service.core.transaction.KotakTransactionManager;
import com.flipkart.fintech.pandora.service.core.transaction.TransactionManager;
import com.flipkart.fintech.pandora.service.core.upi.RazorpayUpiService;
import com.flipkart.fintech.pandora.service.core.upi.RazorpayUpiServiceImpl;
import com.flipkart.fintech.pandora.service.external.IBConfiguration;
import com.flipkart.fintech.pandora.service.external.LenderErrorConfig;
import com.flipkart.fintech.pandora.service.external.coreLogistics.models.CoreLogisticsClientConfig;
import com.flipkart.fintech.pandora.service.hystrix.PandoraHystrixProperties;
import com.flipkart.fintech.pandora.service.resources.internal.HealthCheckResource;
import com.flipkart.fintech.pandora.service.strategy.kyc.*;
import com.flipkart.fintech.pandora.service.strategy.loan_activation.*;
import com.flipkart.fintech.pandora.service.utils.AbConfiguration;
import com.flipkart.fintech.pandora.service.utils.AbConstants;
import com.flipkart.fintech.pandora.service.utils.PandoraContext;
import com.flipkart.fintech.pandora.service.utils.interceptors.RateLimitingInterceptor;
import com.flipkart.fintech.pinaka.client.PinakaClientConfig;
import com.flipkart.fintech.pinaka.client.PinakaClientModule;
import com.flipkart.fintech.security.gibraltar.GibraltarModule;
import com.flipkart.fintech.security.gibraltar.config.GibraltarClientConfig;
import com.flipkart.fintech.sm.card.gateway.CardGatewayClientModule;
import com.flipkart.fintech.sm.card.gateway.config.CardGatewayClientConfig;
import com.flipkart.fintech.user.data.client.UserDataClient;
import com.flipkart.fintech.user.data.client.UserDataClientImpl;
import com.flipkart.fintech.user.data.config.SmUserServiceClientConfig;
import com.flipkart.fintech.winterfell.client.WinterfellClientConfig;
import com.flipkart.fintech.winterfell.client.WinterfellClientModule;
import com.flipkart.kloud.authn.AuthTokenService;
import com.flipkart.kloud.config.ConfigClient;
import com.flipkart.kloud.config.ConfigClientBuilder;
import com.flipkart.kloud.config.DynamicBucket;
import com.flipkart.kloud.config.error.ConfigServiceException;
import com.flipkart.usercluster.chowkidaar.client.ChowkidaarClient;
import com.flipkart.usercluster.chowkidaar.client.impl.ChowkidaarClientImpl;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.inject.*;
import com.google.inject.assistedinject.FactoryModuleBuilder;
import com.google.inject.matcher.Matchers;
import com.google.inject.multibindings.MapBinder;
import com.google.inject.name.Named;
import com.google.inject.name.Names;
import com.netflix.hystrix.contrib.javanica.aop.guice.InterceptorModule;
import com.sumo.bff.card.client.CbcWebhookClient;
import com.sumo.bff.card.client.CbcWebhookClientImpl;
import com.sumo.bff.card.client.GupshupWebhookClient;
import com.sumo.bff.card.client.GupshupWebhookClientImpl;
import com.sumo.bff.card.config.CardServiceClientConfig;
import com.sumo.boomerang.client.BoomerangClientConfig;
import com.sumo.boomerang.client.BoomerangClientModule;
import io.dropwizard.client.JerseyClientBuilder;
import io.dropwizard.client.JerseyClientConfiguration;
import io.dropwizard.jackson.Jackson;
import io.dropwizard.setup.Environment;
import lombok.SneakyThrows;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpHost;
import org.apache.http.conn.ssl.AllowAllHostnameVerifier;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestHighLevelClient;
import org.glassfish.jersey.SslConfigurator;
import org.glassfish.jersey.client.ClientProperties;
import org.glassfish.jersey.client.HttpUrlConnectorProvider;
import org.reflections.Reflections;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.SSLContext;
import javax.ws.rs.client.Client;
import javax.ws.rs.client.ClientBuilder;
import javax.ws.rs.client.WebTarget;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * Created by aniruddha.sharma on 06/09/17.
 */
public class PandoraModule extends AbstractModule {

    private static final Logger logger = LoggerFactory.getLogger(PandoraModule.class);
    private final PandoraConfiguration pandoraConfig;
    private final Environment environment;
    private final DynamicBucket dynamicBucket;

    public PandoraModule(PandoraConfiguration pandoraConfig, Environment environment, DynamicBucket dynamicBucket) {
        this.pandoraConfig = pandoraConfig;
        this.environment = environment;
        this.dynamicBucket = dynamicBucket;
    }

    @SneakyThrows
    @Override
    protected void configure() {
        install(new BoomerangClientModule(providesBoomerangClientConfig()));
        bind(MetricRegistry.class).toInstance(environment.metrics());
        bind(HealthCheck.class).to(HealthCheckResource.class);
        bind(SandboxPlManager.class).to(SandboxPlManagerImpl.class);
        bind(SandboxLendersPlV2Client.class).to(SmSandboxLendersPlV2Client.class);
        bind(KisshtOnboardingServiceClient.class).to(KisshtOnboardingServiceClientImpl.class);
        bind(KisshtTransactionServiceClient.class).to(KisshtTransactionServiceClientImpl.class);
        bind(KisshtBillingServiceClient.class).to(KisshtBillingServiceClientImpl.class);
        bind(KisshtPaybackServiceClient.class).to(KisshtPaybackServiceClientImpl.class);
        bind(KisshtBnplServiceClient.class).to(KisshtBnplServiceClientImpl.class);
        bind(IdfcBnplServiceClient.class).to(IdfcBnplServiceClientImpl.class);
        bind(IdfcBnplServiceClientV2.class).to(IdfcBnplServiceClientV2Impl.class);
        bind(IdfcBnplServiceClientV3.class).to(IdfcBnplServiceClientV3Impl.class);
        bind(YubiCreditLineClient.class).to(YubiCreditLineClientImpl.class).in(Singleton.class);
        bind(IdfcTokenScheduler.class).in(Singleton.class);
        bind(KhaataClient.class).to(KhaataClientImpl.class);
        install(new GibraltarModule());
        bind(LoginServiceClient.class).to(LoginServiceClientImpl.class);
        bind(OAuthClient.class).to(MultiTenantOAuthClientImpl.class);
        bind(EpaylaterServiceClient.class).to(EpaylaterServiceClientImpl.class);
        bind(CgManager.class).to(CgManagerImp.class);
        bind(ArdourClientV2.class).to(ArdourClientImplV2.class);
        bind(SyncCollectionsClient.class).to(SyncCollectionsClientImpl.class);
        bind(RazorpayUpiService.class).to(RazorpayUpiServiceImpl.class);
        bind(HmacAuthentication.class).to(CreditSaisonAuthenticationImpl.class);
        bind(CallbackEventService.class).to(CreditSaisonCallbackEventService.class);
        install(new PinakaClientModule());
        install(new CardGatewayClientModule());
        install(new PandoraServiceClientModule(pandoraConfig.getIdfcConfiguration(), pandoraConfig.getHyperVergeConfiguration(), pandoraConfig.getDocumentStoreConfiguration(), pandoraConfig.getPinakaClientConfig(), pandoraConfig.getSandboxLendersPlConfigurationList(),pandoraConfig.getSmAxisCbcConfiguration(), pandoraConfig.getSmKotakCbcConfiguration(), pandoraConfig.getDigitapAiConfiguration()));
	    install(new FactoryModuleBuilder()
			    .implement(IBillingManager.class, Names.named("india_bulls_efa"), IndiaBullsBillingManager.class)
			    .build(IBillingManagerFactory.class));
        install(new WinterfellClientModule(pandoraConfig.getWinterfellClientConfig()));
	    bind(QuessKycSchedulerService.class).to(QuessKycSchedulerServiceImpl.class);
	    bind(EbcService.class).to(EbcServiceImpl.class);
        bind(DebuggingService.class).to(DebuggingServiceImpl.class);
	    bind(DecryptionService.class).to(DecryptionServiceImpl.class);
	    bind(PlOnboardingManager.class).to(PlOnboardingManagerImpl.class);
        bind(String.class).annotatedWith(Names.named("vkycResumeUrl")).toInstance(pandoraConfig.getIdfcConfiguration().getVkycResumeUrl());
        bind(String.class).annotatedWith(Names.named("emandateRedirectionUrl")).toInstance(pandoraConfig.getIdfcConfiguration().getEmandateRedirectionUrl());
	    if ("PROD".equals(pandoraConfig.getDeployEnv())) {
	        bind(PayoutDataService.class).to(PayoutDataServiceImpl.class);
        } else {
	        bind(PayoutDataService.class).to(PayoutDataMockServiceImpl.class);
        }
        install(new HystrixModule(pandoraConfig.getHystrixModuleConfig().getResourcesFilePath() + pandoraConfig.getHystrixModuleConfig().getHystrixPropertiesFileName()));
        install(new InterceptorModule());
	    bind(IncomeAssessmentService.class).to(IncomeAssessmentServiceImpl.class);
        bindInterceptor(Matchers.any(), Matchers.annotatedWith(RateLimit.class), new RateLimitingInterceptor(pandoraConfig.getRateLimitingConfig(), providesDynamicBucket()));
        bind(IgsClient.class).to(IgsClientImpl.class);
        bind(DirectPaybackService.class).to(DirectPaybackServiceImpl.class);
        bindStrategiesToLender();

        bind(KotakAdvanzServiceClient.class).to(KotakAdvanzServiceClientImpl.class);

        bindTransactionMapper();
        bindLenderActionService();
        bindAuthenticationClientToScope();
        bindLenderUserActionExecutor();
        bindIdfcVkycStatus();
        bindUpswingServices();
        bind(String.class).annotatedWith(Names.named("emandateSecretKey")).toInstance(pandoraConfig.getIdfcConfiguration().getEmandateSecretKey());
        bind(String.class).annotatedWith(Names.named("initializationVector")).toInstance(pandoraConfig.getIdfcConfiguration().getEmandateInitializationVector());
        bindCbcActionExecutor();
        bindCbcFieldValidators();
        bind(UserDataClient.class).to(UserDataClientImpl.class);
        bind(USClient.class).to(USClientImpl.class);
        bind(ChowkidaarClient.class).toInstance(new ChowkidaarClientImpl(pandoraConfig.getChowkidaarClientConfig()));
    }

    private void bindUpswingServices() {
        bind(UpswingHandshakeManager.class).to(UpswingHandshakeManagerImpl.class);
        bind(UpswingInMemoryCache.class).to(UpswingInMemoryCacheImpl.class);
        bind(UpswingServiceClient.class).to(UpswingServiceClientImpl.class);
        bind(UpswingWebhookEventsManager.class).to(UpswingWebhookEventsManagerImpl.class);
        bind(UpswingRewardRedemptionManager.class).to(UpswingRewardRedemptionManagerImpl.class);
        bind(UpswingApplicationJourneyDetailsManager.class).to(UpswingApplicationJourneyJourneyDetailsManagerImpl.class);
        bind(UpswingAccountDetailsManager.class).to(UpswingAccountDetailsManagerImpl.class);
        bind(UpswingFixedDepositManager.class).to(UpswingFixedDepositManagerImpl.class);
    }

    private void bindIdfcVkycStatus() {
        MapBinder<String, VkycStatusEnum> vkycStatusEnumMapBinder = MapBinder.newMapBinder(binder(), String.class, VkycStatusEnum.class);
        vkycStatusEnumMapBinder.addBinding("Maker Call Initiated - Approved").toInstance(VkycStatusEnum.APPROVED);
        vkycStatusEnumMapBinder.addBinding("Auditor Status - Approved").toInstance(VkycStatusEnum.APPROVED);
        vkycStatusEnumMapBinder.addBinding("Maker Call Initiated - Rejected").toInstance(VkycStatusEnum.REJECTED);
        vkycStatusEnumMapBinder.addBinding("Auditor Status - Rejected").toInstance(VkycStatusEnum.REJECTED);
        vkycStatusEnumMapBinder.addBinding("VCIP link generated - Call not initiated").toInstance(VkycStatusEnum.RETRY);
        vkycStatusEnumMapBinder.addBinding("Maker Call Initiated - Unable to Verify").toInstance(VkycStatusEnum.REJECTED);
        vkycStatusEnumMapBinder.addBinding("Auditor Status - Unable to verify").toInstance(VkycStatusEnum.REJECTED);
    }

    private void bindLenderUserActionExecutor() {
        MapBinder<String, LenderUserActionExecutor> lenderUserActionExecutorMapBinder = MapBinder.newMapBinder(binder(), String.class, LenderUserActionExecutor.class);
        lenderUserActionExecutorMapBinder.addBinding("idfc").to(IdfcUserActionExecutor.class);
        lenderUserActionExecutorMapBinder.addBinding("ltfs").to(YubiUserActionExecutor.class);
    }

    private void bindCbcActionExecutor(){
        MapBinder<String, CbcActionExecutor> cbcActionExecutorMapBinder = MapBinder.newMapBinder(binder(), String.class, CbcActionExecutor.class);
        cbcActionExecutorMapBinder.addBinding("axis").to(AxisCbcActionExecutor.class);
    }

    private void bindCbcFieldValidators(){
        MapBinder<ValidateDetailsRequestType, FieldValidator> cbcFieldValidatorMapBinder = MapBinder.newMapBinder(binder(), ValidateDetailsRequestType.class, FieldValidator.class);
        cbcFieldValidatorMapBinder.addBinding(ValidateDetailsRequestType.AXIS_DISPLAY_NAME_VALIDATION).to(AxisDisplayNameFieldValidator.class);
    }

    private void bindTransactionMapper() {
        MapBinder<FinancialProvider, TransactionManager> transactionManagerMapBinder = MapBinder.newMapBinder(binder(), FinancialProvider.class, TransactionManager.class);
        transactionManagerMapBinder.addBinding(FinancialProvider.IDFC).to(IDFCTransactionManagerV2.class);
        transactionManagerMapBinder.addBinding(FinancialProvider.KOTAK).to(KotakTransactionManager.class);
    }

    private void bindLenderActionService() {
        MapBinder<Lender, LenderActionService> lenderActionServiceMapBinder = MapBinder.newMapBinder(binder(),Lender.class, LenderActionService.class);
        lenderActionServiceMapBinder.addBinding(Lender.KOTAK).to(KotakLenderAction.class);
        lenderActionServiceMapBinder.addBinding(Lender.IDFC).to(IdfcLenderAction.class);
    }

    private void bindAuthenticationClientToScope()
    {
        MapBinder<Scope, LenderAuthenticationClient> scopeLenderAuthenticationClientMapBinder = MapBinder.newMapBinder(binder(),
                Scope.class, LenderAuthenticationClient.class);
        scopeLenderAuthenticationClientMapBinder.addBinding(Scope.LENDING_IDFC).to(IdfcAuthenticationClient.class);
        scopeLenderAuthenticationClientMapBinder.addBinding(Scope.LENDING_KOTAK).to(KotakAuthenticationClient.class);
        scopeLenderAuthenticationClientMapBinder.addBinding(Scope.LENDING_KOTAK_M2P).to(KotakM2pAuthenticationClient.class);
        scopeLenderAuthenticationClientMapBinder.addBinding(Scope.IDENTITY_VERIFICATION_HYPERVERGE).to(HyperVergeAuthenticationClient.class);
        scopeLenderAuthenticationClientMapBinder.addBinding(Scope.LENDING_MONEYVIEW).to(MoneyViewAuthenticationClient.class);
        scopeLenderAuthenticationClientMapBinder.addBinding(Scope.LENDING_MONEYVIEWV2).to(MoneyViewV2AuthenticationClient.class);
        scopeLenderAuthenticationClientMapBinder.addBinding(Scope.LENDING_FIBE).to(FibeAuthenticationClient.class);
        scopeLenderAuthenticationClientMapBinder.addBinding(Scope.LENDING_FIBEV2).to(FibeV2AuthenticationClient.class); //fibe to v2
        scopeLenderAuthenticationClientMapBinder.addBinding(Scope.LENDING_MONEYVIEWOPENMKT).to(MoneyViewOpenMktAuthenticationClient.class);
        scopeLenderAuthenticationClientMapBinder.addBinding(Scope.LENDING_MONEYVIEWMFI).to(MoneyviewMfiAuthenticationClient.class);
        scopeLenderAuthenticationClientMapBinder.addBinding(Scope.EXPERIAN).to(ExperianAuthenticationClient.class);
        scopeLenderAuthenticationClientMapBinder.addBinding(Scope.LENDING_DMI).to(DmiAuthenticationClient.class);
        scopeLenderAuthenticationClientMapBinder.addBinding(Scope.LENDING_OMNI).to(OmniAuthenticationClient.class);
        scopeLenderAuthenticationClientMapBinder.addBinding(Scope.LENDING_OMNIV2).to(OmniV2AuthenticationClient.class);
        scopeLenderAuthenticationClientMapBinder.addBinding(Scope.LENDING_SMARTCOIN).to(SmartCoinAuthenticationClient.class);
        scopeLenderAuthenticationClientMapBinder.addBinding(Scope.LENDING_FINNABLE).to(FinnableAuthenticationClient.class);
        scopeLenderAuthenticationClientMapBinder.addBinding(Scope.LENDING_RING).to(RingAuthenticationClient.class);
        scopeLenderAuthenticationClientMapBinder.addBinding(Scope.LENDING_ABFL).to(ABFLAuthenticationClient.class);
        scopeLenderAuthenticationClientMapBinder.addBinding(Scope.LENDING_PFL).to(PFLAuthenticationClient.class);
        scopeLenderAuthenticationClientMapBinder.addBinding(Scope.LENDING_PREFR).to(PrefrAuthenticationClient.class);
    }

    private void bindStrategiesToLender() {
        MapBinder<FinancialProvider, PanValidationStrategy> panValidationStrategyMapBinder =
                MapBinder.newMapBinder(binder(), FinancialProvider.class, PanValidationStrategy.class);
        panValidationStrategyMapBinder.addBinding(FinancialProvider.IDFC).to(IdfcPanValidationStrategy.class);
        panValidationStrategyMapBinder.addBinding(FinancialProvider.KOTAK).to(KotakPanValidationStrategy.class);

        MapBinder<FinancialProvider, CKYCSearchStrategy> ckycSearchStrategyMapBinder =
                MapBinder.newMapBinder(binder(), FinancialProvider.class, CKYCSearchStrategy.class);
        ckycSearchStrategyMapBinder.addBinding(FinancialProvider.IDFC).to(IdfcCKYCSearchStrategy.class);
        ckycSearchStrategyMapBinder.addBinding(FinancialProvider.KOTAK).to(KotakCKYCSearchStrategy.class);

        MapBinder<FinancialProvider, CheckEligibilityStrategy> checkEligibilityStrategyMapBinder =
                MapBinder.newMapBinder(binder(), FinancialProvider.class, CheckEligibilityStrategy.class);
        checkEligibilityStrategyMapBinder.addBinding(FinancialProvider.IDFC).to(IdfcCheckEligibilityStrategy.class);
        checkEligibilityStrategyMapBinder.addBinding(FinancialProvider.KOTAK).to(KotakCheckEligibilityStrategy.class);

        MapBinder<FinancialProvider, LoanCreationStrategy> loanCreationStrategyMapBinder =
                MapBinder.newMapBinder(binder(), FinancialProvider.class, LoanCreationStrategy.class);
        loanCreationStrategyMapBinder.addBinding(FinancialProvider.IDFC).to(IdfcLoanCreationStrategy.class);
        loanCreationStrategyMapBinder.addBinding(FinancialProvider.KOTAK).to(KotakLoanCreationStrategy.class);
    }

    @Provides
    @Singleton
    public EsClientConfig providesEsClientConfig() {
        return pandoraConfig.getEsConfig();
    }

    @Provides
    @Singleton
    public BoomerangClientConfig providesBoomerangClientConfig() {
        return pandoraConfig.getBoomerangClientConfig();
    }

    @Provides
    @Singleton
    public BigfootEntityConfig providesBigfootEntityConfig() {
        return pandoraConfig.getBigfootEntityConfig();
    }

    @Provides
    @Singleton
    public BigfootConfiguration provideBigfootConfig() {
        return pandoraConfig.getBigfootConfiguration();
    }

    @Provides
    @Singleton
    public HttpHost provideHttpHost() {
        return new HttpHost(providesEsClientConfig().getHostName(), providesEsClientConfig().getPort());
    }

    @Provides
    @Singleton
    public RestHighLevelClient provideRestHighLevelClient(){
        return new RestHighLevelClient(RestClient.builder(provideHttpHost()).setRequestConfigCallback(
                requestConfigBuilder -> requestConfigBuilder.setConnectTimeout(providesEsClientConfig().getConnectionTimeout())));
    }

    @Provides
    public ObjectMapper provideObjectMapper() {
        //return environment.getObjectMapper();
        ObjectMapper objectMapper = Jackson.newObjectMapper();
        objectMapper.setPropertyNamingStrategy(
                PropertyNamingStrategy.CAMEL_CASE_TO_LOWER_CASE_WITH_UNDERSCORES);
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        JavaTimeModule module = new JavaTimeModule();

        LocalDateDeserializer localDateTimeDeserializer = new LocalDateDeserializer(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        module.addDeserializer(LocalDate.class, localDateTimeDeserializer);

        objectMapper.setTimeZone(TimeZone.getTimeZone("GMT+530"));
        objectMapper.registerModule(module);
        objectMapper.registerModule(new ParameterNamesModule());
        objectMapper.registerModule(new Jdk8Module());

        return objectMapper;
    }

    @Provides
    @Named("default-case")
    public ObjectMapper provideDefalutCaseObjectMapper() {
        ObjectMapper objectMapper = Jackson.newObjectMapper();
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        JavaTimeModule module = new JavaTimeModule();

        LocalDateDeserializer localDateTimeDeserializer = new LocalDateDeserializer(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        module.addDeserializer(LocalDate.class, localDateTimeDeserializer);

        objectMapper.setTimeZone(TimeZone.getTimeZone("GMT+530"));
        objectMapper.registerModule(module);
        objectMapper.registerModule(new ParameterNamesModule());
        objectMapper.registerModule(new Jdk8Module());

        return objectMapper;
    }

    @Provides
    public KisshtServiceClientConfigEfa provideKisshtServiceClientConfigForEfa() {
        return pandoraConfig.getKisshtServiceClientConfigEfaEfa();
    }

    @Provides
    public RazorpayConfiguration provideRazorpayClientConfig() {
        return pandoraConfig.getRazorpayConfiguration();
    }


    @Provides
    public KisshtServiceClientConfigBnpl provideKisshtServiceClientConfigForBnpl() {
        return pandoraConfig.getKisshtServiceClientConfigEfaBnpl();
    }

    @Provides
    public UserServiceClientConfig provideUserServiceClientConfig() {
        return pandoraConfig.getUserServiceClientConfig();
    }



    @Provides
    public KhaataClientConfig provideKhaataClientConfig() {
        return pandoraConfig.getKhaataClientConfig();
    }

    @Provides
    public BucketingConfiguration provideBucketingConfig() {
        return pandoraConfig.getBucketingConfig();
    }

    @Provides
    public EpaylaterClientConfig provideEpaylaterClientConfig() { return pandoraConfig.getEpaylaterClientConfig();}


    @Provides
    public IdfcConfiguration provideIdfcConfiguration() { return pandoraConfig.getIdfcConfiguration();
    }

    @Provides
    public YubiConfiguration provideYubiConfiguration() { return pandoraConfig.getYubiConfiguration();}

    @Provides
    @Singleton
    public WebTarget provideYubiWebTarget(Client client, YubiConfiguration config) {
        client.property(ClientProperties.CONNECT_TIMEOUT, config.getConnectTimeoutInMillis());
        client.property(ClientProperties.READ_TIMEOUT, config.getReadTimeoutInMillis());
        return client.target(config.getBaseUrl());
    }

    @Provides
    public ArdourClientConfig providesArdourClientConfig() { return pandoraConfig.getArdourClientConfig();}

    @Provides
    public IndiaBullsClientConfig provideIndiaBullsClientConfig(){
        return pandoraConfig.getIndiaBullsClientConfig();
    }


    @Provides
    @Singleton
    public Client provideJerseyClient() {
        JerseyClientConfiguration clientConfiguration = pandoraConfig.getJerseyClientConfiguration();
        Client jerseyClient = (new JerseyClientBuilder(environment)).using(clientConfiguration).using(environment)
                .build("HTTP_CLIENT");
        jerseyClient.property(ClientProperties.CONNECT_TIMEOUT, 15000);
        jerseyClient.property(ClientProperties.READ_TIMEOUT, 15000);
        return jerseyClient;
    }

    @Provides
    @Named("idfc")
    @Singleton
    public Client provideJerseyClientForIdfc() {
        ClientBuilder builder = ClientBuilder.newBuilder();
        builder.property(ClientProperties.CONNECT_TIMEOUT, 30000);
        builder.property(ClientProperties.READ_TIMEOUT, 30000);
        return builder.build();
    }

    @Provides
    @Named("bigfoot")
    @Singleton
    public Client provideJerseyClientForBigfoot() {
        ClientBuilder builder = ClientBuilder.newBuilder();
        builder.property(ClientProperties.CONNECT_TIMEOUT, 1000);
        builder.property(ClientProperties.READ_TIMEOUT, 1000);
        return builder.build();
    }


    @Provides
    @Named("kotak")
    @Singleton
    public Client providesJerseyClientForKotak() {
        ClientBuilder builder = ClientBuilder.newBuilder();
        builder.property(ClientProperties.CONNECT_TIMEOUT, 30000);
        builder.property(ClientProperties.READ_TIMEOUT, 30000);
        return builder.build();
    }

    @Provides
    @Named("cfa")
    @Singleton
    public Client providesCFAClient() {
        ClientBuilder builder = ClientBuilder.newBuilder();
        builder.property(ClientProperties.CONNECT_TIMEOUT, 30000);
        builder.property(ClientProperties.READ_TIMEOUT, 30000);
        return builder.build();
    }

    @Provides
    @Named("razorpay")
    @Singleton
    public Client provideRazorpayClient() {
        ClientBuilder builder = ClientBuilder.newBuilder();
        builder.property(ClientProperties.CONNECT_TIMEOUT, 30000);
        builder.property(ClientProperties.READ_TIMEOUT, 30000);
        return builder.build();
    }

    @Provides
    @Named("kotak_m2p")
    @Singleton
    public Client provideKotakClient() {
        ClientBuilder builder = ClientBuilder.newBuilder();
        builder.property(ClientProperties.CONNECT_TIMEOUT, 30000);
        builder.property(ClientProperties.READ_TIMEOUT, 30000);
        return builder.build();
    }

    @Provides
    @Singleton
    public KotakConfiguration provideKotakConfiguration(){
        return pandoraConfig.getKotakConfiguration();
    }

    @Provides
    @Singleton
    public ExperianConfiguration provideExperianConfiguration(){
        return pandoraConfig.getExperianConfiguration();
    }

    @Provides
    @Singleton
    public MpockketClientConfig provideMpockketClientConfiguration(){
        return pandoraConfig.getMpockketClientConfig();
    }

    @Provides
    @Singleton
    public CreditSaisonConfiguration providesCreditSaisonConfiguration(){
        return pandoraConfig.getCreditSaisonConfiguration();
    }




    /*@Provides
    @Named("")
    @Singleton
    public Client provideJerseyClientForIB() {
        JerseyClientConfiguration clientConfiguration = pandoraConfig.getJerseyClientConfiguration();
        Client jerseyClient = (new JerseyClientBuilder(environment)).using(clientConfiguration).using(environment)
                .build("HTTP_CLIENT");
        jerseyClient.property(ClientProperties.CONNECT_TIMEOUT, 15000);
        jerseyClient.property(ClientProperties.READ_TIMEOUT, 15000);
        return jerseyClient;
    }*/

    @Provides
    @Singleton
    @Named("UbonaClient")
    public Client provideUbonaClient() {
        Client client = ClientBuilder.newClient();
        client.property(ClientProperties.CONNECT_TIMEOUT, 2000);
        client.property(ClientProperties.READ_TIMEOUT, 2000);
        return client;
    }

    @Provides
    @Singleton
    public UpswingConfig provideUpswingConfiguration() {
        return pandoraConfig.getUpswingConfig();
    }

    @Provides
    @Singleton
    public DigioConfiguration provideDigioConfiguration() {
        return pandoraConfig.getDigioConfiguration();
    }

    @Provides
    @Singleton
    public CardGatewayClientConfig provideCardGatewayClientConfig() {
        return pandoraConfig.getCardGatewayClientConfig();
    }

    @Provides
    @Named("upswing")
    @Singleton
    public Client provideJerseyClientForUpswing() {

        UpswingConfig upswingConfig = pandoraConfig.getUpswingConfig();
        ClientBuilder builder = ClientBuilder.newBuilder();
        builder.property(ClientProperties.CONNECT_TIMEOUT, upswingConfig.getUpswingConnectionConfig().getConnectTimeout());
        builder.property(ClientProperties.READ_TIMEOUT, upswingConfig.getUpswingConnectionConfig().getReadTimeout());
        return builder.build();
    }

    @Provides
    @Named("axis")
    @Singleton
    public Client provideJerseyClientForAxis() {
        AxisCbcConfiguration config = pandoraConfig.getAxisCbcConfiguration();
        final SslConfigurator sslConfig = SslConfigurator.newInstance()
            //As per Axis guidelines
            //.trustStoreFile(config.getTrustStorePath())
            //.trustStorePassword(config.getTrustStorePass())
            .keyStoreFile(config.getKeyStorePath())
            .keyPassword(config.getKeyStorePass());
        SSLContext sslContext = sslConfig.createSSLContext();
        ClientBuilder builder = ClientBuilder.newBuilder();
        builder.sslContext(sslContext);
        builder.hostnameVerifier(new AllowAllHostnameVerifier());
        Client secureClient = builder.build();
        //uncomment this for logging request and response
        //        secureClient.register(new LoggingFeature(
        //                java.util.logging.Logger.getLogger(PandoraModule.class.getName()), Level.INFO, null, null));
        secureClient.property(ClientProperties.CONNECT_TIMEOUT, 30000);
        secureClient.property(ClientProperties.READ_TIMEOUT, 30000);
        secureClient.property(ClientProperties.PROXY_URI,config.getProxyUri());
        return secureClient;
    }

    @Provides
    @Singleton
    public SmAxisCbcConfiguration getSmAxisCbcConfiguration() {
        return pandoraConfig.getSmAxisCbcConfiguration();
    }

    @Provides
    @Named(com.flipkart.fintech.pandora.service.client.cbc.axis.Constants.CBC_AXIS_CLIENT)
    @Singleton
    public Client provideJerseyClientForSmAxis() {
        SmAxisCbcConfiguration config = pandoraConfig.getSmAxisCbcConfiguration();
        final SslConfigurator sslConfig = SslConfigurator.newInstance()
                //As per Axis guidelines
                .trustStoreFile(config.getTrustStorePath())
                .trustStorePassword(config.getTrustStorePass())
                .keyStoreFile(config.getKeyStorePath())
                .keyPassword(config.getKeyStorePass());
        SSLContext sslContext = sslConfig.createSSLContext();
        ClientBuilder builder = ClientBuilder.newBuilder();
        builder.sslContext(sslContext);
        builder.hostnameVerifier(new AllowAllHostnameVerifier());
        Client secureClient = builder.build();
        //uncomment this for logging request and response
        //        secureClient.register(new LoggingFeature(
        //                java.util.logging.Logger.getLogger(PandoraModule.class.getName()), Level.INFO, null, null));
        secureClient.property(ClientProperties.CONNECT_TIMEOUT, config.getConnectionTimeout());
        secureClient.property(ClientProperties.READ_TIMEOUT, config.getReadTimeout());
        secureClient.property(ClientProperties.PROXY_URI,config.getProxyUri());
        return secureClient;
    }

    @Provides
    @Singleton
    public GibraltarClientConfig provideGibraltarClientConfig() {
        return pandoraConfig.getGibraltarClientConfig();
    }

    @Provides
    @Singleton
    public PinakaClientConfig providesPinakaClientConfig() {
        return pandoraConfig.getPinakaClientConfig();
    }

    @Provides
    @Singleton
    public UbonaConfiguration providesUbonaConfiguration() {
        return pandoraConfig.getUbonaConfiguration();
    }

    @Provides
    @Singleton
    public CollectionsClientConfiguration providesCollectionClientConfiguration() {
        return pandoraConfig.getCollectionsClientConfiguration();
    }

    @Provides
    @Singleton
    private IBConfiguration getIBConfiguration () {
        return pandoraConfig.getIbConfiguration();
    }

    @Provides
    @Singleton
    private AxisCbcConfiguration getAxisCbcConfiguration () {
        return pandoraConfig.getAxisCbcConfiguration();
    }

    @Provides
    public IBUserOnboardingLenderClientService getIBOnboardingService(ObjectMapper objectMapper, IBConfiguration ibConfiguration) {

        String environment = pandoraConfig.getDeployEnv();
        if (injectMock(environment) || PandoraContext.isTestRequest()) {
            return new IBUserOnboardingLenderClientMockServiceImpl();
        }
        else
            return new IBUserOnboardingLenderClientServiceImpl(objectMapper, ibConfiguration);

    }

    @Provides
    public AxisUserOnboardingLenderClientService getAxisOnboardingService(ObjectMapper objectMapper,
            AxisCbcConfiguration axisCbcConfiguration, @Named("axis") Client client) {

        if(Boolean.TRUE.equals(isMockEnvForAxis(axisCbcConfiguration))){
            return new AxisUserOnboardingLenderClientMockServiceImpl(axisCbcConfiguration);
        }
        return new AxisUserOnboardingLenderClientServiceImpl(objectMapper, axisCbcConfiguration, client);

    }

    @Provides
    @Singleton
    public WinterfellClientConfig providesWinterfellClientConfig() {
        return pandoraConfig.getWinterfellClientConfig();
    }

    @Provides
    public IncomeAssessmentClientService getIncomeClientService(ObjectMapper objectMapper,
                                                                  AxisCbcConfiguration axisCbcConfiguration, @Named("axis") Client client) {

        if(Boolean.TRUE.equals(isMockEnvForAxis(axisCbcConfiguration))){
            return new IncomeAssessmentMockClientServiceImpl(objectMapper, axisCbcConfiguration, client);
        }
        return new IncomeAssessmentClientServiceImpl(objectMapper, axisCbcConfiguration, client);

    }

    private Boolean isMockEnvForAxis(AxisCbcConfiguration axisCbcConfiguration) {
        return StringUtils.isNotEmpty(axisCbcConfiguration.getTestConfigsMap().getMockAxisResponses())
                && axisCbcConfiguration.getTestConfigsMap().getMockAxisResponses().equalsIgnoreCase("Y");
    }


    private boolean injectMock(String environment) {
        return "mock".equalsIgnoreCase(environment);
    }

    @Provides
    @Singleton
    private PandoraHystrixProperties providesPandoraHystrixProperties(){
        return pandoraConfig.getPandoraHystrixProperties();
    }

    @Provides
    @Singleton
    private LenderErrorConfig providesLenderErrorConfig(){
        return pandoraConfig.getLenderErrorConfig();
    }

    @Provides
    public TeleSalesConfiguration providesTeleSaleConfiguration() {
        return pandoraConfig.getTeleSalesConfiguration();
    }

    @Provides
    @Singleton
    private CryptexBundleConfiguration providesCryptexBundleConfiguration(){
        return pandoraConfig.getCryptexBundleConfiguration();
    }

    @Provides
    @Singleton
    private List<DocumentPropertyType> provideDocumentPropertyTypeList(){
        return pandoraConfig.getDocumentPropertyTypeList();
    }
    @Provides
    @Singleton
    private IdfcFfbAuthConfiguration providesIdfcFfbAuthConfiguration() {
        return pandoraConfig.getIdfcFfbAuthConfiguration();
    }

    @Provides
    @Singleton
    public LoginServiceClientConfig provicesLoginServiceClientConfig() {
        return pandoraConfig.getLoginServiceClientConfig();
    }

    @Provides
    @Singleton
    public MultiTenantOAuthClientConfig providesMultiTenantOAuthClientConfig()
    {
        return pandoraConfig.getMultiTenantOAuthClientConfig();
    }

    @Provides
    @Singleton
    public Map<Tenant, String> providesFintechUserServiceTenantMap()
    {
        return pandoraConfig.getFintechUserServiceTenantMap();
    }

    @Provides
    public QuessConfiguration providesQuessCOnfiguration() {
        return pandoraConfig.getQuessConfiguration();
    }

    @Provides
    @Singleton
    public StdTijoriClientConfig providesTijoriClientConfiguration() {
        return pandoraConfig.getTijoriClientConfig();
    }

    @Provides
    public QuessKycSchedulerClientService getQuessKycSchedulerService(ObjectMapper objectMapper,
                                                                      QuessConfiguration quessConfiguration, @Named("axis") Client client) {
        if(quessConfiguration.isMockEnabled()) {
            return new QuessKycSchedulerClientServiceMockImpl(objectMapper, quessConfiguration, client, quessConfiguration);
        }
        return new QuessKycSchedulerClientServiceImpl(objectMapper, quessConfiguration, client);

    }

    @Provides
    @Singleton
    @Named("login_token")
    public LoadingCache<String, String> providesloginTokenCache(QuessConfiguration quessConfiguration) {
        CacheBuilderConfiguration cacheBuilderConfiguration = quessConfiguration.getCacheBuilderConfigurationMap().getOrDefault("login_token", new CacheBuilderConfiguration());
        LoadingCache<String, String> loginTokenCache = CacheBuilder.newBuilder()
                .maximumSize(cacheBuilderConfiguration.getMaximumSize())
                .expireAfterWrite(cacheBuilderConfiguration.getDuration(), TimeUnit.valueOf(cacheBuilderConfiguration.getDurationUnit()))
                .build(new CacheLoader<String, String>() {
                    @Override
                    public String load(String key){
                        return null;
                    }
                });
        return loginTokenCache;
    }

    @Provides
    @Singleton
    public PandoraConfiguration providesPandoraConfiguration()
    {
        return pandoraConfig;
    }


    @Provides
    @Singleton
    public DynamicBucket providesDynamicBucket() throws ConfigServiceException {
        if (dynamicBucket == null) {
            if (pandoraConfig.getCryptexBundleConfiguration().getDynamicBucketConfig().isEnableLocalDynamicBucket()) {
                return new LocalDynamicBucket(pandoraConfig.getCryptexBundleConfiguration().getDynamicBucketConfig().getBucketName());
            }
            ConfigClient configClient = new ConfigClientBuilder().build();
            return configClient.getDynamicBucket(pandoraConfig.getCryptexBundleConfiguration().getDynamicBucketConfig().getBucketName());
        } else {
            return dynamicBucket;
        }
    }

    @Provides
    @Singleton
    @Named("featureFlagBucket")
    public DynamicBucket providesFeatureFlagBucket() throws ConfigServiceException {
        ConfigClient configClient = new ConfigClientBuilder().build();
        return configClient.getDynamicBucket(pandoraConfig.getFeatureFlagBucketName());
    }

    @Provides
    @Singleton
    private AbConfiguration providesAbConfig() {
        return pandoraConfig.getAbConfiguration();
    }

    @Provides
    @Singleton
    private ABService providesABService() throws ABInitializationException, ConfigServiceException {
        DynamicBucket dynamicBucket1 = providesDynamicBucket();
        ConfigHelper configHelper = new ConfigHelper(dynamicBucket1);
        if(configHelper.getBoolean(AbConstants.ENABLE_AB_SERVICE)) {
            if(pandoraConfig.getAbConfiguration().getUnlayeredAbEnabled()) {
                ConfigStore configStore = ABApiConfigStore.initialize(
                    com.flipkart.fintech.pandora.service.utils.Constants.TENANT_FLIPKART,
                    pandoraConfig.getAbConfiguration().getEndpoint(),
                    pandoraConfig.getAbConfiguration().getClientSecretKey()
                );
                return ABService.initialize(configStore);
            }
            else {
                ABApiConfigStore configStore =
                    ABApiConfigStore.initializeForLayers(
                        com.flipkart.fintech.pandora.service.utils.Constants.TENANT_FLIPKART,
                        pandoraConfig.getAbConfiguration().getEndpoint(),
                        Arrays.asList(pandoraConfig.getAbConfiguration().getLayerList().split(",")),
                        pandoraConfig.getAbConfiguration().getClientSecretKey()
                    );
                return ABService.initialize(configStore);
            }
        }
        return ABService.getInstance();
    }

    @Provides
    public EbcClientConfig provideEbcClientConfig() { return pandoraConfig.getEbcClientConfig();}

    @Provides
    @Singleton
    public DatafeedConfiguration providesDatafeedConfiguration() {
        return pandoraConfig.getDatafeedConfiguration();
    }

    @Provides
    @Singleton
    public PhoenixClientConfig providesPhoenixClientConfig() {
        return pandoraConfig.getPhoenixClientConfig();
    }

    @Provides
    @Singleton
    public HystrixModuleConfig hystrixModuleConfig()
    {
        return pandoraConfig.hystrixModuleConfig;
    }

    @Provides
    @Singleton
    public AuthTokenService providesAuthTokenService() {
        return AuthTokenService.getInstance();
    }

    @Provides
    @Singleton
    public CollectionsConfiguration providesIgsConfiguration() {
        return pandoraConfig.getIgsConfig();
    }

    @Provides
    @Singleton
    public ShylockClientConfiguration providesShylockClientConfiguration() {
        return pandoraConfig.getShylockClientConfiguration();
    }

    @Provides
    @Singleton
    @Named("CgClient")
    public Client provideCgClient() {
        Client client = ClientBuilder.newClient();
        client.property(ClientProperties.CONNECT_TIMEOUT, 8000);
        client.property(ClientProperties.READ_TIMEOUT, 8000);
        client.property(HttpUrlConnectorProvider.SET_METHOD_WORKAROUND, true);
        return client;
    }

    @Provides
    @Singleton
    @Inject
    public LenderAdapterFactory providesLenderAdapterFactory(Injector injector) {
        Map<String, ILenderOperationAdapter> lenderOperationAdapterMap = new HashMap<>();
        Reflections reflections = new Reflections(PlConstants.ADAPTER_PACKAGE);
        Set<Class<? extends ILenderOperationAdapter>> plAdapters = reflections.getSubTypesOf(ILenderOperationAdapter.class);
        StringBuilder keyBuilder = new StringBuilder();
        for (Class<? extends ILenderOperationAdapter> adapterClass : plAdapters) {
            LenderConfiguration[] adapterAnnotations = adapterClass.getAnnotationsByType(LenderConfiguration.class);
            for (LenderConfiguration lenderConfiguration : adapterAnnotations) {
                keyBuilder.setLength(0);
                keyBuilder.append(lenderConfiguration.lender().name());
                keyBuilder.append(PlConstants.ADAPTER_FACTORY_DELIMITER);
                keyBuilder.append(lenderConfiguration.operation());
                lenderOperationAdapterMap.put(keyBuilder.toString(), injector.getInstance(adapterClass));
            }
        }

        return new LenderAdapterFactory(lenderOperationAdapterMap);
    }

    @Provides
    @Named(PlClientConstants.PL_LENDER_CLIENT)
    @Singleton
    public Client provideJerseyClientForPlLender() {
        AxisPlConfig config = pandoraConfig.getAxisPlConfig();
        final SslConfigurator sslConfig = SslConfigurator.newInstance()
            .keyStoreFile(config.getKeyStorePath())
            .keyPassword(config.getKeyStorePass())
            .trustStoreFile(config.getTrustStorePath())
            .trustStorePassword(config.getTrustStorePass());
        SSLContext sslContext = sslConfig.createSSLContext();
        ClientBuilder builder = ClientBuilder.newBuilder();
        builder.sslContext(sslContext);
        builder.hostnameVerifier(new AllowAllHostnameVerifier());
        Client secureClient = builder.build();
        secureClient.property(ClientProperties.CONNECT_TIMEOUT, 40000);
        secureClient.property(ClientProperties.READ_TIMEOUT, 40000);
        secureClient.property(ClientProperties.PROXY_URI, config.getProxyUri());
        return secureClient;
    }

    @Provides
    @Singleton
    public CoreLogisticsClientConfig providesCoreLogisticsClientConfig() {
        return pandoraConfig.getCoreLogisticsClientConfig();
    }

    @Provides
    @Singleton
    public KotakClientConfiguration kotakClientConfiguration()
    {
        return pandoraConfig.getKotakClientConfiguration();
    }

    @Provides
    @Singleton
    public SmKotakCbcConfiguration smKotakClientConfiguration() {
        return pandoraConfig.getSmKotakCbcConfiguration();
    }

    @Provides
    @Singleton
    public MockHelper providesMockHelper() throws ConfigServiceException {
        DynamicBucketConfig bucketConfig = pandoraConfig.getIdfcConfiguration()
            .getMockBucketConfig();
        DynamicBucket bucket = getDynamicBucket(bucketConfig);
        return new MockHelper(bucket);
    }

    @Provides
    @Singleton
    public SmUserServiceClientConfig providesSmUserServiceClientConfig() {
        return pandoraConfig.getSmUserServiceClientConfig();
    }

    @Provides
    @Singleton
    public FixeraConfig provideFixeraConfiguration(){
        return pandoraConfig.getFixeraConfig();
    }

    @Provides
    @Singleton
    public GupshupWebhookConfig provideGupshupWebhookConfig(){
        return pandoraConfig.getGupshupWebhookConfig();
    }
    private static DynamicBucket getDynamicBucket(DynamicBucketConfig mockBucketConfig)
        throws ConfigServiceException {
        ConfigClient configClient = new ConfigClientBuilder().build();
        return configClient.getDynamicBucket(mockBucketConfig.getBucketName());
    }

    @Provides
    @Singleton
    public GupshupWebhookClient provideGupshupWebhookClient(@Named("default-case") ObjectMapper objectMapper){
        return new GupshupWebhookClientImpl(pandoraConfig.getCardServiceClientConfig(),objectMapper);
    }

    @Provides
    @Singleton
    public CardServiceClientConfig provideCardServiceClientConfiguration(){
        return pandoraConfig.getCardServiceClientConfig();
    }

    @Provides
    @Singleton
    public CbcWebhookClient provideCbcWebhookClient(ObjectMapper objectMapper){
        return new CbcWebhookClientImpl(pandoraConfig.getCardServiceClientConfig(),objectMapper);
    }

    @Provides
    @Singleton
    public LoanDisbursedDataPublisherConfig providesLoanDisbursedDataPublisherConfig(){
        return pandoraConfig.getLoanDisbursedDataPublisherConfig();
    }

}

