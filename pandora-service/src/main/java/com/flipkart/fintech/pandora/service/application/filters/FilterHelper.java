package com.flipkart.fintech.pandora.service.application.filters;

import com.flipkart.fintech.pandora.api.model.enums.lenderaction.ApiResponseCode;
import com.flipkart.fintech.pandora.api.model.response.lenderaction.LenderActionResponse;
import io.dropwizard.jersey.errors.ErrorMessage;

/**
 * <AUTHOR>
 * @since 06/01/22.
 */
public class FilterHelper {
    public static Object getResponseEntity(int statusCode, String message, String methodName) {
        if (methodName.equals("updateLoanAccount")) {
            return LenderActionResponse.builder()
                    .errorDescription(message)
                    .responseCode(ApiResponseCode.valueOf(statusCode)).build();
        } else {
            return new ErrorMessage(statusCode, message);
        }
    }
}
