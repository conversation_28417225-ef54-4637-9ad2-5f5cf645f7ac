package com.flipkart.fintech.pandora.service.application.filters;

import com.flipkart.fintech.pandora.service.resources.web.*;
import com.flipkart.fintech.pandora.service.resources.web.smefin.PayoutResource;
import com.flipkart.fintech.pandora.service.resources.web.v2.DirectPaybackResource;
import com.flipkart.kloud.config.DynamicBucket;

import javax.ws.rs.container.DynamicFeature;
import javax.ws.rs.container.ResourceInfo;
import javax.ws.rs.core.FeatureContext;

/**
 * Created by aniruddha.sharma on 09/03/18.
 */
public class FilterRegistration implements DynamicFeature {

    private final DynamicBucket dynamicBucket;

    public FilterRegistration(DynamicBucket dynamicBucket) {
        this.dynamicBucket = dynamicBucket;
    }

    @Override
    public void configure(ResourceInfo resourceInfo, FeatureContext featureContext) {

        featureContext.register(IpFilter.class);
        featureContext.register(RequestFilter.class);
        featureContext.register(SmUserIdFilter.class);
        if((PaybackResource.class.equals(resourceInfo.getResourceClass()) &&
                resourceInfo.getResourceMethod().getName().equals("updateSummary")) ) {
            featureContext.register(ElbFilter.class);
            featureContext.register(ClientIdFilter.class);
        }
        else if (!PaybackResource.class.equals(resourceInfo.getResourceClass()) &&
                !TransactionResource.class.equals(resourceInfo.getResourceClass()) &&
                !CgResource.class.equals(resourceInfo.getResourceClass())) {
            if(DirectPaybackResource.class.equals(resourceInfo.getResourceClass()) && resourceInfo.getResourceMethod().getName().equals("directPayback")) {
                featureContext.register(new ClientIPFilter(dynamicBucket, resourceInfo.getResourceMethod().getName()));
                featureContext.register(new JwtValidityFilter(dynamicBucket, resourceInfo.getResourceMethod().getName()));
            }

            else if (QuessResource.class.equals(resourceInfo.getResourceClass()) &&
                    (resourceInfo.getResourceMethod().getName().equals("callback"))) {
                featureContext.register(ClientIdFilter.class);
            }

            else if (IncomeAssessmentResource.class.equals(resourceInfo.getResourceClass()) &&
                    (resourceInfo.getResourceMethod().getName().equals("updateStatus"))) {
                featureContext.register(ClientIdFilter.class);
            }

            else if((EbcResource.class.equals(resourceInfo.getResourceClass()) && resourceInfo.getResourceMethod().getName().equals("updateEbcApplicationStatus"))
                    || (LenderActionResource.class.equals(resourceInfo.getResourceClass()) && resourceInfo.getResourceMethod().getName().equals("updateLoanAccount") )) {
                featureContext.register(new ClientIPFilter(dynamicBucket, resourceInfo.getResourceMethod().getName()));
                featureContext.register(new JwtValidityFilter(dynamicBucket, resourceInfo.getResourceMethod().getName()));
            }

            else if((PayoutResource.class.equals(resourceInfo.getResourceClass()) && resourceInfo.getResourceMethod().getName().equals("getSellerPayoutData"))) {
                featureContext.register(new ClientIPFilter(dynamicBucket, resourceInfo.getResourceMethod().getName()));
                featureContext.register(new JwtValidityFilter(dynamicBucket, resourceInfo.getResourceMethod().getName()));
            }

            else if(resourceInfo.getResourceClass().getSimpleName().contains(DebuggingResource.class.getSimpleName())){
                featureContext.register(new JwtValidityFilter(dynamicBucket, resourceInfo.getResourceMethod().getName()));
            }

            else if((IgsResource.class.equals(resourceInfo.getResourceClass()) && resourceInfo.getResourceMethod().getName().equals("processIgsDispositionChange"))) {
    //            featureContext.register(new ClientIPFilter(dynamicBucket, resourceInfo.getResourceMethod().getName()));
                featureContext.register(new JwtValidityFilter(dynamicBucket, resourceInfo.getResourceMethod().getName()));
            }
            else if (resourceInfo.getResourceClass().getSimpleName().contains(DirectPaybackResource.class.getSimpleName())) {
                featureContext.register(new JwtValidityFilter(dynamicBucket, resourceInfo.getResourceMethod().getName()));
            }
            else if((CgResource.class.equals(resourceInfo.getResourceClass()) && resourceInfo.getResourceMethod().getName().equals("processCgCaasDispositionChange"))
                     || CgResource.class.equals(resourceInfo.getResourceClass()) && resourceInfo.getResourceMethod().getName().equals("processCgSaasDispositionChange")) {
                featureContext.register(new JwtValidityFilter(dynamicBucket, resourceInfo.getResourceMethod().getName()));
            }
        }
    }
}
