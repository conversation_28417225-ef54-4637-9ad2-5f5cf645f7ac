package com.flipkart.fintech.pandora.service.core.UserOnboarding;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.glassfish.jersey.client.ClientProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.ws.rs.ClientErrorException;
import javax.ws.rs.WebApplicationException;
import javax.ws.rs.client.Entity;
import javax.ws.rs.client.Invocation;
import javax.ws.rs.client.WebTarget;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.MultivaluedMap;
import javax.ws.rs.core.Response;
import java.util.HashMap;
import java.util.Objects;

public class AxisTestClient {

    private static final Logger logger = LoggerFactory.getLogger(AxisTestClient.class);

    private final WebTarget webTarget;

    public AxisTestClient(WebTarget webTarget) {
        this.webTarget = webTarget;
    }

    public HashMap<String, HashMap<String,Object>> getPostResponse(String path, Entity requestEntity,
            MultivaluedMap<String, Object> headers) throws ClientErrorException {
        Invocation.Builder invocationBuilder = webTarget.path(path).request(MediaType.APPLICATION_JSON);
        invocationBuilder.headers(headers);
        invocationBuilder.property(ClientProperties.CONNECT_TIMEOUT, 60000);
        invocationBuilder.property(ClientProperties.READ_TIMEOUT, 60000);
        Response response = null;
        String responseEntity = null;
        try {
            response = invocationBuilder.post(requestEntity);
//           return response.readEntity(new GenericType<HashMap<String, HashMap<String,Object>>>() {});
            responseEntity = response.readEntity(String.class);
            logger.info("Response Entity {}",responseEntity);
            if (response.getStatus() == Response.Status.OK.getStatusCode()) {
                return new ObjectMapper().readValue(responseEntity, HashMap.class);
            }else{
                throw new WebApplicationException(responseEntity);
            }


        }
        catch (Exception e) {
            logger.error(String.format("Error in POST %s",e));
            throw new RuntimeException(e);
        }
        finally {
            if (Objects.nonNull(response)) {
                response.close();
            }

        }

    }
}