package com.flipkart.fintech.pandora.service.core.UserOnboarding;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.flipkart.fintech.pandora.service.external.dataModels.axisUserOnboarding.request.encrypted.*;
import com.flipkart.fintech.pandora.service.external.dataModels.axisUserOnboarding.response.BaseHttpResponse;
import com.flipkart.fintech.pandora.service.external.dataModels.axisUserOnboarding.response.encrypted.*;

public interface AxisUserOnboardingLenderClientService {

    CheckCohortEligibilityResponse checkCohortEligibility(CheckCohortEligibilityRequest checkCohortEligibilityRequest);

    DemoConsentOTPGenerationResponse demoCardConsentOtp(
            DemoConsentOTPGenerationRequest demoConsentOTPGenerationRequest, String validationToken, String axisConfigKey) throws JsonProcessingException;

    FetchCustDemographicsResponse fetchCustDemographics(FetchCustDemographicsRequest fetchCustDemographicsRequest, String validationToken, String axisConfigKey) throws JsonProcessingException;

    ApplyCardConsentOTPGenResponse applyCardConsentOtp(
            ApplyCardConsentOTPGenRequest applyCardConsentOTPGenRequest, String validationToken, String axisConfigKey)
            throws JsonProcessingException;

    ProcessNewCardCaseResponse processNewCardCase(
            ProcessNewCardCaseRequest processNewCardCaseRequest, String validationToken, String axisConfigKey)
            throws JsonProcessingException;

    CheckCaseStatusResponse checkCaseStatusMobile(
            CheckCaseStatusRequest checkCaseStatusMobileRequest, String validationToken, String axisConfigKey)
            throws JsonProcessingException;

    CheckCardUpgradeStatusResponse checkCardUpgradeStatus(
            CheckCardUpgradeStatusRequest statusRequest, String validationToken) throws JsonProcessingException;

    ProcessUpgradeCardResponse processUpgradeCard(
            ProcessUpgradeCardRequest processUpgradeCardRequest, String validationToken, String axisConfigKey)
            throws JsonProcessingException;

    CheckCaseStatusResponse checkCaseStatus(
            CheckCaseStatusRequest checkCaseStatusRequest, String validationToken, String axisConfigKey)
            throws JsonProcessingException;

    KYCSchedulerResponse kycSchedule(KYCSchedulerRequest kycSchedulerRequest);
    
    VKYCResponse handshakeVideoKyc(VKYCRequest handshakeVideoKycRequest);

    <T extends BaseHttpResponse> T setHttpResponseCode(T childClass, String response, String status);
}