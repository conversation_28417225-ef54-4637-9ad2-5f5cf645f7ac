package com.flipkart.fintech.pandora.service.core.UserOnboarding;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.pandora.api.model.EligibilityInfo;
import com.flipkart.fintech.pandora.api.model.LenderInfoSection;
import com.flipkart.fintech.pandora.api.model.Tenure;
import com.flipkart.fintech.pandora.api.model.CardCaptureValidationRequest;
import com.flipkart.fintech.pandora.api.model.common.EmploymentStatus;
import com.flipkart.fintech.pandora.api.model.common.STATUS;
import com.flipkart.fintech.pandora.api.model.common.Timeline;
import com.flipkart.fintech.pandora.api.model.request.digitalkyc.DigitalKycSendDocRequest;
import com.flipkart.fintech.pandora.api.model.request.digitalkyc.KycDocCategory;
import com.flipkart.fintech.pandora.api.model.request.mandate.MandateConfirmationRequest;
import com.flipkart.fintech.pandora.api.model.request.mandate.MandateStatus;
import com.flipkart.fintech.pandora.api.model.request.mandate.MandateType;
import com.flipkart.fintech.pandora.api.model.request.onboarding.v2.AltDataInfoRequest;
import com.flipkart.fintech.pandora.api.model.request.onboarding.v2.ApplyNowRequest;
import com.flipkart.fintech.pandora.api.model.request.onboarding.v2.CreditAssessmentInfoRequest;
import com.flipkart.fintech.pandora.api.model.request.onboarding.v2.PanNoRequest;
import com.flipkart.fintech.pandora.api.model.request.onboarding.v2.UserAcceptanceRequest;
import com.flipkart.fintech.pandora.api.model.response.BaseResponse;
import com.flipkart.fintech.pandora.api.model.response.CardCaptureValidationResponse;
import com.flipkart.fintech.pandora.api.model.response.mandate.MandateConfirmationResponse;
import com.flipkart.fintech.pandora.api.model.response.onboarding.v2.ApplyNowResponse;
import com.flipkart.fintech.pandora.api.model.response.onboarding.v2.CreditAssessmentInfoResponse;
import com.flipkart.fintech.pandora.api.model.response.onboarding.v2.FetchCardDetailsResponse;
import com.flipkart.fintech.pandora.api.model.response.onboarding.v2.PanNoResponse;
import com.flipkart.fintech.pandora.api.model.response.onboarding.v2.UserAcceptanceResponse;
import com.flipkart.fintech.pandora.service.external.IBConfiguration;
import com.flipkart.fintech.pandora.service.external.dataModels.AddressProof;
import com.flipkart.fintech.pandora.service.external.dataModels.IBUserOnboardingRequest.IBCardCaptureValidationRequest;
import com.flipkart.fintech.pandora.service.external.dataModels.IBUserOnboardingRequest.IBCardMandateRequest;
import com.flipkart.fintech.pandora.service.external.dataModels.IBUserOnboardingRequest.IBCardPutDocumentsRequest;
import com.flipkart.fintech.pandora.service.external.dataModels.IBUserOnboardingRequest.IBCreditAssessmentRequest;
import com.flipkart.fintech.pandora.service.external.dataModels.IBUserOnboardingRequest.IBPanNameServiceRequest;
import com.flipkart.fintech.pandora.service.external.dataModels.IBUserOnboardingRequest.IBUserSignupRequest;
import com.flipkart.fintech.pandora.service.external.dataModels.IBUserOnboardingResponse.IBCardCaptureValidationResponse;
import com.flipkart.fintech.pandora.service.external.dataModels.IBUserOnboardingResponse.IBCardDetailsResponse;
import com.flipkart.fintech.pandora.service.external.dataModels.IBUserOnboardingResponse.IBCardMandateResponse;
import com.flipkart.fintech.pandora.service.external.dataModels.IBUserOnboardingResponse.IBCreditAssessmentResponse;
import com.flipkart.fintech.pandora.service.external.dataModels.IBUserOnboardingResponse.IBPanNameServiceResponse;
import com.flipkart.fintech.pandora.service.external.dataModels.IBUserOnboardingResponse.IBUserSignupResponse;
import com.flipkart.fintech.pandora.service.external.dataModels.IdentityProof;
import com.flipkart.fintech.pandora.service.external.dataModels.ProofDocumentName;
import com.flipkart.fintech.pandora.service.utils.Constants;
import com.flipkart.fintech.pandora.service.utils.ErrorCodeMapping;
import com.flipkart.fintech.pinaka.api.enums.Lender;
import com.google.inject.Inject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigInteger;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Base64;
import java.util.Calendar;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

public class IBAdapter {

    private final IBConfiguration ibConfiguration;

    private final ObjectMapper objectMapper;

    private final ErrorCodeMapping errorCodeMapping;

    @Inject
    public IBAdapter(IBConfiguration ibConfiguration, ObjectMapper objectMapper, ErrorCodeMapping errorCodeMapping) {
        this.ibConfiguration = ibConfiguration;
        this.objectMapper = objectMapper;
        this.errorCodeMapping = errorCodeMapping;
    }

    public IBUserSignupRequest applyNowRequestToIBUserSignupRequest(ApplyNowRequest applyNowRequest) {
        IBUserSignupRequest ibUserSignupRequest = new IBUserSignupRequest();
        if (Objects.nonNull(applyNowRequest)) {
            ibUserSignupRequest.setMobileNumber(applyNowRequest.getLeadInformation().getMobileNo());
            ibUserSignupRequest.setSource(ibConfiguration.getSource());

            if (Objects.nonNull(applyNowRequest.getFingerprintInfo())) {
                ibUserSignupRequest.setDeviceId(applyNowRequest.getFingerprintInfo().getDeviceId());
            }
        }
        return ibUserSignupRequest;
    }

    public IBCardCaptureValidationRequest getIbCardCaptureValidationRequest(CardCaptureValidationRequest cardCaptureValidationRequest) {
        IBCardCaptureValidationRequest ibCardCaptureValidationRequest = new IBCardCaptureValidationRequest();
        if (Objects.nonNull(cardCaptureValidationRequest)) {
            ibCardCaptureValidationRequest.setCardId(cardCaptureValidationRequest.getLenderReferenceId());
            ibCardCaptureValidationRequest.setAdditionalField1(cardCaptureValidationRequest.getCreditStatus());
            ibCardCaptureValidationRequest.setAdditionalField2(cardCaptureValidationRequest.getCreditLimit());
            ibCardCaptureValidationRequest.setAdditionalField3(cardCaptureValidationRequest.getNoNachFlag());
            ibCardCaptureValidationRequest.setRequestName(cardCaptureValidationRequest.getRequestName());
        }
        return ibCardCaptureValidationRequest;
    }

    public ApplyNowResponse ibUserSignupResponseToApplyNowResponse(IBUserSignupResponse ibUserSignupResponse) {
        ApplyNowResponse applyNowResponse = new ApplyNowResponse();
        LenderInfoSection lenderInfoSection = new LenderInfoSection();
        applyNowResponse.setLenderInfoSection(lenderInfoSection);
        if (Objects.nonNull(ibUserSignupResponse)) {
            applyNowResponse.getLenderInfoSection().setLenderReferenceInfos(objectMapper.convertValue(ibUserSignupResponse, Map.class));
            applyNowResponse.getLenderInfoSection().setLenderLeadReferenceNo(ibUserSignupResponse.getIbLoanDetails().getLeadId());
        }
        return applyNowResponse;
    }

    public IBPanNameServiceRequest panNoRequestToIBPanNameServieRequest(PanNoRequest panNoRequest/*, String firstName, String lastName*/) {
        IBPanNameServiceRequest ibPanNameServiceRequest = new IBPanNameServiceRequest();

        if (Objects.nonNull(panNoRequest)) {
            ibPanNameServiceRequest.setPanNumber(panNoRequest.getPanNumber());
            if (Objects.nonNull(panNoRequest.getLenderInfoSection())) {
                ibPanNameServiceRequest.setLeadId(panNoRequest.getLenderInfoSection().getLenderLeadReferenceNo());
            } else ibPanNameServiceRequest.setLeadId("");
            ibPanNameServiceRequest.setFirstName("");
            ibPanNameServiceRequest.setLastName("");
        }

        return ibPanNameServiceRequest;
    }

    public PanNoResponse ibPanNameServiceResponseToPanNoResponse (IBPanNameServiceResponse ibPanNameServiceResponse) {
        PanNoResponse panNoResponse = new PanNoResponse();

        if (Objects.nonNull(ibPanNameServiceResponse)) {
            panNoResponse.setPanFirstName(ibPanNameServiceResponse.getPanFirstName());
            panNoResponse.setPanMiddleName(ibPanNameServiceResponse.getPanMiddleName());
            panNoResponse.setPanLastName(ibPanNameServiceResponse.getPanLastName());
            panNoResponse.setPanFullName(ibPanNameServiceResponse.getPanFullName());
            //panNoResponse.setPanNumber();
            BaseResponse baseResponse = new BaseResponse();
            if (StringUtils.isNotBlank(ibPanNameServiceResponse.getErrorMessage())) {
                baseResponse.setInfo(ibPanNameServiceResponse.getErrorMessage());
            }

            // Not a valid PAN
            if( StringUtils.isNotBlank(ibPanNameServiceResponse.getErrorMessage())  || StringUtils.isNotBlank(ibPanNameServiceResponse.getPanStatus())){

                String errorCode = ibPanNameServiceResponse.getErrorMessage();

                if(StringUtils.isNotBlank(ibPanNameServiceResponse.getPanStatus())){
                    errorCode = ibPanNameServiceResponse.getPanStatus();
                }
                baseResponse.setStatus(errorCodeMapping.getStatusForLenderAndError(Lender.INDIA_BULLS, errorCode));
            } else {
                baseResponse.setStatus(STATUS.VALID_PAN);
            }


            panNoResponse.setBaseResponse(baseResponse);

            switch (panNoResponse.getBaseResponse().getStatus()){
                case INVALID_PAN:
                    panNoResponse.setStatus(PanNoResponse.PanStatus.INVALID);
                    break;
                case VALID_PAN:
                    panNoResponse.setStatus(PanNoResponse.PanStatus.VALID);
                    break;
                case FAILED_PAN_RESPONSE:
                case FAILURE:
                    panNoResponse.setStatus(PanNoResponse.PanStatus.FAILED);
                    break;
            }
        }

        return panNoResponse;
    }

    public IBCreditAssessmentRequest creditAssessmentInfoRequestToIBCreditAssessmentRequest (CreditAssessmentInfoRequest creditAssessmentInfoRequest) {
        IBCreditAssessmentRequest ibCreditAssessmentRequest = new IBCreditAssessmentRequest();

        ibCreditAssessmentRequest = getIBCreditAssessmentRequest(creditAssessmentInfoRequest);

        return ibCreditAssessmentRequest;
    }

    private IBCreditAssessmentRequest getIBCreditAssessmentRequest(CreditAssessmentInfoRequest creditAssessmentInfoRequest) {
        IBCreditAssessmentRequest ibCreditAssessmentRequest = new IBCreditAssessmentRequest();

        ibCreditAssessmentRequest.setCardId(creditAssessmentInfoRequest.getLenderInfoSection().getLenderLeadReferenceNo());

        switch (creditAssessmentInfoRequest.getPersonalInfoRequest().getGender()) {
            case F:
            case T:
                ibCreditAssessmentRequest.setSalutation("Miss");
                break;
            case M:
            default:
                ibCreditAssessmentRequest.setSalutation("Mr.");
                break;
        }

        ibCreditAssessmentRequest.setFirstName(creditAssessmentInfoRequest.getPersonalInfoRequest().getFirstName());
        ibCreditAssessmentRequest.setLastName(creditAssessmentInfoRequest.getPersonalInfoRequest().getLastName());
        ibCreditAssessmentRequest.setMiddleName(creditAssessmentInfoRequest.getPersonalInfoRequest().getMiddleName());

        String name = "";

        if (Objects.nonNull(creditAssessmentInfoRequest.getPersonalInfoRequest().getFirstName())) {
            name += creditAssessmentInfoRequest.getPersonalInfoRequest().getFirstName()+" ";
        }
        if (Objects.nonNull(creditAssessmentInfoRequest.getPersonalInfoRequest().getMiddleName())) {
            name += creditAssessmentInfoRequest.getPersonalInfoRequest().getMiddleName()+" ";
        }
        if (Objects.nonNull(creditAssessmentInfoRequest.getPersonalInfoRequest().getLastName())) {
            name += creditAssessmentInfoRequest.getPersonalInfoRequest().getLastName();
        }

        ibCreditAssessmentRequest.setAadhaarName(name);
        ibCreditAssessmentRequest.setAadhaarNo(creditAssessmentInfoRequest.getPersonalInfoRequest().getMobileNumber()); //as told by IB



        final DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate localDate = LocalDate.parse(creditAssessmentInfoRequest.getPersonalInfoRequest().getDate(), dtf);

        //DateTimeFormatter formatters = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        ibCreditAssessmentRequest.setDob(creditAssessmentInfoRequest.getPersonalInfoRequest().getDate());
        ibCreditAssessmentRequest.setGender(creditAssessmentInfoRequest.getPersonalInfoRequest().getGender().name());
        ibCreditAssessmentRequest.setEmailId(creditAssessmentInfoRequest.getPersonalInfoRequest().getEmail());

        if (creditAssessmentInfoRequest.getIncomeDetails().getTimeline().equals(Timeline.YEARLY)) {
            ibCreditAssessmentRequest.setMonthlyIncome(creditAssessmentInfoRequest.getIncomeDetails().getIncome().divide(BigInteger.valueOf(12)).toString());
        } else {
            ibCreditAssessmentRequest.setMonthlyIncome(creditAssessmentInfoRequest.getIncomeDetails().getIncome().toString());
        }

        if (Objects.nonNull(creditAssessmentInfoRequest.getEmploymentDetails().getEmploymentYears())) {
            ibCreditAssessmentRequest.setCurrentEnploymentInMonth(creditAssessmentInfoRequest.getEmploymentDetails().getEmploymentYears().toString());
        } else {
            int age = Calendar.getInstance().get(Calendar.YEAR) - localDate.getYear();
            int workingYears = (age-22) > 0 ? age-22:0;
            ibCreditAssessmentRequest.setCurrentEnploymentInMonth(String.valueOf(workingYears * 12));
        }

        ibCreditAssessmentRequest.setPanNumber(creditAssessmentInfoRequest.getPanNumber());
        ibCreditAssessmentRequest.setCurrentAddressSameAsAadhar("true");

        if (Objects.nonNull(creditAssessmentInfoRequest.getPersonalInfoRequest().getAddresses()) && CollectionUtils.isNotEmpty(creditAssessmentInfoRequest.getPersonalInfoRequest().getAddresses())) {
        	
        	  // Appending both address line in one as requested by India Bulls dated 29-04-2019
            ibCreditAssessmentRequest.setAadhaarAddress1(creditAssessmentInfoRequest.getPersonalInfoRequest().getAddresses().get(0).getAddressLine1()+" "+creditAssessmentInfoRequest.getPersonalInfoRequest().getAddresses().get(0).getAddressLine2());
            ibCreditAssessmentRequest.setAadhaarAddress2("");
            ibCreditAssessmentRequest.setAadhaarAddress3("");
            ibCreditAssessmentRequest.setAadhaarCity(creditAssessmentInfoRequest.getPersonalInfoRequest().getAddresses().get(0).getCity());
            ibCreditAssessmentRequest.setAadhaarState(creditAssessmentInfoRequest.getPersonalInfoRequest().getAddresses().get(0).getState());
            ibCreditAssessmentRequest.setAadhaarPincode(creditAssessmentInfoRequest.getPersonalInfoRequest().getAddresses().get(0).getPinCode());

            ibCreditAssessmentRequest.setCurrentAddress1(creditAssessmentInfoRequest.getPersonalInfoRequest().getAddresses().get(0).getAddressLine1()+" "+creditAssessmentInfoRequest.getPersonalInfoRequest().getAddresses().get(0).getAddressLine2());
            ibCreditAssessmentRequest.setCurrentAddress2("");
            ibCreditAssessmentRequest.setCurrentAddress3("");
            ibCreditAssessmentRequest.setCurrentCity(creditAssessmentInfoRequest.getPersonalInfoRequest().getAddresses().get(0).getCity());
            ibCreditAssessmentRequest.setCurrentState(creditAssessmentInfoRequest.getPersonalInfoRequest().getAddresses().get(0).getState());
            ibCreditAssessmentRequest.setCurrentPincode(creditAssessmentInfoRequest.getPersonalInfoRequest().getAddresses().get(0).getPinCode());
        }

        ibCreditAssessmentRequest.setMotherName("Not Available");
        ibCreditAssessmentRequest.setFatherName("Not Available");

        if (Objects.nonNull(creditAssessmentInfoRequest.getPersonalInfoRequest().getMaritalStatus())) {
            ibCreditAssessmentRequest.setMaritalStatus(creditAssessmentInfoRequest.getPersonalInfoRequest().getMaritalStatus().name());
        } else {
            ibCreditAssessmentRequest.setMaritalStatus("SINGLE");
        }

        ibCreditAssessmentRequest.setSpouseName("");
        ibCreditAssessmentRequest.setPanValidationStatus(true);
        ibCreditAssessmentRequest.setResidentialStatus("Indian");
        ibCreditAssessmentRequest.setSource(creditAssessmentInfoRequest.getSource());
        ibCreditAssessmentRequest.setAadhaarUid("");
        ibCreditAssessmentRequest.setCvNameMatch("Yes");
        ibCreditAssessmentRequest.setAadhaarAvailable("Yes");
        ibCreditAssessmentRequest.setNameInPan("");
        ibCreditAssessmentRequest.setFiller1("");
        ibCreditAssessmentRequest.setFiller2("");
        ibCreditAssessmentRequest.setFiller3("");
        ibCreditAssessmentRequest.setFiller4("");
        ibCreditAssessmentRequest.setFiller5("");
        if(Objects.nonNull(creditAssessmentInfoRequest.getSmsScore())){
            ibCreditAssessmentRequest.setSmsBand("A"); //hardcoded for nww
            ibCreditAssessmentRequest.setSmsScore(creditAssessmentInfoRequest.getSmsScore());
        }else{
            ibCreditAssessmentRequest.setSmsBand("Z");
            ibCreditAssessmentRequest.setSmsScore("-99999");
        }

        if (Objects.nonNull(creditAssessmentInfoRequest.getEmploymentDetails().getEmploymentStatus())) {
            ibCreditAssessmentRequest.setOccupation(creditAssessmentInfoRequest.getEmploymentDetails().getEmploymentStatus().getValue());
            switch (creditAssessmentInfoRequest.getEmploymentDetails().getEmploymentStatus()) {
                case SALARIED:
                    ibCreditAssessmentRequest.setSubOccupation("Private Sector");
                    ibCreditAssessmentRequest.setSalaryMode("Direct Transfer to Bank Account");
                    break;
                case NON_SALARIED:
                    ibCreditAssessmentRequest.setSubOccupation("Self-employed");
                    ibCreditAssessmentRequest.setSalaryMode("");
                    break;
                case SELF_EMPLOYED:
                    ibCreditAssessmentRequest.setSubOccupation("Self-employed");
                    ibCreditAssessmentRequest.setSalaryMode("");
                    break;
                case OTHER:
                    ibCreditAssessmentRequest.setOccupation(EmploymentStatus.NON_SALARIED.getValue());
                    ibCreditAssessmentRequest.setSubOccupation("Student");
                    ibCreditAssessmentRequest.setSalaryMode("");
                    break;
            }
        }

        ibCreditAssessmentRequest.setGeoLocation("");
        ibCreditAssessmentRequest.setGeoLocationCheck("");

        return ibCreditAssessmentRequest;
    }

    public CreditAssessmentInfoResponse ibCreditAssessmentResponseToCreditAssessmentInfoResponse (IBCreditAssessmentResponse ibCreditAssessmentResponse) {
        CreditAssessmentInfoResponse creditAssessmentInfoResponse = new CreditAssessmentInfoResponse();
        Tenure tenure = new Tenure();
        EligibilityInfo eligibilityInfo = new EligibilityInfo();

        if (Objects.nonNull(ibCreditAssessmentResponse) && Boolean.TRUE.equals(ibCreditAssessmentResponse.getSuccess())) {

            eligibilityInfo.setCreditLimit(new BigInteger(ibCreditAssessmentResponse.getMaxEligibleLoanAmount().toString()));
            creditAssessmentInfoResponse.setEligibilityInfo(eligibilityInfo);
            creditAssessmentInfoResponse.setTenure(tenure);
            if (ibCreditAssessmentResponse.getHardApprovalDecision().equalsIgnoreCase("A")) {
                creditAssessmentInfoResponse.setApprovalDecision(Constants.DECISION_APPROVED);
            }
//            else if softapproval is A then hit the api with alt data(sms score)
            else if (ibCreditAssessmentResponse.getSoftApprovalDecision().equalsIgnoreCase("A")) {
                creditAssessmentInfoResponse.setApprovalDecision(Constants.DECISION_SOFT_APPROVED);
            }
            else if (ibCreditAssessmentResponse.getHardApprovalDecision().equalsIgnoreCase("D")) {
                creditAssessmentInfoResponse.setApprovalDecision(Constants.DECISION_DECLINED);
            }
        }
        else {
            creditAssessmentInfoResponse.setApprovalDecision(Constants.DECISION_DECLINED);
        }

        /**
         * disaaling autopay when not mandatory
         */
        if(Objects.nonNull(ibCreditAssessmentResponse) && Constants.Y.equals(ibCreditAssessmentResponse.getNoNachFlag())){
            creditAssessmentInfoResponse.setAutopaySetupMandatory(Boolean.FALSE);
            creditAssessmentInfoResponse.setAutopaySetupApplicable(Boolean.FALSE);
        }else{
            creditAssessmentInfoResponse.setAutopaySetupMandatory(Boolean.TRUE);
            creditAssessmentInfoResponse.setAutopaySetupApplicable(Boolean.TRUE);
        }
        creditAssessmentInfoResponse.setCibilScore(-1); //ib won't be sharing the data
        return creditAssessmentInfoResponse;
    }

    public IBCreditAssessmentRequest altDataInfoRequestToIBCreditAssessmentRequest (AltDataInfoRequest altDataInfoRequest) {
        IBCreditAssessmentRequest ibCreditAssessmentRequest = new IBCreditAssessmentRequest();

        if (Objects.nonNull(altDataInfoRequest)) {
            ibCreditAssessmentRequest = getIBCreditAssessmentRequest(altDataInfoRequest);
            ibCreditAssessmentRequest.setSmsScore(altDataInfoRequest.getSmsScore());
        }
        return ibCreditAssessmentRequest;
    }

    public IBCardCaptureValidationRequest getIBCardCaptureValidationRequest(String lenderUserReferenceNumber, String requestName) {
        IBCardCaptureValidationRequest ibCardCaptureValidationRequest = new IBCardCaptureValidationRequest();

        ibCardCaptureValidationRequest.setCardId(lenderUserReferenceNumber);
        ibCardCaptureValidationRequest.setRequestName(requestName);
        ibCardCaptureValidationRequest.setAdditionalField1("true");
        ibCardCaptureValidationRequest.setAdditionalField2("");

        SimpleDateFormat sdfDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");//dd/MM/yyyy
        Date now = new Date();
        String strDate = sdfDate.format(now);
        ibCardCaptureValidationRequest.setAdditionalField3(strDate);

        return ibCardCaptureValidationRequest;
    }

    public UserAcceptanceResponse ibCardCaptureValidationResponseToUserAcceptanceResponse (IBCardCaptureValidationResponse ibCardCaptureValidationResponse) {
        UserAcceptanceResponse userAcceptanceResponse = new UserAcceptanceResponse();
        userAcceptanceResponse.setSuccess(ibCardCaptureValidationResponse.getSuccess());
        userAcceptanceResponse.setErrorMessage(ibCardCaptureValidationResponse.getErrorMessage());
        return userAcceptanceResponse;
    }

    public CardCaptureValidationResponse getCardCaptureValidationResponse(IBCardCaptureValidationResponse ibCardCaptureValidationResponse) {
        CardCaptureValidationResponse cardCaptureValidationResponse = new CardCaptureValidationResponse();
        cardCaptureValidationResponse.setSuccess(ibCardCaptureValidationResponse.getSuccess());
        cardCaptureValidationResponse.setErrorMessage(ibCardCaptureValidationResponse.getErrorMessage());
        return cardCaptureValidationResponse;
    }

    public IBCardPutDocumentsRequest ibCardPutDocumentsRequestFromDigitalKycSendDocRequest (String lenderUserReferenceNumber, InputStream fis, DigitalKycSendDocRequest digitalKycSendDocRequest) throws IOException {
        IBCardPutDocumentsRequest ibCardPutDocumentsRequest = new IBCardPutDocumentsRequest();

        byte[] bytesArray = IOUtils.toByteArray(fis);

        String base64Doc = Base64.getEncoder().encodeToString(bytesArray);

        /*BASE64Decoder decoder = new BASE64Decoder();
        byte[] decodedBytes = decoder.decodeBuffer(s);*/

        ibCardPutDocumentsRequest.setData(base64Doc);
        ibCardPutDocumentsRequest.setCardId(lenderUserReferenceNumber);

        switch (digitalKycSendDocRequest.getKycDocCategory()) {
            case POA:
                ibCardPutDocumentsRequest.setDocumentName(ProofDocumentName.PROOF_OF_ADDRESS.getProofDocument());
                break;
            case POI:
                ibCardPutDocumentsRequest.setDocumentName(ProofDocumentName.PROOF_OF_IDENTITY.getProofDocument());
                break;
            case KYC_PHOTO:
                ibCardPutDocumentsRequest.setDocumentName(ProofDocumentName.PHOTO.getProofDocument());
            default:
                break;
        }

        switch (digitalKycSendDocRequest.getKycDocFace()) {
            case FRONT:
                ibCardPutDocumentsRequest.setDocumentId("1");
                break;
            case BACK:
                ibCardPutDocumentsRequest.setDocumentId("2");
                break;
        }

        ibCardPutDocumentsRequest.setType(digitalKycSendDocRequest.getFileFormat());

        if (digitalKycSendDocRequest.getKycDocCategory()==KycDocCategory.POA) {

            switch (digitalKycSendDocRequest.getKycDocType()) {
                case KYC_VIDEO:
                    break;
                case AADHAAR:
                    ibCardPutDocumentsRequest.setPoaType(AddressProof.AADHAAR_LETTER.getAddressProof());
                    break;
                case DL:
                    ibCardPutDocumentsRequest.setPoaType(AddressProof.DRIVING_LICENCE.getAddressProof());
                    break;
                case VOTERID:
                    ibCardPutDocumentsRequest.setPoaType(AddressProof.VOTER_ID.getAddressProof());
                    break;
                case PASSPORT:
                    ibCardPutDocumentsRequest.setPoaType(AddressProof.PASSPORT.getAddressProof());
                    break;
                default:
                    break;
            }
        } else if (digitalKycSendDocRequest.getKycDocCategory()==KycDocCategory.POI) {
            switch (digitalKycSendDocRequest.getKycDocType()) {
                case PAN:
                    ibCardPutDocumentsRequest.setPoiType(IdentityProof.PAN.getIdentityProof());
                    break;
            }
        }

        return ibCardPutDocumentsRequest;
    }

    public void setNameFromPanNameService (PanNoResponse panNoResponse, CreditAssessmentInfoRequest creditAssessmentInfoRequest) {
        creditAssessmentInfoRequest.getPersonalInfoRequest().setFirstName(panNoResponse.getPanFirstName());
        creditAssessmentInfoRequest.getPersonalInfoRequest().setLastName(panNoResponse.getPanLastName());
        if (Objects.nonNull(panNoResponse.getPanMiddleName())) {
            creditAssessmentInfoRequest.getPersonalInfoRequest().setMiddleName(panNoResponse.getPanMiddleName());
        }
        else {
            creditAssessmentInfoRequest.getPersonalInfoRequest().setMiddleName("");
        }
    }

    public IBCardMandateRequest createMandateRequestForUserOnboarding (String cardId) {
        IBCardMandateRequest ibCardMandateRequest = new IBCardMandateRequest();
        ibCardMandateRequest.setCardId(cardId);

        Date now = new Date();

        SimpleDateFormat sdfDate = new SimpleDateFormat("yyyy-MM-dd");//dd/MM/yyyy
        String strDate = sdfDate.format(now);
        ibCardMandateRequest.setEmandateTime(strDate);

        SimpleDateFormat sdfTime = new SimpleDateFormat("HH:mm:ss");
        String strTime = sdfTime.format(now);
        ibCardMandateRequest.setEmandateTime(strTime);

        ibCardMandateRequest.setAdditional6("Billing");

        return ibCardMandateRequest;
    }

    public UserAcceptanceRequest createUserAcceptanceRequestFromCardId(String cardId) {

        UserAcceptanceRequest userAcceptanceRequest = new UserAcceptanceRequest();
        LenderInfoSection lenderInfoSection = new LenderInfoSection();
        lenderInfoSection.setLenderLeadReferenceNo(cardId);
        userAcceptanceRequest.setLenderInfoSection(lenderInfoSection);

        return userAcceptanceRequest;
    }

    public ApplyNowResponse applyNowFailureResponse(IBUserSignupResponse ibUserSignupResponse) {
        ApplyNowResponse applyNowResponse = new ApplyNowResponse();

        BaseResponse baseResponse = new BaseResponse();
        baseResponse.setInfo(ibUserSignupResponse.getErrorMessage());
        //TODO:get the full set of errors from Indiabulls and add in lender error config
        //baseResponse.setStatus(IBErrorCodeMapper.getStatus(null));//will set failure
        baseResponse.setStatus(errorCodeMapping.getStatusForLenderAndError(Lender.INDIA_BULLS, ibUserSignupResponse.getErrorMessage()));
        applyNowResponse.setBaseResponse(baseResponse);
        return applyNowResponse;
    }
	
	public ApplyNowResponse applyNowRejectResponse(IBUserSignupResponse ibUserSignupResponse) {
		ApplyNowResponse applyNowResponse = new ApplyNowResponse();
		
		BaseResponse baseResponse = new BaseResponse();
		baseResponse.setInfo(ibUserSignupResponse.getErrorMessage());
		baseResponse.setStatus(STATUS.REJECTED);
		
		applyNowResponse.setBaseResponse(baseResponse);
		return applyNowResponse;
	}

    public CreditAssessmentInfoResponse creaditAssessmentRejectResponse(IBCreditAssessmentResponse ibCreditAssessmentResponse) {
        CreditAssessmentInfoResponse creditAssessmentInfoResponse = new CreditAssessmentInfoResponse();
        creditAssessmentInfoResponse.setApprovalDecision(Constants.DECISION_DECLINED);

        BaseResponse baseResponse = new BaseResponse();
        baseResponse.setInfo(ibCreditAssessmentResponse.getErrorMessage());
        baseResponse.setStatus(STATUS.REJECTED);//will not retry here

        creditAssessmentInfoResponse.setBaseResponse(baseResponse);
        return creditAssessmentInfoResponse;
    }

    public CreditAssessmentInfoResponse creditAssessmentFailureResponse(IBCreditAssessmentResponse ibCreditAssessmentResponse) {
        CreditAssessmentInfoResponse creditAssessmentInfoResponse = new CreditAssessmentInfoResponse();
        creditAssessmentInfoResponse.setApprovalDecision(Constants.DECISION_DECLINED);

        BaseResponse baseResponse = new BaseResponse();
        baseResponse.setInfo(ibCreditAssessmentResponse.getErrorMessage());
        baseResponse.setStatus(errorCodeMapping.getStatusForLenderAndError(Lender.INDIA_BULLS, ibCreditAssessmentResponse.getErrorMessage()));//failure

        creditAssessmentInfoResponse.setBaseResponse(baseResponse);
        return creditAssessmentInfoResponse;
    }

    public FetchCardDetailsResponse cardDetailsFailure(IBCardDetailsResponse ibCardDetailsResponse) {
        FetchCardDetailsResponse fetchCardDetailsResponse = new FetchCardDetailsResponse();
        fetchCardDetailsResponse.setErrorMessage(ibCardDetailsResponse.getErrorMessage());
        fetchCardDetailsResponse.setSuccess(false);

        BaseResponse baseResponse = new BaseResponse();
        baseResponse.setInfo(ibCardDetailsResponse.getErrorMessage());
        //baseResponse.setStatus(IBErrorCodeMapper.getStatus(null));
        baseResponse.setStatus(errorCodeMapping.getStatusForLenderAndError(Lender.INDIA_BULLS, ibCardDetailsResponse.getErrorMessage()));

        fetchCardDetailsResponse.setBaseResponse(baseResponse);
        return fetchCardDetailsResponse;
    }

    public IBCardMandateRequest mandateConfirmationRequestToIBMandateConfirmationRequest(MandateConfirmationRequest mandateConfirmationRequest) {
        IBCardMandateRequest ibCardMandateRequest = new IBCardMandateRequest();

        ibCardMandateRequest.setCardId(mandateConfirmationRequest.getLenderReferenceId());
        ibCardMandateRequest.setTransactionId(mandateConfirmationRequest.getTransactionRefNo());
        LocalDateTime mandateCreationDate = LocalDateTime.parse(mandateConfirmationRequest.getMandateCreationDate());
        ibCardMandateRequest.setEmandateDate(mandateCreationDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        ibCardMandateRequest.setEmandateTime(mandateCreationDate.format(DateTimeFormatter.ofPattern("HH:mm:ss")));

        if(mandateConfirmationRequest.getMandateType().equals(MandateType.AUTO)){
            ibCardMandateRequest.setAdditional6("Auto");
            ibCardMandateRequest.setAccountNumber(mandateConfirmationRequest.getHashedAccountNumber());
            ibCardMandateRequest.setPaymentAmount(mandateConfirmationRequest.getMandateAmount().doubleValue());
            if(mandateConfirmationRequest.getMandateStatus().equals(MandateStatus.SUCCESS)){
                ibCardMandateRequest.setMandateStatus("true");
            }
            else {
                ibCardMandateRequest.setMandateStatus("false");
            }
        }
        else if(mandateConfirmationRequest.getMandateType().equals(MandateType.BILLING)){

            ibCardMandateRequest.setAdditional6("Billing");
        }
        return ibCardMandateRequest;
    }

    public MandateConfirmationResponse mandateResponseMapping(IBCardMandateResponse ibCardMandateResponse) {
        MandateConfirmationResponse mandateConfirmationResponse = new MandateConfirmationResponse();
        mandateConfirmationResponse.setMaskedAccountNumber(ibCardMandateResponse.getAcctNum());
        mandateConfirmationResponse.setCustomerName(ibCardMandateResponse.getCustName());
        mandateConfirmationResponse.setStatus(STATUS.SUCCESS);
        return mandateConfirmationResponse;
    }
}

