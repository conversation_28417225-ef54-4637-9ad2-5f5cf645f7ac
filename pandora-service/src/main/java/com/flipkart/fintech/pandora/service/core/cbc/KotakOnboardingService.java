package com.flipkart.fintech.pandora.service.core.cbc;

import com.flipkart.fintech.pandora.api.model.cbc.request.kotak.*;
import com.flipkart.fintech.pandora.api.model.cbc.response.PandoraResponseWrapper;
import com.flipkart.fintech.pandora.api.model.cbc.response.common.WebviewDetailsResponse;
import com.flipkart.fintech.pandora.api.model.cbc.response.kotak.*;
import com.flipkart.fintech.pandora.service.client.cbc.exceptions.CbcException;
import com.flipkart.fintech.pandora.service.client.cbc.kotak.client.onboarding.KotakOnboardingClient;
import com.flipkart.fintech.pandora.service.core.cbc.transformers.KotakTransformers;
import com.google.inject.Inject;

public class KotakOnboardingService {

    private final KotakOnboardingClient kotakOnboardingClient;
    @Inject
    public KotakOnboardingService(KotakOnboardingClient kotakOnboardingClient) {
        this.kotakOnboardingClient = kotakOnboardingClient;
    }

    public CreateLeadResponse createLead(SmCreateLeadRequest request) throws CbcException {
        return KotakTransformers.transformCreateLeadResponse(kotakOnboardingClient.createLead(KotakTransformers.transformCreateLeadRequest(request)));
    }

    public LeadStatusResponse leadStatus(SmLeadIdRequest request) throws CbcException {
        return KotakTransformers.transformLeadStatusResponse(kotakOnboardingClient.leadStatus(KotakTransformers.transformLeadStatusRequest(request)));
    }

    public LeadsResponse fetchLead(SmLeadsRequest request) throws CbcException {
        return KotakTransformers.transformLeadResponse(kotakOnboardingClient.getLead(KotakTransformers.transformLeadRequest(request)));
    }

    public UserDetailsResponse submitUserDetails(SmUserDetailsRequest request) throws CbcException {
        return KotakTransformers.transformSubmitUserDetailsResponse(kotakOnboardingClient.submitUserDetails(KotakTransformers.transformSubmitUserDetailsRequest(request)));

    }

    public VkycStatusResponse vkycStatus(SmVKycStatusRequest request) throws CbcException {
        return KotakTransformers.transformVkycStatusResponse(kotakOnboardingClient.vkycStatus(KotakTransformers.transformVkycStatusRequest(request)));
    }

    public PandoraResponseWrapper<WebviewDetailsResponse> initiateVkyc(SmVideoKycRequest request) throws CbcException {
        return KotakTransformers.transformInitiateVkycResponse(kotakOnboardingClient.initiateVkyc(KotakTransformers.transformInitiateVkycRequest(request)));
    }

    public FdInterestRateResponse fdInterestRate(SmFdInterestRateRequest request) throws CbcException {
        return KotakTransformers.transformInterestRateResponse(kotakOnboardingClient.fdConfig(KotakTransformers.transformInterestRateRequest(request)));
    }

    public CreateNewTransactionResponse payNowUpi(SmCreateNewTransactionRequest request) throws CbcException {
        return KotakTransformers.transformPayNowUpiResponse(kotakOnboardingClient.payNowUpi(KotakTransformers.transformPayNowUpiRequest(request)));
    }

    public PaymentStatusResponse paymentStatus(SmPaymentStatusRequest request) throws CbcException {
        return KotakTransformers.transformPaymentStatusResponse(kotakOnboardingClient.paymentStatus(KotakTransformers.transformPaymentStatusRequest(request)));
    }


    public PinServiceabilityResponse pincodeServiceable(String request) throws CbcException {
        return KotakTransformers.transformPinServiceableResponse(kotakOnboardingClient.pinServiceable(request));
    }

    public PandoraResponseWrapper<WebviewDetailsResponse> initiateKyc(SmInitiateKycRequest request) throws CbcException {
        return KotakTransformers.transformInitiateKycResponse(kotakOnboardingClient.initiateKyc(KotakTransformers.transformInitiateKycRequest(request)));
    }

    public ConsentResponse consent() throws CbcException {
        return KotakTransformers.transformConsentResponse(kotakOnboardingClient.consent());
    }

    public UpdatePanResponse updatePanDetails(SmUpdatePanRquest request) throws CbcException {
        return KotakTransformers.transformUpdatePanResponse(kotakOnboardingClient.updatePanDetails(KotakTransformers.transformUpdatePanRequest(request)));
    }
}
