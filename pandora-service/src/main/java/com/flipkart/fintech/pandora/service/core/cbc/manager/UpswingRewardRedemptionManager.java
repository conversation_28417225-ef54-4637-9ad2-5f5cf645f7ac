package com.flipkart.fintech.pandora.service.core.cbc.manager;

import com.flipkart.fintech.pandora.api.model.request.cbc.upswing.UpswingRewardRedemptionRequest;
import com.flipkart.fintech.pandora.api.model.response.cbc.upswing.UpswingRewardRedemptionResponse;

import java.util.concurrent.ExecutionException;

public interface UpswingRewardRedemptionManager {
    UpswingRewardRedemptionResponse rewardRedemption(UpswingRewardRedemptionRequest request) throws ExecutionException;
}
