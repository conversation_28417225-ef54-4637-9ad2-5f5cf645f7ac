package com.flipkart.fintech.pandora.service.core.cbc.manager.impl;

import com.flipkart.fintech.pandora.api.model.response.cbc.upswing.UpswingApplicationJourneyStatusDetailsResponse;
import com.flipkart.fintech.pandora.service.client.cbc.upswing.response.ApplicationStatusDetails;
import com.flipkart.fintech.pandora.service.client.cbc.upswing.services.UpswingServiceClient;
import com.flipkart.fintech.pandora.service.core.cbc.manager.UpswingApplicationJourneyDetailsManager;
import com.google.inject.Inject;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.ExecutionException;

@Slf4j
public class UpswingApplicationJourneyJourneyDetailsManagerImpl implements UpswingApplicationJourneyDetailsManager {

    private final UpswingServiceClient upswingServiceClient;

    @Inject
    public UpswingApplicationJourneyJourneyDetailsManagerImpl(UpswingServiceClient upswingServiceClient) {
        this.upswingServiceClient = upswingServiceClient;
    }

    @Override
    public UpswingApplicationJourneyStatusDetailsResponse getApplicationStatusDetails(String userId) throws ExecutionException {

        ApplicationStatusDetails applicationStatusDetails = upswingServiceClient.fetchApplicationStatusDetails(userId);
        log.info("Application Status Details response for userId: {} is: {}", userId, applicationStatusDetails);
        return new UpswingApplicationJourneyStatusDetailsResponse(applicationStatusDetails.getUpswingApplicationStatus(), applicationStatusDetails.getUpswingApplicationStep(), applicationStatusDetails.getReEkycRequired());
    }
}
