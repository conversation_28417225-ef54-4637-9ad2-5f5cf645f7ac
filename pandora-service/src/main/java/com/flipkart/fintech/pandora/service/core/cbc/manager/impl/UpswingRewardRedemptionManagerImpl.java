package com.flipkart.fintech.pandora.service.core.cbc.manager.impl;

import com.flipkart.fintech.pandora.api.model.request.cbc.upswing.UpswingRewardRedemptionRequest;
import com.flipkart.fintech.pandora.api.model.response.cbc.upswing.UpswingApplicationJourneyStatusDetailsResponse;
import com.flipkart.fintech.pandora.api.model.response.cbc.upswing.UpswingRewardRedemptionResponse;
import com.flipkart.fintech.pandora.service.client.cbc.upswing.response.ApplicationStatusDetails;
import com.flipkart.fintech.pandora.service.client.cbc.upswing.services.UpswingServiceClient;
import com.flipkart.fintech.pandora.service.core.cbc.manager.UpswingRewardRedemptionManager;
import com.google.inject.Inject;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.ExecutionException;

@Slf4j
public class UpswingRewardRedemptionManagerImpl implements UpswingRewardRedemptionManager {
    private final UpswingServiceClient upswingServiceClient;

    @Inject
    public UpswingRewardRedemptionManagerImpl(UpswingServiceClient upswingServiceClient) {
        this.upswingServiceClient = upswingServiceClient;
    }

    @Override
    public UpswingRewardRedemptionResponse rewardRedemption(UpswingRewardRedemptionRequest request) throws ExecutionException {
        UpswingRewardRedemptionResponse rewardRedemptionResponse = upswingServiceClient.redeemReward(request);
        log.info("Application Status Details response for userId: {} is: {}", request.getUserId(), rewardRedemptionResponse);
        return rewardRedemptionResponse;
    }
}
