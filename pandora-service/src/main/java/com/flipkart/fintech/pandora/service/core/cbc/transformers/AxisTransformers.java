package com.flipkart.fintech.pandora.service.core.cbc.transformers;

import com.axis.model.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.pandora.api.model.cbc.enums.axis.*;
import com.flipkart.fintech.pandora.api.model.cbc.enums.common.SmEmploymentType;
import com.flipkart.fintech.pandora.api.model.cbc.enums.common.SmGenderEnum;
import com.flipkart.fintech.pandora.api.model.cbc.enums.common.SmMaritalStatusEnum;
import com.flipkart.fintech.pandora.api.model.cbc.models.axis.*;
import com.flipkart.fintech.pandora.api.model.cbc.models.axis.Address;
import com.flipkart.fintech.pandora.api.model.cbc.request.axis.*;
import com.flipkart.fintech.pandora.api.model.cbc.response.PandoraResponseWrapper;
import com.flipkart.fintech.pandora.api.model.cbc.response.axis.SubmitApplicationResponse;
import com.flipkart.fintech.pandora.api.model.cbc.response.axis.*;
import com.flipkart.fintech.pandora.api.model.cbc.response.common.WebviewDetailsResponse;
import com.flipkart.fintech.pandora.service.client.cbc.axis.AxisEndPoints;
import com.flipkart.fintech.pandora.service.client.cbc.axis.SmAxisCbcConfiguration;
import com.flipkart.fintech.pandora.service.client.cbc.exceptions.ErrorResponse;
import com.flipkart.fintech.pandora.service.client.cbc.exceptions.CbcException;
import com.flipkart.fintech.pandora.service.core.cbc.Constants;
import com.flipkart.fintech.pandora.service.core.cbc.utils.RegexUtils;
import com.flipkart.fintech.pandora.service.core.decryption.FormDataDecryption;
import com.flipkart.fintech.pandora.service.utils.DateUtil;
import com.flipkart.fintech.pandora.util.EnumUtils;
import com.flipkart.kloud.config.DynamicBucket;
import com.google.inject.Inject;
import com.google.inject.name.Named;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.NotNull;

import javax.annotation.Nullable;
import java.math.BigDecimal;
import java.util.*;

import static com.flipkart.fintech.pandora.service.core.cbc.utils.StringValidationUtils.sanitizeName;

@Slf4j
public class AxisTransformers {

    private static final Map<SmGenderEnum, Gender> genderEnumMap = new HashMap<SmGenderEnum, Gender>() {{
        put(SmGenderEnum.MALE, Gender.MALE);
        put(SmGenderEnum.FEMALE, Gender.FEMALE);
        put(SmGenderEnum.OTHER, Gender.OTHER);
    }};

    private static final Map<SmMaritalStatusEnum, MaritalStatus> maritalStatusEnumMap = new HashMap<SmMaritalStatusEnum, MaritalStatus>() {{
        put(SmMaritalStatusEnum.SINGLE, MaritalStatus.UNMARRIED);
        put(SmMaritalStatusEnum.MARRIED, MaritalStatus.MARRIED);
    }};

    private static final Map<SmEmploymentType, EmploymentType> occupationEnumMap
            = new HashMap<SmEmploymentType, EmploymentType>() {{
        put(SmEmploymentType.SALARIED, EmploymentType.SALARIED);
        put(SmEmploymentType.SELF_EMPLOYED, EmploymentType.SELF_EMPLOYED);
    }};


    private final SmAxisCbcConfiguration smAxisCbcConfig;
    private static final String COUNTRY_CODE = "91";
    private final ObjectMapper objectMapper;
    private final DynamicBucket featureFlagBucket;
    private static final FormDataDecryption formDataDecryption = new FormDataDecryption();

    @Inject
    public AxisTransformers(SmAxisCbcConfiguration smAxisCbcConfig, ObjectMapper objectMapper, @Named("featureFlagBucket") DynamicBucket featureFlagBucket) {
        this.smAxisCbcConfig = smAxisCbcConfig;
        this.objectMapper = objectMapper;
        this.featureFlagBucket = featureFlagBucket;
    }

    public GetUserDetailRequestGetUserDtlsRequest userDetailsRequestToAxisGetUserDtls(UserDetailsRequest userDetailsRequest) throws CbcException {

        GetUserDetailRequestGetUserDtlsRequestGetUserDtlsRequestBody getUserDtlsRequestBody = new GetUserDetailRequestGetUserDtlsRequestGetUserDtlsRequestBody();
        GetUserDetailRequestGetUserDtlsRequestGetUserDtlsRequestBodyData getUserDtlsRequestBodyData = new GetUserDetailRequestGetUserDtlsRequestGetUserDtlsRequestBodyData();
        getUserDtlsRequestBodyData.setCustType(userDetailsRequest.getCustType().getValue());
        getUserDtlsRequestBodyData.setTokenReferenceId(userDetailsRequest.getTokenReferenceId());
        getUserDtlsRequestBodyData.setTokenValue(userDetailsRequest.getTokenValue());
        getUserDtlsRequestBodyData.setPhoneNumber(filterPhoneNo(userDetailsRequest.getPhoneNumber()));
        getUserDtlsRequestBody.setData(getUserDtlsRequestBodyData);
        SubHeader subHeader = getSubHeader(
                AxisEndPoints.GET_USER_DETAILS.getServiceRequestId(),
                AxisEndPoints.GET_USER_DETAILS.getServiceRequestVersion());
        GetUserDetailRequestGetUserDtlsRequest request = new GetUserDetailRequestGetUserDtlsRequest();
        request.setGetUserDtlsRequestBody(getUserDtlsRequestBody);
        request.setSubHeader(subHeader);
        return request;
    }

    public UserDetailsResponse axisGetUserDtlsResToUserDetailsRes(GetUserDetailResponse response) {
        GetUserDetailResponseGetUserDtlsResponseGetUserDtlsResponseBody responseBody = response.getGetUserDtlsResponse().getGetUserDtlsResponseBody();
        List<Address> addresses = new ArrayList<>();

        if (Objects.nonNull(responseBody.getData().getAddresses())) {
            responseBody.getData().getAddresses().forEach(address -> {
                addresses.add(addressesResTranformer(address));
            });
        }
        List<CreditCardDetails> existingCreditCards = new ArrayList<>();
        if (Objects.nonNull(responseBody.getData().getExistingCreditCardDetails())) {
            responseBody.getData().getExistingCreditCardDetails().forEach(card -> {
                existingCreditCards.add(creditCardDetailsTranformer(card));
            });
        }
        return UserDetailsResponse.builder()
                .phoneNumber(responseBody.getData().getPhoneNumber())
                .customerName(responseBody.getData().getDisplayName())
                .addresses(addresses)
                .existingCreditCardDetails(existingCreditCards)
                .maskedAccountNumber(responseBody.getData().getMaskedAccountNumber())
                .build();
    }

    private Address addressesResTranformer(GetUserDetailResponseGetUserDtlsResponseGetUserDtlsResponseBodyDataAddresses axisAddress) {
        return Address.builder()
                .address1(axisAddress.getAddressLine1())
                .address2(axisAddress.getAddressLine2())
                .address3(axisAddress.getAddressLine3())
                .pinCode(axisAddress.getPostalCode())
                .state(axisAddress.getState())
                .addressType(AddressType.valueOf(StringUtils.trim(axisAddress.getAddressType())))
                .city(axisAddress.getCity())
                .country(axisAddress.getCountry())
                .build();
    }

    private CreditCardDetails creditCardDetailsTranformer(GetUserDetailResponseGetUserDtlsResponseGetUserDtlsResponseBodyDataExistingCreditCardDetails existingCreditCardDetails) {
        return CreditCardDetails.builder()
                .maskedCardNumber(existingCreditCardDetails.getMaskedCardNumber())
                .cardSerialNumber(existingCreditCardDetails.getCardSerialNumber())
                .build();
    }

    private SubHeader getSubHeader(String serviceRequestId, String serviceRequestversion) {
        SubHeader subHeader = new SubHeader();
        subHeader.setChannelId(smAxisCbcConfig.getChannelId());
        subHeader.setServiceRequestId(serviceRequestId);
        subHeader.setServiceRequestVersion(serviceRequestversion);
        subHeader.setRequestUUID(UUID.randomUUID().toString());
        return subHeader;
    }


    private String filterPhoneNo(String phoneNumber) {
        if (phoneNumber.length() <= 10)
            return COUNTRY_CODE + phoneNumber;
        else
            return COUNTRY_CODE + phoneNumber.substring(phoneNumber.length() - 10);
    }

    public GetOfferInputGetOfferRequest offerDetailsRequestToAxisGetOfferDtlsRequest(OfferDetailsRequest offerDetailsRequest) {

        GetOfferInputGetOfferRequestGetOfferRequestBody getOfferDtlsRequestBody = new GetOfferInputGetOfferRequestGetOfferRequestBody();
        GetOfferInputGetOfferRequestGetOfferRequestBodyData getOfferDtlsRequestBodyData = new GetOfferInputGetOfferRequestGetOfferRequestBodyData();
        getOfferDtlsRequestBodyData.setPhoneNumber(filterPhoneNo(offerDetailsRequest.getPhoneNumber()));
        getOfferDtlsRequestBodyData.setOfferId(offerDetailsRequest.getOfferId());
        getOfferDtlsRequestBody.setData(getOfferDtlsRequestBodyData);

        SubHeader subHeader = getSubHeader(AxisEndPoints.GET_OFFER_DETAILS.getServiceRequestId(), AxisEndPoints.GET_OFFER_DETAILS.getServiceRequestVersion());
        GetOfferInputGetOfferRequest request = new GetOfferInputGetOfferRequest();
        request.setGetOfferRequestBody(getOfferDtlsRequestBody);
        request.setSubHeader(subHeader);
        return request;
    }

    public OfferDetailsResponse axisGetOfferDtlsResponseToOfferDetailsResponse(GetOfferOutput response) {
        GetOfferOutputGetOfferResponseGetOfferResponseBody responseBody = response.getGetOfferResponse().getGetOfferResponseBody();
        return OfferDetailsResponse.builder()
                .productId(responseBody.getData().getProductId())
                .offerId(responseBody.getData().getOfferId())
                .startTime(Objects.nonNull(responseBody.getData().getStartTime()) ? responseBody.getData().getStartTime().longValue() : 0)
                .endTime(Objects.nonNull(responseBody.getData().getEndTime()) ? responseBody.getData().getEndTime().longValue() : 0)
                .crLimit(Objects.nonNull(responseBody.getData().getCrLimit()) ? responseBody.getData().getCrLimit().longValue() : 0)
                .custType(CustomerType.fromValue(StringUtils.trim(responseBody.getData().getCustType())))
                .build();
    }


    public ApplicationStatusResponse axisApplicationStatusToApplicationStatusRes(GetAppStatusOutput response) {
        GetAppStatusResponseBody responseBody = response.getGetAppStatusResponse().getGetAppStatusResponseBody();
        KycPendingStateContext kycPendingStateContext = null;
        if (ApplicationState.KYC_PENDING.getValue().equals(responseBody.getData().getApplicationState()) && Objects.nonNull(responseBody.getData().getKycPendingStateContext())
                && Objects.nonNull(responseBody.getData().getKycPendingStateContext().getKycFlow())) {

            KycFlow.KycFlowBuilder kycFlowBuilder = KycFlow.builder();

            if (Objects.nonNull(responseBody.getData().getKycPendingStateContext().getKycFlow().getKyc())) {
                KycAppStatusRes.KycAppStatusResBuilder kycAppStatusResBuilder = KycAppStatusRes.builder();
                if (Objects.nonNull(responseBody.getData().getKycPendingStateContext().getKycFlow().getKyc().getEKyc())
                        && Objects.nonNull(responseBody.getData().getKycPendingStateContext().getKycFlow().getKyc().getEKyc().getExpirationTimestamp())) {
                    kycAppStatusResBuilder.eKyc(
                            ExpirationTimestamp.builder().expirationTimestamp(
                                            LastUpdateTimestamp.builder()
                                                    .epochMillis(responseBody.getData().getKycPendingStateContext()
                                                            .getKycFlow().getKyc().getEKyc().getExpirationTimestamp().getEpochMillis())
                                                    .build())
                                    .build());
                }
                if (Objects.nonNull(responseBody.getData().getKycPendingStateContext().getKycFlow().getKyc().getVKyc()) &&
                        Objects.nonNull(responseBody.getData().getKycPendingStateContext().getKycFlow().getKyc().getVKyc().getExpirationTimestamp())) {
                    kycAppStatusResBuilder.vKyc(
                            ExpirationTimestamp.builder().expirationTimestamp(
                                            LastUpdateTimestamp.builder()
                                                    .epochMillis(responseBody.getData().getKycPendingStateContext()
                                                            .getKycFlow().getKyc().getVKyc().getExpirationTimestamp().getEpochMillis())
                                                    .build())
                                    .build());
                }
                if (Objects.nonNull(responseBody.getData().getKycPendingStateContext().getKycFlow().getKyc().getOfflineKyc())&&
                        Objects.nonNull(responseBody.getData().getKycPendingStateContext().getKycFlow().getKyc().getOfflineKyc().getExpirationTimestamp())) {
                    kycAppStatusResBuilder.offlineKyc(
                            ExpirationTimestamp.builder().expirationTimestamp(
                                            LastUpdateTimestamp.builder()
                                                    .epochMillis(responseBody.getData().getKycPendingStateContext()
                                                            .getKycFlow().getKyc().getOfflineKyc().getExpirationTimestamp().getEpochMillis())
                                                    .build())
                                    .build());
                }
                kycFlowBuilder.kyc(kycAppStatusResBuilder.build());
            }

            if (Objects.nonNull(responseBody.getData().getKycPendingStateContext().getKycFlow().getIncomeVerification())) {
                IncomeVerificationAppStatus.IncomeVerificationAppStatusBuilder incomeVerificationAppStatusBuilder = IncomeVerificationAppStatus.builder();
                if (Objects.nonNull(responseBody.getData().getKycPendingStateContext().getKycFlow().getIncomeVerification().getOfflineSubmission())&&
                        Objects.nonNull(responseBody.getData().getKycPendingStateContext().getKycFlow().getIncomeVerification().getOfflineSubmission().getExpirationTimestamp())) {
                    incomeVerificationAppStatusBuilder.offlineSubmission(
                            ExpirationTimestamp.builder()
                                    .expirationTimestamp(LastUpdateTimestamp.builder()
                                            .epochMillis(responseBody.getData().getKycPendingStateContext()
                                                    .getKycFlow().getIncomeVerification().getOfflineSubmission().getExpirationTimestamp().getEpochMillis())
                                            .build())
                                    .build());

                }
                if (Objects.nonNull(responseBody.getData().getKycPendingStateContext().getKycFlow().getIncomeVerification().getOnlineSubmission())&&
                        Objects.nonNull(responseBody.getData().getKycPendingStateContext().getKycFlow().getIncomeVerification().getOnlineSubmission().getExpirationTimestamp())) {
                    incomeVerificationAppStatusBuilder.onlineSubmission(
                            ExpirationTimestamp.builder()
                                    .expirationTimestamp(LastUpdateTimestamp.builder()
                                            .epochMillis(responseBody.getData().getKycPendingStateContext()
                                                    .getKycFlow().getIncomeVerification().getOnlineSubmission().getExpirationTimestamp().getEpochMillis())
                                            .build())
                                    .build());
                }
                kycFlowBuilder.incomeVerification(incomeVerificationAppStatusBuilder.build());
            }

            KycFlow kycFlowData = kycFlowBuilder.build();
            Boolean incomeVerificationRequired = Objects.nonNull(kycFlowData.getIncomeVerification());
            kycPendingStateContext = KycPendingStateContext.builder()
                    .kycFlow(kycFlowData)
                    .incomeVerificationRequired(incomeVerificationRequired)
                    .build();

        }
        return ApplicationStatusResponse.builder()
                .applicationState(ApplicationState.valueOf(StringUtils.trim(responseBody.getData().getApplicationState())))
                .c1Id(StringUtils.trimToNull(responseBody.getData().getC1Id()))
                .reason(responseBody.getData().getReason())
                .description(responseBody.getData().getDescription())
                .message(responseBody.getData().getMessage())
                .accountToken(responseBody.getData().getAccountToken())
                .fieldNames(responseBody.getData().getFieldNames())
                .kycPendingStateContext(kycPendingStateContext)
                .build();
    }


    public InitiateChallengeInputInitiateChallengeRequest initiateChallengeRequestToAxisInitiateChallengeRequest(InitiateChallengeRequest initiateChallengeRequest) throws CbcException {

        InitiateChallengeInputInitiateChallengeRequestInitiateChallengeRequestBody initiateChallengeRequestBody = new InitiateChallengeInputInitiateChallengeRequestInitiateChallengeRequestBody();
        InitiateChallengeInputInitiateChallengeRequestInitiateChallengeRequestBodyData initiateChallengeRequestBodyData = new InitiateChallengeInputInitiateChallengeRequestInitiateChallengeRequestBodyData();

        initiateChallengeRequestBodyData.setPhoneNumber(filterPhoneNo(initiateChallengeRequest.getPhoneNumber()));
        initiateChallengeRequestBodyData.setCustType(initiateChallengeRequest.getCustType().getValue());
        initiateChallengeRequestBodyData.setChallengeContext(initiateChallengeRequest.getChallengeContext());
        initiateChallengeRequestBodyData.setChallengeMechanismType(initiateChallengeRequest.getChallengeMechanismType());
        initiateChallengeRequestBodyData.setCardNumber(initiateChallengeRequest.getCardNumber());
        initiateChallengeRequestBodyData.setPan(decryptData(initiateChallengeRequest.getPan()));
        initiateChallengeRequestBody.setData(initiateChallengeRequestBodyData);

        SubHeader subHeader = getSubHeader(AxisEndPoints.INITIATE_CHALLENGE.getServiceRequestId(), AxisEndPoints.INITIATE_CHALLENGE.getServiceRequestVersion());
        InitiateChallengeInputInitiateChallengeRequest request = new InitiateChallengeInputInitiateChallengeRequest();
        request.setInitiateChallengeRequestBody(initiateChallengeRequestBody);
        request.setSubHeader(subHeader);
        return request;
    }

    public InitiateChallengeResponse axisInitiateChallengeResponseToInitiateChallengeResponse(InitiateChallengeOutput response) {

        InitiateChallengeOutputInitiateChallengeResponseInitiateChallengeResponseBody responseBody = response.getInitiateChallengeResponse().getInitiateChallengeResponseBody();

        return InitiateChallengeResponse.builder()
                .tokenReferenceId(responseBody.getData().getTokenReferenceId())
                .build();
    }

    public GetAppStatusInputGetAppStatusRequest applicationStatusReqToAxisApplicationStatus(ApplicationStatusRequest applicationStatusRequest) {
        GetAppStatusInputGetAppStatusRequestGetAppStatusRequestBody getRequestBody = new GetAppStatusInputGetAppStatusRequestGetAppStatusRequestBody();
        GetAppStatusInputGetAppStatusRequestGetAppStatusRequestBodyData getRequestBodyData = new GetAppStatusInputGetAppStatusRequestGetAppStatusRequestBodyData();
        getRequestBodyData.setApplicationId(applicationStatusRequest.getApplicationId());
        getRequestBodyData.setCustType(applicationStatusRequest.getCustType().getValue());
        getRequestBodyData.setPhoneNumber(filterPhoneNo(applicationStatusRequest.getPhoneNumber()));
        SubHeader subHeader = getSubHeader(
                AxisEndPoints.GET_APPLICATION_STATUS.getServiceRequestId(),
                AxisEndPoints.GET_APPLICATION_STATUS.getServiceRequestVersion());
        GetAppStatusInputGetAppStatusRequest request = new GetAppStatusInputGetAppStatusRequest();
        getRequestBody.setData(getRequestBodyData);
        request.setGetAppStatusRequestBody(getRequestBody);
        request.setSubHeader(subHeader);
        return request;
    }

    public SubmitApplicationResponse axisApplicationToSubmitApplicationRes(com.axis.model.SubmitApplicationResponse response) {
        SubmitApplicationResponseSubmitApplicationResponseSubmitApplicationResponseBody responseBody = response.getSubmitApplicationResponse().getSubmitApplicationResponseBody();
        return SubmitApplicationResponse.builder()
                .applicationState(ApplicationState.valueOf(StringUtils.trim(responseBody.getData().getApplicationState())))
                .build();
    }

    public ETCInputRequestSubmitApplicationRequest submitETCApplicationReqToAxisApplicationReq(ETCSubmitApplicationRequest submitApplicationRequest) throws CbcException {
        ETCInputRequestSubmitApplicationRequestSubmitApplicationRequestBody submitApplicationRequestBody = new ETCInputRequestSubmitApplicationRequestSubmitApplicationRequestBody();
        ETCInputRequestSubmitApplicationRequestSubmitApplicationRequestBodyData submitApplicationRequestBodyData = new ETCInputRequestSubmitApplicationRequestSubmitApplicationRequestBodyData();
        List<TncAcceptanceMetadata> tncList = new ArrayList<>();
        submitApplicationRequest.getTncAcceptanceMetadata().forEach(tnc -> {
            tncList.add(tncTransformer(tnc));
        });
        submitApplicationRequestBodyData.applicationId(submitApplicationRequest.getApplicationId())
                .custType(submitApplicationRequest.getCustType().getValue())
                .phoneNumber(filterPhoneNo(submitApplicationRequest.getPhoneNumber()))
                .tncAcceptanceMetadata(tncList)
                .productId(smAxisCbcConfig.getProductId())
                .promotionCodes(smAxisCbcConfig.getPromotionCodes())
                .cardSerialNumber(submitApplicationRequest.getCardSerialNumber())
                .offerId(submitApplicationRequest.getOfferId())
                .tokenReferenceId(submitApplicationRequest.getToken().getReferenceId())
                .tokenValue(submitApplicationRequest.getToken().getValue());
        if (RegexUtils.checkStringWithMultipleRegex(Constants.AXIS_DISPLAY_NAME_REGEX_LIST, sanitizeName(submitApplicationRequest.getDisplayName()))) {
            submitApplicationRequestBodyData.displayName(sanitizeName(submitApplicationRequest.getDisplayName()));
        } else {
            ErrorResponse errorResponse = ErrorResponse.builder().errorCode(Constants.DISPLAY_NAME_ERROR_CODE).errorDescription(Constants.DISPLAY_NAME_ERROR).build();
            throw new CbcException(errorResponse);
        }
        submitApplicationRequestBody.setData(submitApplicationRequestBodyData);


        SubHeader subHeader = getSubHeader(
                AxisEndPoints.SUBMIT_APPLICATION_ETC.getServiceRequestId(),
                AxisEndPoints.SUBMIT_APPLICATION_ETC.getServiceRequestVersion());
        ETCInputRequestSubmitApplicationRequest request = new ETCInputRequestSubmitApplicationRequest();
        request.setSubmitApplicationRequestBody(submitApplicationRequestBody);
        request.setSubHeader(subHeader);
        return request;
    }

    private TncAcceptanceMetadata tncTransformer(TnCAcceptanceMetadata tnc) {
        TncAcceptanceMetadata tncMetaData = new TncAcceptanceMetadata();
        return tncMetaData.acceptance(tnc.isAcceptance()).type(tnc.getType());
    }

    public AccountDetailsReqGetAccountDetailsRequest accountDtlsRecToAxisAccountDtlsReq(AccountDetailsRequest accountDetailsRequest) {
        AccountDetailsReqGetAccountDetailsRequestGetAccountDetailsRequestBody getAccountDetailsRequestBody = new AccountDetailsReqGetAccountDetailsRequestGetAccountDetailsRequestBody();
        AccountDetailsReqGetAccountDetailsRequestGetAccountDetailsRequestBodyData getAccountDetailsRequestBodyData = new AccountDetailsReqGetAccountDetailsRequestGetAccountDetailsRequestBodyData();
        getAccountDetailsRequestBodyData.setPhoneNumber(filterPhoneNo(accountDetailsRequest.getPhoneNumber()));
        getAccountDetailsRequestBodyData.setCardSerNo(accountDetailsRequest.getCardSerNo());
        getAccountDetailsRequestBody.setData(getAccountDetailsRequestBodyData);
        SubHeader subHeader = getSubHeader(AxisEndPoints.GET_ACCOUNT_DETAILS.getServiceRequestId(), AxisEndPoints.GET_ACCOUNT_DETAILS.getServiceRequestVersion());
        AccountDetailsReqGetAccountDetailsRequest request = new AccountDetailsReqGetAccountDetailsRequest();
        request.setGetAccountDetailsRequestBody(getAccountDetailsRequestBody);
        request.setSubHeader(subHeader);
        return request;

    }

    public AccountDetailsResponse axisAccountDtlsToAccountDtlsRes(AccountDetailsRes getAccountDetailsResponse) {
        AccountDetailsResGetAccountDetailsResponseGetAccountDetailsResponseBody responseBody = getAccountDetailsResponse.getGetAccountDetailsResponse().getGetAccountDetailsResponseBody();
        return AccountDetailsResponse.builder()
                .cardNumber(responseBody.getData().getCardNumber())
                .creditLimit(responseBody.getData().getCreditLimit())
                .build();
    }

    private NTBInputRequestSubmitApplicationRequestSubmitApplicationRequestBodyDataAddresses addressesNTBReqTranformer(Address address, String residenceType) throws CbcException {
        NTBInputRequestSubmitApplicationRequestSubmitApplicationRequestBodyDataAddresses addressReq = new NTBInputRequestSubmitApplicationRequestSubmitApplicationRequestBodyDataAddresses();
        return addressReq.addressLine1(StringUtils.trimToNull(address.getAddress1()))
                .addressLine2(StringUtils.trimToNull(address.getAddress2()))
                .addressLine3(StringUtils.trimToNull(address.getAddress3()))
                .city(StringUtils.trimToNull(address.getCity()))
                .landmark(StringUtils.trimToNull(address.getLandmark()))
                .country(address.getCountry())
                .postalCode(address.getPinCode())
                .state(address.getState())
                .addressTags(address.getAddressTags())
                .residenceType(address.getAddressTags().contains(AddressType.OFFICE_ADDRESS.getValue()) ? null : residenceType);

    }


    public GetCardDetailRequestLCMGetCardDetailsRequest cardDtlsRequestToAxisLcmCardDtls(CardDetailsRequest request) {
        GetCardDetailRequestLCMGetCardDetailsRequestLCMGetCardDetailsRequestBody requestBody = new GetCardDetailRequestLCMGetCardDetailsRequestLCMGetCardDetailsRequestBody();
        requestBody.setCardSerNo(request.getCardSerNo());
        requestBody.setDeviceId(request.getDeviceId());
        requestBody.setMobileNo(filterPhoneNo(request.getMobileNo()));

        SubHeader subHeader = getSubHeader(
                AxisEndPoints.GET_MASKED_CARD.getServiceRequestId(),
                AxisEndPoints.GET_MASKED_CARD.getServiceRequestVersion());

        GetCardDetailRequestLCMGetCardDetailsRequest req = new GetCardDetailRequestLCMGetCardDetailsRequest();
        req.setSubHeader(subHeader);
        req.setLcMGetCardDetailsRequestBody(requestBody);
        return req;
    }

    public CardDetailsResponse axisLcmCardDtlsToCardDtlsRes(GetCardDetailResponse response) {
        GetCardDetailResponseLCMGetCardDetailsResponseLCMGetCardDetailsResponseBody responseBody = response.getLcMGetCardDetailsResponse().getLcMGetCardDetailsResponseBody();
        return CardDetailsResponse.builder()
                .cardNumber(responseBody.getGetCardDetailsResponse().getCardNumber())
                .cardExpiryDate(responseBody.getGetCardDetailsResponse().getCardExpiryDate())
                .build();
    }

    public ETBInputRequestSubmitApplicationRequest submitETBApplicationReqToAxisApplicationReq(ETBSubmitApplicationRequest submitApplicationRequest) throws CbcException {
        ETBInputRequestSubmitApplicationRequestSubmitApplicationRequestBody submitApplicationRequestBody = new ETBInputRequestSubmitApplicationRequestSubmitApplicationRequestBody();


        ETBInputRequestSubmitApplicationRequestSubmitApplicationRequestBodyData submitApplicationRequestBodyData = new ETBInputRequestSubmitApplicationRequestSubmitApplicationRequestBodyData();
        List<TncAcceptanceMetadata> tncList = new ArrayList<>();
        if (Objects.nonNull(submitApplicationRequest.getTncAcceptanceMetadata())) {
            submitApplicationRequest.getTncAcceptanceMetadata().forEach(tnc -> {
                tncList.add(tncTransformer(tnc));
            });
        }
        List<com.axis.model.Address> addresses = new ArrayList<>();

        if (Objects.nonNull(submitApplicationRequest.getAddresses())) {
            submitApplicationRequest.getAddresses().forEach(address -> {
                try {
                    addresses.add(addressesReqTransformer(address));
                } catch (CbcException e) {
                    throw new RuntimeException(e);
                }
            });
        }

        submitApplicationRequestBodyData
                .addresses(addresses)
                .pan(decryptData(StringUtils.trimToNull(submitApplicationRequest.getPan())))
                .productId(smAxisCbcConfig.getProductId())
                .promotionCodes(smAxisCbcConfig.getPromotionCodes())
                .phoneNumber(filterPhoneNo(submitApplicationRequest.getPhoneNumber()))
                .tncAcceptanceMetadata(tncList)
                .custType(Objects.nonNull(submitApplicationRequest.getCustType()) ? submitApplicationRequest.getCustType().getValue() : "")
                .offerId(submitApplicationRequest.getOfferId())
                .applicationId(submitApplicationRequest.getApplicationId());

        if (RegexUtils.checkStringWithMultipleRegex(Constants.AXIS_DISPLAY_NAME_REGEX_LIST, submitApplicationRequest.getDisplayName())) {
            submitApplicationRequestBodyData.displayName(StringUtils.trimToNull(submitApplicationRequest.getDisplayName()));
        } else {
            ErrorResponse errorResponse = ErrorResponse.builder().errorCode(Constants.DISPLAY_NAME_ERROR_CODE).errorDescription(Constants.DISPLAY_NAME_ERROR).build();
            throw new CbcException(errorResponse);
        }
        if (Objects.nonNull(submitApplicationRequest.getToken())) {
            submitApplicationRequestBodyData
                    .tokenReferenceId(submitApplicationRequest.getToken().getReferenceId())
                    .tokenValue(submitApplicationRequest.getToken().getValue());
        }
        if (Objects.nonNull(submitApplicationRequest.getFinancialDetails())) {
            submitApplicationRequestBodyData
                    .standardizationOccupationCode("")
                    .employmentType(EnumUtils.transformEnum(submitApplicationRequest.getFinancialDetails().getEmploymentType(), occupationEnumMap).getValue())
                    .standardizationSourceofFunds(getStandardizationSourceofFunds(
                            submitApplicationRequest.getFinancialDetails().getEmploymentType(), submitApplicationRequest.getFinancialDetails().getBusinessNature()));

            if (submitApplicationRequest.getFinancialDetails().getEmploymentType() == SmEmploymentType.SALARIED) {
                submitApplicationRequestBodyData
                        .totalWorkExperience(new BigDecimal(submitApplicationRequest.getFinancialDetails().getTotalWorkExperience()))
                        .workExperienceInCurrentOrg(new BigDecimal(submitApplicationRequest.getFinancialDetails().getWorkExperienceInCurrentOrg()))
                        .industryType(Objects.nonNull(submitApplicationRequest.getFinancialDetails().getIndustryType()) ?
                                submitApplicationRequest.getFinancialDetails().getIndustryType().getId() : "")
                        .employerSector(Objects.nonNull(submitApplicationRequest.getFinancialDetails().getEmployerSector()) ?
                                submitApplicationRequest.getFinancialDetails().getEmployerSector().getId() : "")
                        .organizationName(StringUtils.trimToNull(submitApplicationRequest.getFinancialDetails().getOrganizationName()))
                        .officeEmailID(StringUtils.trimToNull(submitApplicationRequest.getFinancialDetails().getOfficialEmailId()));
            } else if (submitApplicationRequest.getFinancialDetails().getEmploymentType() == SmEmploymentType.SELF_EMPLOYED) {
                if (RegexUtils.checkStringWithRegex(Constants.AXIS_BUSINESS_NAME_REGEX, submitApplicationRequest.getFinancialDetails().getBusinessName())) {
                    submitApplicationRequestBodyData.businessName(StringUtils.trimToNull(submitApplicationRequest.getFinancialDetails().getBusinessName()));
                } else {
                    ErrorResponse errorResponse = ErrorResponse.builder().errorCode(Constants.BUSINESS_NAME_ERROR_CODE).errorDescription(Constants.BUSINESS_NAME_ERROR).build();
                    throw new CbcException(errorResponse);
                }
                submitApplicationRequestBodyData
                        .workExperienceInCurrentBusiness(new BigDecimal(submitApplicationRequest.getFinancialDetails().getWorkExperienceInCurrentBusiness()))
                        .ownershipType(Objects.nonNull(submitApplicationRequest.getFinancialDetails().getOwnershipType()) ?
                                submitApplicationRequest.getFinancialDetails().getOwnershipType().getId() : "")
                        .businessNature(Objects.nonNull(submitApplicationRequest.getFinancialDetails().getBusinessNature()) ?
                                submitApplicationRequest.getFinancialDetails().getBusinessNature().getId() : "");
            }
        }
        submitApplicationRequestBody.setData(submitApplicationRequestBodyData);
        ETBInputRequestSubmitApplicationRequest request = new ETBInputRequestSubmitApplicationRequest();

        SubHeader subHeader = getSubHeader(
                AxisEndPoints.SUBMIT_APPLICATION_ETB.getServiceRequestId(),
                AxisEndPoints.SUBMIT_APPLICATION_ETB.getServiceRequestVersion());
        request.setSubHeader(subHeader);
        request.setSubmitApplicationRequestBody(submitApplicationRequestBody);
        return request;
    }

    public ETBNPAInputRequestSubmitApplicationRequest submitETBnoPAApplicationReqToAxisApplicationReq(ETBSubmitApplicationRequest submitApplicationRequest) throws CbcException {
        ETBNPAInputRequestSubmitApplicationRequestSubmitApplicationRequestBody submitApplicationRequestBody = new ETBNPAInputRequestSubmitApplicationRequestSubmitApplicationRequestBody();
        ETBNPAInputRequestSubmitApplicationRequestSubmitApplicationRequestBodyData submitApplicationRequestBodyData = new ETBNPAInputRequestSubmitApplicationRequestSubmitApplicationRequestBodyData();
        List<TncAcceptanceMetadata> tncList = new ArrayList<>();
        if (Objects.nonNull(submitApplicationRequest.getTncAcceptanceMetadata())) {
            submitApplicationRequest.getTncAcceptanceMetadata().forEach(tnc -> {
                tncList.add(tncTransformer(tnc));
            });
        }
        List<com.axis.model.Address> addresses = new ArrayList<>();

        if (Objects.nonNull(submitApplicationRequest.getAddresses())) {
            submitApplicationRequest.getAddresses().forEach(address -> {
                try {
                    addresses.add(addressesReqTransformer(address));
                } catch (CbcException e) {
                    throw new RuntimeException(e);
                }
            });
        }
        submitApplicationRequestBodyData
                .addresses(addresses)
                .pan(decryptData(StringUtils.trimToNull(submitApplicationRequest.getPan())))
                .productId(smAxisCbcConfig.getProductId())
                .promotionCodes(smAxisCbcConfig.getPromotionCodes())
                .phoneNumber(filterPhoneNo(submitApplicationRequest.getPhoneNumber()))
                .tncAcceptanceMetadata(tncList)
                .custType(Objects.nonNull(submitApplicationRequest.getCustType()) ? submitApplicationRequest.getCustType().getValue() : "")
                .offerId(submitApplicationRequest.getOfferId())
                .applicationId(submitApplicationRequest.getApplicationId());

        if (RegexUtils.checkStringWithMultipleRegex(Constants.AXIS_DISPLAY_NAME_REGEX_LIST, submitApplicationRequest.getDisplayName())) {
            submitApplicationRequestBodyData.displayName(StringUtils.trimToNull(submitApplicationRequest.getDisplayName()));
        } else {
            ErrorResponse errorResponse = ErrorResponse.builder().errorCode(Constants.DISPLAY_NAME_ERROR_CODE).errorDescription(Constants.DISPLAY_NAME_ERROR).build();
            throw new CbcException(errorResponse);
        }
        if (Objects.nonNull(submitApplicationRequest.getToken())) {
            submitApplicationRequestBodyData
                    .tokenReferenceId(submitApplicationRequest.getToken().getReferenceId())
                    .tokenValue(submitApplicationRequest.getToken().getValue());
        }
        if (Objects.nonNull(submitApplicationRequest.getFinancialDetails())) {
            submitApplicationRequestBodyData
                    .standardizationOccupationCode("")
                    .employmentType(EnumUtils.transformEnum(submitApplicationRequest.getFinancialDetails().getEmploymentType(), occupationEnumMap).getValue())
                    .netAnnualIncome(submitApplicationRequest.getFinancialDetails().getNetAnnualIncome())
                    .standardizationSourceofFunds(getStandardizationSourceofFunds(
                            submitApplicationRequest.getFinancialDetails().getEmploymentType(), submitApplicationRequest.getFinancialDetails().getBusinessNature()));
            if (submitApplicationRequest.getFinancialDetails().getEmploymentType() == SmEmploymentType.SALARIED) {
                submitApplicationRequestBodyData
                        .totalWorkExperience(new BigDecimal(submitApplicationRequest.getFinancialDetails().getTotalWorkExperience()))
                        .workExperienceInCurrentOrg(new BigDecimal(submitApplicationRequest.getFinancialDetails().getWorkExperienceInCurrentOrg()))
                        .industryType(Objects.nonNull(submitApplicationRequest.getFinancialDetails().getIndustryType()) ?
                                submitApplicationRequest.getFinancialDetails().getIndustryType().getId() : "")
                        .employerSector(Objects.nonNull(submitApplicationRequest.getFinancialDetails().getEmployerSector()) ?
                                submitApplicationRequest.getFinancialDetails().getEmployerSector().getId() : "")
                        .organizationName(StringUtils.trimToNull(submitApplicationRequest.getFinancialDetails().getOrganizationName()))
                        .officeEmailID(StringUtils.trimToNull(submitApplicationRequest.getFinancialDetails().getOfficialEmailId()));
            } else if (submitApplicationRequest.getFinancialDetails().getEmploymentType() == SmEmploymentType.SELF_EMPLOYED) {
                if (RegexUtils.checkStringWithRegex(Constants.AXIS_BUSINESS_NAME_REGEX, submitApplicationRequest.getFinancialDetails().getBusinessName())) {
                    submitApplicationRequestBodyData.businessName(StringUtils.trimToNull(submitApplicationRequest.getFinancialDetails().getBusinessName()));
                } else {
                    ErrorResponse errorResponse = ErrorResponse.builder().errorCode(Constants.BUSINESS_NAME_ERROR_CODE).errorDescription(Constants.BUSINESS_NAME_ERROR).build();
                    throw new CbcException(errorResponse);
                }
                submitApplicationRequestBodyData
                        .workExperienceInCurrentBusiness(new BigDecimal(submitApplicationRequest.getFinancialDetails().getWorkExperienceInCurrentBusiness()))
                        .ownershipType(Objects.nonNull(submitApplicationRequest.getFinancialDetails().getOwnershipType()) ?
                                submitApplicationRequest.getFinancialDetails().getOwnershipType().getId() : "")
                        .businessNature(Objects.nonNull(submitApplicationRequest.getFinancialDetails().getBusinessNature()) ?
                                submitApplicationRequest.getFinancialDetails().getBusinessNature().getId() : "");
            }
        }

        submitApplicationRequestBody.setData(submitApplicationRequestBodyData);

        SubHeader subHeader = getSubHeader(
                AxisEndPoints.SUBMIT_APPLICATION_ETB.getServiceRequestId(),
                AxisEndPoints.SUBMIT_APPLICATION_ETB.getServiceRequestVersion());
        ETBNPAInputRequestSubmitApplicationRequest request = new ETBNPAInputRequestSubmitApplicationRequest();
        request.setSubHeader(subHeader);
        request.setSubmitApplicationRequestBody(submitApplicationRequestBody);
        return request;
    }

    public NTBInputRequestSubmitApplicationRequest submitNTBApplicationReqToAxisApplicationReq(NTBSubmitApplicationRequest submitApplicationRequest) throws CbcException {
        NTBInputRequestSubmitApplicationRequestSubmitApplicationRequestBody submitApplicationRequestBody = new NTBInputRequestSubmitApplicationRequestSubmitApplicationRequestBody();
        NTBInputRequestSubmitApplicationRequestSubmitApplicationRequestBodyData submitApplicationRequestBodyData = new NTBInputRequestSubmitApplicationRequestSubmitApplicationRequestBodyData();
        List<TncAcceptanceMetadata> tncList = new ArrayList<>();
        if (Objects.nonNull(submitApplicationRequest.getTncAcceptanceMetadata())) {
            submitApplicationRequest.getTncAcceptanceMetadata().forEach(tnc -> {
                tncList.add(tncTransformer(tnc));
            });
        }
        List<NTBInputRequestSubmitApplicationRequestSubmitApplicationRequestBodyDataAddresses> addresses = new ArrayList<>();

        if (Objects.nonNull(submitApplicationRequest.getAddresses())) {
            submitApplicationRequest.getAddresses().forEach(address -> {
                try {
                    addresses.add(addressesNTBReqTranformer(address, Objects.nonNull(submitApplicationRequest.getResidenceType()) ? submitApplicationRequest.getResidenceType().getValue() : ""));
                } catch (CbcException e) {
                    throw new RuntimeException(e);
                }
            });
        }
        submitApplicationRequestBodyData
                .addresses(addresses)
                .dateOfBirth(DateUtil.getFormattedDateTime(decryptData(submitApplicationRequest.getDateOfBirth()), DateUtil.DDMMYYYY_SLASH_DELIMETER_FORMATTER, DateUtil.YYYYMMDD_FORMATTER))
                .education(submitApplicationRequest.getEducationStatus().getValue())
                .emailAddress(decryptData(submitApplicationRequest.getEmailAddress()))
                .fullName(validateFullNameAndFallbackIfRequired(sanitizeName(submitApplicationRequest.getFullName()), sanitizeName(submitApplicationRequest.getDisplayName())))
                .gender(EnumUtils.transformEnum(submitApplicationRequest.getGender(), genderEnumMap).getValue())
                .incomePeriodType("YEAR")
                .maritalStatus(EnumUtils.transformEnum(submitApplicationRequest.getMaritalStatus(), maritalStatusEnumMap).getValue())
                .nationality("RESIDENT")
                .residenceType(Objects.nonNull(submitApplicationRequest.getResidenceType()) ? submitApplicationRequest.getResidenceType().getValue() : "")
                .pan(decryptData(submitApplicationRequest.getPan()))
                .productId(smAxisCbcConfig.getProductId())
                .promotionCodes(smAxisCbcConfig.getPromotionCodes())
                .phoneNumber(filterPhoneNo(submitApplicationRequest.getPhoneNumber()))
                .tncAcceptanceMetadata(tncList)
                .custType(Objects.nonNull(submitApplicationRequest.getCustType()) ? submitApplicationRequest.getCustType().getValue() : "")
                .offerId(submitApplicationRequest.getOfferId())
                .applicationId(submitApplicationRequest.getApplicationId())
                .enabledFlags(Arrays.asList("enableE2eDigitization", "eligibleForDigitalKyc"));


        if (RegexUtils.checkStringWithMultipleRegex(Constants.AXIS_DISPLAY_NAME_REGEX_LIST, sanitizeName(submitApplicationRequest.getDisplayName()))) {
            submitApplicationRequestBodyData.displayName(sanitizeName(submitApplicationRequest.getDisplayName()));
        } else {
            ErrorResponse errorResponse = ErrorResponse.builder().errorCode(Constants.DISPLAY_NAME_ERROR_CODE).errorDescription(Constants.DISPLAY_NAME_ERROR).build();
            throw new CbcException(errorResponse);
        }
        if (Objects.nonNull(submitApplicationRequest.getToken())) {
            submitApplicationRequestBodyData
                    .tokenReferenceId(submitApplicationRequest.getToken().getReferenceId())
                    .tokenValue(submitApplicationRequest.getToken().getValue())
                    .tokenType(submitApplicationRequest.getToken().getType());
        }

        if (Objects.nonNull(submitApplicationRequest.getFamilyDetails())) {
            String[] fatherName = splitName(submitApplicationRequest.getFamilyDetails().getFatherName());
            String[] motherName = splitName(submitApplicationRequest.getFamilyDetails().getMotherName());
            submitApplicationRequestBodyData
                    .fatherFirstName(fatherName[0])
                    .fatherLastName(fatherName[1])
                    .fatherSalutation("MR")
                    .motherFirstName(motherName[0])
                    .motherLastName(motherName[1])
                    .motherSalutation("MRS");
        }

        if (Objects.nonNull(submitApplicationRequest.getFinancialDetails())) {
            submitApplicationRequestBodyData
                    .employmentType(EnumUtils.transformEnum(submitApplicationRequest.getFinancialDetails().getEmploymentType(), occupationEnumMap).getValue())
                    .incomeValue(new BigDecimal(submitApplicationRequest.getFinancialDetails().getNetAnnualIncome()))
                    .incomeSource(getIncomeSource(
                            submitApplicationRequest.getFinancialDetails().getEmploymentType(), submitApplicationRequest.getFinancialDetails().getBusinessNature()));

            if (submitApplicationRequest.getFinancialDetails().getEmploymentType() == SmEmploymentType.SALARIED) {
                submitApplicationRequestBodyData
                        .totalWorkExperience(new BigDecimal(submitApplicationRequest.getFinancialDetails().getTotalWorkExperience()))
                        .workExperienceInCurrentOrg(new BigDecimal(submitApplicationRequest.getFinancialDetails().getWorkExperienceInCurrentOrg()))
                        .industryType(Objects.nonNull(submitApplicationRequest.getFinancialDetails().getIndustryType()) ?
                                submitApplicationRequest.getFinancialDetails().getIndustryType().getId() : "")
                        .employerSector(Objects.nonNull(submitApplicationRequest.getFinancialDetails().getEmployerSector()) ?
                                submitApplicationRequest.getFinancialDetails().getEmployerSector().getId() : "")
                        .organizationName(submitApplicationRequest.getFinancialDetails().getOrganizationName())
                        .officeEmailID(StringUtils.trimToNull(submitApplicationRequest.getFinancialDetails().getOfficialEmailId()));
            } else if (submitApplicationRequest.getFinancialDetails().getEmploymentType() == SmEmploymentType.SELF_EMPLOYED) {
                if (RegexUtils.checkStringWithRegex(Constants.AXIS_BUSINESS_NAME_REGEX, submitApplicationRequest.getFinancialDetails().getBusinessName())) {
                    submitApplicationRequestBodyData.businessName(submitApplicationRequest.getFinancialDetails().getBusinessName());
                } else {
                    ErrorResponse errorResponse = ErrorResponse.builder().errorCode(Constants.BUSINESS_NAME_ERROR_CODE).errorDescription(Constants.BUSINESS_NAME_ERROR).build();
                    throw new CbcException(errorResponse);
                }
                submitApplicationRequestBodyData
                        .workExperienceInCurrentBusiness(new BigDecimal(submitApplicationRequest.getFinancialDetails().getWorkExperienceInCurrentBusiness()))
                        .ownershipType(Objects.nonNull(submitApplicationRequest.getFinancialDetails().getOwnershipType()) ?
                                submitApplicationRequest.getFinancialDetails().getOwnershipType().getId() : "")
                        .businessNature(Objects.nonNull(submitApplicationRequest.getFinancialDetails().getBusinessNature()) ?
                                submitApplicationRequest.getFinancialDetails().getBusinessNature().getId() : "");
            }
        }
        submitApplicationRequestBody.setData(submitApplicationRequestBodyData);

        SubHeader subHeader = getSubHeader(
                AxisEndPoints.SUBMIT_APPLICATION_NTB.getServiceRequestId(),
                AxisEndPoints.SUBMIT_APPLICATION_NTB.getServiceRequestVersion());
        NTBInputRequestSubmitApplicationRequest request = new NTBInputRequestSubmitApplicationRequest();
        request.setSubHeader(subHeader);
        request.setSubmitApplicationRequestBody(submitApplicationRequestBody);
        return request;
    }

    /**
     * Validates the provided full name against formatting and length rules.
     * Falls back to the display name if the full name is null, too long, or invalid.
     * Validation rules for the full name:
     * - Must be 2–3 words (letters only)
     * - No leading or trailing spaces
     * - At least one word must have 2 or more letters
     * - Maximum length: 30 characters
     *
     * @param fullName     The primary name to validate.
     * @param displayName  The fallback name to use if fullName is invalid.
     * @return A valid name (fullName or fallback), trimmed and truncated to 30 characters if needed.
     */
    @Nullable
    public static String validateFullNameAndFallbackIfRequired(String fullName, String displayName) {
        String result;

        if (fullName == null || fullName.length() > 30 ||
                !RegexUtils.checkStringWithMultipleRegex(Constants.AXIS_WORD_LIMIT_REGEX_LIST, fullName)) {

            if (displayName != null) {
                log.info("validateFullNameAndFallbackIfRequired - Falling back to display name");
                result = displayName.length() > 30 ? displayName.substring(0, 30).trim() : displayName;
            } else {
                result = (fullName == null) ? null : fullName.substring(0, 30).trim();
            }
        } else {
            result = fullName;
        }

        log.info("validateFullNameAndFallbackIfRequired - fullName: '{}', displayName: '{}', output: '{}'",
                fullName, displayName, result);
        return result;
    }
    
    public KycStatusReqGetKycStatusRequest kycStatusReqToAxisKycStatusEncReq(KycStatusRequest request) {
        return getKycStatusReqGetKycStatusRequest(request.getPhoneNumber(), request.getApplicationId());
    }

    public KycStatusReqGetKycStatusRequest appStatusReqToAxisKycStatusEncReq(ApplicationStatusRequest request) {
        return getKycStatusReqGetKycStatusRequest(request.getPhoneNumber(), request.getApplicationId());
    }

    @NotNull
    private KycStatusReqGetKycStatusRequest getKycStatusReqGetKycStatusRequest(String phoneNumber, String applicationId) {
        KycStatusReqGetKycStatusRequestGetKycStatusRequestBody getKycStatusRequestBody = new KycStatusReqGetKycStatusRequestGetKycStatusRequestBody();
        KycStatusReqGetKycStatusRequestGetKycStatusRequestBodyData getKycStatusRequestBodyData = new KycStatusReqGetKycStatusRequestGetKycStatusRequestBodyData();
        getKycStatusRequestBodyData.setPhoneNumber(filterPhoneNo(phoneNumber));
        getKycStatusRequestBodyData.setApplicationId(applicationId);
        getKycStatusRequestBody.setData(getKycStatusRequestBodyData);

        SubHeader subHeader = getSubHeader(AxisEndPoints.GET_KYC_STATUS.getServiceRequestId(), AxisEndPoints.GET_KYC_STATUS.getServiceRequestVersion());
        KycStatusReqGetKycStatusRequest req = new KycStatusReqGetKycStatusRequest();
        req.setGetKycStatusRequestBody(getKycStatusRequestBody);
        req.setSubHeader(subHeader);
        return req;
    }

    public KycAppStatusResponse axisKycAppStatusResToKycAppStatusRes(GetAppStatusOutput appStatusRes, KycStatusRes kycStatusRes) {
        return KycAppStatusResponse.builder()
                .appStatusResponse(axisApplicationStatusToApplicationStatusRes(appStatusRes))
                .kycStatusResponse(axisKycStatusToKycStatusRes(kycStatusRes))
                .build();
    }

    public KycStatusResponse axisKycStatusToKycStatusRes(KycStatusRes response) {

        KycStatusResGetKycStatusResponseGetKycStatusResponseBody responseBody = response.getGetKycStatusResponse().getGetKycStatusResponseBody();

        IncomeVerification incomeVerification = null;
        Kyc kyc = null;

        if (Objects.nonNull(responseBody.getData().getIncomeVerification())) {
            KycStatusResGetKycStatusResponseGetKycStatusResponseBodyDataIncomeVerification incomeVerificationResponse =
                    responseBody.getData().getIncomeVerification();

            StateContext onlineSubmission = Objects.nonNull(incomeVerificationResponse.getOnlineSubmission())
                    ? AxisKycStatusTransformerUtil.createOnlineSubmissionState(incomeVerificationResponse.getOnlineSubmission())
                    : null;
            StateContext offlineSubmission = Objects.nonNull(incomeVerificationResponse.getOfflineSubmission())
                    ? AxisKycStatusTransformerUtil.createOnlineSubmissionState(incomeVerificationResponse.getOfflineSubmission())
                    : null;

            State state = AxisKycStatusTransformerUtil.getStateFromResponse(incomeVerificationResponse.getState());
            String status = AxisKycStatusTransformerUtil.deriveIncomeVerificationStatus(onlineSubmission, offlineSubmission);
            String incomeGranularStatus = AxisKycStatusTransformerUtil.deriveIncomeGranularStatus(onlineSubmission, offlineSubmission);
            incomeVerification = IncomeVerification.builder()
                    .onlineSubmission(onlineSubmission)
                    .offlineSubmission(offlineSubmission)
                    .status(status)
                    .state(state)
                    .granularStatus(incomeGranularStatus)
                    .build();
        }

        if (Objects.nonNull(responseBody.getData().getKyc())) {
            KycStatusResGetKycStatusResponseGetKycStatusResponseBodyDataKyc kycResponse = responseBody.getData().getKyc();

            StateContext eKyc = Objects.nonNull(kycResponse.getEKyc())
                    ? AxisKycStatusTransformerUtil.createEkycState(kycResponse.getEKyc())
                    : null;
            StateContext offlineKyc = Objects.nonNull(kycResponse.getOfflineKyc())
                    ? AxisKycStatusTransformerUtil.createOnlineSubmissionState(kycResponse.getOfflineKyc())
                    : null;
            StateContext vkyc = Objects.nonNull(kycResponse.getVKyc())
                    ? AxisKycStatusTransformerUtil.createVkycState(kycResponse.getVKyc())
                    : null;

            State state = AxisKycStatusTransformerUtil.getStateFromResponse(kycResponse.getState());
            String status = AxisKycStatusTransformerUtil.deriveKycStatus(eKyc, vkyc, offlineKyc);
            String kycGranularStatus = AxisKycStatusTransformerUtil.deriveKycGranularStatus(eKyc, vkyc, offlineKyc);
            kyc = Kyc.builder()
                    .eKyc(eKyc)
                    .offlineKyc(offlineKyc)
                    .state(state)
                    .status(status)
                    .granularStatus(kycGranularStatus)
                    .vkyc(vkyc)
                    .build();
        }

        Pair<State, String> stateStatusPair = AxisKycStatusTransformerUtil.deriveOverallKycState(incomeVerification, kyc);
        String granularStatus = AxisKycStatusTransformerUtil.deriveOverallGranularStatus(incomeVerification, kyc);
        return KycStatusResponse.builder()
                .incomeVerification(incomeVerification)
                .incomeVerificationRequired(Objects.nonNull(incomeVerification))
                .state(stateStatusPair.getKey())
                .status(stateStatusPair.getValue())
                .granularStatus(granularStatus)
                .kyc(kyc)
                .build();

    }

    public PanCkycDetailsReqGetPanCkycDetailsRequest panCkycDtlsToAxisPanCkycDltsReq(PanCkycDetailsRequest request) throws CbcException {
        PanCkycDetailsReqGetPanCkycDetailsRequestGetPanCkycDetailsRequestBody getPanCkycDetailsRequestBody = new PanCkycDetailsReqGetPanCkycDetailsRequestGetPanCkycDetailsRequestBody();
        PanCkycDetailsReqGetPanCkycDetailsRequestGetPanCkycDetailsRequestBodyData getPanCkycDetailsRequestBodyData = new PanCkycDetailsReqGetPanCkycDetailsRequestGetPanCkycDetailsRequestBodyData();
        getPanCkycDetailsRequestBodyData.setPhoneNumber(filterPhoneNo(request.getPhoneNumber()));
        getPanCkycDetailsRequestBodyData.setPan(decryptData(request.getPan()));
        getPanCkycDetailsRequestBodyData.setDob(decryptData(request.getDob()));
        getPanCkycDetailsRequestBodyData.setCheckCKYC(request.getCheckCKYC());
        getPanCkycDetailsRequestBodyData.setTokenValue(request.getTokenValue());
        getPanCkycDetailsRequestBodyData.setTokenReferenceId(request.getTokenReferenceId());
        getPanCkycDetailsRequestBody.setData(getPanCkycDetailsRequestBodyData);


        SubHeader subHeader = getSubHeader(AxisEndPoints.GET_PAN_CKYC_DETAILS.getServiceRequestId(), AxisEndPoints.GET_PAN_CKYC_DETAILS.getServiceRequestVersion());
        PanCkycDetailsReqGetPanCkycDetailsRequest req = new PanCkycDetailsReqGetPanCkycDetailsRequest();
        req.setSubHeader(subHeader);
        req.setGetPanCkycDetailsRequestBody(getPanCkycDetailsRequestBody);
        return req;
    }

    public PanCkycDetailsResponse axisPanCkycDtlsToPanCkycDtlsRes(PanCkycDetailsRes response) {

        PanCkycDetailsResGetPanCkycDetailsResponseGetPanCkycDetailsResponseBody responseBody = response.getGetPanCkycDetailsResponse().getGetPanCkycDetailsResponseBody();
        CustomerDetails customerDetails = CustomerDetails.builder()
                .motherPrefix(responseBody.getData().getCustomerDetails().getMotherPrefix())
                .gender(responseBody.getData().getCustomerDetails().getGender())
                .maidenLName(responseBody.getData().getCustomerDetails().getMaidenLName())
                .maidenMName(responseBody.getData().getCustomerDetails().getMaidenMName())
                .corresCity(responseBody.getData().getCustomerDetails().getCorresCity())
                .corresDist(responseBody.getData().getCustomerDetails().getCorresDist())
                .fatherPrefix(responseBody.getData().getCustomerDetails().getFatherPrefix())
                .fatherFName(responseBody.getData().getCustomerDetails().getFatherFName())
                .motherFullName(responseBody.getData().getCustomerDetails().getMotherFullName())
                .maidenFName(responseBody.getData().getCustomerDetails().getMaidenFName())
                .motherFName(responseBody.getData().getCustomerDetails().getMotherFName())
                .motherMName(responseBody.getData().getCustomerDetails().getMotherMName())
                .corresPOA(responseBody.getData().getCustomerDetails().getCorresPOA())
                .fullName(responseBody.getData().getCustomerDetails().getFullName())
                .maidenPrefix(responseBody.getData().getCustomerDetails().getMaidenPrefix())
                .corresCountry(responseBody.getData().getCustomerDetails().getCorresCountry())
                .motherLName(responseBody.getData().getCustomerDetails().getMotherLName())
                .fatherMName(responseBody.getData().getCustomerDetails().getFatherMName())
                .corresState(responseBody.getData().getCustomerDetails().getCorresState())
                .dob(responseBody.getData().getCustomerDetails().getDob())
                .corresPin(responseBody.getData().getCustomerDetails().getCorresPin())
                .corresLine1(responseBody.getData().getCustomerDetails().getCorresLine1())
                .fatherLName(responseBody.getData().getCustomerDetails().getFatherLName())
                .fatherFullName(responseBody.getData().getCustomerDetails().getFatherFullName())
                .corresLine3(responseBody.getData().getCustomerDetails().getCorresLine3())
                .corresLine2(responseBody.getData().getCustomerDetails().getCorresLine2())
                .build();
        return PanCkycDetailsResponse.builder()
                .panVerification(responseBody.getData().getPanVerification())
                .customerDetails(customerDetails)
                .build();
    }


    public InitiateKycReqInitiateKycRequest initiateKycToAxisInitiateKycReq(InitiateKycRequest request) {
        InitiateKycReqInitiateKycRequestInitiateKycRequestBody initiateKycRequestBody = new InitiateKycReqInitiateKycRequestInitiateKycRequestBody();
        InitiateKycReqInitiateKycRequestInitiateKycRequestBodyData initiateKycRequestBodyData = new InitiateKycReqInitiateKycRequestInitiateKycRequestBodyData();
        initiateKycRequestBodyData.setPhoneNumber(filterPhoneNo(request.getPhoneNumber()));
        initiateKycRequestBodyData.setApplicationId(request.getApplicationId());
        initiateKycRequestBodyData.setCallbackUrl(smAxisCbcConfig.getSmKycCallbackUrl());
        initiateKycRequestBody.setData(initiateKycRequestBodyData);


        SubHeader subHeader = getSubHeader(AxisEndPoints.INITIATE_KYC.getServiceRequestId(), AxisEndPoints.INITIATE_KYC.getServiceRequestVersion());
        InitiateKycReqInitiateKycRequest req = new InitiateKycReqInitiateKycRequest();
        req.setSubHeader(subHeader);
        req.setInitiateKycRequestBody(initiateKycRequestBody);
        return req;
    }

    public PandoraResponseWrapper<WebviewDetailsResponse> axisIntiateKycToInitiateKycRes(InitiateKycRes response) {

        InitiateKycResInitiateKycResponseInitiateKycResponseBody responseBody = response.getInitiateKycResponse().getInitiateKycResponseBody();
        WebviewDetailsResponse webviewDetailsResponse = WebviewDetailsResponse.builder()
                .webviewUrl(smAxisCbcConfig.getKycRedirectionBaseUrl() + "?authCode=" + responseBody.getData().getAuthCode())
                .expirationTime(Objects.nonNull(responseBody.getData().getExpirationTime())?responseBody.getData().getExpirationTime().getEpochMillis():null)
                .build();
        return PandoraResponseWrapper.<WebviewDetailsResponse>builder()
                .response(webviewDetailsResponse)
                .build();
    }

    private com.axis.model.Address addressesReqTransformer(Address address) throws CbcException {
        com.axis.model.Address addressReq = new com.axis.model.Address();
        return addressReq.addressLine1(StringUtils.trimToNull(address.getAddress1()))
                .addressLine2(StringUtils.trimToNull(address.getAddress2()))
                .addressLine3(StringUtils.trimToNull(address.getAddress3()))
                .isCommunicationAddress(address.isCommunicationAddress())
                .addresstype(address.getAddressType().getValue())
                .city(StringUtils.trimToNull(address.getCity()))
                .landmark(StringUtils.trimToNull(address.getLandmark()))
                .postalCode(address.getPinCode())
                .state(address.getState());

    }

    private String getStandardizationSourceofFunds(SmEmploymentType employmentType, BusinessNature businessNature) {
        if (employmentType == SmEmploymentType.SELF_EMPLOYED) {
            switch (businessNature) {
                case ACTIVITIES_ALLIED_TO_AGRICULTURE:
                    return "Agriculture";
                case STOCK_BROKERS:
                    return "Investment Income";
                default:
                    return "Business Income";
            }
        } else if (employmentType == SmEmploymentType.SALARIED)
            return "Salary";
        return "";
    }

    private String getIncomeSource(SmEmploymentType employmentType, BusinessNature businessNature) {
        if (employmentType == SmEmploymentType.SELF_EMPLOYED) {
            switch (businessNature) {
                case ACTIVITIES_ALLIED_TO_AGRICULTURE:
                    return "AGRICULTURE";
                case STOCK_BROKERS:
                    return "INVESTMENT_INCOME";
                default:
                    return "BUSINESS_INCOME";
            }
        } else if (employmentType == SmEmploymentType.SALARIED)
            return "SALARY";
        return "";
    }

    private String[] splitName(String fullName) {
        String[] names = fullName.trim().split("\\s+", 2);
        if (names.length == 2) return names;
        else {
            return new String[]{names[0], ""};
        }
    }

    private String decryptData(String data) throws CbcException {
        if (!featureFlagBucket.getBoolean("cbc.piiData.isEncrypted")) {
            return data;
        }
        if (StringUtils.isEmpty(data)) {
            return null;
        }
        try{
            String encryptionKey = featureFlagBucket.getString("cbc.piiData.encryptionKey");
            return formDataDecryption.decrypt(data, encryptionKey);
        } catch (Exception e){
            log.error("Failed to decrypt the data: ", e);
            throw new CbcException(null, e.getMessage());
        }
    }
}
