package com.flipkart.fintech.pandora.service.core.cbc.utils;

import lombok.extern.slf4j.Slf4j;
import me.xdrop.fuzzywuzzy.FuzzySearch;
import org.apache.commons.text.similarity.LevenshteinDistance;

import javax.inject.Singleton;


@Slf4j
@Singleton
public class NameMatcherUtil {

    private final LevenshteinDistance levenshteinDistance = LevenshteinDistance.getDefaultInstance();

    public static int matchScore(String name1, String name2) {
        name1 = standardizeName(name1);
        name2 = standardizeName(name2);
        return FuzzySearch.tokenSetRatio(name1, name2);
    }

    public static boolean invokeFuzzyMatch(String name1, String name2, double threshold) {
        int fuzzyMatchScore = matchScore(name1, name2);
        log.info("Fuzzy token set ratio match score for {} and {} is {}. The threshold passed is {}",
                name1, name2, fuzzyMatchScore, threshold);
        return fuzzyMatchScore >= threshold;
    }

    private static String standardizeName(String name) {
        name = name.trim();
        name = name.toLowerCase();
        name = name.replace(" ", "");
        return name;
    }

}
