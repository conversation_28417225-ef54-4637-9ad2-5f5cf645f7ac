package com.flipkart.fintech.pandora.service.core.debuggingtool;

import com.flipkart.affordability.bnpl.tijori.api.model.debugging.DebuggableTransaction;
import com.flipkart.affordability.bnpl.tijori.api.model.debugging.user.UserDetails;
import com.flipkart.affordability.bnpl.tijori.api.response.LenderTransactionResponse;
import com.flipkart.affordability.bnpl.tijori.client.TijoriClientException;

import java.util.List;

public interface DebuggingService {

    DebuggableTransaction getTransactionByMerchantTxnId(String merchantTxnId) throws TijoriClientException;

    DebuggableTransaction getTransactionByLenderTransactionId(String lendTxnId) throws TijoriClientException;

    DebuggableTransaction getTransactionByBankTransactionId(String bankTxnId) throws TijoriClientException;

    List<UserDetails> getUserDetails(String lan, String crn) throws TijoriClientException;

    LenderTransactionResponse getTransactionByLanAndCrn(String lan, String crn) throws TijoriClientException;

    byte[] getTransactionByLanAndCrnDownload(String lan, String crn) throws Exception;
}
