package com.flipkart.fintech.pandora.service.core.ekyc;

import com.flipkart.fintech.pandora.api.model.common.STATUS;
import com.flipkart.fintech.pandora.api.model.request.ekyc.EkycSendOtpRequest;
import com.flipkart.fintech.pandora.api.model.request.ekyc.EkycVerifyOtpRequest;
import com.flipkart.fintech.pandora.api.model.request.ekyc.SendKycDocumentRequest;
import com.flipkart.fintech.pandora.api.model.response.ekyc.EkycSendOtpResponse;
import com.flipkart.fintech.pandora.api.model.response.ekyc.EkycVerifyOtpResponse;
import com.flipkart.fintech.pandora.api.model.response.ekyc.KycDetailsResponse;
import com.flipkart.fintech.pandora.service.exception.PandoraException;
import com.flipkart.fintech.security.aes.AESService;
import com.flipkart.fintech.security.gibraltar.GibraltarService;
import com.flipkart.fintech.security.gibraltar.GibraltarServiceException;

import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import javax.ws.rs.core.Response;
import java.util.Base64;

/**
 * Created by aniruddha.sharma on 30/11/17.
 */
public abstract class EkycManager {

    public String getDecryptedData(SecretKey symmetricKey, String encryptedData){
        String decryptedData;
        try {
            byte[] byteData = AESService.decrypt(symmetricKey, Base64.getDecoder().decode(encryptedData.getBytes()));
            decryptedData = new String(byteData);
        } catch (Exception e) {
            throw new PandoraException(Response.Status.INTERNAL_SERVER_ERROR, STATUS.CRYPTO_ERROR.name());
        }
        return decryptedData;
    }

    public void setGibraltarService() {
    }

}
