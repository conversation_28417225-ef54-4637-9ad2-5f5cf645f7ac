package com.flipkart.fintech.pandora.service.core.encryption;

import com.aerospike.client.lua.LuaAerospikeLib;
import com.flipkart.kloud.config.DynamicBucket;
import lombok.extern.slf4j.Slf4j;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.xml.bind.DatatypeConverter;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.InvalidKeyException;
import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Base64;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
public class FixeraDataEncryptionImpl implements FixeraDataEncryption{
    private DynamicBucket dynamicBucket;

    public FixeraDataEncryptionImpl(DynamicBucket dynamicBucket) {
        this.dynamicBucket = dynamicBucket;
    }

    @Override
    public String encryptData(String data) {
        try {
            String key = dynamicBucket.getString("fixeraEncryptionKey");
            String cleanedKey = key.replaceAll("[^A-Za-z0-9+/=]", "");
            byte[] decodedKey = Base64.getDecoder().decode(cleanedKey);
            PKCS8EncodedKeySpec spec  = new PKCS8EncodedKeySpec(decodedKey);
            KeyFactory kf = KeyFactory.getInstance("RSA");
            PrivateKey privateKey = kf.generatePrivate(spec);
            Cipher cipher = Cipher.getInstance("RSA");
            cipher.init(Cipher.ENCRYPT_MODE,privateKey);
            byte[] encryptedBytes = cipher.doFinal(data.getBytes());
            return Base64.getEncoder().encodeToString(encryptedBytes);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        } catch (InvalidKeySpecException e) {
           log.error("Error : {}",e.getMessage());
            throw new RuntimeException(e);
        } catch (NoSuchPaddingException e) {
            throw new RuntimeException(e);
        } catch (IllegalBlockSizeException e) {
            throw new RuntimeException(e);
        } catch (BadPaddingException e) {
            throw new RuntimeException(e);
        } catch (InvalidKeyException e) {
            throw new RuntimeException(e);
        }
    }
}
