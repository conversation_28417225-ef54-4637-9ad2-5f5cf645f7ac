package com.flipkart.fintech.pandora.service.core.flowcontrol.creditassessment;

import com.flipkart.fintech.pandora.api.model.common.STATUS;
import com.flipkart.fintech.pandora.api.model.response.BaseResponse;
import com.flipkart.fintech.pandora.api.model.response.onboarding.v2.CreditAssessmentInfoResponse;
import com.flipkart.fintech.pandora.service.core.flowcontrol.Command;
import com.flipkart.fintech.pandora.service.core.flowcontrol.Target;
import com.flipkart.fintech.pandora.service.core.flowcontrol.TargetType;
import com.flipkart.fintech.pandora.service.utils.Constants;

/**
 * <AUTHOR> H Adavi
 */
public class RejectCreditline extends Command {
	
	private BaseResponse baseResponse;
	private CreditAssessmentTarget creditAssessmentTarget;
	
	@Override
	public void execute(Target target) {
		
		if(TargetType.CREDIT_ASSESSMENT.equals(target.getTargetType())){
			
			CreditAssessmentTarget creditAssessmentTarget = (CreditAssessmentTarget) target;
			
			CreditAssessmentInfoResponse creditAssessmentInfoResponse = creditAssessmentTarget.getCreditAssessmentInfoResponse();
			
			this.baseResponse = creditAssessmentInfoResponse.getBaseResponse();
			
			if(null == this.baseResponse){
				creditAssessmentInfoResponse.setBaseResponse(new BaseResponse());
			}
			creditAssessmentInfoResponse.getBaseResponse().setStatus(STATUS.REJECTED);
			creditAssessmentInfoResponse.setApprovalDecision(Constants.DECISION_DECLINED);
			
			this.creditAssessmentTarget = creditAssessmentTarget;
		
		} else {
			return;
		}
	}
	
	@Override
	public void undo() {
	
		if(null != creditAssessmentTarget && null != baseResponse){
			
			BaseResponse tempBaseResponse = creditAssessmentTarget.getCreditAssessmentInfoResponse().getBaseResponse();
			creditAssessmentTarget.getCreditAssessmentInfoResponse().setBaseResponse(baseResponse);
			this.baseResponse = tempBaseResponse;
		}
	}
	
	@Override
	public void redo() {
		undo();
	}
}
