package com.flipkart.fintech.pandora.service.core.lifeCycleManagement;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.pandora.api.model.request.onboarding.AxisApiConfigurationKeys;
import com.flipkart.fintech.pandora.service.application.configuration.AxisCbcConfiguration;
import com.flipkart.fintech.pandora.service.core.UserOnboarding.AbstractClient;
import com.flipkart.fintech.pandora.service.external.dataModels.axisLifeCycleMangement.request.encrypted.*;
import com.flipkart.fintech.pandora.service.external.dataModels.axisLifeCycleMangement.response.encrypted.*;
import com.flipkart.fintech.pandora.service.external.dataModels.axisUserOnboarding.response.BaseHttpResponse;
import com.flipkart.fintech.pandora.service.utils.Constants;
import com.flipkart.payments.utils.JsonUtils;
import com.google.inject.Inject;
import com.google.inject.name.Named;
import org.apache.http.Header;
import org.apache.http.message.BasicHeader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.validation.constraints.NotNull;
import javax.ws.rs.client.Client;
import javax.ws.rs.client.Entity;
import javax.ws.rs.core.HttpHeaders;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.MultivaluedHashMap;
import javax.ws.rs.core.MultivaluedMap;

public class AxisCardLifeCycleMangementClient extends AbstractClient {

    private final AxisCbcConfiguration clientConfig;

    private static Logger log = LoggerFactory.getLogger(AxisCardLifeCycleMangementClient.class);

    @Inject
    public AxisCardLifeCycleMangementClient(@NotNull ObjectMapper objectMapper, @Named("axis") Client client,
                                            AxisCbcConfiguration clientConfig) {
        super(objectMapper, client.target(clientConfig.getUrl()));
        this.clientConfig = clientConfig;
    }

    public LCMGenerateAuthOTPResponse generateAuthOtp(LCMGenerateAuthOTPRequest lcmGenerateAuthOTPRequest){
        LCMGenerateAuthOTPResponse lcmGenerateAuthOTPResponse = getPostResponse(String.format(
                clientConfig.getAxisCbcApiConfigurationsMap().get(AxisApiConfigurationKeys.LCM_GENERATE_AUTH_OTP.getKey()).getApiPath()),
                Entity.json(lcmGenerateAuthOTPRequest), getHeaders(), LCMGenerateAuthOTPResponse.class);
        return lcmGenerateAuthOTPResponse;
    }

    public LCMValidateAuthOTPResponse validateAuthOtp(LCMValidateAuthOTPRequest lcmValidateAuthOTPRequest, String validationToken){
        LCMValidateAuthOTPResponse lcmValidateAuthOTPResponse = getPostResponse(String.format(
                clientConfig.getAxisCbcApiConfigurationsMap().get(AxisApiConfigurationKeys.LCM_VALIDATE_AUTH_OTP.getKey()).getApiPath()),
                Entity.json(lcmValidateAuthOTPRequest), getHeaders(validationToken), LCMValidateAuthOTPResponse.class);
        return lcmValidateAuthOTPResponse;
    }

    public void sendPlusMembershipEvent(UpdateRewardProfileRequest request) {
        try {
            log.info(String.format(clientConfig.getAxisCbcApiConfigurationsMap().get(AxisApiConfigurationKeys.PLUS_MEMBERSHIP_UPDATE.getKey()).getApiPath()));
            log.info(JsonUtils.getObjectMapper().writeValueAsString(request));
            log.info(JsonUtils.getObjectMapper().writeValueAsString(getHeaders()));
        } catch (Exception e) {}
        getPostResponse(String.format(
                clientConfig.getAxisCbcApiConfigurationsMap().get(AxisApiConfigurationKeys.PLUS_MEMBERSHIP_UPDATE.getKey()).getApiPath()),
                Entity.json(request), getHeaders(), BaseHttpResponse.class);
    }

    public void sendPlusMembershipEvent2(Object request) {
        try {
            log.info(String.format(clientConfig.getAxisCbcApiConfigurationsMap().get(AxisApiConfigurationKeys.PLUS_MEMBERSHIP_UPDATE.getKey()).getApiPath()));
            log.info(JsonUtils.getObjectMapper().writeValueAsString(request));
            log.info(JsonUtils.getObjectMapper().writeValueAsString(getHeaders()));
        } catch (Exception e) {}
        getPostResponse(String.format(
                clientConfig.getAxisCbcApiConfigurationsMap().get(AxisApiConfigurationKeys.PLUS_MEMBERSHIP_UPDATE.getKey()).getApiPath()),
                Entity.json(request), getHeaders(), BaseHttpResponse.class);
    }

    public LCMSendEmailResponse sendEmail(LCMSendEmailRequest lcmSendEmailRequest, String validationToken){
        LCMSendEmailResponse lcmSendEmailResponse = getPostResponse(String.format(
                clientConfig.getAxisCbcApiConfigurationsMap().get(AxisApiConfigurationKeys.LCM_SEND_EMAIL.getKey()).getApiPath()),
                Entity.json(lcmSendEmailRequest), getHeaders(validationToken), LCMSendEmailResponse.class);
        return lcmSendEmailResponse;
    }

    public LCMGetCardDetailsResponse getCardDetails(LCMGetCardDetailsRequest lcmGetCardDetailsRequest, String validationToken, String axisConfigKey){
        LCMGetCardDetailsResponse lcmGetCardDetailsResponse = getPostResponse(String.format(
                clientConfig.getAxisCbcApiConfigurationsMap().get(axisConfigKey).getApiPath()),
                Entity.json(lcmGetCardDetailsRequest), getHeaders(validationToken), LCMGetCardDetailsResponse.class);
        return lcmGetCardDetailsResponse;
    }

    public SCPostingAckResponse getSupercoinUpdateDetails(SCPostingAckRequest SCPostingAckRequest, String validationToken, String axisConfigKey){
        try {
            log.info( "SCPostingAckRequest -> {} {}", JsonUtils.getObjectMapper().writeValueAsString(SCPostingAckRequest), JsonUtils.getObjectMapper().writeValueAsString(getHeaders(validationToken)));
        } catch (Exception e) {}
        SCPostingAckResponse SCPostingAckResponse = getPostResponse(String.format(
                        clientConfig.getAxisCbcApiConfigurationsMap().get(axisConfigKey).getApiPath()),
                Entity.json(SCPostingAckRequest), getHeaders(validationToken), SCPostingAckResponse.class);
        return SCPostingAckResponse;
    }

    public LCMUpdateCardStatusResponse blockCard(LCMUpdateCardStatusRequest lcmUpdateCardStatusRequest, String validationToken){
        LCMUpdateCardStatusResponse lcmUpdateCardStatusResponse = getPostResponse(String.format(
                clientConfig.getAxisCbcApiConfigurationsMap().get(AxisApiConfigurationKeys.BLOCK_CARD.getKey()).getApiPath()),
                Entity.json(lcmUpdateCardStatusRequest), getHeaders(validationToken), LCMUpdateCardStatusResponse.class);
        return lcmUpdateCardStatusResponse;
    }

    public ACPUpdateResponse updateACP(ACPUpdateRequest acpUpdateRequest, String validationToken) {

        ACPUpdateResponse acpUpdateResponse = getPostResponse(String.format(
                clientConfig.getAxisCbcApiConfigurationsMap().get(AxisApiConfigurationKeys.ACP_UPDATE.getKey()).getApiPath()),
                Entity.json(acpUpdateRequest), getHeaders(validationToken), ACPUpdateResponse.class);
        return acpUpdateResponse;
    }

    public DeliveryCodeValidationResponse validateDeliveryCode(DeliveryCodeValidationRequest deliveryCodeValidationRequest, String validationToken) {

        DeliveryCodeValidationResponse deliveryCodeResponse = getPostResponse(String.format(clientConfig.getAxisCbcApiConfigurationsMap().get(AxisApiConfigurationKeys.VALIDATE_DELIVERY_CODE.getKey()).getApiPath()),
                Entity.json(deliveryCodeValidationRequest), getHeaders(validationToken), DeliveryCodeValidationResponse.class);
        return deliveryCodeResponse;
    }

    public ACPInquiryResponse inquireACP(ACPInquiryRequest acpInquiryRequest, String validationToken) {

        ACPInquiryResponse acpInquiryResponse = getPostResponse(String.format(
                clientConfig.getAxisCbcApiConfigurationsMap().get(AxisApiConfigurationKeys.ACP_INQUIRY.getKey()).getApiPath()),
                Entity.json(acpInquiryRequest), getHeaders(validationToken), ACPInquiryResponse.class);
        return acpInquiryResponse;
    }


    public CreditLimitIncrementResponse incrementCreditLimit(CreditLimitIncrementRequest creditLimitIncrementRequest, String validationToken) {

        CreditLimitIncrementResponse creditLimitIncrementResponse = getPostResponse(String.format(
                        clientConfig.getAxisCbcApiConfigurationsMap().get(AxisApiConfigurationKeys.PROCESS_INCREMENT_CREDIT_LIMIT.getKey()).getApiPath()),
                Entity.json(creditLimitIncrementRequest), getHeaders(validationToken), CreditLimitIncrementResponse.class);
        return creditLimitIncrementResponse;
    }

    public CheckCreditLimitEligibilityResponse checkCreditLimitEligibility(CheckCreditLimitEligibilityRequest creditLimitIncrementRequest, String validationToken) {

        CheckCreditLimitEligibilityResponse creditLimitIncrementResponse = getPostResponse(String.format(
                        clientConfig.getAxisCbcApiConfigurationsMap().get(AxisApiConfigurationKeys.CHECK_CREDIT_LIMIT_ELIGIBILITY.getKey()).getApiPath()),
                Entity.json(creditLimitIncrementRequest), getHeaders(validationToken), CheckCreditLimitEligibilityResponse.class);
        return creditLimitIncrementResponse;
    }

    public MultivaluedMap<String, Object> getHeaders() {
        MultivaluedMap<String, Object> map = new MultivaluedHashMap<>();
        map.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON);
        map.add(Constants.AxisCbc.X_IBM_CLIENT_ID, clientConfig.getClient());
        map.add(Constants.AxisCbc.X_IBM_CLIENT_SECRET, clientConfig.getSecretKey());
        map.add(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON);
        return map;
    }

    public MultivaluedMap<String, Object> getHeaders(String validationToken) {
        MultivaluedMap<String, Object> map = new MultivaluedHashMap<>();
        String value = "Bearer " + validationToken;
        map.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON);
        map.add(Constants.AxisCbc.X_IBM_CLIENT_ID, clientConfig.getClient());
        map.add(Constants.AxisCbc.X_IBM_CLIENT_SECRET, clientConfig.getSecretKey());
        map.add(Constants.AxisCbc.AUTHORIZATION, value);
        map.add(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON);
        return map;
    }

    private Header[] getHeadersArray() {
        Header headers[] = new Header[3];
        Header header = new BasicHeader(HttpHeaders.CONTENT_TYPE, "application/json");
        Header clientId = new BasicHeader(Constants.AxisCbc.X_IBM_CLIENT_ID, clientConfig.getClient());
        Header clientSecret = new BasicHeader(Constants.AxisCbc.X_IBM_CLIENT_SECRET, clientConfig.getSecretKey());
        headers[0] = header;
        headers[1] = clientId;
        headers[3] = clientSecret;
        return headers;
    }

    public   <T extends BaseHttpResponse> T setHttpResponseCode(T childClass, String response, String status) {
        BaseHttpResponse baseHttpResponse = new BaseHttpResponse();
        log.info("Inside the deserializaton Of Base Http Reponse {} ",response);
        try {
            org.codehaus.jackson.map.ObjectMapper objectMapper = new org.codehaus.jackson.map.ObjectMapper();
            baseHttpResponse = objectMapper.readValue(response, BaseHttpResponse.class);
            childClass.setHttpCode(baseHttpResponse.getHttpCode());
            childClass.setErrorCode(baseHttpResponse.getErrorCode());
            childClass.setHttpMessage(baseHttpResponse.getHttpMessage());
            childClass.setMoreInformation(baseHttpResponse.getMoreInformation());
            childClass.setErrorDescription(baseHttpResponse.getErrorDescription());
        }
        catch (Exception e) {
            childClass.setHttpCode(500);
            childClass.setHttpMessage(status);
            childClass.setMoreInformation("Exception occured While Deserializing" + e.getMessage());
        }

        return childClass;
    }

    public ConvertTxnToInstalmentResponse convertTxnToInstalment(ConvertTxnToInstalmentRequest lCMConvertTxnToInstalmentRequest, String validationToken, String axisConfigKey){
        ConvertTxnToInstalmentResponse lCMConvertTxnToInstalmentResponse = new ConvertTxnToInstalmentResponse();
        try {
           log.info("Posting Request: {}  : {}",clientConfig.getAxisCbcApiConfigurationsMap().get(AxisApiConfigurationKeys.LCM_CONVERT_TXN_TO_INSTALMENT.getKey()).getApiPath(), lCMConvertTxnToInstalmentRequest);
            lCMConvertTxnToInstalmentResponse = getPostResponse(String.format(
                           clientConfig.getAxisCbcApiConfigurationsMap().get(AxisApiConfigurationKeys.LCM_CONVERT_TXN_TO_INSTALMENT.getKey()).getApiPath()),
                   Entity.json(lCMConvertTxnToInstalmentRequest), getHeaders(validationToken), ConvertTxnToInstalmentResponse.class);
          }
       catch(Exception e){
         log.error("Error in convertTxnToInstalment {}", e.getMessage());
       }
        return lCMConvertTxnToInstalmentResponse;
    }
}
