package com.flipkart.fintech.pandora.service.core.payback;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.affordability.khaata.client.KhaataClientException;
import com.flipkart.affordability.khaata.model.product.Product;
import com.flipkart.affordability.khaata.model.transaction.response.v2.RefundResponse;
import com.flipkart.fintech.pandora.api.model.common.STATUS;
import com.flipkart.fintech.pandora.api.model.request.payback.PaybackForwardRequest;
import com.flipkart.fintech.pandora.api.model.request.payback.PaybackRefundRequest;
import com.flipkart.fintech.pandora.api.model.response.payback.ForwardPaybackTransactions;
import com.flipkart.fintech.pandora.api.model.response.payback.PaybackForwardTransactionsResponse;
import com.flipkart.fintech.pandora.api.model.response.payback.PaybackRefundResponse;
import com.flipkart.fintech.pandora.service.client.kissht.client.payback.KisshtPaybackServiceClient;
import com.flipkart.fintech.pandora.service.entity.KisshtMapper;
import com.flipkart.fintech.pandora.service.exception.PandoraException;
import com.flipkart.affordability.khaata.client.KhaataClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.inject.Inject;
import javax.ws.rs.core.Response;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by aniruddha.sharma on 26/12/17.
 */
public class KisshtPaybackManager implements PaybackManager{
    private static final Logger logger = LoggerFactory.getLogger(KisshtPaybackManager.class);
    private final KisshtPaybackServiceClient kisshtPaybackServiceClient;
    private final KhaataClient khaataClient;
    private final ObjectMapper objectMapper;

    @Inject
    public KisshtPaybackManager(KisshtPaybackServiceClient kisshtPaybackServiceClient,
                                KhaataClient khaataClient, ObjectMapper objectMapper) {
        this.kisshtPaybackServiceClient = kisshtPaybackServiceClient;
        this.khaataClient = khaataClient;
        this.objectMapper = objectMapper;
    }

    @Override
    public PaybackForwardTransactionsResponse paybackTransaction(PaybackForwardRequest paybackForwardRequest) {
        PaybackForwardTransactionsResponse paybackForwardTransactionsResponse = new PaybackForwardTransactionsResponse();

        com.flipkart.fintech.pandora.service.client.kissht.request.PaybackForwardRequest paybackForwardRequestClient
                = new com.flipkart.fintech.pandora.service.client.kissht.request.PaybackForwardRequest();

        paybackForwardRequestClient.setAmount(paybackForwardRequest.getAmount());
        paybackForwardRequestClient.setPaymentDate(paybackForwardRequest.getPaymentDate());
        paybackForwardRequestClient.setPgIdentifier(paybackForwardRequest.getPgIdentifier());
        paybackForwardRequestClient.setUserId(paybackForwardRequest.getUserId());

        com.flipkart.fintech.pandora.service.client.kissht.response.PaybackForwardTransactionsResponse
                paybackForwardTransactionsResponseClient = kisshtPaybackServiceClient.paybackTransaction
                (paybackForwardRequestClient);
        if(null == paybackForwardTransactionsResponseClient){
            logger.error("Some error occured while contacting Kissht");
            throw new PandoraException(Response.Status.INTERNAL_SERVER_ERROR, STATUS.FAILURE.name());
        }
        paybackForwardTransactionsResponse.setInfo(paybackForwardTransactionsResponseClient.getInfo());
        if(!paybackForwardTransactionsResponseClient.isSuccess()){
            paybackForwardTransactionsResponse.setStatus(KisshtMapper.getKisshtStatus
                    (paybackForwardTransactionsResponseClient.getErrorCode()));
            logger.info("Response is {}", paybackForwardTransactionsResponse);
            throw new PandoraException(Response.Status.BAD_REQUEST, paybackForwardTransactionsResponse);
        }
        else{
            paybackForwardTransactionsResponse.setStatus(STATUS.SUCCESS);
            List<ForwardPaybackTransactions> forwardPaybackTransactionsList = new
                    ArrayList<>();
            for(com.flipkart.fintech.pandora.service.client.kissht.response.ForwardPaybackTransactions
                forwardPaybackTransactionsClient : paybackForwardTransactionsResponseClient
                    .getFowardPaybackTransactionsList()){
                ForwardPaybackTransactions forwardPaybackTransactions = new ForwardPaybackTransactions();

                forwardPaybackTransactions.setTransactionAmount(forwardPaybackTransactionsClient.getTransactionAmount());
                forwardPaybackTransactions.setTransactionId(forwardPaybackTransactionsClient.getTransactionId());
                forwardPaybackTransactions.setTransactionSubType(forwardPaybackTransactionsClient.getTransactionSubType());

                forwardPaybackTransactionsList.add(forwardPaybackTransactions);
            }
            paybackForwardTransactionsResponse.setForwardPaybackTransactionsList(forwardPaybackTransactionsList);
        }
        logger.info("Response is {}", paybackForwardTransactionsResponse);
        return paybackForwardTransactionsResponse;
    }

    @Override
    public PaybackRefundResponse refundTransaction(PaybackRefundRequest paybackRefundRequest) {
        PaybackRefundResponse paybackRefundResponse = new PaybackRefundResponse();

        com.flipkart.affordability.khaata.model.transaction.request.PaybackRefundRequest paybackRefundRequestClient =
            new com.flipkart.affordability.khaata.model.transaction.request.PaybackRefundRequest();
        paybackRefundRequestClient.setAmount(paybackRefundRequest.getAmount().doubleValue());
        paybackRefundRequestClient.setLenderTxnId(paybackRefundRequest.getLenderTxnId());
        paybackRefundRequestClient.setRefundOrderId(paybackRefundRequest.getRefundOrderId());
        paybackRefundRequestClient.setPaybackTransactionId(paybackRefundRequest.getPgIdentifier());

        try {
            RefundResponse reversePayback=khaataClient.reversePaybackRequest(paybackRefundRequestClient, Product.EFA);
            logger.info("Response from Khaata for reversePayback is {}",reversePayback);
            if(reversePayback != null){
                paybackRefundResponse.setStatus(STATUS.SUCCESS);
                paybackRefundResponse.setRefundIdentifier(reversePayback.getRefundId());
            }
            else{
                paybackRefundResponse.setStatus(STATUS.FAILURE);
            }
        }
        catch (KhaataClientException e) {
            logger.error("Error from Khaata {}",e.getMessage());
            paybackRefundResponse.setStatus(STATUS.FAILURE);
        }
        return paybackRefundResponse;
    }
}
