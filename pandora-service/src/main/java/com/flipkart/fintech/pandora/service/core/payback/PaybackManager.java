package com.flipkart.fintech.pandora.service.core.payback;

import com.flipkart.fintech.pandora.api.model.request.payback.PaybackForwardRequest;
import com.flipkart.fintech.pandora.api.model.request.payback.PaybackRefundRequest;
import com.flipkart.fintech.pandora.api.model.response.payback.PaybackForwardTransactionsResponse;
import com.flipkart.fintech.pandora.api.model.response.payback.PaybackRefundResponse;

/**
 * Created by aniruddha.sharma on 26/12/17.
 */
public interface PaybackManager {
    PaybackForwardTransactionsResponse paybackTransaction(PaybackForwardRequest paybackForwardRequest);
    PaybackRefundResponse refundTransaction(PaybackRefundRequest paybackRefundRequestList);
}
