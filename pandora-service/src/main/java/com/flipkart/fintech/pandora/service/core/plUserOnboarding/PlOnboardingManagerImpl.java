package com.flipkart.fintech.pandora.service.core.plUserOnboarding;

import calm.client.shade.com.nimbusds.jose.JOSEException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.flipkart.affordability.model.response.AddressDetailResponse;
import com.flipkart.fintech.common.jwt.JWTTokenGenerator;
import com.flipkart.fintech.pandora.api.model.pl.sandbox.dto.UserInfo;
import com.flipkart.fintech.pandora.api.model.request.lenderdedupe.LenderDedupeRequest;
import com.flipkart.fintech.pandora.api.model.request.lenderdedupe.LenderDedupeResponse;
import com.flipkart.fintech.pandora.service.client.exceptions.LenderException;
import com.flipkart.fintech.pandora.service.client.pl.request.mpockket.MpockketSubmitLeadRequest;
import com.flipkart.fintech.pandora.api.model.request.plOnboarding.*;
import com.flipkart.fintech.pandora.api.model.request.plOnboarding.sandbox.v1.CreateApplicationRequest;
import com.flipkart.fintech.pandora.api.model.request.plOnboarding.sandbox.v1.GetApplicationRequest;
import com.flipkart.fintech.pandora.api.model.request.plOnboarding.sandbox.v1.GetApplicationStatusRequest;
import com.flipkart.fintech.pandora.service.client.pl.response.mpockket.MpockketGetStatusResponse;
import com.flipkart.fintech.pandora.service.client.pl.response.mpockket.MpockketSubmitLeadResponse;
import com.flipkart.fintech.pandora.api.model.response.plOnboarding.*;
import com.flipkart.fintech.pandora.service.client.auth.Scope;
import com.flipkart.fintech.pandora.service.client.configuration.SandboxLendersPlConfiguration;
import com.flipkart.fintech.pandora.service.client.encryptor.JweEncryptorDecryptor;
import com.flipkart.fintech.pandora.service.client.pl.client.MpockketClient;
import com.flipkart.fintech.pandora.service.client.pl.client.SandboxLendersPlClient;
import com.flipkart.fintech.pandora.service.client.plOnboarding.GenericLenderClient;
import com.flipkart.fintech.pandora.service.client.plOnboarding.PlHttpRequest;
import com.flipkart.fintech.pandora.service.client.plOnboarding.exceptions.InvalidJweSignatureException;
import com.flipkart.fintech.pandora.service.client.plOnboarding.request.LenderRequest;
import com.flipkart.fintech.pandora.service.client.plOnboarding.request.Step;
import com.flipkart.fintech.pandora.service.client.plOnboarding.request.WebHookRequest;
import com.flipkart.fintech.pandora.service.client.plOnboarding.response.LenderRes;
import com.flipkart.fintech.pandora.service.client.sandbox.ApiParamModel;
import com.flipkart.fintech.pandora.service.client.sandbox.v1.dto.EventsState;
import com.flipkart.fintech.pandora.service.client.sandbox.v1.request.ApplicationStateEventRequest;
import com.flipkart.fintech.pandora.service.client.sandbox.v1.response.CreateApplicationResponse;
import com.flipkart.fintech.pandora.service.client.sandbox.v1.response.GetApplicationResponse;
import com.flipkart.fintech.pandora.api.model.response.sandbox.v1.GetApplicationStatusResponse;
import com.flipkart.fintech.pandora.service.client.utils.PlEncryptionUtil;
import com.flipkart.fintech.pandora.service.core.decryption.FormDataDecryption;
import com.flipkart.fintech.pandora.service.core.plUserOnboarding.adapters.ILenderOperationAdapter;
import com.flipkart.fintech.pandora.service.core.plUserOnboarding.adapters.LenderAdapterFactory;
import com.flipkart.fintech.pandora.service.core.plUserOnboarding.exceptions.PlOnboardingInvalidParamException;
import com.flipkart.fintech.pandora.service.core.plUserOnboarding.models.*;
import com.flipkart.fintech.pandora.service.core.plUserOnboarding.utils.PlUserOnboardingUtil;
import com.flipkart.fintech.pandora.service.exception.PandoraException;
import com.flipkart.fintech.pandora.service.external.BQIngestionHelper;
import com.flipkart.fintech.pandora.service.external.ElasticSearchClient;
import com.flipkart.fintech.pandora.service.external.coreLogistics.client.CoreLogisticsClient;
import com.flipkart.fintech.pandora.service.external.coreLogistics.models.GeoLocationResponse;
import com.flipkart.fintech.pandora.service.hystrix.PandoraHystrixProperties;
import com.flipkart.fintech.pandora.service.hystrix.plUserOnboarding.InvokeLenderApiCommand;
import com.flipkart.fintech.pandora.service.transformer.UserDataTransformer;
import com.flipkart.fintech.pinaka.api.request.v6.AxisWebhooksRequest;
import com.flipkart.fintech.pinaka.api.request.v6.SandboxWebhooksRequest;
import com.flipkart.fintech.pinaka.api.request.v6.Stage;
import com.flipkart.fintech.pinaka.client.ObjectMapperUtil;
import com.flipkart.fintech.pinaka.client.PinakaClientException;
import com.flipkart.fintech.pinaka.client.v7.PinakaClientV7;
import com.flipkart.fintech.user.data.client.UserDataClient;
import com.flipkart.fintech.user.data.models.AddressData;
import com.flipkart.fintech.user.data.models.UserData;
import com.flipkart.fintech.user.data.models.enums.Merchant;
import com.flipkart.fintech.user.data.models.enums.PIIDataType;
import com.flipkart.fintech.winterfell.api.request.VariableData;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.fintech.winterfell.api.response.http.WinterfellNodeResponse;
import com.flipkart.fintech.winterfell.client.WintefellClientException;
import com.flipkart.fintech.winterfell.client.WinterfellClient;
import com.google.inject.Inject;
import com.netflix.hystrix.exception.HystrixRuntimeException;
import com.supermoney.schema.PandoraService.OfferEventV1;
import com.supermoney.schema.PandoraService.OfferDetail;
import com.flipkart.fintech.pandora.api.model.pl.sandbox.dto.enums.OfferType;
import com.flipkart.fintech.pandora.api.model.request.plOnboarding.ChargeBreakUp;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.impl.DefaultClaims;

import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONException;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.ws.rs.core.HttpHeaders;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.MultivaluedHashMap;
import javax.ws.rs.core.MultivaluedMap;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.UnrecoverableKeyException;
import java.security.cert.CertificateException;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.text.ParseException;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static com.flipkart.fintech.pandora.service.utils.Constants.SUPERMONEY;
import static com.flipkart.fintech.pandora.service.utils.MetricUtils.pushMetricForSmAccountIdPresence;

public class PlOnboardingManagerImpl implements PlOnboardingManager {
    private static final Logger logger = LoggerFactory.getLogger(PlOnboardingManagerImpl.class);

    private final LenderAdapterFactory lenderAdapterFactory;
    private final PandoraHystrixProperties pandoraHystrixProperties;
    private final PlUserOnboardingConfig plUserOnboardingConfig;
    private final GenericLenderClient genericLenderClient;
    private final CoreLogisticsClient coreLogisticsClient;
    private final PinakaClientV7 pinakaClientV7;
    private final SandboxLendersPlClient sandboxLendersPlClient;
    private final ApiParamModel apiParamModel;
    private final ElasticSearchClient elasticSearchClient;
    private final UserDataClient userDataClient;
    private final UserDataTransformer userDataTransformer;
    private final BQIngestionHelper bqIngestionHelper;
    private final WinterfellClient winterfellClient;
    private final MpockketClient mpockketClient;
    private final String bqEventIdFormat = "%s_%s";
    private final String userTokenKeyword = "&userToken=";
    private final FormDataDecryption formDataDecryption;

    @Inject
    public PlOnboardingManagerImpl(LenderAdapterFactory lenderAdapterFactory, PandoraHystrixProperties pandoraHystrixProperties,
                                   PlUserOnboardingConfig plUserOnboardingConfig, GenericLenderClient genericLenderClient, CoreLogisticsClient coreLogisticsClient,
                                   PinakaClientV7 pinakaClientV7, SandboxLendersPlClient sandboxLendersPlClient, ApiParamModel apiParamModel,
                                   ElasticSearchClient elasticSearchClient, UserDataClient userDataClient, UserDataTransformer userDataTransformer, BQIngestionHelper bqIngestionHelper, WinterfellClient winterfellClient, MpockketClient mpockketClient) {
        this.lenderAdapterFactory = lenderAdapterFactory;
        this.pandoraHystrixProperties = pandoraHystrixProperties;
        this.plUserOnboardingConfig = plUserOnboardingConfig;
        this.genericLenderClient = genericLenderClient;
        this.coreLogisticsClient = coreLogisticsClient;
        this.pinakaClientV7 = pinakaClientV7;
        this.sandboxLendersPlClient = sandboxLendersPlClient;
        this.apiParamModel = apiParamModel;
        this.elasticSearchClient = elasticSearchClient;
        this.userDataClient = userDataClient;
        this.userDataTransformer = userDataTransformer;
        this.bqIngestionHelper = bqIngestionHelper;
        this.winterfellClient = winterfellClient;
        this.mpockketClient = mpockketClient;
        this.formDataDecryption = new FormDataDecryption();
    }

    private static LenderDedupeRequest decryptLenderRequest(LenderDedupeRequest request) {
        if (request == null) {
            return null;
        }
        request.setPan(PlUserOnboardingUtil.decrypt(request.getPan())); // only pan is encrypted currently
        request.setEmail(PlUserOnboardingUtil.decrypt(request.getEmail()));
        return request;
    }

    @Override
    public PlOnboardingResponse identifyCustomerByLenders(IdentifiyCustomerRequest identifyCustomerRequest,
                                                          String requestId, String merchantId)
            throws PlOnboardingInvalidParamException, WintefellClientException {
        // TODO: Remove this validation after we start supporting Multi partner call
        if (identifyCustomerRequest.getLenders().size() != 1) {
            throw new PlOnboardingInvalidParamException(PlConstants.UNSUPPORTED_LENDER_LIST_SIZE_ERROR_MSG);
        }
        pushMetricForSmAccountIdPresence("identifyCustomerByLenders",
                StringUtils.isNotBlank(identifyCustomerRequest.getContactDetails().getSmUserId()));
        // Data Enrichemnt
        IdentifyCustomerEnriched identifyCustomerEnriched = enrichCustomerData(identifyCustomerRequest, merchantId);

        Lender lender = identifyCustomerRequest.getLenders().get(0);
        ILenderOperationAdapter<IdentifyCustomerEnriched> ciAdapter = lenderAdapterFactory
                .getAdapterByLenderAndOperation(lender, PlConstants.CUSTOMER_IDENTIFICATION);

        //Generate Lender Specific request
        LenderRequest lenderRequest = ciAdapter.generateLenderRequestBody(identifyCustomerEnriched);

        String commandGroupKeyName = plUserOnboardingConfig.getLenderSpecificConfigValueAsString(lender.name(),
                PlConstants.CUSTOMER_IDENTIFICATION,
                PlConstants.COMMAND_GROUP_KEY_NAME);
        String commandKeyName = plUserOnboardingConfig.getLenderSpecificConfigValueAsString(lender.name(),
                PlConstants.CUSTOMER_IDENTIFICATION,
                PlConstants.COMMAND_KEY_NAME);
        String threadPoolKeyName = plUserOnboardingConfig.getLenderSpecificConfigValueAsString(lender.name(),
                PlConstants.CUSTOMER_IDENTIFICATION,
                PlConstants.THREAD_POOL_KEY_NAME);

        PlHttpRequest plHttpRequest = prepareRequest(lender, PlConstants.CUSTOMER_IDENTIFICATION,
                lenderRequest, identifyCustomerRequest.getLspApplicationId(), requestId);
        LenderRes lenderRes = new InvokeLenderApiCommand(commandGroupKeyName,
                commandKeyName,
                threadPoolKeyName, pandoraHystrixProperties, plHttpRequest, genericLenderClient)
                .execute();
        PlOnboardingResponse ciResponse = ciAdapter.generatePandoraResponseBody(identifyCustomerEnriched, lenderRes);

        //Fire and forget call for Bureau Pull and CDD

        if (isCiApiSuccess((IdentifyCustomerResponse) ciResponse)) {
            triggerBureauPull(identifyCustomerRequest, requestId);
        }

        return ciResponse;
    }

    @Override
    public void triggerBureauPull(IdentifiyCustomerRequest identifyCustomerRequest, String requestId)
            throws PlOnboardingInvalidParamException, WintefellClientException {
        // TODO: Remove this validation after we start supporting Multi partner call
        if (identifyCustomerRequest.getLenders().size() != 1) {
            throw new PlOnboardingInvalidParamException(PlConstants.UNSUPPORTED_LENDER_LIST_SIZE_ERROR_MSG);
        }
        Lender lender = identifyCustomerRequest.getLenders().get(0);
        ILenderOperationAdapter<IdentifiyCustomerRequest> triggerBureauPullAdapter = lenderAdapterFactory
                .getAdapterByLenderAndOperation(lender, PlConstants.TRIGGER_BUREAU_PULL);

        //Generate Lender Specific request
        LenderRequest lenderRequest = triggerBureauPullAdapter.generateLenderRequestBody(identifyCustomerRequest);

        String commandGroupKeyName = plUserOnboardingConfig.getLenderSpecificConfigValueAsString(lender.name(),
                PlConstants.TRIGGER_BUREAU_PULL,
                PlConstants.COMMAND_GROUP_KEY_NAME);
        String commandKeyName = plUserOnboardingConfig.getLenderSpecificConfigValueAsString(lender.name(),
                PlConstants.TRIGGER_BUREAU_PULL,
                PlConstants.COMMAND_KEY_NAME);
        String threadPoolKeyName = plUserOnboardingConfig.getLenderSpecificConfigValueAsString(lender.name(),
                PlConstants.TRIGGER_BUREAU_PULL,
                PlConstants.THREAD_POOL_KEY_NAME);

        PlHttpRequest plHttpRequest = prepareRequest(lender, PlConstants.TRIGGER_BUREAU_PULL,
                lenderRequest, identifyCustomerRequest.getLspApplicationId(), requestId);
        try {
            LenderRes lenderRes = new InvokeLenderApiCommand(commandGroupKeyName, commandKeyName, threadPoolKeyName,
                    pandoraHystrixProperties, plHttpRequest, genericLenderClient).execute();
        } catch (HystrixRuntimeException e) {
            // Ignore since this is fire and forget nature of call and this is expected
        }
    }

    @Override
    public PlOnboardingResponse fetchOffersFromLenders(EligibleLoanOfferRequest eligibleLoanOfferRequest,
                                                       String requestId, String merchantId)
            throws PlOnboardingInvalidParamException, WintefellClientException {
        if (eligibleLoanOfferRequest.getLenders().size() != 1) {
            throw new PlOnboardingInvalidParamException(PlConstants.UNSUPPORTED_LENDER_LIST_SIZE_ERROR_MSG);
        }
        Lender lender = eligibleLoanOfferRequest.getLenders().get(0);
        ILenderOperationAdapter<EligibleLoanOfferEnriched> loanOfferFetchAdapter = lenderAdapterFactory
                .getAdapterByLenderAndOperation(lender, PlConstants.LOAN_OFFER_FETCH);

        pushMetricForSmAccountIdPresence("fetchOffersFromLenders",
                StringUtils.isNotBlank(eligibleLoanOfferRequest.getContactDetails().getSmUserId()));
        EligibleLoanOfferEnriched eligibleLoanOfferEnriched = enrichLocationData(eligibleLoanOfferRequest, merchantId);


        //Generate Lender Specific request
        LenderRequest lenderRequest = loanOfferFetchAdapter.generateLenderRequestBody(eligibleLoanOfferEnriched);

        String commandGroupKeyName = plUserOnboardingConfig.getLenderSpecificConfigValueAsString(lender.name(),
                PlConstants.LOAN_OFFER_FETCH,
                PlConstants.COMMAND_GROUP_KEY_NAME);
        String commandKeyName = plUserOnboardingConfig.getLenderSpecificConfigValueAsString(lender.name(),
                PlConstants.LOAN_OFFER_FETCH,
                PlConstants.COMMAND_KEY_NAME);
        String threadPoolKeyName = plUserOnboardingConfig.getLenderSpecificConfigValueAsString(lender.name(),
                PlConstants.LOAN_OFFER_FETCH,
                PlConstants.THREAD_POOL_KEY_NAME);
        PlHttpRequest plHttpRequest = prepareRequest(lender, PlConstants.LOAN_OFFER_FETCH,
                lenderRequest, eligibleLoanOfferRequest.getLspApplicationId(), requestId);
        logger.info("lender request: {}",plHttpRequest);
        LenderRes lenderRes = new InvokeLenderApiCommand(commandGroupKeyName,
                commandKeyName,
                threadPoolKeyName,
                pandoraHystrixProperties,
                plHttpRequest, genericLenderClient)
                .execute();

        PlOnboardingResponse loanOfferFetchResp = loanOfferFetchAdapter.generatePandoraResponseBody(eligibleLoanOfferEnriched, lenderRes);
        submitOfferEventToBQ(generateInitialOfferEvent(eligibleLoanOfferEnriched, loanOfferFetchResp));
        return loanOfferFetchResp;
    }

    @Override
    public PlOnboardingResponse fetchKfsByLoanAndLenders(KfsRequest kfsRequest, String requestId) throws PlOnboardingInvalidParamException, WintefellClientException {

        if (kfsRequest.getLender() == null || kfsRequest.getLender().name().isEmpty()) {
            throw new PlOnboardingInvalidParamException(PlConstants.UNSUPPORTED_LENDER_LIST_SIZE_ERROR_MSG);
        }
        Lender lender = Lender.valueOf(kfsRequest.getLender().name());
        ILenderOperationAdapter<KfsRequest> kfsAdapter = lenderAdapterFactory
                .getAdapterByLenderAndOperation(lender, PlConstants.FETCH_KFS);

        LenderRequest lenderRequest = kfsAdapter.generateLenderRequestBody(kfsRequest);
        String commandGroupKeyName = plUserOnboardingConfig.getLenderSpecificConfigValueAsString(lender.name(),
                PlConstants.FETCH_KFS,
                PlConstants.COMMAND_GROUP_KEY_NAME);
        String commandKeyName = plUserOnboardingConfig.getLenderSpecificConfigValueAsString(lender.name(),
                PlConstants.FETCH_KFS,
                PlConstants.COMMAND_KEY_NAME);
        String threadPoolKeyName = plUserOnboardingConfig.getLenderSpecificConfigValueAsString(lender.name(),
                PlConstants.FETCH_KFS,
                PlConstants.THREAD_POOL_KEY_NAME);
        PlHttpRequest plHttpRequest = prepareRequest(lender, PlConstants.FETCH_KFS,
                lenderRequest, kfsRequest.getLspApplicationId(), requestId);
        String userToken = generateJwtToken(kfsRequest.getLender().name(), kfsRequest.getLspApplicationId());
        addUserToken(plHttpRequest.getHeaders(), userToken);

        LenderRes lenderRes = new InvokeLenderApiCommand
                (commandGroupKeyName, commandKeyName, threadPoolKeyName, pandoraHystrixProperties, plHttpRequest, genericLenderClient)
                .execute();

        KfsResponse kfsResponse = (KfsResponse) kfsAdapter.generatePandoraResponseBody(kfsRequest,
            lenderRes);
        if (Objects.nonNull(kfsResponse) && Objects.nonNull(kfsResponse.getRedirectionURL())) {
            kfsResponse.setRedirectionURL(kfsResponse.getRedirectionURL()
                .concat(userTokenKeyword.concat(userToken)));
        }

        return kfsResponse;

    }

    @Override
    public PlOnboardingResponse submitLoanOfferToLender(SubmitLoanOfferRequest submitLoanOfferRequest, String requestId)
            throws PlOnboardingInvalidParamException, WintefellClientException {


        if (submitLoanOfferRequest.getLender() == null || submitLoanOfferRequest.getLender().name().isEmpty()) {
            throw new PlOnboardingInvalidParamException(PlConstants.UNSUPPORTED_LENDER_LIST_SIZE_ERROR_MSG);
        }
        Lender lender = Lender.valueOf(submitLoanOfferRequest.getLender().name());
        SubmitOfferEnrich submitOfferEnrich = enrichSubmitData(submitLoanOfferRequest);
        ILenderOperationAdapter<SubmitOfferEnrich> submitAdapter = lenderAdapterFactory
                .getAdapterByLenderAndOperation(lender, PlConstants.SUBMIT_OFFER);

        LenderRequest lenderRequest = submitAdapter.generateLenderRequestBody(submitOfferEnrich);

        String commandKeyName = plUserOnboardingConfig.getLenderSpecificConfigValueAsString(lender.name(),
                PlConstants.SUBMIT_OFFER,
                PlConstants.COMMAND_KEY_NAME);
        String commandGroupKeyName = plUserOnboardingConfig.getLenderSpecificConfigValueAsString(lender.name(),
                PlConstants.SUBMIT_OFFER,
                PlConstants.COMMAND_GROUP_KEY_NAME);
        String threadPoolKeyName = plUserOnboardingConfig.getLenderSpecificConfigValueAsString(lender.name(),
                PlConstants.SUBMIT_OFFER,
                PlConstants.THREAD_POOL_KEY_NAME);

        PlHttpRequest plHttpRequest = prepareRequest(lender, PlConstants.SUBMIT_OFFER,
                lenderRequest, submitLoanOfferRequest.getLspApplicationId(), requestId);
        addUserToken(plHttpRequest.getHeaders(), submitOfferEnrich.getJwtToken());

        LenderRes lenderRes = new InvokeLenderApiCommand(commandGroupKeyName, commandKeyName, threadPoolKeyName, pandoraHystrixProperties, plHttpRequest, genericLenderClient)
                .execute();

        PlOnboardingResponse submitResponse = submitAdapter.generatePandoraResponseBody(submitOfferEnrich, lenderRes);
        submitOfferEventToBQ(genrerateSubmitOfferBQEvent(submitLoanOfferRequest, submitResponse));
        return submitResponse;
    }

    @Override
    public CreateApplicationResponse createLoanApplicationAtLender(CreateApplicationRequest createApplicationRequest, String requestId) throws PlOnboardingInvalidParamException, LenderException, WintefellClientException {
        pushMetricForSmAccountIdPresence("createLoanApplicationAtLender",
                StringUtils.isNotBlank(createApplicationRequest.getSmUserId()));

        Scope scope = getScopeUtil(createApplicationRequest.getLender());

        ILenderOperationAdapter<CreateApplicationRequest> createApplicationAdapter = lenderAdapterFactory
                .getAdapterByLenderAndOperation(Lender.SMALL_LENDERS_COMMON, PlConstants.CREATE_APPLICATION_AT_LENDER);

        LenderRequest applicationRequest = createApplicationAdapter.generateLenderRequestBody(createApplicationRequest);

        return sandboxLendersPlClient.getCreateApplicationResponse((com.flipkart.fintech.pandora.service.client.sandbox.v1.request.CreateApplicationRequest) applicationRequest, scope, requestId);
    }

    @Override
    public GetApplicationResponse getApplicationFromLender(GetApplicationRequest getApplicationRequest, String requestId) throws PlOnboardingInvalidParamException, LenderException {

        Scope scope = getScopeUtil(getApplicationRequest.getLender());

        com.flipkart.fintech.pandora.service.client.sandbox.v1.request.GetApplicationRequest request =
                com.flipkart.fintech.pandora.service.client.sandbox.v1.request.GetApplicationRequest.builder()
                        .lenderApplicationId(getApplicationRequest.getLenderApplicationId()).lspId(SUPERMONEY).build();

        return sandboxLendersPlClient.getApplicationResponse(request, scope, requestId);
    }

    @Override
    public GetApplicationStatusResponse getApplicationStatusFromLender(GetApplicationStatusRequest getApplicationStatusRequest, String requestId) throws PlOnboardingInvalidParamException, LenderException {
        Scope scope = getScopeUtil(getApplicationStatusRequest.getLender());

        com.flipkart.fintech.pandora.service.client.sandbox.v1.request.GetApplicationStatusRequest request =
                com.flipkart.fintech.pandora.service.client.sandbox.v1.request.GetApplicationStatusRequest.builder()
                        .lenderApplicationId(getApplicationStatusRequest.getLenderApplicationId()).lspId(SUPERMONEY).build();

        return sandboxLendersPlClient.getApplicationStatusResponse(request, scope, requestId);
    }

    @Override
    public PlOnboardingResponse getStatusFromLender(StatusRequest statusRequest, String requestId) throws PlOnboardingInvalidParamException, WintefellClientException {

        if (statusRequest.getLenders() == null || statusRequest.getLenders().name().isEmpty()) {
            throw new PlOnboardingInvalidParamException(PlConstants.UNSUPPORTED_LENDER_LIST_SIZE_ERROR_MSG);
        }
        Lender lender = Lender.valueOf(statusRequest.getLenders().name());
        GetStatusEnrich getStatusEnrich = enrichStatusData(statusRequest);
        ILenderOperationAdapter<GetStatusEnrich> statusAdapter = lenderAdapterFactory
                .getAdapterByLenderAndOperation(lender, PlConstants.GET_STATUS);

        LenderRequest lenderRequest = statusAdapter.generateLenderRequestBody(getStatusEnrich);
        String commandGroupKeyName = plUserOnboardingConfig.getLenderSpecificConfigValueAsString(lender.name(),
                PlConstants.GET_STATUS,
                PlConstants.COMMAND_GROUP_KEY_NAME);
        String commandKeyName = plUserOnboardingConfig.getLenderSpecificConfigValueAsString(lender.name(),
                PlConstants.GET_STATUS,
                PlConstants.COMMAND_KEY_NAME);
        String threadPoolKeyName = plUserOnboardingConfig.getLenderSpecificConfigValueAsString(lender.name(),
                PlConstants.GET_STATUS,
                PlConstants.THREAD_POOL_KEY_NAME);
        PlHttpRequest plHttpRequest = prepareRequest(lender, PlConstants.GET_STATUS,
                lenderRequest, statusRequest.getLspApplicationId(), requestId);
        addUserToken(plHttpRequest.getHeaders(), getStatusEnrich.getJwtToken());
        LenderRes lenderRes = new InvokeLenderApiCommand(commandGroupKeyName, commandKeyName, threadPoolKeyName, pandoraHystrixProperties, plHttpRequest, genericLenderClient)
                .execute();
        PlOnboardingResponse statusResponse = statusAdapter.generatePandoraResponseBody(getStatusEnrich, lenderRes);

        return statusResponse;
    }

    @Override
    public void consumeLenderPlatformEvent(EncryptedRequest encryptedRequest, String clientId,
                                           String requestId, String timestamp)
            throws PlOnboardingInvalidParamException, PinakaClientException {
        Lender lender = Lender.valueOf(plUserOnboardingConfig.getLenderSpecificConfigValueAsString(
                PlConstants.PL_CONFIG_PREFIX, clientId, PlConstants.LENDER_NAME));
        RSAPublicKey publicKeyToVerify = PlUserOnboardingUtil.getRsaPublicKey(
                plUserOnboardingConfig.getLenderSpecificConfigValueAsString(PlConstants.PL_CONFIG_PREFIX,
                        lender.name(), PlConstants.REVERSE_FLOW, PlConstants.PUBLIC_KEY_TO_VERIFY));
        RSAPrivateKey privateKeyToDecrypt = PlUserOnboardingUtil.getRsaPrivateKey(
                plUserOnboardingConfig.getLenderSpecificConfigValueAsString(PlConstants.PL_CONFIG_PREFIX,
                        lender.name(), PlConstants.REVERSE_FLOW, PlConstants.PRIVATE_KEY_TO_DECRYPT));
        String decryptedData = null;
        try {
            decryptedData = PlEncryptionUtil.jweVerifyAndDecrypt(publicKeyToVerify,
                    privateKeyToDecrypt, encryptedRequest.getData());
            logger.info("decryptedData: \n{}", decryptedData);
        } catch (ParseException | NoSuchAlgorithmException | CertificateException | KeyStoreException | IOException |
                 UnrecoverableKeyException e) {
            String logMsg = String.format(PlConstants.PL_RESOURCE_LOG_FORMAT, requestId, PlConstants.SUBMIT_EVENT, e.getMessage());
            logger.error(logMsg);
            throw new PandoraException(PlConstants.JWE_VERIFICATION_FAILED_ERROR_MSG);
        } catch (JOSEException | InvalidJweSignatureException e) {
            String logMsg = String.format(PlConstants.PL_RESOURCE_LOG_FORMAT, requestId, PlConstants.SUBMIT_EVENT, e.getMessage());
            logger.error(logMsg);
            throw new PlOnboardingInvalidParamException(PlConstants.JWE_VERIFICATION_FAILED_ERROR_MSG, e);
        }
        WebHookRequest webHookRequest = PlUserOnboardingUtil.parseJsonToObject(decryptedData, WebHookRequest.class);
        if (webHookRequest != null
                && webHookRequest.getStage() != null
                && webHookRequest.getStage().getStep() != null
                && webHookRequest.getStage().getStatus() != null
                && webHookRequest.getApplicationStatus() != null
                && ((webHookRequest.getStage().getStep() == Step.LOAN_DISBURSAL
                && webHookRequest.getStage().getStatus() == com.flipkart.fintech.pandora.service.client.plOnboarding.request.Status.SUCCESS)
                || webHookRequest.getApplicationStatus() == com.flipkart.fintech.pandora.service.client.plOnboarding.response.Status.SUCCESS)) {

            submitOfferEventToBQ(generateLenderPlatformOfferEvent(webHookRequest));
        }else{
            logger.info("Not submitting offer event to bq: {}", String.valueOf(webHookRequest));
        }


        logger.info("PersonalLoans_Event received for lspApplicationID: {}", webHookRequest.getLspApplicationId());

        Stage stage = Stage.builder()
                .lender(com.flipkart.fintech.pinaka.api.request.v6.Lender.valueOf(lender.name()))
                .step(com.flipkart.fintech.pinaka.api.request.v6.Step.valueOf(webHookRequest.getStage().getStep().name()))
                .subStep(com.flipkart.fintech.pinaka.api.request.v6.SubStep.valueOf(webHookRequest.getStage().getSubStep().name()))
                .status(com.flipkart.fintech.pinaka.api.response.v6.Status.valueOf(webHookRequest.getStage().getStatus().name()))
                .failureCode(webHookRequest.getStage().getFailureCode())
                .build();
        AxisWebhooksRequest pinakaWebhooksRequest = AxisWebhooksRequest.builder()
                .lspApplicationId(webHookRequest.getLspApplicationId())
                .lenderApplicationId(webHookRequest.getLenderApplicationId())
                .stage(stage)
                .stateTransitionTime(webHookRequest.getStateTransitionTime())
                .applicationStatus(webHookRequest.getApplicationStatus().name())
                .build();
        pinakaClientV7.submitLenderEvent(pinakaWebhooksRequest, requestId);
    }

    @Override
    public WinterfellNodeResponse checkAddressServicability(String shippingId, Lender lender, String accountId, String merchantId, String smUserId) {
        WinterfellNodeResponse winterfellNodeResponse = new WinterfellNodeResponse();
        Map<String, VariableData> workflowVariables = new HashMap<>();
        VariableData pincodeServiceable = new VariableData();
        pincodeServiceable.setTransient(false);

        pushMetricForSmAccountIdPresence("checkAddressServicability", StringUtils.isNotBlank(smUserId));

        AddressData addressData = userDataClient.getAddressData(Merchant.getOrDefaultByValue(merchantId), accountId, smUserId, shippingId, PIIDataType.PLAINTEXT);

        boolean isServicable = elasticSearchClient.getPincodeServicableLender(addressData.getPincode(),
                lender.toString());
        PincodeServiceableModel pincodeServiceableModel = PincodeServiceableModel.builder().isServiceable(isServicable)
                .pincode(addressData.getPincode()).lender(lender).build();

        pincodeServiceable.setData(pincodeServiceableModel);
        workflowVariables.put("pincodeServiceableModel", pincodeServiceable);
        Map<String, Object> applicationData = new HashMap<>();
        applicationData.put("pincodeServiceableModel", pincodeServiceableModel);
        winterfellNodeResponse.setApplicationData(applicationData);

        winterfellNodeResponse.setWorkflowData(workflowVariables);

        return winterfellNodeResponse;
    }

    @Override
    public LenderDedupeResponse checkLenderDedupe(LenderDedupeRequest lenderDedupeRequest, String requestId) throws PlOnboardingInvalidParamException {
        Scope scope = getScopeUtil(lenderDedupeRequest.getLender());
        return sandboxLendersPlClient.dedupeLenderApplication(decryptLenderRequest(lenderDedupeRequest), scope, requestId);
    }

    @Override
    public void consumeSandboxLenderPlatformEvent(String encryptedRequest, String clientId, String requestId)
        throws UnrecoverableKeyException, InvalidJweSignatureException,
        CertificateException, ParseException, NoSuchAlgorithmException, KeyStoreException, IOException, JOSEException, PlOnboardingInvalidParamException, PinakaClientException, JSONException {

        String encryptedString = new JSONObject(encryptedRequest).getString("data");
        Scope scope = apiParamModel.getScopeFromClientId(clientId);
        SandboxLendersPlConfiguration config = apiParamModel.getPlConfigurationForLender(scope);

        String decryptedString = PlEncryptionUtil.jweVerifyAndDecrypt(JweEncryptorDecryptor.getRSAPublicKey(config.getLenderBase64PublicKey()),
                JweEncryptorDecryptor.getRsaPrivateKey(config.getLspBase64PrivateKey()), encryptedString);
        logger.info(decryptedString);

        ApplicationStateEventRequest applicationStateEventRequest = new ObjectMapper().readValue(decryptedString, ApplicationStateEventRequest.class);
        logger.info(ObjectMapperUtil.get().writeValueAsString(applicationStateEventRequest));

        EventsState currentState = applicationStateEventRequest.getApplicationStateEvents().stream()
                .sorted(Comparator.comparingLong(EventsState::getTs).reversed())
                .collect(Collectors.toList()).get(0);

        SandboxWebhooksRequest sandboxWebhooksRequest = SandboxWebhooksRequest.builder()
                .lenderApplicationId(applicationStateEventRequest.getLenderId())
                .lspApplicationId(applicationStateEventRequest.getLspApplicationId())
                .applicationStatus(applicationStateEventRequest.getApplicationStatus().toString())
                .applicationState(currentState.getState().name())
                .status(currentState.getStatus().name())
                .subStatus(currentState.getSubStatus())
                .stateTransitionTime(currentState.getTs())
                .build();
        pinakaClientV7.submitSandboxLenderEvent(sandboxWebhooksRequest, requestId);

    }
    public MpockketGetStatusResponse getMpockketLeadStatus(String mpockketRequestId) throws Exception {
        return this.mpockketClient.fetchStatus(mpockketRequestId);
    }
    public MpockketSubmitLeadResponse createLeadAtMpockket(String applicationId) throws Exception {
        ApplicationDataResponse applicationDataResponse = winterfellClient.getApplication("CALM",applicationId,null,null);
        UserData userData = getUserInfo(applicationDataResponse);
        MpockketSubmitLeadRequest mpockketSubmitLeadRequest = new MpockketSubmitLeadRequest();
        Map<String,String> basicDetails = (Map<String, String>) applicationDataResponse.getApplicationData().get("basicDetails");

        Map<String, Object> decryptData = decryptData(basicDetails);

        mpockketSubmitLeadRequest.setEmailId((String) decryptData.get("email"));
        mpockketSubmitLeadRequest.setFirstName((String) decryptData.get("firstName"));
        mpockketSubmitLeadRequest.setLastName((String) decryptData.get("lastName"));

        mpockketSubmitLeadRequest.setMobileNumber(getMobileNo(userData.getPrimaryPhone()));
        mpockketSubmitLeadRequest.setProfession(basicDetails.get("employmentType"));
        mpockketSubmitLeadRequest.setGender(basicDetails.get("gender"));
        return this.mpockketClient.submitLead(mpockketSubmitLeadRequest, applicationId);
    }

    private Map<String, Object> decryptData(Map<String,String> basicDetails) {
        Map<String, Object> encryptData = new HashMap<>();
        encryptData.put("email", basicDetails.get("email"));
        encryptData.put("firstName", basicDetails.get("firstName"));
        encryptData.put("lastName", basicDetails.get("lastName"));
        return formDataDecryption.decryptFormData(encryptData);
    }

    private String getMobileNo(String primaryPhone) {
        if(Objects.nonNull(primaryPhone) && primaryPhone.length() > 10) {
            return primaryPhone.substring(3, 13);
        }
        return primaryPhone;
    }

    private UserData getUserInfo(ApplicationDataResponse applicationDataResponse){
        return userDataClient.getUserData(com.flipkart.fintech.user.data.models.enums.Merchant.getOrDefaultByValue(applicationDataResponse.getMerchantId()),
                applicationDataResponse.getExternalUserId(),applicationDataResponse.getSmUserId(),PIIDataType.PLAINTEXT);
    }
    private IdentifyCustomerEnriched enrichCustomerData(IdentifiyCustomerRequest identifyCustomerRequest, String merchantId) {
        AddressDetailResponse addressDetailResponse = identifyCustomerRequest.getContactDetails().getAddressDetailResponse();
        String accountId = identifyCustomerRequest.getContactDetails().getAccountId();
        String smUserId = identifyCustomerRequest.getContactDetails().getSmUserId();
        Merchant merchant = Merchant.getOrDefaultByValue(merchantId);
        if (Objects.isNull(addressDetailResponse)) {
            String shippingAddressId = identifyCustomerRequest.getContactDetails().getShippingAddressId();
            AddressData addressData = userDataClient.getAddressData(merchant, accountId, smUserId, shippingAddressId, PIIDataType.PLAINTEXT);
            addressDetailResponse = userDataTransformer.transformAddressDetailResponse(addressData);
        }
        UserData userData = userDataClient.getNonVerifiedUserData(merchant, accountId, smUserId, PIIDataType.PLAINTEXT);
        String mobNo = userData.getPrimaryPhone();
        String userAddress = addressDetailResponse.getAddressLine1() + addressDetailResponse.getAddressLine2();
        String pincode = addressDetailResponse.getPincode();
        String email = userData.getPrimaryEmail();
        UserCommunication userCommunication = UserCommunication.builder()
                .mobileNumber(mobNo)
                .emailId(email)
                .address(userAddress)
                .firstName(identifyCustomerRequest.getIdentificationDetails().getFirstName())
                .lastName(identifyCustomerRequest.getIdentificationDetails().getLastName())
                .pincode(pincode)
                .build();

        return IdentifyCustomerEnriched.builder()
                .identifiyCustomerRequest(identifyCustomerRequest)
                .userCommunication(userCommunication)
                .build();
    }

    private GetStatusEnrich enrichStatusData(StatusRequest statusRequest) {
        String userToken = generateJwtToken(statusRequest.getLenders().name(), statusRequest.getLspApplicationId());
        GetStatusEnrich getStatusEnrich = GetStatusEnrich.builder().statusRequest(statusRequest).jwtToken(userToken).build();

        return getStatusEnrich;
    }

    private SubmitOfferEnrich enrichSubmitData(SubmitLoanOfferRequest submitLoanOfferRequest) {
        String userToken = generateJwtToken(submitLoanOfferRequest.getLender().name(), submitLoanOfferRequest.getLspApplicationId());
        SubmitOfferEnrich submitOfferEnrich = SubmitOfferEnrich.builder().submitLoanOfferRequest(submitLoanOfferRequest).jwtToken(userToken).build();

        return submitOfferEnrich;
    }

    private String generateJwtToken(String lender, String lspApplicationId) {
        int validityInMinutes = plUserOnboardingConfig.getLenderSpecificConfigValueAsInt(lender, PlConstants.PL_CONFIG_PREFIX, PlConstants.TIME);
        Date tokenExpiryDate = Date.from(Instant.now().plus(validityInMinutes, ChronoUnit.MINUTES));
        Date tokenStartDate = Date.from(Instant.now());
        String privateKey = plUserOnboardingConfig.getLenderSpecificConfigValueAsString(PlConstants.PL_CONFIG_PREFIX,
                lender, PlConstants.FORWARD_FLOW, PlConstants.JWT, PlConstants.PRIVATE_KEY);
        Claims claims = new DefaultClaims();
        claims.setExpiration(tokenExpiryDate);
        claims.setIssuer(lspApplicationId);
        claims.setIssuedAt(tokenStartDate);

        return JWTTokenGenerator.generateJwtToken(claims, privateKey, SignatureAlgorithm.RS256, null);
    }

    private MultivaluedMap<String, Object> getHeaders(String lender, String operation, String lspApplicationId,
                                                      String requestIdFromCallingService) {
        MultivaluedMap<String, Object> headers = new MultivaluedHashMap<>();
        Map<String, String> staticHeaders = PlUserOnboardingUtil.parseJsonToObject(
                plUserOnboardingConfig.getLenderSpecificConfigValueAsString(PlConstants.PL_CONFIG_PREFIX,
                        lender, PlConstants.STATIC_HEADERS),
                new TypeReference<HashMap<String, String>>() {
                }
        );
        for (Map.Entry<String, String> entry : staticHeaders.entrySet()) {
            headers.putSingle(entry.getKey(), entry.getValue());
        }
        String requestIdPref = plUserOnboardingConfig.getLenderSpecificConfigValueAsString(lender,
                operation, PlConstants.REQUEST_ID_PREF);
        headers.putSingle(PlConstants.REQUEST_ID, requestIdPref + lspApplicationId);
        headers.putSingle(HttpHeaders.CONTENT_TYPE, MediaType.TEXT_PLAIN);
        headers.putSingle(PlConstants.X_FAPI_EPOCH_MILLIS, String.valueOf(System.currentTimeMillis()));
        headers.putSingle(PlConstants.X_FAPI_UUID, requestIdFromCallingService);

        return headers;
    }

    private void addUserToken(MultivaluedMap<String, Object> headers, String userToken) {
        headers.putSingle(PlConstants.USER_TOKEN, userToken);
    }

    private PlHttpRequest prepareRequest(Lender lender, String operation, LenderRequest lenderRequest,
                                         String lspApplicationId, String requestIdFromCallingService) {
        PlHttpRequest plHttpRequest = PlHttpRequest.builder()
                .url(getFullUrl(lender, operation))
                .headers(getHeaders(lender.name(), operation, lspApplicationId, requestIdFromCallingService))
                .lenderRequest(lenderRequest)
                .connectTimeoutMs(plUserOnboardingConfig.getLenderSpecificConfigValueAsInt(lender.name(),
                        operation, PlConstants.CONNECTION_TIMEOUT))
                .readTimeoutMs(plUserOnboardingConfig.getLenderSpecificConfigValueAsInt(lender.name(),
                        operation, PlConstants.READ_TIMEOUT))
                .publicKeyToEncrypt(PlUserOnboardingUtil.getRsaPublicKey(
                        plUserOnboardingConfig.getLenderSpecificConfigValueAsString(PlConstants.PL_CONFIG_PREFIX,
                                lender.name(), PlConstants.FORWARD_FLOW, PlConstants.PUBLIC_KEY_TO_ENCRYPT)))
                .privateKeyToSign(PlUserOnboardingUtil.getRsaPrivateKey(
                        plUserOnboardingConfig.getLenderSpecificConfigValueAsString(PlConstants.PL_CONFIG_PREFIX,
                                lender.name(), PlConstants.FORWARD_FLOW, PlConstants.PRIVATE_KEY_TO_SIGN)))
                .privateKeyToDecrypt(PlUserOnboardingUtil.getRsaPrivateKey(
                        plUserOnboardingConfig.getLenderSpecificConfigValueAsString(PlConstants.PL_CONFIG_PREFIX,
                                lender.name(), PlConstants.FORWARD_FLOW, PlConstants.PRIVATE_KEY_TO_DECRYPT)))
                .publicKeyToVerify(PlUserOnboardingUtil.getRsaPublicKey(
                        plUserOnboardingConfig.getLenderSpecificConfigValueAsString(PlConstants.PL_CONFIG_PREFIX,
                                lender.name(), PlConstants.FORWARD_FLOW, PlConstants.PUBLIC_KEY_TO_VERIFY)))
                .requestIdentifier(lspApplicationId)
                .build();

        return plHttpRequest;
    }

    private String getFullUrl(Lender lender, String operation) {
        return plUserOnboardingConfig.getLenderSpecificConfigValueAsString(PlConstants.PL_CONFIG_PREFIX,
                lender.name(), PlConstants.LENDER_BASE_URL) +
                plUserOnboardingConfig.getLenderSpecificConfigValueAsString(lender.name(),
                        operation, PlConstants.LENDER_API_ENDPOINT);
    }

    private EligibleLoanOfferEnriched enrichLocationData(EligibleLoanOfferRequest eligibleLoanOfferRequest, String merchantId) {
        AddressDetailResponse addressDetailResponse=eligibleLoanOfferRequest.getContactDetails().getAddressDetailResponse();
        String accountId = eligibleLoanOfferRequest.getContactDetails().getAccountId();
        String smUserId = eligibleLoanOfferRequest.getContactDetails().getSmUserId();
        Merchant merchant = Merchant.getOrDefaultByValue(merchantId);
        if(Objects.isNull(addressDetailResponse)){
            String shippingAddressId = eligibleLoanOfferRequest.getContactDetails().getShippingAddressId();
            AddressData addressData = userDataClient.getAddressData(merchant, accountId, smUserId, shippingAddressId, PIIDataType.PLAINTEXT);
            addressDetailResponse = userDataTransformer.transformAddressDetailResponse(addressData);
        }
        Map<String, String> addressMap = new HashMap<>();
        try {
            addressMap.put(PlConstants.GEO_LOCATION_FETCH_ADDR_1, URLEncoder.encode(addressDetailResponse.getAddressLine1(), PlConstants.UTF8));
            String addressLine2 = addressDetailResponse.getAddressLine2();
            addressMap.put(PlConstants.GEO_LOCATION_FETCH_ADDR_2, StringUtils.isNotEmpty(addressLine2) ? URLEncoder.encode(addressLine2, PlConstants.UTF8) : "");
            addressMap.put(PlConstants.GEO_LOCATION_FETCH_CITY, URLEncoder.encode(addressDetailResponse.getCity(), PlConstants.UTF8));
            addressMap.put(PlConstants.GEO_LOCATION_FETCH_STATE, URLEncoder.encode(addressDetailResponse.getState(), PlConstants.UTF8));
            addressMap.put(PlConstants.GEO_LOCATION_FETCH_PINCODE, URLEncoder.encode(addressDetailResponse.getPincode(), PlConstants.UTF8));
            GeoLocationResponse geoLocationResponse = coreLogisticsClient.geoLocationResponse(addressMap);
            if (Objects.nonNull(geoLocationResponse)) {
                return EligibleLoanOfferEnriched.builder()
                        .eligibleLoanOfferRequest(eligibleLoanOfferRequest)
                        .location(geoLocationResponse.getLocation())
                        .build();
            } else {
                throw new PandoraException(PlConstants.GEO_LOCATION_FETCH_ERROR);
            }
        } catch (UnsupportedEncodingException e) {
            String logMsg = String.format(PlConstants.PL_LOG_FORMAT, PlConstants.LSP_ID + eligibleLoanOfferRequest.getLspApplicationId(), e.getMessage());
            logger.error(logMsg);
            throw new PandoraException(e);
        }
    }

    private boolean isCiApiSuccess(IdentifyCustomerResponse identifyCustomerResponse) {
        return identifyCustomerResponse.getStatus() == Status.SUCCESS;
    }

    private Scope getScopeUtil(String lender) throws PlOnboardingInvalidParamException {
        String lenderScope_ = "LENDING_"+lender;
        if(EnumUtils.isValidEnum(Scope.class, lenderScope_)){
            return EnumUtils.getEnum(Scope.class, lenderScope_);
        }
        throw new PlOnboardingInvalidParamException("lender not present in pandora config");
    }


    private boolean isValidPlOnboardingResponseType(PlOnboardingResponse plOnboardingResponse, PlOnboardingResponseType plOnboardingResponseType) {
        if (plOnboardingResponseType.equals(plOnboardingResponse.checkPlOnboardingResponseType())) {
            return true;
        }
        logger.error("Invalid response type in getPlOnboardingResponseType function: {}, expected: {}", plOnboardingResponse.checkPlOnboardingResponseType(), plOnboardingResponseType);
        return false;
    }

    private OfferEventV1 generateInitialOfferEvent(EligibleLoanOfferEnriched eligibleLoanOfferEnriched, PlOnboardingResponse plOnboardingResponse) {
        try {
            if (!isValidPlOnboardingResponseType(plOnboardingResponse, PlOnboardingResponseType.EligibleLoanOfferResponse)) {
                return null;
            }
            ;
            EligibleLoanOfferResponse event = (EligibleLoanOfferResponse) plOnboardingResponse;
            if (event == null) {
                return null;
            }
            ApplicationDataResponse applicationDataResponse = winterfellClient.getApplication("CALM", event.getLspApplicationId(), null, null);
            ArrayList<OfferDetail> offerDetails = new ArrayList<>();
            OfferDetail offerDetail = new OfferDetail();
            if (event.getOffer() != null && event.getOffer().getCharge() != null) {
                for (Charges charge : event.getOffer().getCharge()) {
                    if (charge == null) continue;
                    if (charge.getChargeType() == Chargetype.PROCESSING_FEE) {
                        offerDetail.setPfAmount(charge.getValue().doubleValue());
                        offerDetail.setPfType(charge.getType().toString());
                        offerDetail.setGst(charge.getGst().doubleValue());
                    }
                    if (charge.getChargeType() == Chargetype.STAMP_DUTY) {
                        offerDetail.setStampDuty(charge.getValue().doubleValue());
                    }
                }
            }
            ;

            LoanAmountDetail loanAmountDetail = event.getOffer() != null ? event.getOffer().getLoanAmountDetail() : null;
            TennureDetails tennureDetails = event.getOffer() != null ? event.getOffer().getTenureDetails() : null;
            BigDecimal roi = event.getOffer() != null ? event.getOffer().getRoi() : null;
            ContactDetails contactDetails = eligibleLoanOfferEnriched.getEligibleLoanOfferRequest() != null ? eligibleLoanOfferEnriched.getEligibleLoanOfferRequest().getContactDetails() : null;
            offerDetail.setMinAmount(loanAmountDetail != null ? Double.valueOf(loanAmountDetail.getMin()) : null);
            offerDetail.setMaxAmount(loanAmountDetail != null ? Double.valueOf(loanAmountDetail.getMax()) : null);
            offerDetail.setMinTenure(tennureDetails != null ? Double.valueOf(tennureDetails.getMin()) : null);
            offerDetail.setMaxTenure(tennureDetails != null ? Double.valueOf(tennureDetails.getMax()) : null);
            offerDetail.setRoi(roi != null ? roi.doubleValue() : null);
            offerDetail.setEmi(Optional.ofNullable(event.getOffer()).map(offer -> offer.getEmiThreshold().longValue()).orElse(null));
            offerDetails.add(offerDetail);
            return OfferEventV1.newBuilder()
                    .setEventId(String.format(bqEventIdFormat, event.getLspApplicationId(), contactDetails.getSmUserId()))
                    .setLender(Lender.AXIS.toString())
                    .setAccountId(contactDetails != null ? contactDetails.getSmUserId() : null)
                    .setApplicationId(event.getLspApplicationId())
                    .setValidity(event.getLenderApplicationValidity())
                    .setOfferDetails(offerDetails)
                    .setOfferType(String.valueOf(OfferType.INITIAL))
                    .setValidity(event.getLenderApplicationValidity())
                    .setExternalUserId(contactDetails != null ? contactDetails.getAccountId() : null)
                    .setOfferId(String.valueOf(Optional.ofNullable(applicationDataResponse != null ? applicationDataResponse.getApplicationData() : null).map(applicationData -> applicationData.get("offer_id")).orElse(null)))
                    .build();
        } catch (Exception e) {
            logger.error("Error in generating Initial Offer Event: {}", e.getMessage());
            return null;
        }

    }

    private OfferEventV1 genrerateSubmitOfferBQEvent(SubmitLoanOfferRequest submitLoanOfferRequest, PlOnboardingResponse plOnboardingResponse) {
        try {
            if (!isValidPlOnboardingResponseType(plOnboardingResponse, PlOnboardingResponseType.SubmitLoanOfferResponse)) {
                return null;
            }
            SubmitLoanOfferResponse submitLoanOfferResponse = (SubmitLoanOfferResponse) plOnboardingResponse;
            if (submitLoanOfferResponse == null) {
                return null;
            }
            ApplicationDataResponse applicationDataResponse = winterfellClient.getApplication("CALM", submitLoanOfferRequest.getLspApplicationId(), null, null);
            ArrayList<OfferDetail> offerDetails = new ArrayList<>();
            OfferDetail offerDetail = new OfferDetail();
            if (submitLoanOfferRequest.getLoanDetails() != null && submitLoanOfferRequest.getLoanDetails().getCharges() != null) {
                for (ChargeBreakUp charge : submitLoanOfferRequest.getLoanDetails().getCharges()) {
                    if (charge == null) continue;
                    if (charge.getChargeType() == com.flipkart.fintech.pandora.api.model.request.plOnboarding.ChargeType.PROCESSING_FEE) {
                        offerDetail.setPfAmount(charge.getAmount());
                        offerDetail.setGst(charge.getGstAmount().doubleValue());
                    }
                    if (charge.getChargeType() == com.flipkart.fintech.pandora.api.model.request.plOnboarding.ChargeType.STAMP_DUTY) {
                        offerDetail.setStampDuty(charge.getAmount());
                    }
                }
            }
            LoanParameter loanParameter = submitLoanOfferRequest.getLoanDetails();
            Tenure tenure = loanParameter != null ? loanParameter.getTenure() : null;
            BigDecimal roi = loanParameter != null ? loanParameter.getRoi() : null;
            offerDetail.setMinAmount(loanParameter != null ? (double) loanParameter.getLoanAmount() : null);
            offerDetail.setMaxAmount(loanParameter != null ? (double) loanParameter.getLoanAmount() : null);
            offerDetail.setMinTenure(tenure != null ? (double) tenure.getValue() : null);
            offerDetail.setMaxTenure(tenure != null ? (double) tenure.getValue() : null);
            offerDetail.setRoi(roi != null ? roi.doubleValue() : null);
            offerDetail.setNetDisbursed(loanParameter != null ? loanParameter.getNetDisbursalAmount().longValue() : null);
            offerDetail.setEmi(loanParameter != null ? loanParameter.getEmi().longValue() : null);
            offerDetails.add(offerDetail);
            return OfferEventV1
                    .newBuilder()
                    .setEventId(String.format(bqEventIdFormat, submitLoanOfferRequest.getLspApplicationId(), submitLoanOfferRequest.getRequestTime()))
                    .setLender(String.valueOf(Lender.AXIS))
                    .setApplicationId(submitLoanOfferRequest.getLspApplicationId())
                    .setValidity(submitLoanOfferResponse.getLenderApplicationValidity())
                    .setOfferDetails(offerDetails)
                    .setOfferType(String.valueOf(OfferType.SUBMITTED))
                    .setAccountId(Optional.ofNullable(applicationDataResponse).map(ApplicationDataResponse::getSmUserId).orElse(null))
                    .setExternalUserId(Optional.ofNullable(applicationDataResponse).map(ApplicationDataResponse::getExternalUserId).orElse(null))
                    .setOfferId(String.valueOf(Optional.ofNullable(applicationDataResponse != null ? applicationDataResponse.getApplicationData() : null).map(applicationData -> applicationData.get("offer_id")).orElse(null)))
                    .build();
        } catch (Exception e) {
            logger.error("Error in generating Submit Offer Event: {}", e.getMessage());
            return null;
        }

    }

    private OfferEventV1 generateLenderPlatformOfferEvent(WebHookRequest webHookRequest) {
        try {
            ApplicationDataResponse applicationDataResponse = winterfellClient.getApplication("CALM", webHookRequest.getLspApplicationId(), null, null);
            return OfferEventV1.newBuilder()
                    .setEventId(String.format(bqEventIdFormat, applicationDataResponse.getApplicationId(), Instant.now().toEpochMilli()))
                    .setApplicationId(webHookRequest.getLspApplicationId())
                    .setAccountId(Optional.ofNullable(applicationDataResponse).map(ApplicationDataResponse::getSmUserId).orElse(null))
                    .setLead(String.valueOf(Optional.ofNullable(applicationDataResponse != null ? applicationDataResponse.getApplicationData() : null).map(applicationData -> applicationData.get("leadId")).orElse(null)))
                    .setExternalUserId(Optional.ofNullable(applicationDataResponse).map(ApplicationDataResponse::getExternalUserId).orElse(null))
                    .setOfferId(String.valueOf(Optional.ofNullable(applicationDataResponse != null ? applicationDataResponse.getApplicationData() : null).map(applicationData -> applicationData.get("offer_id")).orElse(null)))
                    .setOfferType(String.valueOf(OfferType.DISBURSED))
                    .setOfferDetails(new ArrayList<>())
                    .setLender(String.valueOf(Lender.AXIS))
                    .build();
        } catch (Exception e) {
            logger.error("PlOnboardingManagerImpl.generateLenderPlatformOfferEvent: Error in generating Lender Platform Offer: {}", e.getMessage());
            return null;
        }
    }

    private void submitOfferEventToBQ(OfferEventV1 offerEventV1) {
        try {
            logger.info("PlOnboardingManagerImpl.submitOfferEventToBQ");
            bqIngestionHelper.ingestOfferEvent(offerEventV1);
        } catch (Exception e) {
            logger.error("PlOnboardingManagerImpl: Error in submitOfferEventToBQ: {}", e.getMessage());
        }
    }
}
