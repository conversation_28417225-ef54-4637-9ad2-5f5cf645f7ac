package com.flipkart.fintech.pandora.service.core.plUserOnboarding;

import calm.client.shade.com.nimbusds.jose.JOSEException;
import com.flipkart.fintech.pandora.api.model.pl.sandbox.response.AccountAggregatorResponse;
import com.flipkart.fintech.pandora.api.model.request.plOnboarding.sandbox.v2.GetApplicationStatusRequest;
import com.flipkart.fintech.pandora.api.model.request.plOnboarding.sandbox.v2.AccountAggregatorRequest;
import com.flipkart.fintech.pandora.api.model.request.plOnboarding.sandbox.v2.CreateApplicationRequest;
import com.flipkart.fintech.pandora.api.model.request.plOnboarding.sandbox.v2.OfferRequest;
import com.flipkart.fintech.pandora.api.model.request.plOnboarding.sandbox.v2.SubmitOfferRequest;
import com.flipkart.fintech.pandora.api.model.response.sandbox.v2.GetApplicationStatusResponse;
import com.flipkart.fintech.pandora.service.client.exceptions.LenderException;
import com.flipkart.fintech.pandora.service.client.plOnboarding.exceptions.InvalidJweSignatureException;
import com.flipkart.fintech.pandora.service.client.sandbox.v2.response.*;
import com.flipkart.fintech.pandora.service.core.plUserOnboarding.exceptions.PlOnboardingInvalidParamException;
import com.flipkart.fintech.pinaka.client.PinakaClientException;

import java.io.IOException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.UnrecoverableKeyException;
import java.security.cert.CertificateException;
import java.text.ParseException;

import com.flipkart.fintech.winterfell.client.WintefellClientException;
import org.json.JSONException;

public interface SandboxPlManager {

    CreateApplicationResponse getCreateApplicationResponse(CreateApplicationRequest createApplicationRequest, String requestId) throws PlOnboardingInvalidParamException, LenderException, WintefellClientException;

    GenerateOfferResponse getGenerateOfferResponse(OfferRequest offerRequest, String requestId) throws PlOnboardingInvalidParamException;

    GetOfferResponse getGetOfferResponse(OfferRequest getOfferRequest, String requestId) throws PlOnboardingInvalidParamException;

    GetApplicationResponse getApplicationResponse(GetApplicationStatusRequest getApplicationStatusRequest, String requestId) throws PlOnboardingInvalidParamException, LenderException;

    GetApplicationStatusResponse getApplicationStatusResponse(GetApplicationStatusRequest getApplicationStatusRequest, String requestId) throws PlOnboardingInvalidParamException, LenderException;

    SubmitOfferResponse getSubmitOfferResponse(SubmitOfferRequest submitOfferRequest, String requestId) throws PlOnboardingInvalidParamException;

    void consumeLenderPlatformEvent(String encryptedRequest, String clientId, String requestId)
        throws UnrecoverableKeyException, InvalidJweSignatureException,
        CertificateException, ParseException, NoSuchAlgorithmException, KeyStoreException, IOException, JOSEException, PinakaClientException, JSONException;

    AccountAggregatorResponse initAccountAggregator(AccountAggregatorRequest accountAggregatorRequest, String requestId) throws PlOnboardingInvalidParamException, LenderException;

}
