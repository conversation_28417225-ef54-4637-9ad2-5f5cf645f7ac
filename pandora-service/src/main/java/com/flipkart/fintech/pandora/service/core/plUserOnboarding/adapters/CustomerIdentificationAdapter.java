package com.flipkart.fintech.pandora.service.core.plUserOnboarding.adapters;

import com.flipkart.fintech.pandora.api.model.common.Gender;
import com.flipkart.fintech.pandora.api.model.request.plOnboarding.Lender;
import com.flipkart.fintech.pandora.api.model.response.plOnboarding.CustomerSegment;
import com.flipkart.fintech.pandora.api.model.response.plOnboarding.IdentifyCustomerResponse;
import com.flipkart.fintech.pandora.api.model.response.plOnboarding.Segment;
import com.flipkart.fintech.pandora.api.model.response.plOnboarding.Status;
import com.flipkart.fintech.pandora.service.application.PandoraMetricRegistry;
import com.flipkart.fintech.pandora.service.client.plOnboarding.request.CommunicationAddress;
import com.flipkart.fintech.pandora.service.client.plOnboarding.request.CustomerIdentificationRequest;
import com.flipkart.fintech.pandora.service.client.plOnboarding.request.ProductDetailsRequest;
import com.flipkart.fintech.pandora.service.client.plOnboarding.request.UserDetailsRequest;
import com.flipkart.fintech.pandora.service.client.plOnboarding.response.CustomerIdentificationResponse;
import com.flipkart.fintech.pandora.service.client.plOnboarding.response.CustomerIdentificationResponseWrapper;
import com.flipkart.fintech.pandora.service.client.plOnboarding.response.ErrorResponse;
import com.flipkart.fintech.pandora.service.client.plOnboarding.response.ErrorResponseWrapper;
import com.flipkart.fintech.pandora.service.client.plOnboarding.response.LenderRes;
import com.flipkart.fintech.pandora.service.core.plUserOnboarding.PlConstants;
import com.flipkart.fintech.pandora.service.core.plUserOnboarding.PlUserOnboardingConfig;
import com.flipkart.fintech.pandora.service.core.plUserOnboarding.annotations.LenderConfiguration;
import com.flipkart.fintech.pandora.service.core.plUserOnboarding.exceptions.PlOnboardingInvalidParamException;
import com.flipkart.fintech.pandora.service.core.plUserOnboarding.models.IdentifyCustomerEnriched;
import com.flipkart.fintech.pandora.service.core.plUserOnboarding.utils.PlUserOnboardingUtil;
import com.flipkart.fintech.pandora.service.exception.PandoraException;
import com.google.inject.Inject;
import javax.ws.rs.core.Response;

@LenderConfiguration(lender = Lender.AXIS, operation = PlConstants.CUSTOMER_IDENTIFICATION)
public class CustomerIdentificationAdapter implements ILenderOperationAdapter<IdentifyCustomerEnriched> {
    private final PlUserOnboardingConfig plUserOnboardingConfig;

    @Inject
    public CustomerIdentificationAdapter(PlUserOnboardingConfig plUserOnboardingConfig) {
        this.plUserOnboardingConfig = plUserOnboardingConfig;
    }

    @Override
    public CustomerIdentificationRequest generateLenderRequestBody(IdentifyCustomerEnriched identifyCustomerEnriched)
            throws PlOnboardingInvalidParamException {
        String lenderName = identifyCustomerEnriched.getIdentifiyCustomerRequest().getLenders().get(0).name();
        CommunicationAddress communicationAddress = CommunicationAddress.builder()
                .address(identifyCustomerEnriched.getUserCommunication().getAddress())
                .pinCode(identifyCustomerEnriched.getUserCommunication().getPincode())
                .build();
        String applicationType = plUserOnboardingConfig.getLenderSpecificConfigValueAsString(lenderName,
                PlConstants.CUSTOMER_IDENTIFICATION,
                PlConstants.APPLICATION_TYPE);
        ProductDetailsRequest productDetailsRequest = ProductDetailsRequest.builder()
                .productType(plUserOnboardingConfig.getLenderSpecificConfigValueAsString(lenderName,
                        PlConstants.CUSTOMER_IDENTIFICATION,
                        identifyCustomerEnriched.getIdentifiyCustomerRequest().getProductDetails().getType(),
                        PlConstants.PRODUCT_TYPE))
                .subProductType(plUserOnboardingConfig.getLenderSpecificConfigValueAsString(lenderName,
                        PlConstants.CUSTOMER_IDENTIFICATION,
                        identifyCustomerEnriched.getIdentifiyCustomerRequest().getProductDetails().getType(),
                        PlConstants.SUB_PRODUCT_TYPE))
                .build();
        Gender userGender = Gender.valueOf(plUserOnboardingConfig
                .getLenderSpecificConfigValueAsString(lenderName,
                        PlConstants.CUSTOMER_IDENTIFICATION,
                        identifyCustomerEnriched.getIdentifiyCustomerRequest().getIdentificationDetails().getGender().name()));
        UserDetailsRequest userDetails = UserDetailsRequest.builder()
                .pan(identifyCustomerEnriched.getIdentifiyCustomerRequest().getIdentificationDetails().getPan())
                .dob(identifyCustomerEnriched.getIdentifiyCustomerRequest().getIdentificationDetails().getDob())
                .mobileNumber(PlUserOnboardingUtil.removeMobNumberPref(identifyCustomerEnriched.
                                getUserCommunication().getMobileNumber()))
                .communicationAddress(communicationAddress)
                .emailId(identifyCustomerEnriched.getUserCommunication().getEmailId())
                .gender(userGender)
                .firstName(identifyCustomerEnriched.getIdentifiyCustomerRequest().getIdentificationDetails().getFirstName())
                .lastName(identifyCustomerEnriched.getIdentifiyCustomerRequest().getIdentificationDetails().getLastName())
                .build();
        CustomerIdentificationRequest customerIdentificationRequest = CustomerIdentificationRequest.builder()
                .lspApplicationId(identifyCustomerEnriched.getIdentifiyCustomerRequest().getLspApplicationId())
                .userDetails(userDetails)
                .productDetails(productDetailsRequest)
                .applicationType(applicationType)
                .build();

        return customerIdentificationRequest;
    }

    @Override
    public IdentifyCustomerResponse generatePandoraResponseBody(IdentifyCustomerEnriched identifyCustomerEnriched,
                                                                LenderRes lenderRes) {
        IdentifyCustomerResponse identifyCustomerResponse;
        String lenderName = identifyCustomerEnriched.getIdentifiyCustomerRequest().getLenders().get(0).name();
        switch (Response.Status.Family.familyOf(lenderRes.getResponseStatus())) {
            case SUCCESSFUL:
                CustomerIdentificationResponse customerIdentificationResponse = PlUserOnboardingUtil.parseJsonToObject(lenderRes.getContext(),
                        CustomerIdentificationResponseWrapper.class)
                        .getCustomerIdentificationResponse();
                if (customerIdentificationResponse.getStatus() == com.flipkart.fintech.pandora.service.client.plOnboarding.response.Status.FAILED) {
                    PandoraMetricRegistry
                            .getMetricRegistry().meter(String.format(PlConstants.CI_LENDER_BUSINESS_REJECTION_METRIC, lenderName)).mark();
                    identifyCustomerResponse = IdentifyCustomerResponse.builder()
                            .applicationId(identifyCustomerEnriched.getIdentifiyCustomerRequest().getLspApplicationId())
                            .status(Status.REJECTED)
                            .errorMessage(customerIdentificationResponse.getSubStatus())
                            .build();
                } else {
                    PandoraMetricRegistry
                            .getMetricRegistry().meter(String.format(PlConstants.CI_LENDER_SUCCESSFUL_METRIC, lenderName)).mark();
                    CustomerSegment customerSegment = new CustomerSegment();
                    customerSegment.setSegment(Segment.valueOf(plUserOnboardingConfig.getLenderSpecificConfigValueAsString(lenderName,
                            PlConstants.CUSTOMER_IDENTIFICATION,
                            PlConstants.CUSTOMER_SEGMENT,
                            customerIdentificationResponse.getCustomerSegment().name())));
                    customerSegment.setSubsegment(customerIdentificationResponse.getCustomerSubSegment());
                    identifyCustomerResponse = IdentifyCustomerResponse.builder()
                            .status(Status.SUCCESS)
                            .applicationId(identifyCustomerEnriched.getIdentifiyCustomerRequest().getLspApplicationId())
                            .customerSegment(customerSegment)
                            .applicationValidTill(customerIdentificationResponse.getApplicationValidTill())
                            .lenderApplicationId(customerIdentificationResponse.getLenderApplicationId())
                            .build();
                }
                break;
            case CLIENT_ERROR:
                PandoraMetricRegistry
                        .getMetricRegistry().meter(String.format(PlConstants.CI_LENDER_CLIENT_ERROR_METRIC, lenderName)).mark();
                ErrorResponse clientErrorResponse = PlUserOnboardingUtil.parseJsonToObject(lenderRes.getContext(),
                        ErrorResponseWrapper.class)
                        .getErrorResponse();
                identifyCustomerResponse = IdentifyCustomerResponse.builder()
                        .applicationId(identifyCustomerEnriched.getIdentifiyCustomerRequest().getLspApplicationId())
                        .status(Status.RETRY_WITH_EDIT)
                        .errorMessage(String.format(PlConstants.LENDER_ERROR_MSG,
                                clientErrorResponse.getErrorCode(),
                                clientErrorResponse.getClientErrorMessage(), clientErrorResponse.getServerErrorMessage()))
                        .build();
                break;
            case SERVER_ERROR:
                PandoraMetricRegistry
                        .getMetricRegistry().meter(String.format(PlConstants.CI_LENDER_SERVER_ERROR_METRIC, lenderName)).mark();
                ErrorResponse serverErrorResponse = PlUserOnboardingUtil.parseJsonToObject(lenderRes.getContext(),
                        ErrorResponseWrapper.class)
                        .getErrorResponse();
                identifyCustomerResponse = IdentifyCustomerResponse.builder()
                        .applicationId(identifyCustomerEnriched.getIdentifiyCustomerRequest().getLspApplicationId())
                        .status(Status.RETRY_WITHOUT_EDIT)
                        .errorMessage(String.format(PlConstants.LENDER_ERROR_MSG,
                                serverErrorResponse.getErrorCode(),
                                serverErrorResponse.getClientErrorMessage(),
                                serverErrorResponse.getServerErrorMessage()))
                        .build();
                break;
            default:
                throw new PandoraException(PlConstants.HTTP_STATUS_NOT_SUPPORTED_ERROR_MSG);
        }

        return identifyCustomerResponse;
    }
}
