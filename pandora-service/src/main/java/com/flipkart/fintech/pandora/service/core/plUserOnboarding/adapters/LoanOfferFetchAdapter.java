package com.flipkart.fintech.pandora.service.core.plUserOnboarding.adapters;

import com.flipkart.fintech.pandora.api.model.request.plOnboarding.*;
import com.flipkart.fintech.pandora.api.model.response.plOnboarding.Charges;
import com.flipkart.fintech.pandora.api.model.response.plOnboarding.Chargetype;
import com.flipkart.fintech.pandora.api.model.response.plOnboarding.EligibleLoanOfferResponse;
import com.flipkart.fintech.pandora.api.model.response.plOnboarding.LoanAmountDetail;
import com.flipkart.fintech.pandora.api.model.response.plOnboarding.Offer;
import com.flipkart.fintech.pandora.api.model.response.plOnboarding.RateType;
import com.flipkart.fintech.pandora.api.model.response.plOnboarding.Status;
import com.flipkart.fintech.pandora.api.model.response.plOnboarding.TennureDetails;
import com.flipkart.fintech.pandora.api.model.response.plOnboarding.Unit;
import com.flipkart.fintech.pandora.service.application.PandoraMetricRegistry;
import com.flipkart.fintech.pandora.service.client.plOnboarding.request.ActionData;
import com.flipkart.fintech.pandora.service.client.plOnboarding.request.Consent;
import com.flipkart.fintech.pandora.service.client.plOnboarding.request.ConsentDocument;
import com.flipkart.fintech.pandora.service.client.plOnboarding.request.ConsentType;
import com.flipkart.fintech.pandora.service.client.plOnboarding.request.EmployerDetails;
import com.flipkart.fintech.pandora.service.client.plOnboarding.request.Intent;
import com.flipkart.fintech.pandora.service.client.plOnboarding.request.LoanOfferRequest;
import com.flipkart.fintech.pandora.service.client.plOnboarding.request.LspVariable;
import com.flipkart.fintech.pandora.service.client.plOnboarding.request.OccupationType;
import com.flipkart.fintech.pandora.service.client.plOnboarding.request.UserDetails;
import com.flipkart.fintech.pandora.service.client.plOnboarding.request.UserDetails.UserDetailsBuilder;
import com.flipkart.fintech.pandora.service.client.plOnboarding.response.ErrorResponse;
import com.flipkart.fintech.pandora.service.client.plOnboarding.response.ErrorResponseWrapper;
import com.flipkart.fintech.pandora.service.client.plOnboarding.response.LenderRes;
import com.flipkart.fintech.pandora.service.client.plOnboarding.response.LoanOfferResponse;
import com.flipkart.fintech.pandora.service.client.plOnboarding.response.LoanOfferResponseWrapper;
import com.flipkart.fintech.pandora.service.client.plOnboarding.response.OfferData;
import com.flipkart.fintech.pandora.service.client.plOnboarding.response.OfferLimitData;
import com.flipkart.fintech.pandora.service.core.plUserOnboarding.PlConstants;
import com.flipkart.fintech.pandora.service.core.plUserOnboarding.PlUserOnboardingConfig;
import com.flipkart.fintech.pandora.service.core.plUserOnboarding.annotations.LenderConfiguration;
import com.flipkart.fintech.pandora.service.core.plUserOnboarding.models.EligibleLoanOfferEnriched;
import com.flipkart.fintech.pandora.service.core.plUserOnboarding.utils.PlUserOnboardingUtil;
import com.flipkart.fintech.pandora.service.exception.PandoraException;
import com.google.inject.Inject;

import java.math.BigDecimal;
import java.util.*;
import javax.ws.rs.core.Response;

import static com.flipkart.fintech.pandora.service.application.filters.ClientIdFilter.logger;

@LenderConfiguration(lender = Lender.AXIS, operation = PlConstants.LOAN_OFFER_FETCH)
public class LoanOfferFetchAdapter implements ILenderOperationAdapter<EligibleLoanOfferEnriched> {
    private final PlUserOnboardingConfig plUserOnboardingConfig;

    @Inject
    public LoanOfferFetchAdapter(PlUserOnboardingConfig plUserOnboardingConfig) {
        this.plUserOnboardingConfig = plUserOnboardingConfig;
    }

    @Override
    public LoanOfferRequest generateLenderRequestBody(EligibleLoanOfferEnriched eligibleLoanOfferEnriched) {

        String lenderName = eligibleLoanOfferEnriched.getEligibleLoanOfferRequest().getLenders().get(0).name();
        ConsentDocument consentDocument = ConsentDocument.builder()
                .consentType(ConsentType.CONSENT_DATASHARING_BUREAUPULL)
                .actionData(new ActionData(plUserOnboardingConfig.getLenderSpecificConfigValueAsString(lenderName,
                        PlConstants.LOAN_OFFER_FETCH,
                        PlConstants.CONSENT_ACTION)))
                .id(plUserOnboardingConfig.getLenderSpecificConfigValueAsInt(lenderName,
                        PlConstants.LOAN_OFFER_FETCH,
                        PlConstants.CONSENT_ID))
                .userAction(plUserOnboardingConfig.getLenderSpecificConfigValueAsString(lenderName,
                        PlConstants.LOAN_OFFER_FETCH,
                        PlConstants.CONSENT_USER_ACTION))
                .build();

        List<ConsentDocument> consentDocumentList= new ArrayList<>();
        consentDocumentList.add(consentDocument);

        if(Objects.nonNull(eligibleLoanOfferEnriched.getEligibleLoanOfferRequest().getConsentMetadataList())){
            for(ConsentMetadata consentMetadata: eligibleLoanOfferEnriched.getEligibleLoanOfferRequest().getConsentMetadataList()){
                if(consentMetadata.getConsentFor().equals(ConsentFor.MFI.toString())){
                    ConsentDocument consentDocument2 = ConsentDocument.builder()
                            .consentType(ConsentType.CONSENT_CONFIRM_HOUSEHOLD_INCOME)
                            .actionData(new ActionData(plUserOnboardingConfig.getLenderSpecificConfigValueAsString(lenderName,
                                    PlConstants.LOAN_OFFER_FETCH,
                                    PlConstants.CONSENT_ACTION)))
                            .id(plUserOnboardingConfig.getLenderSpecificConfigValueAsInt(lenderName,
                                    PlConstants.LOAN_OFFER_FETCH,
                                    PlConstants.CONSENT_ID_2))
                            .userAction(plUserOnboardingConfig.getLenderSpecificConfigValueAsString(lenderName,
                                    PlConstants.LOAN_OFFER_FETCH,
                                    PlConstants.CONSENT_USER_ACTION))
                            .build();

                    consentDocumentList.add(consentDocument2);
                }
            }
        }

        Consent consent = populateConsentDetails(eligibleLoanOfferEnriched, consentDocumentList);
        LspVariable lspVariable = LspVariable.builder()
                .userScore(makePlScore(eligibleLoanOfferEnriched.getEligibleLoanOfferRequest().getPlScore(), lenderName))
                .loanPurpose(eligibleLoanOfferEnriched.getEligibleLoanOfferRequest().getLoanPurpose())
                .freeField15(eligibleLoanOfferEnriched.getEligibleLoanOfferRequest().getFreeField15())
                .freeField6(eligibleLoanOfferEnriched.getEligibleLoanOfferRequest().getFreeField6())
                .freeField7(eligibleLoanOfferEnriched.getEligibleLoanOfferRequest().getFreeField7())
                .freeField10(eligibleLoanOfferEnriched.getEligibleLoanOfferRequest().getFreeField10())
                .freeField11(eligibleLoanOfferEnriched.getEligibleLoanOfferRequest().getFreeField11())
                .freeField12(eligibleLoanOfferEnriched.getEligibleLoanOfferRequest().getFreeField12())
                .freeField13(eligibleLoanOfferEnriched.getEligibleLoanOfferRequest().getFreeField13())
                .freeField14(eligibleLoanOfferEnriched.getEligibleLoanOfferRequest().getFreeField14())
                .freeField8(eligibleLoanOfferEnriched.getLocation().getLat())
                .freeField9(eligibleLoanOfferEnriched.getLocation().getLng())
                .freeField16(eligibleLoanOfferEnriched.getEligibleLoanOfferRequest().getFreeField16())
                .freeField17(eligibleLoanOfferEnriched.getEligibleLoanOfferRequest().getFreeField17())
                .build();
        UserDetails userDetails = getUserDetails(eligibleLoanOfferEnriched.getEligibleLoanOfferRequest());

        LoanOfferRequest loanOfferRequest = LoanOfferRequest.builder()
                .lspApplicationId(eligibleLoanOfferEnriched.getEligibleLoanOfferRequest().getLspApplicationId())
                .consents(consent)
                .lspVariables(lspVariable)
                .userDetails(userDetails)
                .intent(Intent.UPGRADE)
                .build();

        return loanOfferRequest;
    }

    // method for same case exists in Pinaka also, to be removed from Pinaka
    private String makePlScore(String plScore, String lenderName) {
        return (plScore == null || plScore.equals("null") || plScore.trim().isEmpty()) ? plUserOnboardingConfig.getDefaultPlScore(lenderName
                ,PlConstants.DEFAULT_PL_SCORE) : plScore;
    }

    private Consent populateConsentDetails(EligibleLoanOfferEnriched eligibleLoanOfferEnriched, List<ConsentDocument> consentDocumentList) {

        ConsentMetadata consentMetadata = eligibleLoanOfferEnriched.getEligibleLoanOfferRequest().getConsentMetadata();

        Consent consent = new Consent();

        if(Objects.nonNull(consentMetadata)) {
            consent = Consent.builder().
                    deviceId(consentMetadata.getDeviceId()).
                    deviceInfo(consentMetadata.getDeviceInfo()).
                    deviceParams(consentMetadata.getDeviceParams() == null ? "NA" : consentMetadata.getDeviceParams()).
                    currentTimeStamp(consentMetadata.getCurrentTimeStamp()).
                    ipAddresses(PlUserOnboardingUtil.ipsAsList(consentMetadata.getUserIP())).
                    build();
        }
        consent.setDocumentList(consentDocumentList);
        return consent;
    }

    @Override
    public EligibleLoanOfferResponse generatePandoraResponseBody(EligibleLoanOfferEnriched eligibleLoanOfferEnriched,
            LenderRes lenderRes) {
        EligibleLoanOfferResponse eligibleLoanOfferResponse;
        String lenderName = eligibleLoanOfferEnriched.getEligibleLoanOfferRequest().getLenders().get(0).name();
        switch (Response.Status.Family.familyOf(lenderRes.getResponseStatus())) {
            case SUCCESSFUL:
                LoanOfferResponse loanOfferResponse = PlUserOnboardingUtil.parseJsonToObject(lenderRes.getContext(),
                                LoanOfferResponseWrapper.class)
                        .getLoanOfferResponse();
                if (loanOfferResponse.getStatus() == com.flipkart.fintech.pandora.service.client.plOnboarding.response.Status.FAILED) {
                    PandoraMetricRegistry
                            .getMetricRegistry().meter(String.format(PlConstants.EO_LENDER_BUSINESS_REJECTION_METRIC, lenderName)).mark();
                    eligibleLoanOfferResponse = EligibleLoanOfferResponse.builder()
                            .lspApplicationId(eligibleLoanOfferEnriched.getEligibleLoanOfferRequest().getLspApplicationId())
                            .status(Status.REJECTED)
                            .errorMessage(loanOfferResponse.getSubStatus())
                            .build();
                } else {
                    PandoraMetricRegistry
                            .getMetricRegistry().meter(String.format(PlConstants.EO_LENDER_SUCCESSFUL_METRIC, lenderName)).mark();
                    OfferLimitData offerLimitData = loanOfferResponse
                            .getOfferData()
                            .getFinalOffer()
                            .getOfferStateData()
                            .getOfferLimitData()
                            .get(0);
                    OfferData offerData = offerLimitData.getOfferData();
                    TennureDetails tenureDetails = TennureDetails.builder()
                            .min(offerLimitData.getOfferData().getTenure().getMinTenure())
                            .max(offerLimitData.getOfferData().getTenure().getMaxTenure())
                            .stepSize(offerLimitData.getOfferData().getTenure().getTenureStepSize())
                            .unit(Unit.valueOf(offerLimitData.getOfferData().getTenure().getTenureType().name()))
                            .build();
                    List<Charges> charges = getCharges(offerData);
                    LoanAmountDetail loanAmountDetail = LoanAmountDetail.builder()
                            .min(offerData.getMinSanctionedAmt().longValue())
                            .max(offerData.getMaxSanctionedAmt().longValue())
                            .build();
                    Offer offer = Offer.builder()
                            .tenureDetails(tenureDetails)
                            .emiThreshold(offerData.getRepayingCapacityPerMonth())
                            .charge(charges)
                            .loanAmountDetail(loanAmountDetail)
                            .roi(offerData.getRoi())
                            .build();
                    eligibleLoanOfferResponse = EligibleLoanOfferResponse.builder()
                            .lspApplicationId(eligibleLoanOfferEnriched.getEligibleLoanOfferRequest().getLspApplicationId())
                            .status(Status.SUCCESS)
                            .LenderApplicationValidity(loanOfferResponse.getApplicationValidTill())
                            .offer(offer)
                            .build();
                }
                break;
            case CLIENT_ERROR:
                PandoraMetricRegistry
                        .getMetricRegistry().meter(String.format(PlConstants.EO_LENDER_CLIENT_ERROR_METRIC, lenderName)).mark();
                ErrorResponse clientErrorResponse = PlUserOnboardingUtil.parseJsonToObject(lenderRes.getContext(),
                                ErrorResponseWrapper.class)
                        .getErrorResponse();
                eligibleLoanOfferResponse = EligibleLoanOfferResponse.builder()
                        .lspApplicationId(eligibleLoanOfferEnriched.getEligibleLoanOfferRequest().getLspApplicationId())
                        .status(Status.RETRY_WITH_EDIT)
                        .errorMessage(String.format(PlConstants.LENDER_ERROR_MSG,
                                clientErrorResponse.getErrorCode(),
                                clientErrorResponse.getClientErrorMessage(),
                                clientErrorResponse.getServerErrorMessage()))
                        .build();
                break;
            case SERVER_ERROR:
                PandoraMetricRegistry
                        .getMetricRegistry().meter(String.format(PlConstants.EO_LENDER_SERVER_ERROR_METRIC, lenderName)).mark();
                ErrorResponse serverErrorResponse = PlUserOnboardingUtil.parseJsonToObject(lenderRes.getContext(),
                                ErrorResponseWrapper.class)
                        .getErrorResponse();
                eligibleLoanOfferResponse = EligibleLoanOfferResponse.builder()
                        .lspApplicationId(eligibleLoanOfferEnriched.getEligibleLoanOfferRequest().getLspApplicationId())
                        .status(Status.RETRY_WITHOUT_EDIT)
                        .errorMessage(String.format(PlConstants.LENDER_ERROR_MSG,
                                serverErrorResponse.getErrorCode(),
                                serverErrorResponse.getClientErrorMessage(),
                                serverErrorResponse.getServerErrorMessage()))
                        .build();
                break;
            default:
                throw new PandoraException(PlConstants.HTTP_STATUS_NOT_SUPPORTED_ERROR_MSG);
        }

        return eligibleLoanOfferResponse;
    }

    private UserDetails getUserDetails(EligibleLoanOfferRequest eligibleLoanOfferRequest) {
        UserDetails userDetails;
        UserDetailsBuilder userDetailsBuilder = UserDetails.builder();
        userDetailsBuilder.firstName(eligibleLoanOfferRequest.getFirstName());
        userDetailsBuilder.lastName(eligibleLoanOfferRequest.getLastName());
        if (eligibleLoanOfferRequest.getOccupationaDetails().getEmploymentType() == EmploymentType.SALARIED) {
            EmployerDetails employerDetails = EmployerDetails.builder()
                    .employerId(eligibleLoanOfferRequest.getOccupationaDetails().getEmploymentDetails().getEmployerId())
                    .employerName(eligibleLoanOfferRequest.getOccupationaDetails().getEmploymentDetails().getEmployerName())
                    .build();
            //TODO: Remove below monthlyIncome & annualIncome post Axis fix the issue in CUG testing
            BigDecimal monthlyIncome = eligibleLoanOfferRequest.getOccupationaDetails()
                    .getEmploymentDetails().getMonthlyIncome();
            BigDecimal annualIncome = monthlyIncome.multiply(new BigDecimal(12));
            userDetails = userDetailsBuilder
                    .occupationType(OccupationType.valueOf(
                            eligibleLoanOfferRequest.getOccupationaDetails().getEmploymentType().name()))
                    .employerDetails(employerDetails)
                    //TODO: Remove below anualIncome post Axis fix the issue in CUG testing
                    .annualIncome(annualIncome)
                    .monthlyIncome(eligibleLoanOfferRequest.getOccupationaDetails().getEmploymentDetails().getMonthlyIncome())
                    .build();
        } else {
            EmployerDetails employerDetails = EmployerDetails.builder()
                    .industry(eligibleLoanOfferRequest.getOccupationaDetails().getEmploymentDetails().getIndustryId())
                    .industryName(eligibleLoanOfferRequest.getOccupationaDetails().getEmploymentDetails().getIndustryName())
                    .build();
            userDetails = userDetailsBuilder
                    .occupationType(OccupationType.valueOf(
                            eligibleLoanOfferRequest.getOccupationaDetails().getEmploymentType().name()))
                    .employerDetails(employerDetails)
                    .annualTurnover(eligibleLoanOfferRequest.getOccupationaDetails()
                            .getEmploymentDetails().getAnnualTurnOver())
                    .build();
        }

        return userDetails;
    }

    private List<Charges> getCharges(OfferData offerData) {
        List<Charges> chargesList = new ArrayList<>();
        Charges processingFeeCharge = Charges.builder()
                .chargeType(Chargetype.PROCESSING_FEE)
                .type(RateType.valueOf(offerData.getPf().getType().name()))
                .value(offerData.getPf().getValue())
                .gst(offerData.getPf().getGst())
                .build();
        Charges stampDuty = Charges.builder()
                .chargeType(Chargetype.STAMP_DUTY)
                .type(RateType.FLAT)
                .value(offerData.getStampDuty())
                .build();
        chargesList.add(processingFeeCharge);
        chargesList.add(stampDuty);

        return chargesList;
    }
}
