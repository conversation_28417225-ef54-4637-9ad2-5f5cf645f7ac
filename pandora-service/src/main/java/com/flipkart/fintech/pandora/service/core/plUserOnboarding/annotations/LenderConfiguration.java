package com.flipkart.fintech.pandora.service.core.plUserOnboarding.annotations;

import com.flipkart.fintech.pandora.api.model.request.plOnboarding.Lender;
import java.lang.annotation.ElementType;
import java.lang.annotation.Repeatable;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Repeatable(LenderConfigurations.class)
public @interface LenderConfiguration {
    Lender lender();
    String operation();
}
