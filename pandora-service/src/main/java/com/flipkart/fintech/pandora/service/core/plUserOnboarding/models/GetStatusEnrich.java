package com.flipkart.fintech.pandora.service.core.plUserOnboarding.models;

import com.flipkart.fintech.pandora.api.model.request.plOnboarding.PlOnboardingRequest;
import com.flipkart.fintech.pandora.api.model.request.plOnboarding.StatusRequest;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GetStatusEnrich implements PlOnboardingRequest {
    private StatusRequest statusRequest;

    private String jwtToken;
}
