package com.flipkart.fintech.pandora.service.core.transaction;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.flipkart.affordability.bnpl.tijori.api.enums.ProductCode;
import com.flipkart.fintech.common.enums.Tenant;
import com.flipkart.fintech.filter.RequestContextThreadLocal;
import com.flipkart.fintech.pandora.api.model.common.STATUS;
import com.flipkart.fintech.pandora.api.model.request.*;
import com.flipkart.fintech.pandora.api.model.request.ivr.enums.PaymentMethod;
import com.flipkart.fintech.pandora.api.model.response.*;
import com.flipkart.fintech.pandora.service.application.configuration.PandoraConfiguration;
import com.flipkart.fintech.pandora.service.client.auth.Scope;
import com.flipkart.fintech.pandora.service.client.PandoraServiceClientException;
import com.flipkart.fintech.pandora.service.client.pl.*;
import com.flipkart.fintech.pandora.service.client.pl.request.*;
import com.flipkart.fintech.pandora.service.client.pl.response.*;
import com.flipkart.fintech.pandora.service.core.mock.MockService;
import com.flipkart.fintech.pandora.service.client.auth.AccessTokenProvider;
import com.flipkart.fintech.pandora.service.hystrix.PandoraHystrixNameConstants;
import com.flipkart.fintech.pandora.service.hystrix.PandoraHystrixProperties;
import com.flipkart.fintech.pandora.service.hystrix.idfc.*;
import com.flipkart.fintech.pandora.service.utils.TijoriPandoraQueueUtil;
import com.flipkart.kloud.config.DynamicBucket;
import com.google.inject.Inject;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.ws.rs.core.Response;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

import static com.flipkart.affordability.common.ObjectMapperFactory.OBJECT_MAPPER;
import static com.flipkart.fintech.pandora.service.client.pl.LenderConstants.BULK_PAYMENT;
import static com.flipkart.fintech.pandora.service.hystrix.PandoraHystrixNameConstants.IDFC_CANCEL_REASON_STRING;
import static com.flipkart.fintech.pandora.service.utils.Constants.LENDER_FAILED;
import static com.flipkart.fintech.pandora.service.utils.Constants.LENDER_NULL_RESPONSE;

public class IDFCTransactionManagerV3 implements TransactionManager{
    private static final Logger logger = LoggerFactory.getLogger(IDFCTransactionManagerV3.class);
    private final IdfcBnplServiceClientV2 idfcBnplServiceClientV2;
    private final IdfcBnplServiceClientV3 idfcBnplServiceClientV3;
    private final PandoraHystrixProperties pandoraHystrixProperties;
    private final AccessTokenProvider accessTokenProvider;
    private final IdfcConfiguration idfcConfiguration;
    private final PandoraConfiguration pandoraConfiguration;
    private DynamicBucket dynamicBucket;
    public static final  String SALE_TRANSACTION_TYPE = "sale";
    public static final  String PENALTY_TRANSACTION_TYPE = "penalty";
    public static final  String CONV_FEE_TRANSACTION_TYPE = "conv_fee";
    public static final  String USAGE_FEE_TRANSACTION_TYPE = "usage_fee";
    public static final String EMI_PROCESSING_FEE_TRANSACTION_TYPE = "emi_processing_fee";

    @Inject
    public IDFCTransactionManagerV3(IdfcBnplServiceClientV2 idfcBnplServiceClientV2, IdfcBnplServiceClientV3 idfcBnplServiceClientV3, PandoraHystrixProperties pandoraHystrixProperties, AccessTokenProvider accessTokenProvider, IdfcConfiguration idfcConfiguration, PandoraConfiguration pandoraConfiguration, DynamicBucket dynamicBucket) {
        this.idfcBnplServiceClientV2 = idfcBnplServiceClientV2;
        this.idfcBnplServiceClientV3 = idfcBnplServiceClientV3;
        this.pandoraHystrixProperties = pandoraHystrixProperties;
        this.accessTokenProvider = accessTokenProvider;
        this.idfcConfiguration = idfcConfiguration;
        this.pandoraConfiguration = pandoraConfiguration;
        this.dynamicBucket = dynamicBucket;
    }


    @Override
    public SaleResponse createSaleForwardTransaction(SaleForwardRequest saleForwardRequest, ProductCode product) {
        return null;
    }

    @Override
    public SaleResponse createSaleReverseTransaction(SaleReverseRequest saleReverseRequest, ProductCode product) {
        SaleResponse saleResponse = null;
        switch (product) {
            case BNPL:
                saleResponse = createFcanSaleReverseTransaction(saleReverseRequest);
                break;
            case FLIPKART_ADVANZ:
                if(LenderConstants.EMI.equals(saleReverseRequest.getLoanType()) && LenderConstants.PARTIAL_REVERSAL_REQUEST_TYPE.equals(IdfcUtil.getCancellationRequestType(saleReverseRequest.getLoanAmount(), saleReverseRequest.getAmount()))) {
                    saleResponse = createAdvanzSaleReverseTransaction(saleReverseRequest);
                } else {
                    saleResponse = createFcanSaleReverseTransaction(saleReverseRequest);
                }
        }
        return saleResponse;
    }

    public SaleResponse createAdvanzSaleReverseTransaction(SaleReverseRequest advanzSaleReverseRequest) throws PandoraServiceClientException {

        SaleResponse advanzSaleResponse = new SaleResponse();

        EmiIdfcSaleReverseRequestV2 emiSaleReverseDownStreamRequest = new EmiIdfcSaleReverseRequestV2();
        emiSaleReverseDownStreamRequest.setSourceSystem(IdfcUtil.getTransactionSourceSystem(advanzSaleReverseRequest.getAppId()));
        emiSaleReverseDownStreamRequest.setSourceRequestId(shortenReqId(advanzSaleReverseRequest.getMerchantOrderId()));
        emiSaleReverseDownStreamRequest.setRequestType(IdfcUtil.getCancellationRequestType(advanzSaleReverseRequest.getLoanAmount(), advanzSaleReverseRequest.getAmount()));
        if (advanzSaleReverseRequest.getLoanType().equals("EMI")) {
            emiSaleReverseDownStreamRequest.setLoanId(advanzSaleReverseRequest.getLenderOrderId());
        } else {
            emiSaleReverseDownStreamRequest.setLoanId("");
        }
        emiSaleReverseDownStreamRequest.setLoanAmount(advanzSaleReverseRequest.getLoanAmount().toString());
        /**
         * TODO: temporary check due to bug in tijori, should be removed once the queue is cleared
         * this should always be the merchantPrevTxnId
         */
        if (advanzSaleReverseRequest.getMerchantPrevTxnId() == null || advanzSaleReverseRequest.getMerchantPrevTxnId().equals(advanzSaleReverseRequest.getMerchantOrderId())) {
            HashMap<String, String> stuckPztMap = TijoriPandoraQueueUtil.getMapFromJsonFile("/usr/share/pandora/src/main/resources/pztMap.json");
            if(stuckPztMap.keySet().contains(advanzSaleReverseRequest.getMerchantOrderId())) {
                emiSaleReverseDownStreamRequest.setOriginal_requestid(shortenReqId(stuckPztMap.get(advanzSaleReverseRequest.getMerchantOrderId())));
            } else {
                emiSaleReverseDownStreamRequest.setOriginal_requestid(shortenReqId(advanzSaleReverseRequest.getMerchantForwardOrderId()));
            }
        } else {
            emiSaleReverseDownStreamRequest.setOriginal_requestid(shortenReqId(advanzSaleReverseRequest.getMerchantPrevTxnId()));
        }
        emiSaleReverseDownStreamRequest.setTran_date(IdfcUtil.getFormattedDatetime(advanzSaleReverseRequest.getTransactionDate()));
        emiSaleReverseDownStreamRequest.setRevisedLoanAmount(
                (advanzSaleReverseRequest.getLoanAmount().subtract(advanzSaleReverseRequest.getAmount()).toString()));
        emiSaleReverseDownStreamRequest.setRevisedEMI("");
        emiSaleReverseDownStreamRequest.setRevisedTenure("");
        emiSaleReverseDownStreamRequest.setOrderId("");
        emiSaleReverseDownStreamRequest.setCancelReason(IDFC_CANCEL_REASON_STRING);


        EmiIdfcSaleReverseResponse emiSaleDownStreamResponse = null;
        try {
            if (RequestContextThreadLocal.get().isPerfRequest()) {
                emiSaleDownStreamResponse = MockService.mockEmiSaleReversal(advanzSaleReverseRequest, dynamicBucket.getInt("idfcSaleReverseDelay"));
            } else {
                emiSaleDownStreamResponse = new IdfcTermLoanCancellationCommandV2(idfcBnplServiceClientV2, emiSaleReverseDownStreamRequest,
                        accessTokenProvider.getAccessToken(Scope.LENDING_IDFC), PandoraHystrixNameConstants.IDFC, PandoraHystrixNameConstants.IDFC_SALE_REVERSE_KEY,
                        PandoraHystrixNameConstants.IDFC_TERMLOAN_CANCELLATION_KEY, pandoraHystrixProperties).execute();
            }
        } catch (Exception e) {
            try {
                throw new PandoraServiceClientException(e.getMessage(), LenderConstants.TERM_LOAN_CANCELLATION_ENDPOINT_V2, OBJECT_MAPPER.writeValueAsString(emiSaleReverseDownStreamRequest), null);
            } catch (JsonProcessingException ex) {
                throw new PandoraServiceClientException(ex);
            }
        }

        if (null == emiSaleDownStreamResponse) {
            try {
                throw new PandoraServiceClientException(LENDER_NULL_RESPONSE, LenderConstants.TERM_LOAN_CANCELLATION_ENDPOINT_V2, OBJECT_MAPPER.writeValueAsString(emiSaleReverseDownStreamRequest), null);
            } catch (JsonProcessingException ex) {
                throw new PandoraServiceClientException(ex);
            }
        }
        if (!emiSaleDownStreamResponse.isSuccess()) {
            try {
                throw new PandoraServiceClientException(LENDER_FAILED,
                        LenderConstants.TERM_LOAN_CANCELLATION_ENDPOINT_V2,
                        OBJECT_MAPPER.writeValueAsString(emiSaleReverseDownStreamRequest),
                        OBJECT_MAPPER.writeValueAsString(emiSaleDownStreamResponse)
                );
            } catch (JsonProcessingException ex) {
                throw new PandoraServiceClientException(ex);
            }

        }
        advanzSaleResponse.setLenderOrderId(emiSaleDownStreamResponse.getTransaction_id());
        advanzSaleResponse.setSuccess(emiSaleDownStreamResponse.isSuccess());

        return advanzSaleResponse;
    }

    private SaleResponse createFcanSaleReverseTransaction(SaleReverseRequest saleReverseRequest) {
        SaleResponse bnplSaleResponse = new SaleResponse();

        FcanIdfcSaleReverseRequest fcanIdfcSaleReverseRequest = new FcanIdfcSaleReverseRequest();
        fcanIdfcSaleReverseRequest.setEntityCode(IdfcUtil.getTransactionSourceSystem(saleReverseRequest.getAppId()));
        fcanIdfcSaleReverseRequest.setEntityReqId(saleReverseRequest.getMerchantOrderId());
        fcanIdfcSaleReverseRequest.setProductType(IdfcUtil.getIdfcProductType(saleReverseRequest.getLoanType()));
        fcanIdfcSaleReverseRequest.setLoanId((saleReverseRequest.getLoanType() != null && LenderConstants.EMI.equals(saleReverseRequest.getLoanType())) ? saleReverseRequest.getLenderOrderId() : saleReverseRequest.getLenderAccountId());
        fcanIdfcSaleReverseRequest.setCancelAmount(saleReverseRequest.getAmount().toString());
        fcanIdfcSaleReverseRequest.setCancelReason("Flipkart Product cancellations");
        fcanIdfcSaleReverseRequest.setTransactionDate(IdfcUtil.getFormattedDatetimeV2(saleReverseRequest.getTransactionDate()));
        if(LenderConstants.EMI.equals(saleReverseRequest.getLoanType())) {
            if(saleReverseRequest.getMerchantPrevTxnId() == null || saleReverseRequest.getMerchantPrevTxnId().equals(saleReverseRequest.getMerchantOrderId())) {
                HashMap<String, String> stuckPztMap = TijoriPandoraQueueUtil.getMapFromJsonFile("/usr/share/pandora/src/main/resources/pztMap.json");
                if(stuckPztMap.containsKey(saleReverseRequest.getMerchantOrderId())) {
                    fcanIdfcSaleReverseRequest.setDrawdownReqId(shortenReqId(stuckPztMap.get(saleReverseRequest.getMerchantOrderId())));
                } else {
                    fcanIdfcSaleReverseRequest.setDrawdownReqId(shortenReqId(saleReverseRequest.getMerchantForwardOrderId()));
                }
            } else {
                fcanIdfcSaleReverseRequest.setDrawdownReqId(shortenReqId(saleReverseRequest.getMerchantPrevTxnId()));
            }
        } else {
            fcanIdfcSaleReverseRequest.setDrawdownReqId(shortenReqId(saleReverseRequest.getMerchantForwardOrderId()));
        }
        fcanIdfcSaleReverseRequest.setReconId(fcanIdfcSaleReverseRequest.getDrawdownReqId());

        FcanIdfcSaleReverseResponse fcanIdfcSaleReverseResponse = null;
        try {
            if (RequestContextThreadLocal.get().isPerfRequest()) {
                fcanIdfcSaleReverseResponse = MockService.mockSaleReversalV2(fcanIdfcSaleReverseRequest, dynamicBucket.getInt("idfcSaleReverseDelay"));
            } else {
                fcanIdfcSaleReverseResponse = new IdfcSaleReverseCommandV3(PandoraHystrixNameConstants.IDFC, PandoraHystrixNameConstants.IDFC_SALE_REVERSE_KEY,
                        PandoraHystrixNameConstants.IDFC_SALE_REVERSE_KEY, pandoraHystrixProperties, idfcBnplServiceClientV3, fcanIdfcSaleReverseRequest,
                        accessTokenProvider.getAccessToken(Scope.LENDING_IDFC)).execute();
            }
        } catch (Exception e) {
            logger.error("exception in fcan sale reverse request : {}", saleReverseRequest.getUserIdentifier(), e);
            throw new PandoraServiceClientException(e.getMessage());
        }

        if (null == fcanIdfcSaleReverseResponse) {
            logger.error("exception in fcan sale reverse request : {}", saleReverseRequest.getUserIdentifier());
            try {
                throw new PandoraServiceClientException(LENDER_NULL_RESPONSE, LenderConstants.FULL_CANCELLATION_ENDPOINT, OBJECT_MAPPER.writeValueAsString(fcanIdfcSaleReverseRequest), null);
            } catch (JsonProcessingException ex) {
                throw new PandoraServiceClientException(ex);
            }
        }

        if (!fcanIdfcSaleReverseResponse.isSuccess()) {
            logger.error("exception in fcan sale reverse request : {} {}", saleReverseRequest.getUserIdentifier(), fcanIdfcSaleReverseResponse);
            String message = "";
            try {
                throw new PandoraServiceClientException(LENDER_FAILED,
                        LenderConstants.FULL_CANCELLATION_ENDPOINT,
                        OBJECT_MAPPER.writeValueAsString(fcanIdfcSaleReverseRequest),
                        OBJECT_MAPPER.writeValueAsString(fcanIdfcSaleReverseResponse)
                );
            } catch (JsonProcessingException ex) {
                throw new PandoraServiceClientException(ex);
            }
        }
        bnplSaleResponse.setLenderOrderId(fcanIdfcSaleReverseResponse.getResourceData().get(0).getEntityReqId());
        bnplSaleResponse.setSuccess(fcanIdfcSaleReverseResponse.isSuccess());

        return bnplSaleResponse;

    }

    @Override
    public PaybackForwardResponse createPaybackForwardTransaction(PaybackForwardRequest paybackForwardRequest, ProductCode product) {

        PaybackForwardResponse paybackForwardResponse = null;
        switch (product) {
            case BNPL:
                paybackForwardResponse = createBnplPaybackForwardTransaction((IdfcPaybackForwardRequest) paybackForwardRequest);
                break;
            case FLIPKART_ADVANZ:
                paybackForwardResponse = createAdvanzPaybackForwardTransaction((IdfcPaybackForwardRequest) paybackForwardRequest);
        }
        return paybackForwardResponse;
    }

    public PaybackForwardResponse createBnplPaybackForwardTransaction(IdfcPaybackForwardRequest bnplPaybackForwardRequest) {
        PaybackForwardResponse                bnplPaybackForwardResponse          = new PaybackForwardResponse();
        PaybackForwardResponse.PaymentDetails paymentDetails                      = new PaybackForwardResponse.PaymentDetails();
        BnplIdfcPaybackForwardRequestV2         bnplPaybackForwardDownStreamRequest = new BnplIdfcPaybackForwardRequestV2();

        bnplPaybackForwardDownStreamRequest.setLoanId(bnplPaybackForwardRequest.getLenderAccountId());
        bnplPaybackForwardDownStreamRequest.setReqId(bnplPaybackForwardRequest.getPgIdentifier());
        bnplPaybackForwardDownStreamRequest.setSourceName(IdfcUtil.getTransactionSourceSystem(bnplPaybackForwardRequest.getAppId()));
        bnplPaybackForwardDownStreamRequest.setTransactionType(getTransactionTypeFromPaymentMethod(bnplPaybackForwardRequest.getPaymentMethod()));
        bnplPaybackForwardDownStreamRequest.setTransactionDate(IdfcUtil.getFormattedDatetime(bnplPaybackForwardRequest.getTransactionDate()));
        bnplPaybackForwardDownStreamRequest.setChequeNo(bnplPaybackForwardRequest.getPgIdentifier());
        bnplPaybackForwardDownStreamRequest.setReceiptNo(bnplPaybackForwardRequest.getTxnId() != null ? shortenReqId(bnplPaybackForwardRequest.getTxnId()) : bnplPaybackForwardRequest.getPgIdentifier());


        List<IdfcPaybackForwardRequest.TransactionDetail> transactionDetails = bnplPaybackForwardRequest.getTransactionDetails();

        BigDecimal penaltyAmount = new BigDecimal(0);
        BigDecimal saleAmount    = new BigDecimal(0);

        List<IdfcPaybackForwardRequest.TransactionDetail> saleTransactionsList = transactionDetails.stream().filter(transactionDetail -> transactionDetail.getTransactionType().equals(SALE_TRANSACTION_TYPE)).collect(Collectors.toList());
        for (int i = 0; i < saleTransactionsList.size(); i++) {
            IdfcPaybackForwardRequest.TransactionDetail saleTransaction = saleTransactionsList.get(i);
            if (saleTransaction.getSaleTxnAmt().compareTo(BigDecimal.ZERO) != 0) {
                saleAmount = saleAmount.add(saleTransaction.getSaleTxnAmt());
            }
        }


        List<IdfcPaybackForwardRequest.TransactionDetail> penaltyTransactionsList = transactionDetails.stream().filter(transactionDetail -> transactionDetail.getTransactionType().equals(PENALTY_TRANSACTION_TYPE)).collect(Collectors.toList());
        for (int i = 0; i < penaltyTransactionsList.size(); i++) {
            IdfcPaybackForwardRequest.TransactionDetail penaltyTransaction = penaltyTransactionsList.get(i);
            if (penaltyTransaction.getSaleTxnAmt().compareTo(BigDecimal.ZERO) != 0) {
                penaltyAmount = penaltyAmount.add(penaltyTransaction.getSaleTxnAmt());
            }
        }

        bnplPaybackForwardDownStreamRequest.setPOSAmount(saleAmount.toString());
        bnplPaybackForwardDownStreamRequest.setLPPAmount(penaltyAmount.toString());
        bnplPaybackForwardDownStreamRequest.setTotalAmount(saleAmount.add(penaltyAmount).toString());


        BnplIdfcPaybackForwardResponse bnplPaybackForwardDownStreamResponse = null;
        try {
            if (RequestContextThreadLocal.get().isPerfRequest()) {
                bnplPaybackForwardDownStreamResponse = MockService.mockPaybackForward(bnplPaybackForwardRequest, dynamicBucket.getInt("mockPaybackForwardDelay"));
            } else {
                bnplPaybackForwardDownStreamResponse = new IdfcPaybackForwardCommandV2(idfcBnplServiceClientV2, bnplPaybackForwardDownStreamRequest,
                        accessTokenProvider.getAccessToken(Scope.LENDING_IDFC), PandoraHystrixNameConstants.IDFC, PandoraHystrixNameConstants.IDFC_PAYBACK_FORWARD_KEY,
                        PandoraHystrixNameConstants.IDFC_PAYBACK_FORWARD_KEY, pandoraHystrixProperties).execute();
            }
        } catch (Exception e) {
            logger.error("exception in  payback forward request : {}", bnplPaybackForwardRequest.getUserIdentifier(), e);
            throw new PandoraServiceClientException(e.getMessage());
        }

        if (null == bnplPaybackForwardDownStreamResponse) {
            logger.error("exception in  payback forward request : {}", bnplPaybackForwardRequest.getUserIdentifier());
            throw new PandoraServiceClientException(Response.Status.INTERNAL_SERVER_ERROR, STATUS.FAILURE.name());
        }
        if (!bnplPaybackForwardDownStreamResponse.isSuccess()) {
            logger.error("exception in  payback forward request : {} , {}", bnplPaybackForwardRequest.getUserIdentifier(), bnplPaybackForwardDownStreamResponse.getRespDesc());

            String message = "";
            try {
                message = OBJECT_MAPPER.writeValueAsString(bnplPaybackForwardDownStreamResponse);
            } catch (Exception e) {
                throw new PandoraServiceClientException("Error parsing JSON PaybackForwardDownStreamResponse");
            }
            throw new PandoraServiceClientException(message);


        }
        bnplPaybackForwardResponse.setSuccess(bnplPaybackForwardDownStreamResponse.isSuccess());
        paymentDetails.setInvoiceNo(bnplPaybackForwardDownStreamResponse.getChequeId());
//        paymentDetails.setPaymentIdentifiers(PaybackForwardDownStreamResponse.getPaymentDetails().getPaymentIdentifiers());
        bnplPaybackForwardResponse.setPaymentDetails(paymentDetails);
        return bnplPaybackForwardResponse;

    }

    public PaybackForwardResponse createAdvanzPaybackForwardTransaction(IdfcPaybackForwardRequest advanzPaybackForwardRequest) throws PandoraServiceClientException {

        PaybackForwardResponse                advanzPaybackForwardResponse = new PaybackForwardResponse();
        PaybackForwardResponse.PaymentDetails paymentDetails               = new PaybackForwardResponse.PaymentDetails();

        AdvanzIdfcPaybackForwardRequestV3 advanzPaybackForwardDownStreamRequest = new AdvanzIdfcPaybackForwardRequestV3();

        advanzPaybackForwardDownStreamRequest.setEntityCode(IdfcUtil.getTransactionSourceSystem(advanzPaybackForwardRequest.getAppId()));
        if (StringUtils.isEmpty(advanzPaybackForwardRequest.getPgIdentifier())) {
            HashMap<String, String> stuckPztMap = TijoriPandoraQueueUtil.getMapFromJsonFile("/usr/share/pandora/src/main/resources/nullPgIdentifierMap.json");
            advanzPaybackForwardRequest.setPgIdentifier(stuckPztMap.get(advanzPaybackForwardRequest.getTxnId()));
        }
        advanzPaybackForwardDownStreamRequest.setEntityReqId(advanzPaybackForwardRequest.getPgIdentifier());
        advanzPaybackForwardDownStreamRequest.setTransactionId(advanzPaybackForwardRequest.getPgIdentifier());
        advanzPaybackForwardDownStreamRequest.setTransactionDate(IdfcUtil.getFormattedDatetimeV2(advanzPaybackForwardRequest.getTransactionDate()));
        advanzPaybackForwardDownStreamRequest.setChequeNo(advanzPaybackForwardRequest.getPgIdentifier());
        advanzPaybackForwardDownStreamRequest.setReceiptNo(shortenReqId(advanzPaybackForwardRequest.getTxnId()));
        advanzPaybackForwardDownStreamRequest.setTransactionType(BULK_PAYMENT);


        List<AdvanzIdfcPaybackForwardRequestV3.PaymentTransaction> paymentTransactionList = new ArrayList<>();
        BigDecimal                   totalAmount        = new BigDecimal(0);
        BigDecimal                   payLaterAmt        = new BigDecimal(0);
        BigDecimal                   lppAmount          = new BigDecimal(0);
        BigDecimal                   conFeeAmount       = new BigDecimal(0);
        BigDecimal                   usageFeeAmount     = new BigDecimal(0);
        BigDecimal                   emiPfAmount        = new BigDecimal(0);
        for (int i = 0; i < advanzPaybackForwardRequest.getTransactionDetails().size(); i++) {
            IdfcPaybackForwardRequest.TransactionDetail transactionDetail = advanzPaybackForwardRequest.getTransactionDetails().get(i);
            if (transactionDetail.getSaleTxnAmt().compareTo(BigDecimal.ZERO) != 0) {
                AdvanzIdfcPaybackForwardRequestV3.PaymentTransaction paymentTransaction = new AdvanzIdfcPaybackForwardRequestV3.PaymentTransaction();
                paymentTransaction.setLoanId((transactionDetail.getLenderTxnId() != null && !transactionDetail.getLenderTxnId().equals("")) ? transactionDetail.getLenderTxnId() : transactionDetail.getTxnId());
                totalAmount = totalAmount.add(transactionDetail.getSaleTxnAmt());
                if (transactionDetail.getTransactionType().equals(SALE_TRANSACTION_TYPE)) {
                    if (transactionDetail.getSublimitType().equals("PAY_LATER") || transactionDetail.getSublimitType().equals("")) {
                        payLaterAmt = payLaterAmt.add(transactionDetail.getSaleTxnAmt());
                        continue;
                    } else {
                        paymentTransaction.setChargeAmount("0");
                        paymentTransaction.setEmiAmount(transactionDetail.getSaleTxnAmt().toString());
                    }
                } else if(transactionDetail.getTransactionType().equals(PENALTY_TRANSACTION_TYPE)) {
                    lppAmount = lppAmount.add(transactionDetail.getSaleTxnAmt());
                    continue;
                }
                else if(transactionDetail.getTransactionType().equals(CONV_FEE_TRANSACTION_TYPE)) {
                    conFeeAmount = conFeeAmount.add(transactionDetail.getSaleTxnAmt());
                    continue;
                }
                else if(transactionDetail.getTransactionType().equals(USAGE_FEE_TRANSACTION_TYPE)) {
                    usageFeeAmount = usageFeeAmount.add(transactionDetail.getSaleTxnAmt());
                    continue;
                }
                else if(transactionDetail.getTransactionType().equals(EMI_PROCESSING_FEE_TRANSACTION_TYPE)){
                    emiPfAmount = emiPfAmount.add(transactionDetail.getSaleTxnAmt());
                    continue;
                }
                paymentTransaction.setTotalAmount(transactionDetail.getSaleTxnAmt().toString());
                paymentTransaction.setChargeId("0");
                paymentTransactionList.add(paymentTransaction);
            }
        }
        if(BigDecimal.ZERO.compareTo(payLaterAmt) != 0) {
            AdvanzIdfcPaybackForwardRequestV3.PaymentTransaction paylaterPaymentTransaction = new AdvanzIdfcPaybackForwardRequestV3.PaymentTransaction();
            paylaterPaymentTransaction.setTotalAmount(payLaterAmt.toString());
            paylaterPaymentTransaction.setEmiAmount(payLaterAmt.toString());
            paylaterPaymentTransaction.setLoanId(advanzPaybackForwardRequest.getLenderAccountId());
            paylaterPaymentTransaction.setChargeId("0");
            paylaterPaymentTransaction.setChargeAmount("0");
            paymentTransactionList.add(paylaterPaymentTransaction);
        }
        if(BigDecimal.ZERO.compareTo(lppAmount) != 0) {
            AdvanzIdfcPaybackForwardRequestV3.PaymentTransaction penaltyPaymentTransaction = new AdvanzIdfcPaybackForwardRequestV3.PaymentTransaction();
            penaltyPaymentTransaction.setLoanId(advanzPaybackForwardRequest.getLenderAccountId());
            penaltyPaymentTransaction.setChargeAmount(lppAmount.toString());
            penaltyPaymentTransaction.setChargeId(idfcConfiguration.getLateFeeChargeId());
            //Charge Id for LPP needs to get configured
            penaltyPaymentTransaction.setEmiAmount("0");
            penaltyPaymentTransaction.setTotalAmount(lppAmount.toString());
            paymentTransactionList.add(penaltyPaymentTransaction);
        }
        if(BigDecimal.ZERO.compareTo(conFeeAmount) != 0) {
            AdvanzIdfcPaybackForwardRequestV3.PaymentTransaction convFeePaymentTransaction = new AdvanzIdfcPaybackForwardRequestV3.PaymentTransaction();
            convFeePaymentTransaction.setLoanId(advanzPaybackForwardRequest.getLenderAccountId());
            convFeePaymentTransaction.setChargeAmount(conFeeAmount.toString());
            convFeePaymentTransaction.setChargeId(idfcConfiguration.getConvFeeChargeId());
            convFeePaymentTransaction.setEmiAmount("0");
            convFeePaymentTransaction.setTotalAmount(conFeeAmount.toString());
            paymentTransactionList.add(convFeePaymentTransaction);
        }

        if(BigDecimal.ZERO.compareTo(usageFeeAmount) != 0) {
            AdvanzIdfcPaybackForwardRequestV3.PaymentTransaction usageFeePaymentTransaction = new AdvanzIdfcPaybackForwardRequestV3.PaymentTransaction();
            usageFeePaymentTransaction.setLoanId(advanzPaybackForwardRequest.getLenderAccountId());
            usageFeePaymentTransaction.setChargeAmount(usageFeeAmount.toString());
            usageFeePaymentTransaction.setChargeId(idfcConfiguration.getUsageFeeChargeId());
            usageFeePaymentTransaction.setEmiAmount("0");
            usageFeePaymentTransaction.setTotalAmount(usageFeeAmount.toString());
            paymentTransactionList.add(usageFeePaymentTransaction);
        }
        if(BigDecimal.ZERO.compareTo(emiPfAmount) != 0) {
            AdvanzIdfcPaybackForwardRequestV3.PaymentTransaction emiPfPaymentTransaction = new AdvanzIdfcPaybackForwardRequestV3.PaymentTransaction();
            emiPfPaymentTransaction.setLoanId(advanzPaybackForwardRequest.getLenderAccountId());
            emiPfPaymentTransaction.setChargeAmount(emiPfAmount.toString());
            emiPfPaymentTransaction.setChargeId(idfcConfiguration.getEmiPfChargeId());
            emiPfPaymentTransaction.setEmiAmount("0");
            emiPfPaymentTransaction.setTotalAmount(emiPfAmount.toString());
            paymentTransactionList.add(emiPfPaymentTransaction);
        }

        advanzPaybackForwardDownStreamRequest.setTotalTxnAmount(totalAmount.toString());
        advanzPaybackForwardDownStreamRequest.setPaymentTransactionList(paymentTransactionList);


        AdvanzIdfcPaybackForwardResponseV3 advanzPaybackForwardDownStreamResponse = null;

        try {
            if (RequestContextThreadLocal.get().isPerfRequest()) {
                advanzPaybackForwardDownStreamResponse = MockService.mockAdvanzPaybackForwardV2(advanzPaybackForwardRequest, dynamicBucket.getInt("mockPaybackForwardDelay"));
            } else {
                advanzPaybackForwardDownStreamResponse = new IdfcAdvanzPaybackForwardCommandV3(idfcBnplServiceClientV3, advanzPaybackForwardDownStreamRequest,
                        accessTokenProvider.getAccessToken(Scope.LENDING_IDFC), PandoraHystrixNameConstants.IDFC, PandoraHystrixNameConstants.IDFC_ADVANZ_PAYBACK_FORWARD_KEY,
                        PandoraHystrixNameConstants.IDFC_ADVANZ_PAYBACK_FORWARD_KEY, pandoraHystrixProperties).execute();
            }
        } catch (Exception e) {
            try {
                throw new PandoraServiceClientException(e.getMessage(), LenderConstants.PAYMENT_FORWARD_ADVANZ_ENDPOINT_V3, OBJECT_MAPPER.writeValueAsString(advanzPaybackForwardDownStreamRequest), null);
            } catch (JsonProcessingException ex) {
                throw new PandoraServiceClientException(ex);
            }
        }

        if (null == advanzPaybackForwardDownStreamResponse) {
            try {
                throw new PandoraServiceClientException(LENDER_NULL_RESPONSE, LenderConstants.PAYMENT_FORWARD_ADVANZ_ENDPOINT_V3, OBJECT_MAPPER.writeValueAsString(advanzPaybackForwardDownStreamRequest), null);
            } catch (JsonProcessingException ex) {
                throw new PandoraServiceClientException(ex);
            }
        }
        if (!advanzPaybackForwardDownStreamResponse.isSuccess()) {
            try {
                throw new PandoraServiceClientException(LENDER_FAILED,
                        LenderConstants.PAYMENT_FORWARD_ADVANZ_ENDPOINT_V3,
                        OBJECT_MAPPER.writeValueAsString(advanzPaybackForwardDownStreamRequest),
                        OBJECT_MAPPER.writeValueAsString(advanzPaybackForwardDownStreamResponse)
                );
            } catch (JsonProcessingException ex) {
                throw new PandoraServiceClientException(ex);
            }
        }
        advanzPaybackForwardResponse.setSuccess(advanzPaybackForwardDownStreamResponse.isSuccess());
        paymentDetails.setInvoiceNo(advanzPaybackForwardDownStreamResponse.getResourceData().get(0).getEntityReqId());
        advanzPaybackForwardResponse.setPaymentDetails(paymentDetails);
        return advanzPaybackForwardResponse;

    }

    private String getTransactionTypeFromPaymentMethod(PaymentMethod paymentMethod) {
        if (paymentMethod == null) {
            return LenderConstants.PAYBACK_DIGITAL_FORWARD_REQUEST_TYPE;
        }
        switch (paymentMethod) {
            case CASH:
                return LenderConstants.PAYBACK_CASH_FORWARD_REQUEST_TYPE;

            case DIGITAL:
                return LenderConstants.PAYBACK_DIGITAL_FORWARD_REQUEST_TYPE;
        }
        return null;
    }

    protected String shortenReqId(String reqId) {
        if (reqId.length() > 60) {
            return reqId.substring(reqId.length() - 60, reqId.length());
        }
        return reqId;
    }

    @Override
    public PaybackReverseResponse createPaybackReverseTransaction(PaybackReverseRequest advanzPaybackReverseRequest, ProductCode product) {
        if (!idfcConfiguration.getIdfcPaybackReversalEnabledTenants().contains(RequestContextThreadLocal.get().getTenantId())) {
            logger.info("Payback Reversal is not enabled for tenant, {}", RequestContextThreadLocal.get().getTenantId().name());
            throw new UnsupportedOperationException();
        }

        PaybackReverseResponse paybackReverseResponse = new PaybackReverseResponse();
        IdfcPaybackReverseRequest advanzIdfcPaybackReverseDownstreamRequest = new IdfcPaybackReverseRequest();

        advanzIdfcPaybackReverseDownstreamRequest.setEntityCode(IdfcUtil.getTransactionSourceSystem(advanzPaybackReverseRequest.getAppId()));
        advanzIdfcPaybackReverseDownstreamRequest.setEntityReqId(advanzPaybackReverseRequest.getPgIdentifier());
        advanzIdfcPaybackReverseDownstreamRequest.setDrReqId(advanzPaybackReverseRequest.getMerchantOrderId());
        advanzIdfcPaybackReverseDownstreamRequest.setReconId(advanzPaybackReverseRequest.getMerchantOrderId());
        setRequestTypeInSaleReverseRequest(advanzIdfcPaybackReverseDownstreamRequest, advanzPaybackReverseRequest.getPaymentMethod());
        advanzIdfcPaybackReverseDownstreamRequest.setCrn(advanzPaybackReverseRequest.getLenderCardNumber());
        if(product == ProductCode.BNPL || Tenant.FFB.equals(RequestContextThreadLocal.get().getTenantId())) {
            advanzIdfcPaybackReverseDownstreamRequest.setLoanId(advanzPaybackReverseRequest.getLenderAccountId());
        }
        else{
            advanzIdfcPaybackReverseDownstreamRequest.setLoanId(advanzPaybackReverseRequest.getLenderOrderId()!=null?advanzPaybackReverseRequest.getLenderOrderId():"");
        }
        advanzIdfcPaybackReverseDownstreamRequest.setRefundAmt(advanzPaybackReverseRequest.getAmount().toString());
        advanzIdfcPaybackReverseDownstreamRequest.setTxnDate(IdfcUtil.getFormattedDatetimeV2(advanzPaybackReverseRequest.getTransactionDate()));
        advanzIdfcPaybackReverseDownstreamRequest.setRefundReason("FK Payback Reversal");

        IdfcPaybackReverseResponse advanzIdfcPaybackReverseDownstreamResponse = null;
        try {
            if (RequestContextThreadLocal.get().isPerfRequest()) {
                advanzIdfcPaybackReverseDownstreamResponse = MockService.mockPaybackReversalV2(advanzPaybackReverseRequest, dynamicBucket.getInt("idfcPaybackReverseDelay"));
            } else {
                advanzIdfcPaybackReverseDownstreamResponse = new IdfcPaybackReversalCommand(PandoraHystrixNameConstants.IDFC, PandoraHystrixNameConstants.IDFC_SALE_REVERSE_KEY,
                        PandoraHystrixNameConstants.IDFC_TERMLOAN_CANCELLATION_KEY, pandoraHystrixProperties, idfcBnplServiceClientV3, accessTokenProvider.getAccessToken(Scope.LENDING_IDFC), advanzIdfcPaybackReverseDownstreamRequest).execute();
            }
        } catch(Exception e){
            try {
                throw new PandoraServiceClientException(e.getMessage(), LenderConstants.PAYBACK_REVERSE_ENDPOINT, OBJECT_MAPPER.writeValueAsString(advanzIdfcPaybackReverseDownstreamRequest), null);
            } catch (JsonProcessingException ex) {
                throw new PandoraServiceClientException(ex);
            }
        }

        if(null == advanzIdfcPaybackReverseDownstreamResponse){
            try {
                throw new PandoraServiceClientException(LENDER_NULL_RESPONSE, LenderConstants.PAYBACK_REVERSE_ENDPOINT, OBJECT_MAPPER.writeValueAsString(advanzIdfcPaybackReverseDownstreamRequest), null);
            } catch (JsonProcessingException ex) {
                throw new PandoraServiceClientException(ex);
            }
        }
        if(!advanzIdfcPaybackReverseDownstreamResponse.isSuccess()){
            logger.error("exception in advanz payback reverse request : {} , {}", advanzPaybackReverseRequest.getUserIdentifier(), advanzIdfcPaybackReverseDownstreamResponse.getResourceData().get(0).getEntityReqId());
            String message = "";
            try {
                throw new PandoraServiceClientException(LENDER_FAILED,
                        LenderConstants.PAYBACK_REVERSE_ENDPOINT,
                        OBJECT_MAPPER.writeValueAsString(advanzIdfcPaybackReverseDownstreamRequest),
                        OBJECT_MAPPER.writeValueAsString(advanzIdfcPaybackReverseDownstreamResponse)
                );
            } catch (JsonProcessingException ex) {
                throw new PandoraServiceClientException(ex);
            }
        }
        logger.info("IDFC payback reversal response : {}", advanzIdfcPaybackReverseDownstreamResponse);
        paybackReverseResponse.setPaymentIdentifier(advanzIdfcPaybackReverseDownstreamResponse.getResourceData().get(0).getEntityReqId());
        paybackReverseResponse.setSuccess(advanzIdfcPaybackReverseDownstreamResponse.isSuccess());
        paybackReverseResponse.setReversedAmount(advanzPaybackReverseRequest.getAmount());
        return paybackReverseResponse;
    }

    private void setRequestTypeInSaleReverseRequest(IdfcPaybackReverseRequest request, PaymentMethod paymentMethod) {
        if (paymentMethod == null) {
            request.setReqType(LenderConstants.PAYBACK_REVERSAL_DIGITAL_REQUEST_TYPE);
            return;
        }

        switch (paymentMethod) {
            case DIGITAL:
                request.setReqType(LenderConstants.PAYBACK_REVERSAL_DIGITAL_REQUEST_TYPE);
                return;
            case CASH:
                request.setReqType(LenderConstants.PAYBACK_REVERSAL_CASH_REQUEST_TYPE);
                return;
        }
    }

    @Override
    public FeeChargeResponse createFeeChargeTransaction(FeeChargeRequest feeChargeRequest, ProductCode product, String txnType) {
        if (product != ProductCode.FLIPKART_ADVANZ) {
            throw new PandoraServiceClientException(String.format("Product Code is not correct - %s",product));
        }
        FeeChargeResponse feeChargeResponse = new FeeChargeResponse();

        IdfcFeeChargeRequestV3 idfcFeeChargeRequest = new IdfcFeeChargeRequestV3();
        idfcFeeChargeRequest.setEntityCode(IdfcUtil.getTransactionSourceSystem(feeChargeRequest.getAppId()));
        idfcFeeChargeRequest.setEntityReqId(feeChargeRequest.getMerchantOrderId());
        idfcFeeChargeRequest.setLoanId(feeChargeRequest.getLenderAccountId());
        idfcFeeChargeRequest.setRequestType(IdfcUtil.getChargeRequestTypeV2(txnType));
        idfcFeeChargeRequest.setChargeNarration(IdfcUtil.getChargeNarration(txnType));
        idfcFeeChargeRequest.setChargeAmount(feeChargeRequest.getAmount().toString());
        idfcFeeChargeRequest.setTxnDate(IdfcUtil.getFormattedDatetimeV2(feeChargeRequest.getTransactionDate()));


        if(feeChargeRequest.getTransactionType()!= null && feeChargeRequest.getTransactionType().equals(USAGE_FEE_TRANSACTION_TYPE)){
            idfcFeeChargeRequest.setChargeId(idfcConfiguration.getUsageFeeChargeId());
        }
        else if(feeChargeRequest.getTransactionType() != null && feeChargeRequest.getTransactionType().equals(EMI_PROCESSING_FEE_TRANSACTION_TYPE)){
            idfcFeeChargeRequest.setChargeId(idfcConfiguration.getEmiPfChargeId());
        }
        else if(feeChargeRequest.getTransactionType() != null && feeChargeRequest.getTransactionType().equals(CONV_FEE_TRANSACTION_TYPE) ){
            idfcFeeChargeRequest.setChargeId(idfcConfiguration.getConvFeeChargeId());
        } else if(feeChargeRequest.getTransactionType() != null && feeChargeRequest.getTransactionType().equals(PENALTY_TRANSACTION_TYPE)) {
            idfcFeeChargeRequest.setChargeId(idfcConfiguration.getLateFeeChargeId());
        }

        IdfcFeeChargeResponseV3 idfcFeeChargeResponse = null;
        try {
            idfcFeeChargeResponse = new IdfcFeeChargeCommandV3(PandoraHystrixNameConstants.IDFC, PandoraHystrixNameConstants.IDFC_FEE_CHARGE_KEY, PandoraHystrixNameConstants.IDFC_FEE_CHARGE_KEY, pandoraHystrixProperties, idfcBnplServiceClientV3, idfcFeeChargeRequest, accessTokenProvider.getAccessToken(Scope.LENDING_IDFC)).execute();
        } catch (Exception e) {
            try {
                throw new PandoraServiceClientException(e.getMessage(), LenderConstants.FEE_CHARGE_ENDPOINT_V3, OBJECT_MAPPER.writeValueAsString(idfcFeeChargeRequest), null);
            } catch (JsonProcessingException ex) {
                throw new PandoraServiceClientException(ex);
            }
        }

        if (null == idfcFeeChargeResponse) {
            try {
                throw new PandoraServiceClientException(LENDER_NULL_RESPONSE, LenderConstants.FEE_CHARGE_ENDPOINT_V3, OBJECT_MAPPER.writeValueAsString(idfcFeeChargeRequest), null);
            } catch (JsonProcessingException ex) {
                throw new PandoraServiceClientException(ex);
            }
        }
        if (!idfcFeeChargeResponse.isSuccess()) {
            try {
                throw new PandoraServiceClientException(LENDER_FAILED,
                        LenderConstants.FEE_CHARGE_ENDPOINT_V3,
                        OBJECT_MAPPER.writeValueAsString(idfcFeeChargeRequest),
                        OBJECT_MAPPER.writeValueAsString(idfcFeeChargeResponse)
                );
            } catch (JsonProcessingException ex) {
                throw new PandoraServiceClientException(ex);
            }
        }
        feeChargeResponse.setLenderOrderId(idfcFeeChargeResponse.getResourceData().get(0).getEntityReqId());
        feeChargeResponse.setSuccess(idfcFeeChargeResponse.isSuccess());
        return feeChargeResponse;
    }

    @Override
    public BillGenerationResponse billGeneration(BillGenerationRequest billGenerationRequest) {
        return null;
    }
}
