package com.flipkart.fintech.pandora.service.entity;

import com.flipkart.fintech.pandora.service.client.Ivr.request.enums.Status;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 26/09/18.
 */
public class UbonaMapper {

  private static final Map<Status, com.flipkart.fintech.pandora.api.model.request.ivr.enums.Status> responseStatusHashMap = new HashMap<>();
  static {
    responseStatusHashMap.put(Status.SUCCESS, com.flipkart.fintech.pandora.api.model.request.ivr.enums.Status.SUCCESS);
    responseStatusHashMap.put(Status.INVALID_EXPIARY, com.flipkart.fintech.pandora.api.model.request.ivr.enums.Status.INVALID_EXPIARY);
    responseStatusHashMap.put(Status.INVALID_NAME, com.flipkart.fintech.pandora.api.model.request.ivr.enums.Status.INVALID_NAME);
    responseStatusHashMap.put(Status.INVALID_PARAMETER, com.flipkart.fintech.pandora.api.model.request.ivr.enums.Status.INVALID_PARAMETER);
    responseStatusHashMap.put(Status.INVALID_UID, com.flipkart.fintech.pandora.api.model.request.ivr.enums.Status.INVALID_UID);
    responseStatusHashMap.put(Status.MANDATORY_PARAMETER_MISSING, com.flipkart.fintech.pandora.api.model.request.ivr.enums.Status.MANDATORY_PARAMETER_MISSING);
    responseStatusHashMap.put(Status.DUPLICATE_REQUEST, com.flipkart.fintech.pandora.api.model.request.ivr.enums.Status.DUPLICATE_REQUEST);
    responseStatusHashMap.put(Status.TOO_MANY_ORDERS, com.flipkart.fintech.pandora.api.model.request.ivr.enums.Status.TOO_MANY_ORDERS);
    responseStatusHashMap.put(Status.SYSTEM_ERROR, com.flipkart.fintech.pandora.api.model.request.ivr.enums.Status.SYSTEM_ERROR);
    responseStatusHashMap.put(Status.ALREADY_CALLED, com.flipkart.fintech.pandora.api.model.request.ivr.enums.Status.ALREADY_CALLED);
    responseStatusHashMap.put(Status.ALREADY_CANCELLED, com.flipkart.fintech.pandora.api.model.request.ivr.enums.Status.ALREADY_CANCELLED);
    responseStatusHashMap.put(Status.USER_ALREADY_PROMISED, com.flipkart.fintech.pandora.api.model.request.ivr.enums.Status.USER_ALREADY_PROMISED);
  }

  public static com.flipkart.fintech.pandora.api.model.request.ivr.enums.Status getUbonaStatus(Status status) {
    com.flipkart.fintech.pandora.api.model.request.ivr.enums.Status status1 = responseStatusHashMap.get(status);
    if (null != status1) {
      return status1;
    } else {
      return null;
    }

  }


}


