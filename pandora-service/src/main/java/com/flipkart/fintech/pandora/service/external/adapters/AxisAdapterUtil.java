package com.flipkart.fintech.pandora.service.external.adapters;

import com.flipkart.fintech.filter.RequestContextThreadLocal;
import com.flipkart.fintech.pandora.api.model.AddressCategory;
import com.flipkart.fintech.pandora.api.model.request.onboarding.Address;
import com.flipkart.fintech.pandora.service.application.configuration.AxisCbcConfiguration;
import com.flipkart.fintech.pandora.service.external.dataModels.axisUserOnboarding.Address2;
import com.flipkart.fintech.pandora.service.external.dataModels.axisUserOnboarding.SubHeader;
import com.google.inject.Inject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.text.WordUtils;
import org.apache.commons.text.RandomStringGenerator;

import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.Objects;

@Slf4j
public class AxisAdapterUtil {

    public static final int MAX_LENGTH_ADDRESS_LINE_TOTAL = 150;
    public static final int MAX_LENGTH_ADDRESS_LINE = 50;
    private final AxisCbcConfiguration axisCbcConfiguration;

    @Inject
    public AxisAdapterUtil(AxisCbcConfiguration axisCbcConfiguration) {
        this.axisCbcConfiguration = axisCbcConfiguration;
    }

    public Address requestAddressAdapter(
            com.flipkart.fintech.pandora.service.external.dataModels.axisUserOnboarding.Address address) {
        Address requestAddress = new Address();
        requestAddress.setAddressLine1("");
        requestAddress.setAddressLine2("");

        if(StringUtils.isNotBlank(address.getAddressLine1())){
            requestAddress.setAddressLine1(address.getAddressLine1());
        }

        if(StringUtils.isNotBlank(address.getAddressLine2())){
            requestAddress.setAddressLine2(address.getAddressLine2());
        }

        if(StringUtils.isNotBlank(address.getAddressLine3())){
            requestAddress.setAddressLine2(requestAddress.getAddressLine2() + "," + address.getAddressLine3());
        }

        if(StringUtils.isNotBlank(address.getAddressLine4())){
            requestAddress.setAddressLine2(requestAddress.getAddressLine2() + "," + address.getAddressLine4());
        }

        if(StringUtils.isNotEmpty(address.getAddressLine5())) {
            requestAddress.setLandMark(address.getAddressLine5());
        }
        requestAddress.setCity(address.getCity());
        requestAddress.setCountryCode(address.getCountry());
        requestAddress.setPinCode(address.getPinCode());
        if(StringUtils.isNotEmpty(address.getLandmark())) {
            requestAddress.setLandMark(address.getLandmark());
        }
        requestAddress.setState(address.getState());
        if (address.getAddresstype().equals("PERMANENT")) {
            requestAddress.setAddressCategory(AddressCategory.PA);
        }
        else if (address.getAddresstype().equals("CURRENT")) {
            requestAddress.setAddressCategory(AddressCategory.CA);
        }
        return requestAddress;
    }

    public Address2 axisRequestAddressAdapter(Address address, String mobilenumber) {
        Address2 axisRequestAddress = new Address2();

        // Max Address Line limit is 50chars at Axis end
        String addressLine = address.getAddressLine1();
        if (addressLine.length() > MAX_LENGTH_ADDRESS_LINE_TOTAL) {
            log.error("Note: Addressline1 is greater than 150 chars and address will get purged for {}.", mobilenumber);
        }
        if (Objects.nonNull(address.getAddressLine2())) {
            addressLine = addressLine + " " + address.getAddressLine2();
        }
        addressLine = addressLine.replaceAll("[^a-zA-Z0-9\\.\\/\\:\\&\\@\\-\\,\\s]", "");
        addressLine = addressLine.replaceAll("[[\n]|[\\n]]", " ");
        String[] addressLineArray = WordUtils.wrap(addressLine, MAX_LENGTH_ADDRESS_LINE).split(System.lineSeparator());
        axisRequestAddress.setAddressLine1(addressLineArray[0]);
        if (addressLineArray.length > 1) {
            axisRequestAddress.setAddressLine2(addressLineArray[1]);
        }
        if (addressLineArray.length > 2) {
            axisRequestAddress.setAddressLine3(addressLineArray[2]);
        }

        axisRequestAddress.setCity(address.getCity());
        axisRequestAddress.setPincode(address.getPinCode());
        axisRequestAddress.setLandmark(address.getLandMark());
        axisRequestAddress.setState(address.getState());
        axisRequestAddress.setMobileNumber(getMobileNumber(mobilenumber));
        axisRequestAddress.setAddresstype(address.getAddressType().name());
        if (address.getAddressCategory() == AddressCategory.PA
                || address.getAddressCategory() == AddressCategory.BOTH) {
            axisRequestAddress.setIsCommunicationAddress(true);
        }
        else {
            axisRequestAddress.setIsCommunicationAddress(false);
        }
        return axisRequestAddress;
    }

    public SubHeader getSubHeader(String serviceRequestId, String serviceRequestversion) {
        SubHeader subHeader = new SubHeader();
        subHeader.setChannelId(axisCbcConfiguration.getChannelId());
        subHeader.setServiceRequestId(serviceRequestId);
        subHeader.setServiceRequestVersion(serviceRequestversion);
        subHeader.setRequestUUID(getRequestUUID());
        return subHeader;
    }

    public static String getRequestUUID() {
        char[][] range = {{'a', 'z'}, {'A', 'Z'}, {'0', '9'}};
        RandomStringGenerator generator = new RandomStringGenerator.Builder().withinRange(range).build();
        String requestUUID = generator.generate(9);
        return requestUUID;
    }

    @NotNull
    public String getMobileNumber(String inputMobileNumber) {
        String mobileNumber = inputMobileNumber;
        // Note: Checking if phone number needs to be mocked
        if (RequestContextThreadLocal.get() != null && RequestContextThreadLocal.get().isPerfRequest()) {
    		return "";
    	}
        if (StringUtils.isNotEmpty(axisCbcConfiguration.getTestConfigsMap().getReplacePhoneNumber())
                && StringUtils.isNotEmpty(axisCbcConfiguration.getTestConfigsMap().getMockPhoneNumber())
                && axisCbcConfiguration.getTestConfigsMap().getReplacePhoneNumber().equals("Y")) {
            String[] mockMobileArray = axisCbcConfiguration.getTestConfigsMap().getMockPhoneNumber().split(",");
            mobileNumber = Arrays.stream(mockMobileArray)
                    .filter(s -> inputMobileNumber.contains(s.split(":")[0])).findFirst()
                    .orElse(mockMobileArray[0])
                    .split(":")[1];
        }
        return  "91"+ mobileNumber.substring(mobileNumber.length()-10);
    }


}
