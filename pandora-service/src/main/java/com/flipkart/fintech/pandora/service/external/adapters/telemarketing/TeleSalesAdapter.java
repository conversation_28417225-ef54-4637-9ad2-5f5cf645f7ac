package com.flipkart.fintech.pandora.service.external.adapters.telemarketing;

import com.flipkart.fintech.filter.RequestContextThreadLocal;
import com.flipkart.fintech.pandora.api.model.nudge.NudgingRequest;
import com.flipkart.fintech.pandora.service.application.configuration.TeleSalesConfiguration;
import com.flipkart.fintech.pandora.service.external.dataModels.teleMarketing.TeleSalesRequest;
import com.flipkart.fintech.pandora.service.utils.EncryptionUtilTeleSales;
import com.flipkart.fintech.user.data.client.UserDataClient;
import com.flipkart.fintech.user.data.models.UserData;
import com.flipkart.fintech.user.data.models.enums.Merchant;
import com.flipkart.fintech.user.data.models.enums.PIIDataType;
import com.google.inject.Inject;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.ws.rs.BadRequestException;
import java.lang.reflect.InvocationTargetException;
import java.util.Objects;


public class TeleSalesAdapter {

	private final TeleSalesConfiguration clientConfig;
    private static final String NO_NAME = "NO_NAME";
    private final UserDataClient userDataClient;
	private static final Logger log = LoggerFactory.getLogger(TeleSalesAdapter.class);
	@Inject
	public TeleSalesAdapter(TeleSalesConfiguration clientConfig, UserDataClient userDataClient) {
		this.clientConfig = clientConfig;
        this.userDataClient = userDataClient;
    }

	public TeleSalesRequest populateTeleSalesRequest(NudgingRequest nudgingRequest)
			throws IllegalAccessException, InvocationTargetException {
		TeleSalesRequest teleSalesRequest = new TeleSalesRequest();
		if (StringUtils.isNotEmpty(nudgingRequest.getAccountId())) {
			teleSalesRequest.setAccountId(nudgingRequest.getAccountId());
		}
		if (StringUtils.isEmpty(nudgingRequest.getCustomerName())) {
			teleSalesRequest.setCustomerName(NO_NAME);
		} else {
			teleSalesRequest.setCustomerName(nudgingRequest.getCustomerName());
		}
		if (StringUtils.isNotEmpty(nudgingRequest.getAppCreatedTime())) {
			teleSalesRequest.setAppCreatedTime(nudgingRequest.getAppCreatedTime());
		}
		if (StringUtils.isNotEmpty(nudgingRequest.getAppUpdatedTime())) {
			teleSalesRequest.setAppUpdatedTime(nudgingRequest.getAppUpdatedTime());
		}
		if (StringUtils.isNotEmpty(nudgingRequest.getAppStatus())) {
			teleSalesRequest.setAppStatus(nudgingRequest.getAppStatus());
		}
		if (StringUtils.isNotEmpty(nudgingRequest.getCity())) {
			teleSalesRequest.setCity(nudgingRequest.getCity());
		}
		if (StringUtils.isNotEmpty(nudgingRequest.getCohort())) {
			teleSalesRequest.setCohort(nudgingRequest.getCohort());
		}
		if (StringUtils.isNotEmpty(nudgingRequest.getDropOffStage())) {
			teleSalesRequest.setDropOfStage(nudgingRequest.getDropOffStage());
		}
		if (StringUtils.isNotEmpty(nudgingRequest.getLanguage())) {
			teleSalesRequest.setLanguage(nudgingRequest.getLanguage());
		}
		if (StringUtils.isNotEmpty(nudgingRequest.getBin())) {
			teleSalesRequest.setBin(nudgingRequest.getBin());
		}
		if (StringUtils.isNotEmpty(nudgingRequest.getPropensity())) {
			teleSalesRequest.setPropensity(nudgingRequest.getPropensity());
		}
		if (StringUtils.isNotEmpty(nudgingRequest.getProductType())) {
			teleSalesRequest.setProductType(nudgingRequest.getProductType());
		}
		if (StringUtils.isNotEmpty(nudgingRequest.getAllocationPushedDate())) {
			teleSalesRequest.setAllocationPushedDate(nudgingRequest.getAllocationPushedDate());
		}
		if (StringUtils.isNotEmpty(nudgingRequest.getAllocationType())) {
			teleSalesRequest.setAllocationType(nudgingRequest.getAllocationType());
		}
		if (StringUtils.isNotEmpty(nudgingRequest.getIvrDataType())) {
			teleSalesRequest.setIvrDataType(nudgingRequest.getIvrDataType());
		}
		if (StringUtils.isNotEmpty(nudgingRequest.getNewCohort())) {
			teleSalesRequest.setNewCohort(nudgingRequest.getNewCohort());
		}

		String mobileNumber;
		if (Objects.nonNull(nudgingRequest.getUnEncryptedAccountId())) {
			// Need to add internal AcocuntId
			mobileNumber = getPrimaryMobileNo(nudgingRequest.getUnEncryptedAccountId(), null, null);
		} else {
			mobileNumber = nudgingRequest.getMobileNumber();
		}

		if(Objects.isNull(mobileNumber)) {
			throw new BadRequestException("empty mobile number for telesales request "+ nudgingRequest.getAccountId());
		}
		int length = mobileNumber.length();
		StringBuilder mobileNumberToSend = new StringBuilder();
		//Mobile Number is encrypted as 16 bit which is decrypted by telesales.
		//Adding 0 at the start which will be ignore after decryption at telesales.
		for (int i = 0; i < 16 - length; i++) {
			mobileNumberToSend.append("0");
		}
		mobileNumberToSend.append(mobileNumber);
		EncryptionUtilTeleSales encryptionUtilTeleSales = new EncryptionUtilTeleSales();
		teleSalesRequest.setMobileNumber(encryptionUtilTeleSales.encryptAndEncode(mobileNumberToSend.toString(),
				clientConfig.getAesKey(), clientConfig.getAesSalt(), clientConfig.getAesIv()));

		log.error("TeleSalesRequest of {} is {}", nudgingRequest.getAccountId(), teleSalesRequest);
		return teleSalesRequest;
	}

	private String getPrimaryMobileNo(String externalId, String smUserId, String merchantId) {
		UserData userData = userDataClient.getUserData(Merchant.getOrDefaultByValue(merchantId), externalId, smUserId, PIIDataType.PLAINTEXT);
		if (RequestContextThreadLocal.get() != null && RequestContextThreadLocal.get().isPerfRequest()) {
			return null;
		}
		if (Objects.isNull(userData) || org.apache.commons.lang3.StringUtils.isEmpty(userData.getPrimaryPhone())) {
			throw new BadRequestException("Primary phone number does not exist for user " + externalId);
		}
		return userData.getPrimaryPhone();
	}
}
