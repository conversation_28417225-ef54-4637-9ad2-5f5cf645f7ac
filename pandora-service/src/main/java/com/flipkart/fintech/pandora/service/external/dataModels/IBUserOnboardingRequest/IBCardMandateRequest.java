package com.flipkart.fintech.pandora.service.external.dataModels.IBUserOnboardingRequest;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class IBCardMandateRequest {

    @JsonProperty ("card_id")
    private String cardId;

    @JsonProperty ("transaction_ref_no")
    private String transactionRefNo;

    @JsonProperty ("transaction_id")
    private String transactionId;

    @JsonProperty ("status")
    private String status;

    @JsonProperty ("status_msg")
    private String statusMsg;

    @JsonProperty ("emandate_date")
    private String emandateDate;

    @JsonProperty ("emandate_time")
    private String emandateTime;

    @JsonProperty ("payment_gateway_ref_id")
    private String paymentGatewayRefId;

    @JsonProperty ("mandate_status")
    private String mandateStatus;

    @JsonProperty ("payment_amount")
    private double paymentAmount;

    @JsonProperty ("mandate_registration_num")
    private String mandateRegistrationNum;

    @JsonProperty ("bank_name")
    private String bankName;

    @JsonProperty ("account_number")
    private String accountNumber;

    @JsonProperty ("ifsc_code")
    private String ifscCode;

    @JsonProperty ("eko_validation")
    private Boolean ekoValidation;

    @JsonProperty ("crn_number")
    private String crnNumber;

    @JsonProperty ("additional_6")
    private String additional6;

    public String getCardId() {
        return cardId;
    }

    public void setCardId(String cardId) {
        this.cardId = cardId;
    }

    public String getTransactionRefNo() {
        return transactionRefNo;
    }

    public void setTransactionRefNo(String transactionRefNo) {
        this.transactionRefNo = transactionRefNo;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatusMsg() {
        return statusMsg;
    }

    public void setStatusMsg(String statusMsg) {
        this.statusMsg = statusMsg;
    }

    public String getEmandateDate() {
        return emandateDate;
    }

    public void setEmandateDate(String emandateDate) {
        this.emandateDate = emandateDate;
    }

    public String getEmandateTime() {
        return emandateTime;
    }

    public void setEmandateTime(String emandateTime) {
        this.emandateTime = emandateTime;
    }

    public String getPaymentGatewayRefId() {
        return paymentGatewayRefId;
    }

    public void setPaymentGatewayRefId(String paymentGatewayRefId) {
        this.paymentGatewayRefId = paymentGatewayRefId;
    }

    public String getMandateStatus() {
        return mandateStatus;
    }

    public void setMandateStatus(String mandateStatus) {
        this.mandateStatus = mandateStatus;
    }

    public String getMandateRegistrationNum() {
        return mandateRegistrationNum;
    }

    public void setMandateRegistrationNum(String mandateRegistrationNum) {
        this.mandateRegistrationNum = mandateRegistrationNum;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getAccountNumber() {
        return accountNumber;
    }

    public void setAccountNumber(String accountNumber) {
        this.accountNumber = accountNumber;
    }

    public String getIfscCode() {
        return ifscCode;
    }

    public void setIfscCode(String ifscCode) {
        this.ifscCode = ifscCode;
    }

    public Boolean getEkoValidation() {
        return ekoValidation;
    }

    public void setEkoValidation(Boolean ekoValidation) {
        this.ekoValidation = ekoValidation;
    }

    public String getCrnNumber() {
        return crnNumber;
    }

    public void setCrnNumber(String crnNumber) {
        this.crnNumber = crnNumber;
    }

    public String getAdditional6() {
        return additional6;
    }

    public void setAdditional6(String additional6) {
        this.additional6 = additional6;
    }

    public double getPaymentAmount() {
        return paymentAmount;
    }

    public void setPaymentAmount(double paymentAmount) {
        this.paymentAmount = paymentAmount;
    }

    @Override
    public String toString() {
        return "IBCardMandateRequest{" +
                "cardId='" + cardId + '\'' +
                ", transactionRefNo='" + transactionRefNo + '\'' +
                ", transactionId='" + transactionId + '\'' +
                ", status='" + status + '\'' +
                ", statusMsg='" + statusMsg + '\'' +
                ", emandateDate='" + emandateDate + '\'' +
                ", emandateTime='" + emandateTime + '\'' +
                ", paymentGatewayRefId='" + paymentGatewayRefId + '\'' +
                ", mandateStatus='" + mandateStatus + '\'' +
                ", paymentAmount=" + paymentAmount +
                ", mandateRegistrationNum='" + mandateRegistrationNum + '\'' +
                ", bankName='" + bankName + '\'' +
                ", accountNumber='" + accountNumber + '\'' +
                ", ifscCode='" + ifscCode + '\'' +
                ", ekoValidation=" + ekoValidation +
                ", crnNumber='" + crnNumber + '\'' +
                ", additional6='" + additional6 + '\'' +
                '}';
    }
}
