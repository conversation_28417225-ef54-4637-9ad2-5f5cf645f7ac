/*
 * Created by Harsh
 */

package com.flipkart.fintech.pandora.service.external.dataModels.IBUserOnboardingRequest;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import javax.validation.constraints.NotNull;

@JsonIgnoreProperties(ignoreUnknown = true)
public class IBCardPutDocumentsRequest {

    @NotNull
    @JsonProperty("document_name")
    private String documentName; //ProofDocumentName

    @NotNull
    @JsonProperty("card_id")
    private String cardId;

    @NotNull
    @JsonProperty("document_id")
    private String documentId;

    @NotNull
    @JsonProperty
    private String type;

    @NotNull
    @JsonProperty
    private String data;

    @NotNull
    @JsonProperty("poa_type")
    private String poaType; //AddressInfo

    @NotNull
    @JsonProperty("poi_type")
    private String poiType;

    public String getCardId() {
        return cardId;
    }

    public void setCardId(String cardId) {
        this.cardId = cardId;
    }

    public String getDocumentId() {
        return documentId;
    }

    public void setDocumentId(String documentId) {
        this.documentId = documentId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getDocumentName() {
        return documentName;
    }

    public void setDocumentName(String documentName) {
        this.documentName = documentName;
    }

    public String getPoaType() {
        return poaType;
    }

    public void setPoaType(String poaType) {
        this.poaType = poaType;
    }

    @Override
    public String toString() {
        return "IBCardPutDocumentsRequest{" +
                "documentName='" + documentName + '\'' +
                ", cardId='" + cardId + '\'' +
                ", documentId='" + documentId + '\'' +
                ", type='" + type + '\'' +
//                ", data='" + data + '\'' +
                ", poaType=" + poaType +
                '}';
    }

    public String getPoiType() {
        return poiType;
    }

    public void setPoiType(String poiType) {
        this.poiType = poiType;
    }
}
