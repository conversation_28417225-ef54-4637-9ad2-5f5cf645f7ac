/*
 * Created by Harsh
 */

package com.flipkart.fintech.pandora.service.external.dataModels.IBUserOnboardingResponse;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class IBCardDetailsResponse {

    @JsonProperty
    private Boolean success;

    @JsonProperty("error_message")
    private String errorMessage;

    @JsonProperty("sub_occupation")
    private String subOccupation;

    @JsonProperty("stage")
    private String stage;

    @JsonProperty("salutation")
    private String salutation;

    @JsonProperty("pickup_time")
    private String pickupTime;

    @JsonProperty("pickup_status")
    private String pickupStatus;

    @JsonProperty("pickup_state")
    private String pickupState;

    @JsonProperty("pickup_pincode")
    private String pickupPincode;

    @JsonProperty("pickup_date")
    private String pickupDate;

    @JsonProperty("pickup_city")
    private String pickupCity;

    @JsonProperty("pickup_address")
    private String pickupAddress;

    @JsonProperty("physical_document_received")
    private String physicalDocumentReceived;

    @JsonProperty("physical_pickup_method")
    private String physicalPickupMethod;

    @JsonProperty("permanent_state")
    private String permanentState;

    @JsonProperty("permanent_pincode")
    private String permanentPincode;

    @JsonProperty("permanent_city")
    private String permanentCity;

    @JsonProperty("permanent_address")
    private String permanentAddress;

    @JsonProperty("pan_validation_status")
    private String panValidationStatus;

    @JsonProperty("pan_number")
    private String panNumber;

    @JsonProperty("occupation")
    private String occupation;

    @JsonProperty("mother_name")
    private String motherName;

    @JsonProperty("mobile_number")
    private String mobileNumber;

    @JsonProperty("middle_name")
    private String middleName;

    @JsonProperty("last_name")
    private String lastName;

    @JsonProperty("kyc_status")
    private Boolean kycStatus;

    @JsonProperty("is_poi_uploaded")
    private Boolean isPoiUploaded;

    @JsonProperty("is_poa_uploaded")
    private Boolean isPoaUploaded;

    @JsonProperty("is_alternate_data_valid")
    private Boolean isAlternateDataValid;

    @JsonProperty("gender")
    private String gender;

    @JsonProperty("flux_status")
    private String fluxStatus;

    @JsonProperty("first_name")
    private String firstName;

    @JsonProperty("father_name")
    private String fatherName;

    @JsonProperty("emi_due_day")
    private String emiDueDay;

    @JsonProperty("email_id")
    private String emaidId;

    @JsonProperty("document_uploaded")
    private Object documentUploaded;

    @JsonProperty("decline_reason")
    private String declineReason;

    @JsonProperty("debit_start_date")
    private String debitStartDate;

    @JsonProperty("debit_end_date")
    private String debitEndDate;

    @JsonProperty("daas_call")
    private String daasCall;

    @JsonProperty("cust_id")
    private String custId;

    @JsonProperty("current_state")
    private String currentState;

    @JsonProperty("current_pincode")
    private String currentPincode;

    @JsonProperty("current_city")
    private String currentCity;

    @JsonProperty("current_address")
    private String currentAddress;

    @JsonProperty("card_number")
    private String cardNumber;

    @JsonProperty("card_limit")
    private String cardLimit;

    @JsonProperty("card_id")
    private String cardId;

    @JsonProperty("card_expiry")
    private String cardExpiry;

    @JsonProperty("bank_acct_validation_status")
    private String bankAcctValidationStatus;

    @JsonProperty("aadhaar_uid")
    private String aadhaarUid;

    @JsonProperty("aadhaar_name")
    private String aadhaarName;

    @JsonProperty("existing_dhani")
    private String existingDhani;

    @JsonProperty("is_photo_uploaded")
    private Boolean isPhotoUploaded;

    @JsonProperty("mandate_status")
    private String mandateStatus;

    @JsonProperty("poa_count")
    private String poaCount;

    @JsonProperty("poa_type")
    private String poaType;

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public String getSubOccupation() {
        return subOccupation;
    }

    public void setSubOccupation(String subOccupation) {
        this.subOccupation = subOccupation;
    }

    public String getStage() {
        return stage;
    }

    public void setStage(String stage) {
        this.stage = stage;
    }

    public String getSalutation() {
        return salutation;
    }

    public void setSalutation(String salutation) {
        this.salutation = salutation;
    }

    public String getPickupTime() {
        return pickupTime;
    }

    public void setPickupTime(String pickupTime) {
        this.pickupTime = pickupTime;
    }

    public String getPickupStatus() {
        return pickupStatus;
    }

    public void setPickupStatus(String pickupStatus) {
        this.pickupStatus = pickupStatus;
    }

    public String getPickupState() {
        return pickupState;
    }

    public void setPickupState(String pickupState) {
        this.pickupState = pickupState;
    }

    public String getPickupPincode() {
        return pickupPincode;
    }

    public void setPickupPincode(String pickupPincode) {
        this.pickupPincode = pickupPincode;
    }

    public String getPickupDate() {
        return pickupDate;
    }

    public void setPickupDate(String pickupDate) {
        this.pickupDate = pickupDate;
    }

    public String getPickupCity() {
        return pickupCity;
    }

    public void setPickupCity(String pickupCity) {
        this.pickupCity = pickupCity;
    }

    public String getPickupAddress() {
        return pickupAddress;
    }

    public void setPickupAddress(String pickupAddress) {
        this.pickupAddress = pickupAddress;
    }

    public String getPhysicalDocumentReceived() {
        return physicalDocumentReceived;
    }

    public void setPhysicalDocumentReceived(String physicalDocumentReceived) {
        this.physicalDocumentReceived = physicalDocumentReceived;
    }

    public String getPhysicalPickupMethod() {
        return physicalPickupMethod;
    }

    public void setPhysicalPickupMethod(String physicalPickupMethod) {
        this.physicalPickupMethod = physicalPickupMethod;
    }

    public String getPermanentState() {
        return permanentState;
    }

    public void setPermanentState(String permanentState) {
        this.permanentState = permanentState;
    }

    public String getPermanentPincode() {
        return permanentPincode;
    }

    public void setPermanentPincode(String permanentPincode) {
        this.permanentPincode = permanentPincode;
    }

    public String getPermanentCity() {
        return permanentCity;
    }

    public void setPermanentCity(String permanentCity) {
        this.permanentCity = permanentCity;
    }

    public String getPermanentAddress() {
        return permanentAddress;
    }

    public void setPermanentAddress(String permanentAddress) {
        this.permanentAddress = permanentAddress;
    }

    public String getPanValidationStatus() {
        return panValidationStatus;
    }

    public void setPanValidationStatus(String panValidationStatus) {
        this.panValidationStatus = panValidationStatus;
    }

    public String getPanNumber() {
        return panNumber;
    }

    public void setPanNumber(String panNumber) {
        this.panNumber = panNumber;
    }

    public String getOccupation() {
        return occupation;
    }

    public void setOccupation(String occupation) {
        this.occupation = occupation;
    }

    public String getMotherName() {
        return motherName;
    }

    public void setMotherName(String motherName) {
        this.motherName = motherName;
    }

    public String getMobileNumber() {
        return mobileNumber;
    }

    public void setMobileNumber(String mobileNumber) {
        this.mobileNumber = mobileNumber;
    }

    public String getMiddleName() {
        return middleName;
    }

    public void setMiddleName(String middleName) {
        this.middleName = middleName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public Boolean getKycStatus() {
        return kycStatus;
    }

    public void setKycStatus(Boolean kycStatus) {
        this.kycStatus = kycStatus;
    }

    public Boolean getPoiUploaded() {
        return isPoiUploaded;
    }

    public void setPoiUploaded(Boolean poiUploaded) {
        isPoiUploaded = poiUploaded;
    }

    public Boolean getPoaUploaded() {
        return isPoaUploaded;
    }

    public void setPoaUploaded(Boolean poaUploaded) {
        isPoaUploaded = poaUploaded;
    }

    public Boolean getAlternateDataValid() {
        return isAlternateDataValid;
    }

    public void setAlternateDataValid(Boolean alternateDataValid) {
        isAlternateDataValid = alternateDataValid;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getFluxStatus() {
        return fluxStatus;
    }

    public void setFluxStatus(String fluxStatus) {
        this.fluxStatus = fluxStatus;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getFatherName() {
        return fatherName;
    }

    public void setFatherName(String fatherName) {
        this.fatherName = fatherName;
    }

    public String getEmiDueDay() {
        return emiDueDay;
    }

    public void setEmiDueDay(String emiDueDay) {
        this.emiDueDay = emiDueDay;
    }

    public String getEmaidId() {
        return emaidId;
    }

    public void setEmaidId(String emaidId) {
        this.emaidId = emaidId;
    }

    public Object getDocumentUploaded() {
        return documentUploaded;
    }

    public void setDocumentUploaded(Object documentUploaded) {
        this.documentUploaded = documentUploaded;
    }

    public String getDeclineReason() {
        return declineReason;
    }

    public void setDeclineReason(String declineReason) {
        this.declineReason = declineReason;
    }

    public String getDebitStartDate() {
        return debitStartDate;
    }

    public void setDebitStartDate(String debitStartDate) {
        this.debitStartDate = debitStartDate;
    }

    public String getDebitEndDate() {
        return debitEndDate;
    }

    public void setDebitEndDate(String debitEndDate) {
        this.debitEndDate = debitEndDate;
    }

    public String getDaasCall() {
        return daasCall;
    }

    public void setDaasCall(String daasCall) {
        this.daasCall = daasCall;
    }

    public String getCustId() {
        return custId;
    }

    public void setCustId(String custId) {
        this.custId = custId;
    }

    public String getCurrentState() {
        return currentState;
    }

    public void setCurrentState(String currentState) {
        this.currentState = currentState;
    }

    public String getCurrentPincode() {
        return currentPincode;
    }

    public void setCurrentPincode(String currentPincode) {
        this.currentPincode = currentPincode;
    }

    public String getCurrentCity() {
        return currentCity;
    }

    public void setCurrentCity(String currentCity) {
        this.currentCity = currentCity;
    }

    public String getCurrentAddress() {
        return currentAddress;
    }

    public void setCurrentAddress(String currentAddress) {
        this.currentAddress = currentAddress;
    }

    public String getCardNumber() {
        return cardNumber;
    }

    public void setCardNumber(String cardNumber) {
        this.cardNumber = cardNumber;
    }

    public String getCardLimit() {
        return cardLimit;
    }

    public void setCardLimit(String cardLimit) {
        this.cardLimit = cardLimit;
    }

    public String getCardId() {
        return cardId;
    }

    public void setCardId(String cardId) {
        this.cardId = cardId;
    }

    public String getCardExpiry() {
        return cardExpiry;
    }

    public void setCardExpiry(String cardExpiry) {
        this.cardExpiry = cardExpiry;
    }

    public String getBankAcctValidationStatus() {
        return bankAcctValidationStatus;
    }

    public void setBankAcctValidationStatus(String bankAcctValidationStatus) {
        this.bankAcctValidationStatus = bankAcctValidationStatus;
    }

    public String getAadhaarUid() {
        return aadhaarUid;
    }

    public void setAadhaarUid(String aadhaarUid) {
        this.aadhaarUid = aadhaarUid;
    }

    public String getAadhaarName() {
        return aadhaarName;
    }

    public void setAadhaarName(String aadhaarName) {
        this.aadhaarName = aadhaarName;
    }

    public String getExistingDhani() {
        return existingDhani;
    }

    public void setExistingDhani(String existingDhani) {
        this.existingDhani = existingDhani;
    }

    public Boolean getPhotoUploaded() {
        return isPhotoUploaded;
    }

    public void setPhotoUploaded(Boolean photoUploaded) {
        isPhotoUploaded = photoUploaded;
    }

    public String getMandateStatus() {
        return mandateStatus;
    }

    public void setMandateStatus(String mandateStatus) {
        this.mandateStatus = mandateStatus;
    }

    public String getPoaCount() {
        return poaCount;
    }

    public void setPoaCount(String poaCount) {
        this.poaCount = poaCount;
    }

    public String getPoaType() {
        return poaType;
    }

    public void setPoaType(String poaType) {
        this.poaType = poaType;
    }

    @Override
    public String toString() {
        return "IBCardDetailsResponse{" +
                "success=" + success +
                ", errorMessage='" + errorMessage + '\'' +
                ", subOccupation='" + subOccupation + '\'' +
                ", stage='" + stage + '\'' +
                ", salutation='" + salutation + '\'' +
                ", pickupTime='" + pickupTime + '\'' +
                ", pickupStatus='" + pickupStatus + '\'' +
                ", pickupState='" + pickupState + '\'' +
                ", pickupPincode='" + pickupPincode + '\'' +
                ", pickupDate='" + pickupDate + '\'' +
                ", pickupCity='" + pickupCity + '\'' +
                ", pickupAddress='" + pickupAddress + '\'' +
                ", physicalDocumentReceived='" + physicalDocumentReceived + '\'' +
                ", physicalPickupMethod='" + physicalPickupMethod + '\'' +
                ", permanentState='" + permanentState + '\'' +
                ", permanentPincode='" + permanentPincode + '\'' +
                ", permanentCity='" + permanentCity + '\'' +
                ", permanentAddress='" + permanentAddress + '\'' +
                ", panValidationStatus='" + panValidationStatus + '\'' +
                ", panNumber='" + panNumber + '\'' +
                ", occupation='" + occupation + '\'' +
                ", motherName='" + motherName + '\'' +
                ", mobileNumber='" + mobileNumber + '\'' +
                ", middleName='" + middleName + '\'' +
                ", lastName='" + lastName + '\'' +
                ", kycStatus=" + kycStatus +
                ", isPoiUploaded=" + isPoiUploaded +
                ", isPoaUploaded=" + isPoaUploaded +
                ", isAlternateDataValid=" + isAlternateDataValid +
                ", gender='" + gender + '\'' +
                ", fluxStatus='" + fluxStatus + '\'' +
                ", firstName='" + firstName + '\'' +
                ", fatherName='" + fatherName + '\'' +
                ", emiDueDay='" + emiDueDay + '\'' +
                ", emaidId='" + emaidId + '\'' +
                ", documentUploaded=" + documentUploaded +
                ", declineReason='" + declineReason + '\'' +
                ", debitStartDate='" + debitStartDate + '\'' +
                ", debitEndDate='" + debitEndDate + '\'' +
                ", daasCall='" + daasCall + '\'' +
                ", custId='" + custId + '\'' +
                ", currentState='" + currentState + '\'' +
                ", currentPincode='" + currentPincode + '\'' +
                ", currentCity='" + currentCity + '\'' +
                ", currentAddress='" + currentAddress + '\'' +
                ", cardNumber='" + cardNumber + '\'' +
                ", cardLimit='" + cardLimit + '\'' +
                ", cardId='" + cardId + '\'' +
                ", cardExpiry='" + cardExpiry + '\'' +
                ", bankAcctValidationStatus='" + bankAcctValidationStatus + '\'' +
                ", aadhaarUid='" + aadhaarUid + '\'' +
                ", aadhaarName='" + aadhaarName + '\'' +
                ", existingDhani='" + existingDhani + '\'' +
                ", isPhotoUploaded=" + isPhotoUploaded +
                ", mandateStatus='" + mandateStatus + '\'' +
                ", poaCount='" + poaCount + '\'' +
                ", poaType='" + poaType + '\'' +
                '}';
    }
}
