/*
 * Created by Harsh
 */

package com.flipkart.fintech.pandora.service.external.dataModels.IBUserOnboardingResponse;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class IBCardPutDocumentsResponse {

    @JsonProperty
    private Boolean success;

    @JsonProperty("error_message")
    private String errorMessage;

    @JsonProperty("document_SF_id")
    private String lenderDocumentReferenceId;

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public String getLenderDocumentReferenceId() {
        return lenderDocumentReferenceId;
    }

    public void setLenderDocumentReferenceId(String lenderDocumentReferenceId) {
        this.lenderDocumentReferenceId = lenderDocumentReferenceId;
    }

    @Override
    public String toString() {
        return "IBCardPutDocumentsResponse{" +
                "success=" + success +
                ", errorMessage='" + errorMessage + '\'' +
                ", lenderDocumentReferenceId='" + lenderDocumentReferenceId + '\'' +
                '}';
    }
}
