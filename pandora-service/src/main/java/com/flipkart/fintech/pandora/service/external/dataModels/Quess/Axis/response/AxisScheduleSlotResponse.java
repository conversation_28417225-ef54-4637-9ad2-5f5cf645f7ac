package com.flipkart.fintech.pandora.service.external.dataModels.Quess.Axis.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pandora.service.external.dataModels.Quess.Axis.ReserveSlotResponse;
import com.flipkart.fintech.pandora.service.external.dataModels.axisUserOnboarding.response.BaseHttpResponse;
import lombok.Data;

@Data
public class AxisScheduleSlotResponse extends BaseHttpResponse {
    @JsonProperty("ReserveSlotResponse")
    private ReserveSlotResponse reserveSlotResponse;
}
