package com.flipkart.fintech.pandora.service.external.dataModels.axisLifeCycleMangement.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 05/01/22.
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class CreditLimitIncrementRequestBody {
    @JsonProperty("cardSerNo")
    private String cardSerNo;

    @JsonProperty("deviceId")
    private String deviceId;
}
