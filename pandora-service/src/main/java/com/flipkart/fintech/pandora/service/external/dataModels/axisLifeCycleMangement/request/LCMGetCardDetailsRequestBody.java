package com.flipkart.fintech.pandora.service.external.dataModels.axisLifeCycleMangement.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.sensitive.annotation.SensitiveField;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 11/05/20.
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class LCMGetCardDetailsRequestBody {
    @JsonProperty("cardSerNo")
    private String cardSerNo;

    @JsonProperty("deviceId")
    private String deviceId;

    @JsonProperty("mobileNo")
    @SensitiveField(keyName = "cbcKey")
    private String mobileNo;
}
