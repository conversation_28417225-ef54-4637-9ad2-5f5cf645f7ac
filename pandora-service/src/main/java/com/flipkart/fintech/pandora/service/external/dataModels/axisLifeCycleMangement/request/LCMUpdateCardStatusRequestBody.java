package com.flipkart.fintech.pandora.service.external.dataModels.axisLifeCycleMangement.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 11/05/20.
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class LCMUpdateCardStatusRequestBody {
    @JsonProperty("cardSerNo")
    private String cardSerNo;
    @JsonProperty("cardStatus")
    private String cardStatus;
    @JsonProperty("deviceId")
    private String deviceId;
}
