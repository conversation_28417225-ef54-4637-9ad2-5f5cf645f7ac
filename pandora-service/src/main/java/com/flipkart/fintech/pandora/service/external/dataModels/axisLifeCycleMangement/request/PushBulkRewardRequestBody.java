package com.flipkart.fintech.pandora.service.external.dataModels.axisLifeCycleMangement.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pandora.service.external.dataModels.axisUserOnboarding.request.BaseRequest;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class PushBulkRewardRequestBody {

    @NotNull
    @JsonProperty("rewardsDetailsRequestBody")
    private List<PushRewardRequestBody> rewardsDetailsRequestBody;
}
