package com.flipkart.fintech.pandora.service.external.dataModels.axisLifeCycleMangement.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pinaka.api.enums.RewardType;
import com.flipkart.fintech.pinaka.api.enums.TransactionType;
import lombok.Data;

import javax.validation.constraints.NotNull;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class PushRewardRequestBody {

    @NotNull
    @JsonProperty("applicationSerno")
    private String applicationSerno;

    @NotNull
    @JsonProperty("amount")
    private Double amount;

    @NotNull
    @JsonProperty("merchantTransactionId")
    private String merchantTransactionId;

    @NotNull
    @JsonProperty("transactionType")
    private TransactionType transactionType;

    @NotNull
    @JsonProperty("rewardType")
    private RewardType rewardType;

    @JsonProperty("statementStartDate")
    private Long statementStartDate;

    @JsonProperty("statementEndDate")
    private Long statementEndDate;
}
