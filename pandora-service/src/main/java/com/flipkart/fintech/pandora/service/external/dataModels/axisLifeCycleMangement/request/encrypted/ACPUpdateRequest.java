package com.flipkart.fintech.pandora.service.external.dataModels.axisLifeCycleMangement.request.encrypted;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.flipkart.fintech.pandora.service.external.dataModels.axisUserOnboarding.request.BaseRequest;
import lombok.Data;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonTypeInfo(include = JsonTypeInfo.As.WRAPPER_OBJECT, use = JsonTypeInfo.Id.NAME)
@Data
public class ACPUpdateRequest extends BaseRequest {

    @JsonProperty("ACPUpdateRequestBodyEncrypted")
    private String ACPUpdateRequestBodyEncrypted;
}