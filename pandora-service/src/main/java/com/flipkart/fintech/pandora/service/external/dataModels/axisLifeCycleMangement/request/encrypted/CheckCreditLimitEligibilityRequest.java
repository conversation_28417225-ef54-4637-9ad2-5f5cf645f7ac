package com.flipkart.fintech.pandora.service.external.dataModels.axisLifeCycleMangement.request.encrypted;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.flipkart.fintech.pandora.service.external.dataModels.axisUserOnboarding.request.BaseRequest;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 05/01/22.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonTypeInfo(include = JsonTypeInfo.As.WRAPPER_OBJECT, use = JsonTypeInfo.Id.NAME)
@Data
public class CheckCreditLimitEligibilityRequest extends BaseRequest {
    @JsonProperty("CheckCreditLimitEligibilityRequestBodyEncrypted")
    private String CheckCreditLimitEligibilityRequestBodyEncrypted;
}
