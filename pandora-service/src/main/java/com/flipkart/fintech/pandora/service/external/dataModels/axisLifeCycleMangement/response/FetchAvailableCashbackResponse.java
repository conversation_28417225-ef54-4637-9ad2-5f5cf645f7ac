package com.flipkart.fintech.pandora.service.external.dataModels.axisLifeCycleMangement.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 11/05/20.
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class FetchAvailableCashbackResponse {
    @JsonProperty("rewardPoint")
    private String rewardPoint;
}
