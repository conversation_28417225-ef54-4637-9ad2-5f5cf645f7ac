package com.flipkart.fintech.pandora.service.external.dataModels.axisLifeCycleMangement.response;

import com.fasterxml.jackson.annotation.JsonProperty;

public class LCMGenerateAuthOTPResponseBody {

    @JsonProperty("isOTPGenerated")
    private boolean isOTPGenerated;

    @JsonProperty("otpReferenceId")
    private String otpReferenceId;

    @JsonProperty("validationToken")
    private String validationToken;

    public boolean getIsOTPGenerated() {
        return isOTPGenerated;
    }

    public void setIsOTPGenerated(boolean isOTPGenerated) {
        this.isOTPGenerated = isOTPGenerated;
    }

    public String getOtpReferenceId() {
        return otpReferenceId;
    }

    public void setOtpReferenceId(String otpReferenceId) {
        this.otpReferenceId = otpReferenceId;
    }

    public String getValidationToken() {
        return validationToken;
    }

    public void setValidationToken(String validationToken) {
        this.validationToken = validationToken;
    }

    @Override
    public String toString() {
        return "LCMGenerateAuthOTPResponseBody{" +
                "isOTPGenerated=" + isOTPGenerated +
                ", otpReferenceId='" + otpReferenceId + '\'' +
                ", validationToken='" + validationToken + '\'' +
                '}';
    }
}
