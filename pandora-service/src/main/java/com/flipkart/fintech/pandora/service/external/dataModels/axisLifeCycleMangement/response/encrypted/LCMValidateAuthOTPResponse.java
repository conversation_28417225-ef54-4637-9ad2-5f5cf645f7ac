package com.flipkart.fintech.pandora.service.external.dataModels.axisLifeCycleMangement.response.encrypted;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.flipkart.fintech.pandora.service.external.dataModels.axisUserOnboarding.SubHeader;
import com.flipkart.fintech.pandora.service.external.dataModels.axisUserOnboarding.response.BaseHttpResponse;

/**
 * <AUTHOR>
 * @since 13/05/19.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonTypeInfo(include = JsonTypeInfo.As.WRAPPER_OBJECT, use = JsonTypeInfo.Id.NAME)
public class LCMValidateAuthOTPResponse extends BaseHttpResponse {
    @JsonProperty("SubHeader")
    private SubHeader subHeader;

    @JsonProperty("LCMValidateAuthOTPResponseBodyEncrypted")
    private String lcmValidateAuthOtpResponseBodyEncrypted;

    public SubHeader getSubHeader() {
        return subHeader;
    }

    public void setSubHeader(SubHeader subHeader) {
        this.subHeader = subHeader;
    }

    public String getLcmValidateAuthOtpResponseBodyEncrypted() {
        return lcmValidateAuthOtpResponseBodyEncrypted;
    }

    public void setLcmValidateAuthOtpResponseBodyEncrypted(String lcmValidateAuthOtpResponseBodyEncrypted) {
        this.lcmValidateAuthOtpResponseBodyEncrypted = lcmValidateAuthOtpResponseBodyEncrypted;
    }

    @Override
    public String toString() {
        return "LCMValidateAuthOTPResponse{" +
                "subHeader=" + subHeader +
                ", lcmValidateAuthOtpResponseBodyEncrypted='" + lcmValidateAuthOtpResponseBodyEncrypted + '\'' +
                '}';
    }
}
