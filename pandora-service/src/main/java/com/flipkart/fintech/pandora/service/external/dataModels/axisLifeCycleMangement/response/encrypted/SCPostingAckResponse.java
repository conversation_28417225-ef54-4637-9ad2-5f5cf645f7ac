package com.flipkart.fintech.pandora.service.external.dataModels.axisLifeCycleMangement.response.encrypted;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.pandora.service.external.dataModels.axisUserOnboarding.SubHeader;
import com.flipkart.fintech.pandora.service.external.dataModels.axisUserOnboarding.response.BaseHttpResponse;
import lombok.Data;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonTypeInfo(include = JsonTypeInfo.As.WRAPPER_OBJECT, use = JsonTypeInfo.Id.NAME)
@Data
public class SCPostingAckResponse extends BaseHttpResponse {
    @JsonProperty("SubHeader")
    private SubHeader subHeader;

    @JsonProperty("SCPostingAckResponseResponseBodyEncrypted")
    private String SCPostingAckResponseResponseBodyEncrypted;
}
