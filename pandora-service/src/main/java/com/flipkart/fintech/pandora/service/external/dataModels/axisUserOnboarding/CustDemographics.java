package com.flipkart.fintech.pandora.service.external.dataModels.axisUserOnboarding;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.sensitive.annotation.SensitiveField;

import java.util.List;

/**
 * <AUTHOR>
 * @since 03/05/19.
 */
@JsonIgnoreProperties(ignoreUnknown = true)

public class CustDemographics {
    @JsonProperty("title")
    private String title;

    @JsonProperty("firstName")
    private String firstName;

    @JsonProperty("lastName")
    private String lastName;

    @JsonProperty("mothersMaidenName")
    private String mothersMaidenName;

    @JsonProperty("dateOfBirth")
    private String dateOfBirth;

    @JsonProperty("name")
    private String name;

    @JsonProperty("custSex")
    private String custSex;

    @JsonProperty("nationality")
    private String nationality;

    @JsonProperty("education")
    private String education;

    @JsonProperty("martialStatus")
    private String martialStatus;

    @JsonProperty("noofDependent")
    private String noofDependent;

    @JsonProperty("caste")
    private String caste;

    @JsonProperty("community")
    private String community;

    @JsonProperty("health")
    private String health;

    @JsonProperty("pan")
    @SensitiveField(keyName = "cbcKey")
    private String pan;

    @JsonProperty("Address")
    private List<Address> addresses;

    @JsonProperty("country")
    private String country;

    @JsonProperty("phone")
    @SensitiveField(keyName = "cbcKey")
    private String phone;

    @JsonProperty("phone2")
    @SensitiveField(keyName = "cbcKey")
    private String phone2;

    @JsonProperty("mobile")
    @SensitiveField(keyName = "cbcKey")
    private String mobile;

    @JsonProperty("email")
    @SensitiveField(keyName = "cbcKey")
    private String email;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getMothersMaidenName() {
        return mothersMaidenName;
    }

    public void setMothersMaidenName(String mothersMaidenName) {
        this.mothersMaidenName = mothersMaidenName;
    }

    public String getDateOfBirth() {
        return dateOfBirth;
    }

    public void setDateOfBirth(String dateOfBirth) {
        this.dateOfBirth = dateOfBirth;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCustSex() {
        return custSex;
    }

    public void setCustSex(String custSex) {
        this.custSex = custSex;
    }

    public String getNationality() {
        return nationality;
    }

    public void setNationality(String nationality) {
        this.nationality = nationality;
    }

    public String getEducation() {
        return education;
    }

    public void setEducation(String education) {
        this.education = education;
    }

    public String getMartialStatus() {
        return martialStatus;
    }

    public void setMartialStatus(String martialStatus) {
        this.martialStatus = martialStatus;
    }

    public String getNoofDependent() {
        return noofDependent;
    }

    public void setNoofDependent(String noofDependent) {
        this.noofDependent = noofDependent;
    }

    public String getCaste() {
        return caste;
    }

    public void setCaste(String caste) {
        this.caste = caste;
    }

    public String getCommunity() {
        return community;
    }

    public void setCommunity(String community) {
        this.community = community;
    }

    public String getHealth() {
        return health;
    }

    public void setHealth(String health) {
        this.health = health;
    }

    public String getPan() {
        return pan;
    }

    public void setPan(String pan) {
        this.pan = pan;
    }

    public List<Address> getAddresses() {
        return addresses;
    }

    public void setAddresses(List<Address> addresses) {
        this.addresses = addresses;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getPhone2() {
        return phone2;
    }

    public void setPhone2(String phone2) {
        this.phone2 = phone2;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    @Override
    public String toString() {
        return "CustDemographics{" + "title='" + title + '\'' + ", firstName='" + firstName + '\'' + ", lastName='"
                + lastName + '\'' + ", mothersMaidenName='" + mothersMaidenName + '\'' + ", dateOfBirth='" + dateOfBirth
                + '\'' + ", name='" + name + '\'' + ", custSex='" + custSex + '\'' + ", nationality='" + nationality
                + '\'' + ", education='" + education + '\'' + ", martialStatus='" + martialStatus + '\''
                + ", noofDependent='" + noofDependent + '\'' + ", caste='" + caste + '\'' + ", community='" + community
                + '\'' + ", health='" + health + '\'' + ", pan='" + pan + '\''
                + ", country='" + country + '\'' + ", phone='" + phone + '\'' + ", phone2='" + phone2 + '\''
                + ", mobile='" + mobile + '\'' + ", email='" + email + '\'' + '}';
    }
}
