package com.flipkart.fintech.pandora.service.external.dataModels.axisUserOnboarding.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pandora.service.external.dataModels.axisUserOnboarding.SubHeader;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 24/04/19.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class BaseRequest {
    @JsonProperty("SubHeader")
    private SubHeader subHeader;

    public SubHeader getSubHeader() {
        return subHeader;
    }

    public void setSubHeader(SubHeader subHeader) {
        this.subHeader = subHeader;
    }

    @Override
    public String toString() {
        return "BaseRequest{" + "subHeader=" + subHeader + '}';
    }
}