package com.flipkart.fintech.pandora.service.external.dataModels.axisUserOnboarding.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 * @since 03/05/19.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class CheckCaseStatusRequestBody {
    @JsonProperty("caseReferenceId")
    private String caseReferenceId;

    @JsonProperty("deviceId")
    private String deviceId;

    public String getCaseReferenceId()
    {
        return caseReferenceId;
    }

    public void setCaseReferenceId(String caseReferenceId)
    {
        this.caseReferenceId = caseReferenceId;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    @Override
    public String toString() {
        return "CheckCaseStatusRequestBody{" +
                "caseReferenceId='" + caseReferenceId + '\'' +
                ", deviceId='" + deviceId + '\'' +
                '}';
    }
}

