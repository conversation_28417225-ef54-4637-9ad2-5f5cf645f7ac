package com.flipkart.fintech.pandora.service.external.dataModels.axisUserOnboarding.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.sensitive.annotation.SensitiveField;

@JsonIgnoreProperties(ignoreUnknown = true)
public class CheckCaseStatusRequestBodyV3 {
    @JsonProperty("caseReferenceId")
    private String caseReferenceId;

    @JsonProperty("deviceId")
    private String deviceId;

    @JsonProperty("channelId")
    private String channelId;

    @JsonProperty("mobileNumber")
    @SensitiveField(keyName = "cbcKey")
    private String mobileNumber;

    @JsonProperty("applicationReferenceId")
    private String applicationReferenceId;


    public String getCaseReferenceId()
    {
        return caseReferenceId;
    }

    public void setCaseReferenceId(String caseReferenceId)
    {
        this.caseReferenceId = caseReferenceId;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getChannelId() {
        return channelId;
    }

    public void setChannelId(String channelId) {
        this.channelId = channelId;
    }

    public String getMobileNumber() {
        return mobileNumber;
    }

    public void setMobileNumber(String mobileNumber) {
        this.mobileNumber = mobileNumber;
    }

    public String getApplicationReferenceId() {
        return applicationReferenceId;
    }

    public void setApplicationReferenceId(String applicationReferenceId) {
        this.applicationReferenceId = applicationReferenceId;
    }

    @Override
    public String toString() {
        return "CheckCaseStatusRequestBody{" +
                "caseReferenceId='" + caseReferenceId + '\'' +
                ", deviceId='" + deviceId + '\'' +
                ", channelId='" + channelId + '\'' +
                ", mobileNumber='" + mobileNumber + '\'' +
                ", applicationReferenceId='" + applicationReferenceId + '\'' +
                '}';
    }
}