package com.flipkart.fintech.pandora.service.external.dataModels.axisUserOnboarding.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.sensitive.annotation.SensitiveField;

/**
 * <AUTHOR>
 * @since 03/05/19.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class FetchCustomerDemographicDetailsRequestBody {
    @JsonProperty("applicationReferenceId")
    private String applicationReferenceId;

    @JsonProperty("mobileNumber")
    @SensitiveField(keyName = "cbcKey")
    private String mobileNumber;

    @JsonProperty("otpReferenceId")
    private String otpReferenceId;

    @JsonProperty("deviceId")
    private String deviceId;

    @JsonProperty("otp")
    private String otp;

    public String getApplicationReferenceId() {
        return applicationReferenceId;
    }

    public void setApplicationReferenceId(String applicationReferenceId) {
        this.applicationReferenceId = applicationReferenceId;
    }

    public String getMobileNumber() {
        return mobileNumber;
    }

    public void setMobileNumber(String mobileNumber) {
        this.mobileNumber = mobileNumber;
    }

    public String getOtpReferenceId() {
        return otpReferenceId;
    }

    public void setOtpReferenceId(String otpReferenceId) {
        this.otpReferenceId = otpReferenceId;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getOtp() {
        return otp;
    }

    public void setOtp(String otp) {
        this.otp = otp;
    }

    @Override
    public String toString() {
        return "FetchCustomerDemographicDetailsRequestBody{" + "applicationReferenceId='"
                + applicationReferenceId + '\'' + ", mobileNumber='" + mobileNumber + '\'' + ", otpReferenceId='"
                + otpReferenceId + '\'' + ", deviceId='" + deviceId + '\'' + ", otp='" + otp + '\'' + '}';
    }
}
