package com.flipkart.fintech.pandora.service.external.dataModels.axisUserOnboarding.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pandora.service.external.dataModels.axisUserOnboarding.CustomerCardDetails;
import com.flipkart.sensitive.annotation.SensitiveField;

/**
 * <AUTHOR>
 * @since 06/05/19.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class ProcessNewCardCaseRequestBody {
    @JsonProperty("applicationReferenceId")
    private String applicationReferenceId;

    @JsonProperty("mobileNumber")
    @SensitiveField(keyName = "cbcKey")
    private String mobileNumber;

    @JsonProperty("otpReferenceId")
    private String otpReferenceId;

    @JsonProperty("deviceId")
    private String deviceId;

    @JsonProperty("otp")
    private String otp;

    @JsonProperty("CustomerCardDetails")
    private CustomerCardDetails customerCardDetails;

    public String getApplicationReferenceId()
    {
        return applicationReferenceId;
    }

    public void setApplicationReferenceId(String applicationReferenceId)
    {
        this.applicationReferenceId = applicationReferenceId;
    }

    public String getMobileNumber()
    {
        return mobileNumber;
    }

    public void setMobileNumber(String mobileNumber)
    {
        this.mobileNumber = mobileNumber;
    }

    public String getOtpReferenceId()
    {
        return otpReferenceId;
    }

    public void setOtpReferenceId(String otpReferenceId)
    {
        this.otpReferenceId = otpReferenceId;
    }

    public String getDeviceId()
    {
        return deviceId;
    }

    public void setDeviceId(String deviceId)
    {
        this.deviceId = deviceId;
    }

    public String getOtp()
    {
        return otp;
    }

    public void setOtp(String otp)
    {
        this.otp = otp;
    }

    public CustomerCardDetails getCustomerCardDetails()
    {
        return customerCardDetails;
    }

    public void setCustomerCardDetails(CustomerCardDetails customerCardDetails)
    {
        this.customerCardDetails = customerCardDetails;
    }

    @Override
    public String toString() {
        return "ProcessNewCardCaseRequestBody{" +
                "applicationReferenceId='" + applicationReferenceId + '\'' +
                ", mobileNumber='" + mobileNumber + '\'' +
                ", otpReferenceId='" + otpReferenceId + '\'' +
                ", deviceId='" + deviceId + '\'' +
                ", otp='" + otp + '\'' +
                ", customerCardDetails=" + customerCardDetails +
                '}';
    }
}
