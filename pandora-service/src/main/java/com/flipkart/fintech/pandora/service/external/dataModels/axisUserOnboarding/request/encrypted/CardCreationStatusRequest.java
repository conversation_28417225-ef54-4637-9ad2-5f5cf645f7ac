package com.flipkart.fintech.pandora.service.external.dataModels.axisUserOnboarding.request.encrypted;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonTypeInfo;

import com.flipkart.fintech.pandora.service.external.dataModels.axisUserOnboarding.SubHeader;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 30/04/19.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonTypeInfo(include = JsonTypeInfo.As.WRAPPER_OBJECT, use = JsonTypeInfo.Id.NAME)
public class CardCreationStatusRequest {
    @NotNull
    @JsonProperty("SubHeader")
    private SubHeader subHeader;
    @NotNull
    @JsonProperty("CardCreationStatusRequestBodyEncrypted")
    private String cardCreationStatusRequestBodyEncrypted;

    public SubHeader getSubHeader() {
        return subHeader;
    }

    public void setSubHeader(SubHeader subHeader) {
        this.subHeader = subHeader;
    }

    public String getCardCreationStatusRequestBodyEncrypted() {
        return cardCreationStatusRequestBodyEncrypted;
    }

    public void setCardCreationStatusRequestBodyEncrypted(String cardCreationStatusRequestBodyEncrypted) {
        this.cardCreationStatusRequestBodyEncrypted = cardCreationStatusRequestBodyEncrypted;
    }

    @Override
    public String toString() {
        return "CardCreationStatusRequest{" +
                "subHeader=" + subHeader +
                ", cardCreationStatusRequestBodyEncrypted='" + cardCreationStatusRequestBodyEncrypted + '\'' +
                '}';
    }
}
