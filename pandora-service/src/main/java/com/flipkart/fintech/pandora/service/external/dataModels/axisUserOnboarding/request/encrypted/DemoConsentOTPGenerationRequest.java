package com.flipkart.fintech.pandora.service.external.dataModels.axisUserOnboarding.request.encrypted;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.flipkart.fintech.pandora.service.external.dataModels.axisUserOnboarding.request.BaseRequest;

/**
 * <AUTHOR>
 * @since 01/05/19.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonTypeInfo(include = JsonTypeInfo.As.WRAPPER_OBJECT, use = JsonTypeInfo.Id.NAME)
public class DemoConsentOTPGenerationRequest extends BaseRequest {
    @JsonProperty("DemoConsentOTPGenerationRequestBodyEncrypted")
    private String demoConsentOTPGenerationRequestBodyEncrypted;

    public String getDemoConsentOTPGenerationRequestBodyEncrypted() {
        return demoConsentOTPGenerationRequestBodyEncrypted;
    }

    public void setDemoConsentOTPGenerationRequestBodyEncrypted(String demoConsentOTPGenerationRequestBodyEncrypted) {
        this.demoConsentOTPGenerationRequestBodyEncrypted = demoConsentOTPGenerationRequestBodyEncrypted;
    }

    @Override
    public String toString() {
        return "DemoConsentOTPGenerationRequest{" +
                "demoConsentOTPGenerationRequestBodyEncrypted='" + demoConsentOTPGenerationRequestBodyEncrypted + '\'' +
                '}';
    }
}
