package com.flipkart.fintech.pandora.service.external.dataModels.axisUserOnboarding.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pandora.service.external.dataModels.axisUserOnboarding.CustDemographics;

public class FetchCustomerDemographicDetailsResponseBody {
    @JsonProperty("isCustDemographicsFetched")
    private boolean isCustDemographicsFetched;

    @JsonProperty("validationToken")
    private String validationToken;

    @JsonProperty("CustDemographics")
    private CustDemographics custDemographics;

    public boolean getIsCustDemographicsFetched() {
        return isCustDemographicsFetched;
    }

    public void setIsCustDemographicsFetched(boolean isCustDemographicsFetched) {
        this.isCustDemographicsFetched = isCustDemographicsFetched;
    }

    public String getValidationToken() {
        return validationToken;
    }

    public void setValidationToken(String validationToken) {
        this.validationToken = validationToken;
    }

    public CustDemographics getCustDemographics() {
        return custDemographics;
    }

    public void setCustDemographics(CustDemographics custDemographics) {
        this.custDemographics = custDemographics;
    }

    @Override
    public String toString() {
        return "FetchCustomerDemographicDetailsResponseBody{" +
                "isCustDemographicsFetched=" + isCustDemographicsFetched +
                ", validationToken='" + validationToken + '\'' +
                ", custDemographics=" + custDemographics +
                '}';
    }
}
