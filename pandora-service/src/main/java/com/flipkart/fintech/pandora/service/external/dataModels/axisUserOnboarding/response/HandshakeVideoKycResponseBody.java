package com.flipkart.fintech.pandora.service.external.dataModels.axisUserOnboarding.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class HandshakeVideoKycResponseBody extends BaseHttpResponse {
    @JsonProperty("dropOffURL")
    private String dropOffURL;

    @JsonProperty("redirectionURLToPartner")
    private String redirectionURLToPartner;

    @JsonProperty("status")
    private String status;

    @JsonProperty("message")
    private String message;

}
