package com.flipkart.fintech.pandora.service.external.dataModels.axisUserOnboarding.response.encrypted;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.flipkart.fintech.pandora.service.external.dataModels.axisUserOnboarding.SubHeader;
import com.flipkart.fintech.pandora.service.external.dataModels.axisUserOnboarding.response.BaseHttpResponse;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonTypeInfo(include = JsonTypeInfo.As.WRAPPER_OBJECT, use = JsonTypeInfo.Id.NAME)
public class CheckCardUpgradeStatusResponse extends BaseHttpResponse {
    @JsonProperty("SubHeader")
    private SubHeader subHeader;
    @JsonProperty("CheckCardUpgradeStatusResponseBodyEncrypted")
    private String checkCardUpgradeStatusResponseBodyEncrypted;

    public SubHeader getSubHeader() {
        return subHeader;
    }

    public void setSubHeader(SubHeader subHeader) {
        this.subHeader = subHeader;
    }

    public String getCheckCardUpgradeStatusResponseBodyEncrypted() {
        return checkCardUpgradeStatusResponseBodyEncrypted;
    }

    public void setCheckCardUpgradeStatusResponseBodyEncrypted(String checkCardUpgradeStatusResponseBodyEncrypted) {
        this.checkCardUpgradeStatusResponseBodyEncrypted = checkCardUpgradeStatusResponseBodyEncrypted;
    }

    @Override
    public String toString() {
        return "CheckCardUpgradeStatusResponse{" +
                "subHeader=" + subHeader +
                ", checkCardUpgradeStatusResponseBodyEncrypted='" + checkCardUpgradeStatusResponseBodyEncrypted + '\'' +
                '}';
    }
}
