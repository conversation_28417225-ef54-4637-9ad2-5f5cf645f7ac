package com.flipkart.fintech.pandora.service.external.dataModels.axisUserOnboarding.response.encrypted;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.flipkart.fintech.pandora.service.external.dataModels.axisUserOnboarding.SubHeader;
import com.flipkart.fintech.pandora.service.external.dataModels.axisUserOnboarding.response.BaseHttpResponse;

/**
 * <AUTHOR>
 * @since 01/05/19.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonTypeInfo(include = JsonTypeInfo.As.WRAPPER_OBJECT, use = JsonTypeInfo.Id.NAME)
public class DemoConsentOTPGenerationResponse  extends BaseHttpResponse {
    @JsonProperty("SubHeader")
    private SubHeader subHeader;

    @JsonProperty("DemoConsentOTPGenerationResponseBodyEncrypted")
    private String demoConsentOTPGenerationResponseBodyEncrypted;

    public SubHeader getSubHeader()
    {
        return subHeader;
    }

    public void setSubHeader(SubHeader subHeader)
    {
        this.subHeader = subHeader;
    }

    public String getDemoConsentOTPGenerationResponseBodyEncrypted()
    {
        return demoConsentOTPGenerationResponseBodyEncrypted;
    }

    public void setDemoConsentOTPGenerationResponseBodyEncrypted(String demoConsentOTPGenerationResponseBodyEncrypted)
    {
        this.demoConsentOTPGenerationResponseBodyEncrypted = demoConsentOTPGenerationResponseBodyEncrypted;
    }

    @Override
    public String toString() {
        return "DemoConsentOTPGenerationResponse{" +
                "subHeader=" + subHeader +
                ", demoConsentOTPGenerationResponseBodyEncrypted='" + demoConsentOTPGenerationResponseBodyEncrypted + '\'' +
                '}';
    }
}
