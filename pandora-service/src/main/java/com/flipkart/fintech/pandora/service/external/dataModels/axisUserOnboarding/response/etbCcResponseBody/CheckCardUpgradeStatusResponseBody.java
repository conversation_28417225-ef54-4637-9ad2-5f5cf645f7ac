package com.flipkart.fintech.pandora.service.external.dataModels.axisUserOnboarding.response.etbCcResponseBody;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CheckCardUpgradeStatusResponseBody {
    @JsonProperty("isCardCreated")
    private Boolean isCardCreated;

    @JsonProperty("failureReason")
    private String failureReason;

    @JsonProperty("cardSerNo")
    private String cardSerNo;

    @JsonProperty("creditLimit")
    private String crLimit;

    @JsonProperty("validationToken")
    private String validationToken;

    public Boolean getIsCardCreated() {
        return isCardCreated;
    }

    public void setIsCardCreated(Boolean isCardCreated) {
        this.isCardCreated = isCardCreated;
    }

    public String getFailureReason() {
        return failureReason;
    }

    public void setFailureReason(String failureReason) {
        this.failureReason = failureReason;
    }

    public String getCardSerNo() {
        return cardSerNo;
    }

    public void setCardSerNo(String cardSerNo) {
        this.cardSerNo = cardSerNo;
    }

    public String getCrLimit() {
        return crLimit;
    }

    public void setCrLimit(String crLimit) {
        this.crLimit = crLimit;
    }

    public String getValidationToken() {
        return validationToken;
    }

    public void setValidationToken(String validationToken) {
        this.validationToken = validationToken;
    }

    @Override
    public String toString() {
        return "CheckCardUpgradeStatusResponseBody{" +
                "isCardCreated=" + isCardCreated +
                ", failureReason='" + failureReason + '\'' +
                ", cardSerNo='" + cardSerNo + '\'' +
                ", crLimit='" + crLimit + '\'' +
                ", validationToken='" + validationToken + '\'' +
                '}';
    }
}
