package com.flipkart.fintech.pandora.service.external.dataModels.axisUserOnboarding.response.etbCcResponseBody;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 * @since 23/05/19.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProcessUpgradeCardResponseBody {
    @JsonProperty("isCardCreated")
    private boolean isCardCreated;

    @JsonProperty("cardNumber")
    private String cardNumber;

    @JsonProperty("cardSerNo")
    private String cardSerNo;

    @JsonProperty("crLimit")
    private String crLimit;

    @JsonProperty("validationToken")
    private String validationToken;

    public boolean getIsCardCreated() {
        return isCardCreated;
    }

    public void setIsCardCreated(boolean isCardCreated) {
        this.isCardCreated = isCardCreated;
    }

    public String getCardNumber() {
        return cardNumber;
    }

    public void setCardNumber(String cardNumber) {
        this.cardNumber = cardNumber;
    }

    public String getValidationToken() {
        return validationToken;
    }

    public void setValidationToken(String validationToken) {
        this.validationToken = validationToken;
    }

    public String getCardSerNo() {
        return cardSerNo;
    }

    public void setCardSerNo(String cardSerNo) {
        this.cardSerNo = cardSerNo;
    }

    public String getCrLimit() {
        return crLimit;
    }

    public void setCrLimit(String crLimit) {
        this.crLimit = crLimit;
    }

    @Override
    public String toString() {
        return "ProcessUpgradeCardResponseBody{" +
                "isCardCreated=" + isCardCreated +
                ", cardSerNo='" + cardSerNo + '\'' +
                ", crLimit='" + crLimit + '\'' +
                ", validationToken='" + validationToken + '\'' +
                '}';
    }
}
