package com.flipkart.fintech.pandora.service.external.externalClients;

import org.glassfish.jersey.client.ClientProperties;
import org.glassfish.jersey.logging.LoggingFeature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.inject.Inject;
import javax.ws.rs.client.Client;
import javax.ws.rs.client.ClientBuilder;
import javax.ws.rs.client.Entity;
import javax.ws.rs.client.WebTarget;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.MultivaluedMap;
import javax.ws.rs.core.Response;
import java.util.logging.Level;

public abstract class AbstractClient {

    private final Client client;
    private final WebTarget webTarget;
    private static final Logger LOGGER = LoggerFactory.getLogger(AbstractClient.class);

    @Inject
    public AbstractClient(String endpoint) {

        this.client = ClientBuilder.newClient();
        this.client.property(ClientProperties.CONNECT_TIMEOUT, 10000);
        this.client.property(ClientProperties.READ_TIMEOUT, 10000);

        this.webTarget = client.target(endpoint);
    }

    public AbstractClient(Client client, String endPoint){
        this.client = client;
        this.client.property(ClientProperties.CONNECT_TIMEOUT, 10000);
        this.client.property(ClientProperties.READ_TIMEOUT, 10000);
        this.client.register(new LoggingFeature(
                java.util.logging.Logger.getLogger(AbstractClient.class.getName()), Level.INFO, null, null));

        this.webTarget = client.target(endPoint);

    }

    public Response makePostApiCall(String path, MultivaluedMap<String, Object> headers, Object request)
    {
        Response response = null;

        try
        {
            response = webTarget.path(path).request(MediaType.APPLICATION_JSON).headers(headers)
                    .post(Entity.json(request));
        }
        catch (Exception e)
        {
            LOGGER.error("Exception thrown by Jersey. ",   e);
            // Unfortunately we have to catch all Exceptions as Jersey does not throw checked exceptions
        }
        finally {
            if (response != null) {
                response.close();
            }
        }
        return response;
    }

    public Response makeGetApiCall(String path, MultivaluedMap<String, Object> headers)
    {
        Response response = null;

        try
        {
            response = webTarget.path(path).request(MediaType.APPLICATION_JSON).headers(headers).get();
            if (Response.Status.OK.getStatusCode() == response.getStatus())
            {
                return response;
            }
        }
        catch (Exception e)
        {
            if (null != response)
            {
                response.close();
            }
            // Unfortunately we have to catch all Exceptions as Jersey does not throw checked exceptions
        }
        return response;
    }

    public Response makePutApiCall(String path, MultivaluedMap<String, Object> headers, Object request)
    {
        Response response = null;

        try
        {
            response = webTarget.path(path).request(MediaType.APPLICATION_JSON).headers(headers)
                    .put(Entity.entity(request, MediaType.APPLICATION_JSON));
            if (Response.Status.OK.getStatusCode() == response.getStatus())
            {
                return response;
            }
        }
        catch (Exception e)
        {
            if (null != response)
            {
                response.close();
            }
            // Unfortunately we have to catch all Exceptions as Jersey does not throw checked exceptions
        }
        return response;
    }
}
