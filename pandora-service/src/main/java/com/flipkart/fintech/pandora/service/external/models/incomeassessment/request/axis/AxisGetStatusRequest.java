package com.flipkart.fintech.pandora.service.external.models.incomeassessment.request.axis;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pandora.service.external.models.incomeassessment.request.encrypted.GetStatusRequestBody;
import lombok.Data;

@Data
public class AxisGetStatusRequest {
    @JsonProperty("GetStatusRequest")
    private GetStatusRequestBody getStatusRequestBody;

}
