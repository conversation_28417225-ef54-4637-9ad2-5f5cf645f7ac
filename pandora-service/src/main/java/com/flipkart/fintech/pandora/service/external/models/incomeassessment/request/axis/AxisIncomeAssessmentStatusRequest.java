package com.flipkart.fintech.pandora.service.external.models.incomeassessment.request.axis;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pandora.service.external.models.incomeassessment.request.encrypted.IncomeAssessmentStatusRequestBody;
import lombok.Data;

@Data
public class AxisIncomeAssessmentStatusRequest {
    @JsonProperty("IncomeAssessmentStatusRequest")
    private IncomeAssessmentStatusRequestBody incomeAssessmentStatusRequestBody;
}
