package com.flipkart.fintech.pandora.service.external.models.incomeassessment.request.axis;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pandora.service.external.models.incomeassessment.request.encrypted.InitiateApplicationRequestBody;
import lombok.Data;

@Data
public class AxisInitiateApplicationRequest {
    @JsonProperty("InitiateApplicationRequest")
    private InitiateApplicationRequestBody initiateApplicationRequestBody;
}
