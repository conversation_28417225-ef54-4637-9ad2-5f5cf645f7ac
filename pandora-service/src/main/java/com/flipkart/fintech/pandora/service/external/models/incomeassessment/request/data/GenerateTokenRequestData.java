package com.flipkart.fintech.pandora.service.external.models.incomeassessment.request.data;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class GenerateTokenRequestData {
    @JsonProperty("applicationReferenceId")
    private String applicationReferenceId;
    @JsonProperty("productCode")
    private String productCode;
    @JsonProperty("targetApplication")
    private String targetApplication;
}
