package com.flipkart.fintech.pandora.service.external.models.incomeassessment.response.data;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class GetStatusResponseData {
    @JsonProperty("incomeAssessmentId")
    private String incomeAssessmentId;
    private String status;
}
