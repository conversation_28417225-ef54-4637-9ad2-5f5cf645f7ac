package com.flipkart.fintech.pandora.service.external.models.incomeassessment.response.encrypted;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pandora.service.external.dataModels.axisUserOnboarding.SubHeader;
import lombok.Data;

@Data
public class GenerateTokenResponseEncrypted {
    @JsonProperty("SubHeader")
    private SubHeader subHeader;
    @JsonProperty("GenerateTokenResponseBodyEncrypted")
    private String generateTokenResponseBodyEncrypted;
}
