package com.flipkart.fintech.pandora.service.hystrix.axisCbc.lifeCycleManagement;

import com.flipkart.fintech.pandora.api.model.common.STATUS;
import com.flipkart.fintech.pandora.service.core.lifeCycleManagement.AxisCardLifeCycleMangementClient;
import com.flipkart.fintech.pandora.service.external.dataModels.axisLifeCycleMangement.request.encrypted.LCMUpdateCardStatusRequest;
import com.flipkart.fintech.pandora.service.external.dataModels.axisLifeCycleMangement.response.encrypted.LCMUpdateCardStatusResponse;
import com.flipkart.fintech.pandora.service.hystrix.PandoraHystrixAbstractCommand;
import com.flipkart.fintech.pandora.service.hystrix.PandoraHystrixProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class LCMUpdateCardStatusCommand extends PandoraHystrixAbstractCommand<LCMUpdateCardStatusResponse> {
    private static final Logger log = LoggerFactory.getLogger(LCMUpdateCardStatusCommand.class);
    private AxisCardLifeCycleMangementClient axisCardLifeCycleMangementClient;
    private LCMUpdateCardStatusRequest lcmUpdateCardStatusRequest;
    private String validationToken;

    public LCMUpdateCardStatusCommand(String commandGroupKeyName, String commandKeyName,
                                      String threadPoolKeyName, PandoraHystrixProperties pandoraHystrixProperties,
                                      AxisCardLifeCycleMangementClient axisCardLifeCycleMangementClient,
                                      LCMUpdateCardStatusRequest lcmUpdateCardStatusRequest, String validationToken) {
        super(commandGroupKeyName, commandKeyName, threadPoolKeyName, pandoraHystrixProperties);
        this.axisCardLifeCycleMangementClient = axisCardLifeCycleMangementClient;
        this.lcmUpdateCardStatusRequest = lcmUpdateCardStatusRequest;
        this.validationToken = validationToken;
    }

    @Override
    protected LCMUpdateCardStatusResponse run() throws Exception {
        return axisCardLifeCycleMangementClient.blockCard(lcmUpdateCardStatusRequest,validationToken);
    }

    @Override
    protected LCMUpdateCardStatusResponse getFallback() {
        Exception errorFromThrowable = getExceptionFromThrowable(getExecutionException());
        log.error("while hystrix call, error in LCMUpdateCardStatusCommand {}, {}", errorFromThrowable.getMessage(),
                errorFromThrowable.toString());
        LCMUpdateCardStatusResponse lcmUpdateCardStatusResponse = new LCMUpdateCardStatusResponse();
        axisCardLifeCycleMangementClient.setHttpResponseCode(lcmUpdateCardStatusResponse,errorFromThrowable.getLocalizedMessage(),
                STATUS.LCM_UPGRADE_CARD_STATUS_EXCEPTION.toString());

        return lcmUpdateCardStatusResponse;
    }
}
