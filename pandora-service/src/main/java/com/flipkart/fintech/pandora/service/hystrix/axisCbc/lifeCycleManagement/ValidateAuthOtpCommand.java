package com.flipkart.fintech.pandora.service.hystrix.axisCbc.lifeCycleManagement;

import com.flipkart.fintech.pandora.api.model.common.STATUS;
import com.flipkart.fintech.pandora.service.core.lifeCycleManagement.AxisCardLifeCycleMangementClient;
import com.flipkart.fintech.pandora.service.external.dataModels.axisLifeCycleMangement.request.encrypted.LCMValidateAuthOTPRequest;
import com.flipkart.fintech.pandora.service.external.dataModels.axisLifeCycleMangement.response.encrypted.LCMValidateAuthOTPResponse;
import com.flipkart.fintech.pandora.service.hystrix.PandoraHystrixAbstractCommand;
import com.flipkart.fintech.pandora.service.hystrix.PandoraHystrixProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ValidateAuthOtpCommand extends PandoraHystrixAbstractCommand<LCMValidateAuthOTPResponse> {

    private static final Logger log = LoggerFactory.getLogger(ValidateAuthOtpCommand.class);
    private AxisCardLifeCycleMangementClient axisCardLifeCycleMangementClient;
    private LCMValidateAuthOTPRequest lcmValidateAuthOTPRequest;
    private String validationToken;

    public ValidateAuthOtpCommand(String commandGroupKeyName, String commandKeyName, String threadPoolKeyName,
                                  PandoraHystrixProperties pandoraHystrixProperties, LCMValidateAuthOTPRequest lcmValidateAuthOTPRequest,
                                  AxisCardLifeCycleMangementClient axisCardLifeCycleMangementClient, String validationToken) {
        super(commandGroupKeyName, commandKeyName, threadPoolKeyName, pandoraHystrixProperties);
        this.lcmValidateAuthOTPRequest = lcmValidateAuthOTPRequest;
        this.axisCardLifeCycleMangementClient = axisCardLifeCycleMangementClient;
        this.validationToken = validationToken;
    }

    @Override
    protected LCMValidateAuthOTPResponse run() throws Exception {
        return axisCardLifeCycleMangementClient.validateAuthOtp(lcmValidateAuthOTPRequest, validationToken);
    }

    @Override
    protected LCMValidateAuthOTPResponse getFallback() {
        Exception errorFromThrowable = getExceptionFromThrowable(getExecutionException());
        log.error("while hystrix call, error in ValidateAuthOtpCommand {}, {}", errorFromThrowable.getMessage(),
                errorFromThrowable.toString());
        LCMValidateAuthOTPResponse lcmValidateAuthOTPResponse = new LCMValidateAuthOTPResponse();
        axisCardLifeCycleMangementClient.setHttpResponseCode(lcmValidateAuthOTPResponse,errorFromThrowable.getLocalizedMessage(),
                STATUS.LCM_VALIDATE_AUTH_OTP_EXCEPTION.toString());

        return lcmValidateAuthOTPResponse;
    }
}
