package com.flipkart.fintech.pandora.service.hystrix.axisCbc.userOnboarding;

import com.flipkart.fintech.pandora.api.model.common.STATUS;
import com.flipkart.fintech.pandora.service.core.UserOnboarding.AxisUserOnboardingLenderClientService;
import com.flipkart.fintech.pandora.service.external.dataModels.axisUserOnboarding.request.encrypted.CheckCardUpgradeStatusRequest;
import com.flipkart.fintech.pandora.service.external.dataModels.axisUserOnboarding.response.encrypted.CheckCardUpgradeStatusResponse;
import com.flipkart.fintech.pandora.service.hystrix.PandoraHystrixAbstractCommand;
import com.flipkart.fintech.pandora.service.hystrix.PandoraHystrixProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class CheckCardUpgradeStatusCommand extends PandoraHystrixAbstractCommand<CheckCardUpgradeStatusResponse> {

    private static final Logger log = LoggerFactory.getLogger(CheckCardUpgradeStatusCommand.class);

    private CheckCardUpgradeStatusRequest statusRequest;
    private AxisUserOnboardingLenderClientService axisUserOnboardingLenderClientService;
    private String validationToken;

    public CheckCardUpgradeStatusCommand(String commandGroupKeyName, String commandKeyName, String threadPoolKeyName,
                                         PandoraHystrixProperties pandoraHystrixProperties,
                                         CheckCardUpgradeStatusRequest statusRequest,
                                         AxisUserOnboardingLenderClientService axisUserOnboardingLenderClientService,
                                         String validationToken) {
        super(commandGroupKeyName, commandKeyName, threadPoolKeyName, pandoraHystrixProperties);
        this.statusRequest = statusRequest;
        this.axisUserOnboardingLenderClientService = axisUserOnboardingLenderClientService;
        this.validationToken = validationToken;
    }

    @Override
    protected CheckCardUpgradeStatusResponse run() throws Exception {
        return axisUserOnboardingLenderClientService.checkCardUpgradeStatus(statusRequest, validationToken);
    }

    @Override
    protected CheckCardUpgradeStatusResponse getFallback() {
        Exception errorFromThrowable = getExceptionFromThrowable(getExecutionException());
        log.error("while hystrix call, error in check card upgrade status {}, {}", errorFromThrowable.getMessage(),
                errorFromThrowable.toString());
        CheckCardUpgradeStatusResponse statusResponse = new CheckCardUpgradeStatusResponse();
        axisUserOnboardingLenderClientService.setHttpResponseCode(statusResponse, errorFromThrowable.getLocalizedMessage(),
                STATUS.CARD_UPGRADE_STATUS_CHECK_EXCEPTION.name());
        return statusResponse;
    }
}

