package com.flipkart.fintech.pandora.service.hystrix.axisCbc.userOnboarding;

import com.flipkart.fintech.pandora.api.model.common.STATUS;
import com.flipkart.fintech.pandora.service.core.UserOnboarding.AxisUserOnboardingLenderClientService;
import com.flipkart.fintech.pandora.service.external.dataModels.axisUserOnboarding.request.encrypted.KYCSchedulerRequest;
import com.flipkart.fintech.pandora.service.external.dataModels.axisUserOnboarding.response.encrypted.KYCSchedulerResponse;
import com.flipkart.fintech.pandora.service.hystrix.PandoraHystrixAbstractCommand;
import com.flipkart.fintech.pandora.service.hystrix.PandoraHystrixProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class KYCSchedulerCommand extends PandoraHystrixAbstractCommand<KYCSchedulerResponse> {
    private KYCSchedulerRequest kycSchedulerRequest;
    private AxisUserOnboardingLenderClientService axisUserOnboardingLenderClientService;
    private static final Logger log = LoggerFactory.getLogger(KYCSchedulerCommand.class);

    public KYCSchedulerCommand(String commandGroupKeyName, String commandKeyName, String threadPoolKeyName,
                                         PandoraHystrixProperties pandoraHystrixProperties, KYCSchedulerRequest kycSchedulerRequest,
                                         AxisUserOnboardingLenderClientService axisUserOnboardingLenderClientService) {
        super(commandGroupKeyName, commandKeyName, threadPoolKeyName, pandoraHystrixProperties);
        this.kycSchedulerRequest = kycSchedulerRequest;
        this.axisUserOnboardingLenderClientService = axisUserOnboardingLenderClientService;
    }

    @Override
    protected KYCSchedulerResponse run() throws Exception {
        return axisUserOnboardingLenderClientService.kycSchedule(kycSchedulerRequest);
    }

    @Override
    protected KYCSchedulerResponse getFallback() {
        Exception errorFromThrowable = getExceptionFromThrowable(getExecutionException());
        log.error("while hystrix call, error in kyc scheduler response{}, {}", errorFromThrowable.getMessage(),
                errorFromThrowable.toString());
        KYCSchedulerResponse kycSchedulerResponse = new KYCSchedulerResponse();
        axisUserOnboardingLenderClientService.setHttpResponseCode(kycSchedulerResponse,errorFromThrowable.getLocalizedMessage(),
                STATUS.KYC_SCHEDULER_EXCEPTION.name());
        return kycSchedulerResponse;
    }
}
