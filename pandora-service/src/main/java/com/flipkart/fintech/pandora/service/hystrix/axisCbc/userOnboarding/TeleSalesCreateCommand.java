package com.flipkart.fintech.pandora.service.hystrix.axisCbc.userOnboarding;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.flipkart.fintech.pandora.service.external.teleSales.TeleSalesClientService;
import com.flipkart.fintech.pandora.service.external.dataModels.teleMarketing.TeleSalesRequest;
import com.flipkart.fintech.pandora.service.external.dataModels.teleMarketing.TeleSalesResponse;
import com.flipkart.fintech.pandora.service.hystrix.PandoraHystrixAbstractCommand;
import com.flipkart.fintech.pandora.service.hystrix.PandoraHystrixProperties;

public class TeleSalesCreateCommand extends PandoraHystrixAbstractCommand<TeleSalesResponse> {
	private TeleSalesClientService teleSalesClientService;
	private TeleSalesRequest teleSalesRequest;
	private static final Logger log = LoggerFactory.getLogger(TeleSalesCreateCommand.class);

	public TeleSalesCreateCommand(String commandGroupKeyName, String commandKeyName, String threadPoolKeyName,
			PandoraHystrixProperties pandoraHystrixProperties, TeleSalesRequest teleSalesRequest,
			TeleSalesClientService teleSalesClientService) {
		super(commandGroupKeyName, commandKeyName, threadPoolKeyName, pandoraHystrixProperties);
		this.teleSalesClientService = teleSalesClientService;
		this.teleSalesRequest = teleSalesRequest;
		
	}

	@Override
	protected TeleSalesResponse run() throws Exception {
		return teleSalesClientService.createTeleSalesCase(teleSalesRequest);
	}

	@Override
	protected TeleSalesResponse getFallback() {
		Exception errorFromThrowable = getExceptionFromThrowable(getExecutionException());
		log.error("while hystrix call, error in create telesales - {}, {}", errorFromThrowable.getMessage(),
				errorFromThrowable.toString());
		TeleSalesResponse teleSalesResponse = new TeleSalesResponse();
		return teleSalesResponse;
	}
	
}
