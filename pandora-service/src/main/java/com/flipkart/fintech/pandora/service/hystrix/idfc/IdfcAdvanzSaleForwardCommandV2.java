package com.flipkart.fintech.pandora.service.hystrix.idfc;

import com.flipkart.fintech.pandora.service.client.pl.IdfcBnplServiceClientV2;
import com.flipkart.fintech.pandora.service.client.pl.request.AdvanzIdfcSaleForwardRequestV2;
import com.flipkart.fintech.pandora.service.client.pl.response.IdfcSaleForwardResponse;
import com.flipkart.fintech.pandora.service.client.pl.response.IdfcSaleForwardResponseResult;
import com.flipkart.fintech.pandora.service.hystrix.PandoraHystrixAbstractCommand;
import com.flipkart.fintech.pandora.service.hystrix.PandoraHystrixProperties;

public class IdfcAdvanzSaleForwardCommandV2  extends PandoraHystrixAbstractCommand<IdfcSaleForwardResponse> {
    private IdfcBnplServiceClientV2 idfcBnplServiceClient;
    private AdvanzIdfcSaleForwardRequestV2 advanzIdfcSaleForwardRequest;
    private String accessToken;

    public IdfcAdvanzSaleForwardCommandV2(IdfcBnplServiceClientV2 idfcBnplServiceClient, AdvanzIdfcSaleForwardRequestV2 advanzIdfcSaleForwardRequest, String accessToken,
                                          String commandGroupKeyName, String commandKeyName, String threadPoolKeyName, PandoraHystrixProperties pandoraHystrixProperties) {
        super(commandGroupKeyName, commandKeyName, threadPoolKeyName, pandoraHystrixProperties);
        this.idfcBnplServiceClient = idfcBnplServiceClient;
        this.advanzIdfcSaleForwardRequest = advanzIdfcSaleForwardRequest;
        this.accessToken = accessToken;
    }

    @Override
    protected IdfcSaleForwardResponse run() throws Exception {
        return idfcBnplServiceClient.createAdvanzSaleForwardTransaction(advanzIdfcSaleForwardRequest, accessToken);
    }

    @Override
    protected IdfcSaleForwardResponse getFallback() {
        Exception errorFromThrowable = getExceptionFromThrowable(getExecutionException());
        IdfcSaleForwardResponse idfcSaleForwardResponse = new IdfcSaleForwardResponse();
        IdfcSaleForwardResponseResult idfcSaleForwardResponseResult = new IdfcSaleForwardResponseResult();
        idfcSaleForwardResponseResult.setMsg(errorFromThrowable.getMessage());
        idfcSaleForwardResponse.setResult(idfcSaleForwardResponseResult);
        return idfcSaleForwardResponse;
    }
}
