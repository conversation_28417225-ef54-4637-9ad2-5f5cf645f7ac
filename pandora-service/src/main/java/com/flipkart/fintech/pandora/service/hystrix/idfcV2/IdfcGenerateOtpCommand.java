package com.flipkart.fintech.pandora.service.hystrix.idfcV2;

import com.flipkart.fintech.pandora.service.client.pl.kyc.IdfcClientV2;
import com.flipkart.fintech.pandora.service.client.pl.request.AadhaarGenerateOTPRequest;
import com.flipkart.fintech.pandora.service.client.pl.response.AadhaarGenerateOTPResponse;
import com.flipkart.fintech.pandora.service.hystrix.PandoraHystrixAbstractCommand;
import com.flipkart.fintech.pandora.service.hystrix.PandoraHystrixProperties;
import lombok.extern.slf4j.Slf4j;


@Slf4j
public class IdfcGenerateOtpCommand extends PandoraHystrixAbstractCommand<AadhaarGenerateOTPResponse> {
        private IdfcClientV2 idfcClient;
        private String                    accessToken;
        private AadhaarGenerateOTPRequest aadhaarGenerateOTPRequest;

        public IdfcGenerateOtpCommand(IdfcClientV2 idfcClient, AadhaarGenerateOTPRequest aadhaarGenerateOTPRequest, String accessToken,
                                      String commandGroupKeyName, String commandKeyName, String threadPoolKeyName, PandoraHystrixProperties pandoraHystrixProperties) {
            super(commandGroupKeyName, commandKeyName, threadPoolKeyName, pandoraHystrixProperties);
            this.idfcClient = idfcClient;
            this.aadhaarGenerateOTPRequest = aadhaarGenerateOTPRequest;
            this.accessToken = accessToken;
        }

        @Override
        protected AadhaarGenerateOTPResponse run() throws Exception {
            return idfcClient.aadhaarGenerateOTP(aadhaarGenerateOTPRequest, accessToken);
        }
}

