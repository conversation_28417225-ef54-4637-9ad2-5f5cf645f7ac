package com.flipkart.fintech.pandora.service.hystrix.idfcV2;


import com.flipkart.fintech.pandora.service.client.pl.pennydrop.IdfcClientV2;
import com.flipkart.fintech.pandora.service.client.pl.requestV2.IdfcImpsPennyDropRequestV2;
import com.flipkart.fintech.pandora.service.client.pl.response.pennyDrop.IdfcImpsPennyDropResponse;
import com.flipkart.fintech.pandora.service.hystrix.PandoraHystrixAbstractCommand;
import com.flipkart.fintech.pandora.service.hystrix.PandoraHystrixProperties;

public class IdfcImpsPennyDropCommand extends PandoraHystrixAbstractCommand<IdfcImpsPennyDropResponse> {
        private IdfcClientV2 idfcClient;
        private IdfcImpsPennyDropRequestV2 idfcImpsPennyDropRequest;
        private String accessToken;

        public IdfcImpsPennyDropCommand(IdfcClientV2 idfcClient, IdfcImpsPennyDropRequestV2 idfcImpsPennyDropRequest, String accessToken, String commandGroupKeyName, String commandKeyName, String threadPoolKeyName, PandoraHystrixProperties pandoraHystrixProperties) {
            super(commandGroupKeyName, commandKeyName, threadPoolKeyName, pandoraHystrixProperties);
            this.idfcClient = idfcClient;
            this.idfcImpsPennyDropRequest = idfcImpsPennyDropRequest;
            this.accessToken = accessToken;
        }

        @Override
        protected IdfcImpsPennyDropResponse run() throws Exception {
            return idfcClient.impsPennyDrop(idfcImpsPennyDropRequest, accessToken);
        }
}

