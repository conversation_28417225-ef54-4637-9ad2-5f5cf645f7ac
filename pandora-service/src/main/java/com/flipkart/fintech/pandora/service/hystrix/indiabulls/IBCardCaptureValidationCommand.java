package com.flipkart.fintech.pandora.service.hystrix.indiabulls;

import com.flipkart.fintech.pandora.api.model.common.STATUS;
import com.flipkart.fintech.pandora.service.core.UserOnboarding.IBUserOnboardingLenderClientService;
import com.flipkart.fintech.pandora.service.external.dataModels.IBUserOnboardingRequest.IBCardCaptureValidationRequest;
import com.flipkart.fintech.pandora.service.external.dataModels.IBUserOnboardingResponse.IBCardCaptureValidationResponse;
import com.flipkart.fintech.pandora.service.hystrix.PandoraHystrixAbstractCommand;
import com.flipkart.fintech.pandora.service.hystrix.PandoraHystrixProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class IBCardCaptureValidationCommand extends PandoraHystrixAbstractCommand<IBCardCaptureValidationResponse> {

    private IBCardCaptureValidationRequest ibCardCaptureValidationRequest;
    private IBUserOnboardingLenderClientService ibUserOnboardingLenderClientService;
    private static final Logger log = LoggerFactory.getLogger(IBCardCaptureValidationCommand.class);

    public IBCardCaptureValidationCommand(IBCardCaptureValidationRequest ibCardCaptureValidationRequest, IBUserOnboardingLenderClientService ibUserOnboardingLenderClientService, String commandGroupKeyName, String commandKeyName, String threadPoolKeyName, PandoraHystrixProperties pandoraHystrixProperties) {
        super(commandGroupKeyName, commandKeyName, threadPoolKeyName, pandoraHystrixProperties);
        this.ibCardCaptureValidationRequest = ibCardCaptureValidationRequest;
        this.ibUserOnboardingLenderClientService = ibUserOnboardingLenderClientService;
    }

    @Override
    protected IBCardCaptureValidationResponse run() throws Exception {
        return ibUserOnboardingLenderClientService.cardCaptureValidation(ibCardCaptureValidationRequest);
    }

    @Override
    protected IBCardCaptureValidationResponse getFallback() {
        Exception errorFromThrowable = getExceptionFromThrowable(getExecutionException());
        log.error("while hystrix call, error in card capture validation {}, {}", errorFromThrowable.getMessage(), errorFromThrowable.toString());

        IBCardCaptureValidationResponse ibCardCaptureValidationResponse = new IBCardCaptureValidationResponse();
        ibCardCaptureValidationResponse.setSuccess(false);
        ibCardCaptureValidationResponse.setErrorMessage(STATUS.CARD_CAPTURE_EXCEPTION.name());
        return ibCardCaptureValidationResponse;
    }
}