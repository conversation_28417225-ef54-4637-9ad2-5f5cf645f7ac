package com.flipkart.fintech.pandora.service.hystrix.indiabulls;

import com.flipkart.fintech.pandora.api.model.common.STATUS;
import com.flipkart.fintech.pandora.service.core.UserOnboarding.IBUserOnboardingLenderClientService;
import com.flipkart.fintech.pandora.service.external.dataModels.IBUserOnboardingRequest.IBUserSignupRequest;
import com.flipkart.fintech.pandora.service.external.dataModels.IBUserOnboardingResponse.IBUserSignupResponse;
import com.flipkart.fintech.pandora.service.hystrix.PandoraHystrixAbstractCommand;
import com.flipkart.fintech.pandora.service.hystrix.PandoraHystrixProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class IBUserSignupCommand extends PandoraHystrixAbstractCommand<IBUserSignupResponse> {

    private IBUserSignupRequest ibUserSignupRequest;
    private IBUserOnboardingLenderClientService ibUserOnboardingLenderClientService;
    private static final Logger log = LoggerFactory.getLogger(IBUserSignupCommand.class);


    public IBUserSignupCommand(IBUserSignupRequest ibUserSignupRequest, IBUserOnboardingLenderClientService ibUserOnboardingLenderClientService, String commandGroupKeyName, String commandKeyName, String threadPoolKeyName, PandoraHystrixProperties pandoraHystrixProperties) {
        super(commandGroupKeyName, commandKeyName, threadPoolKeyName, pandoraHystrixProperties);
        this.ibUserSignupRequest = ibUserSignupRequest;
        this.ibUserOnboardingLenderClientService = ibUserOnboardingLenderClientService;
    }

    @Override
    protected IBUserSignupResponse run() throws Exception {
        return ibUserOnboardingLenderClientService.userSignup(ibUserSignupRequest);
    }

    @Override
    protected IBUserSignupResponse getFallback() {
        Exception errorFromThrowable = getExceptionFromThrowable(getExecutionException());
        log.error("while hystrix call, error in apply now {}, {}", errorFromThrowable.getMessage(), errorFromThrowable.toString());

        IBUserSignupResponse ibUserSignupResponse = new IBUserSignupResponse();
        ibUserSignupResponse.setSuccess(false);
        ibUserSignupResponse.setErrorMessage(STATUS.APPLY_NOW_FETCH_EXCEPTION.name());
        return ibUserSignupResponse;
    }
}
