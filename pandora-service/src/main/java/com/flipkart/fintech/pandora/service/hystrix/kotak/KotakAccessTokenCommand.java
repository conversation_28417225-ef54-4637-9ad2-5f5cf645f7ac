package com.flipkart.fintech.pandora.service.hystrix.kotak;

import com.flipkart.fintech.pandora.service.client.kotak.KotakAdvanzServiceClient;
import com.flipkart.fintech.pandora.service.client.kotak.request.AccessTokenRequest;

import com.flipkart.fintech.pandora.service.hystrix.PandoraHystrixAbstractCommand;
import com.flipkart.fintech.pandora.service.hystrix.PandoraHystrixProperties;

public class KotakAccessTokenCommand extends PandoraHystrixAbstractCommand<String> {

    private KotakAdvanzServiceClient kotakAdvanzServiceClient;
    private AccessTokenRequest accessTokenRequest;

    public KotakAccessTokenCommand(KotakAdvanzServiceClient kotakAdvanzServiceClient, AccessTokenRequest accessTokenRequest,String commandGroupKeyName, String commandKeyName, String threadPoolKeyName, PandoraHystrixProperties pandoraHystrixProperties) {
        super(commandGroupKeyName, commandKeyName, threadPoolKeyName, pandoraHystrixProperties);
        this.kotakAdvanzServiceClient = kotakAdvanzServiceClient;
        this.accessTokenRequest = accessTokenRequest;
    }

    @Override
    protected String run() throws Exception {
        return kotakAdvanzServiceClient.getAccessToken(accessTokenRequest);
    }
}
