package com.flipkart.fintech.pandora.service.hystrix.kotak;

import com.flipkart.fintech.pandora.service.client.kotak.KotakAdvanzServiceClient;
import com.flipkart.fintech.pandora.service.client.kotak.request.KotakSaleForwardRequest;
import com.flipkart.fintech.pandora.service.client.kotak.response.KotakResponse;
import com.flipkart.fintech.pandora.service.hystrix.PandoraHystrixAbstractCommand;
import com.flipkart.fintech.pandora.service.hystrix.PandoraHystrixProperties;


public class KotakPayLaterSaleForwardCommand extends PandoraHystrixAbstractCommand<KotakResponse> {
    private KotakAdvanzServiceClient kotakAdvanzServiceClient;
    private KotakSaleForwardRequest kotakSaleForwardRequest;
    private String accessToken;

    public KotakPayLaterSaleForwardCommand(KotakAdvanzServiceClient kotakAdvanzServiceClient, KotakSaleForwardRequest kotakSaleForwardRequest, String accessToken, String commandGroupKeyName, String commandKeyName, String threadPoolKeyName, PandoraHystrixProperties pandoraHystrixProperties) {
        super(commandGroupKeyName, commandKeyName, threadPoolKeyName, pandoraHystrixProperties);
        this.kotakAdvanzServiceClient = kotakAdvanzServiceClient;
        this.kotakSaleForwardRequest = kotakSaleForwardRequest;
        this.accessToken = accessToken;
    }

    @Override
    protected KotakResponse run() throws Exception {
        return kotakAdvanzServiceClient.createPayLaterSaleForwardTransaction(kotakSaleForwardRequest, accessToken);
    }

}
