package com.flipkart.fintech.pandora.service.resources.cbc;

import com.codahale.metrics.annotation.ExceptionMetered;
import com.codahale.metrics.annotation.Timed;
import com.flipkart.fintech.pandora.api.model.cbc.request.axis.*;
import com.flipkart.fintech.pandora.api.model.cbc.request.common.ValidateDetailsRequest;
import com.flipkart.fintech.pandora.api.model.cbc.response.PandoraResponseWrapper;
import com.flipkart.fintech.pandora.api.model.cbc.response.axis.*;
import com.flipkart.fintech.pandora.api.model.cbc.response.common.ValidateDetailsResponse;
import com.flipkart.fintech.pandora.api.model.cbc.response.common.WebviewDetailsResponse;
import com.flipkart.fintech.pandora.service.core.cbc.OnboardingService;
import com.flipkart.fintech.pandora.service.plugins.Controller;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpStatus;

import javax.inject.Inject;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;

@Path("/sm-cbc/{financialProvider}")
@Produces(MediaType.APPLICATION_JSON)
@Slf4j
@Controller
public class AxisUserOnboardingResource {
    private final OnboardingService onboardingService;
    @Inject
    public AxisUserOnboardingResource(OnboardingService onboardingService) {
        this.onboardingService = onboardingService;
    }

    @Path("/application/status")
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "API for fetching status of the application")
    @ApiResponses(value = {
            @ApiResponse(code = HttpStatus.SC_OK, message = "SUCCESS"),
            @ApiResponse(code = HttpStatus.SC_INTERNAL_SERVER_ERROR, message = "INTERNAL_ERROR")
    })
    @Timed
    @ExceptionMetered
    public ApplicationStatusResponse getApplicationStatus(@Valid @ApiParam ApplicationStatusRequest request,
                                                          @Valid @PathParam("financialProvider") @NotNull String financialProvider) throws Exception {
        log.info("POST API: Request for getApplicationStatus : {} for Financial Provider {}", request,financialProvider);
        return onboardingService.getApplicationStatus(financialProvider, request);
    }

    @Path("/user-details")
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "API for fetching user details")
    @ApiResponses(value = {
            @ApiResponse(code = HttpStatus.SC_OK, message = "SUCCESS"),
            @ApiResponse(code = HttpStatus.SC_INTERNAL_SERVER_ERROR, message = "INTERNAL_ERROR")
    })
    @Timed
    @ExceptionMetered
    public UserDetailsResponse getUserDetails(@Valid @ApiParam UserDetailsRequest request,
                                              @Valid @PathParam("financialProvider") @NotNull String financialProvider) throws Exception {
        log.info("POST API: Request for getUserDetails : {} for Financial Provider {}", request,financialProvider);
        return onboardingService.getUserDetail(financialProvider, request);
    }

    @Path("/offer-details")
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "API for fetching  customer type as well as real time offer eligibility for preapproved base.")
    @ApiResponses(value = {
            @ApiResponse(code = HttpStatus.SC_OK, message = "SUCCESS"),
            @ApiResponse(code = HttpStatus.SC_INTERNAL_SERVER_ERROR, message = "INTERNAL_ERROR")
    })
    @Timed
    @ExceptionMetered
    public OfferDetailsResponse getOfferDetails(@PathParam("financialProvider") String financialProvider,
                                                            @Valid OfferDetailsRequest request) throws Exception {
        log.info("POST API: Request for getOfferDetails : {} for Financial Provider {}", request,financialProvider);
        return onboardingService.getOfferDetails(financialProvider, request);
    }

    @Path("/masked-card-details")
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "API for fetching card details")
    @ApiResponses(value = {
            @ApiResponse(code = HttpStatus.SC_OK, message = "SUCCESS"),
            @ApiResponse(code = HttpStatus.SC_INTERNAL_SERVER_ERROR, message = "INTERNAL_ERROR")
    }) 
    @Timed
    @ExceptionMetered
    public CardDetailsResponse getMaskedCardDetails(@Valid @ApiParam CardDetailsRequest request,
                                                    @Valid @PathParam("financialProvider") @NotNull String financialProvider) throws Exception {
        log.info("POST API: Request for getMaskedCardDetails : {} for Financial Provider {}", request,financialProvider);
        return onboardingService.getMaskedCardDetails(financialProvider, request);
    }

    @Path("/initiate-challenge")
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "Initiates request to send OTP on customer mobile.")
    @ApiResponses(value = {
            @ApiResponse(code = HttpStatus.SC_OK, message = "SUCCESS"),
            @ApiResponse(code = HttpStatus.SC_INTERNAL_SERVER_ERROR, message = "INTERNAL_ERROR")
    })
    @Timed
    @ExceptionMetered
    public InitiateChallengeResponse initiateChallenge(@PathParam("financialProvider") String financialProvider,
                                                       @Valid InitiateChallengeRequest request) throws Exception {
        log.info("POST API: Request for initiateChallenge : {} for Financial Provider {}", request,financialProvider);
        return onboardingService.initiateChallenge(financialProvider, request);
    }

    @Path("/application/submit/etb")
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "API for submitting ETB application")
    @ApiResponses(value = {
            @ApiResponse(code = HttpStatus.SC_OK, message = "SUCCESS"),
            @ApiResponse(code = HttpStatus.SC_INTERNAL_SERVER_ERROR, message = "INTERNAL_ERROR")
    })
    @Timed
    @ExceptionMetered
    public SubmitApplicationResponse submitETBApplication(@Valid @ApiParam ETBSubmitApplicationRequest request,
                                                          @Valid @PathParam("financialProvider") @NotNull String financialProvider) throws Exception {
        log.info("POST API: Request for submitETBApplication : {} for Financial Provider {}", request,financialProvider);
        return onboardingService.submitETBApplication(financialProvider, request);
    }
    @Path("/application/submit/etc")
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "API for submitting ETC application")
    @ApiResponses(value = {
            @ApiResponse(code = HttpStatus.SC_OK, message = "SUCCESS"),
            @ApiResponse(code = HttpStatus.SC_INTERNAL_SERVER_ERROR, message = "INTERNAL_ERROR")
    })
    @Timed
    @ExceptionMetered
    public SubmitApplicationResponse submitETCApplication(@Valid @ApiParam ETCSubmitApplicationRequest request,
                                                          @Valid @PathParam("financialProvider") @NotNull String financialProvider) throws Exception {
        log.info("POST API: Request for submitETCApplication : {} for Financial Provider {}", request,financialProvider);
        return onboardingService.submitETCApplication(financialProvider, request);
    }

    @Path("/application/submit/ntb")
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "API for submitting NTB application")
    @ApiResponses(value = {
            @ApiResponse(code = HttpStatus.SC_OK, message = "SUCCESS"),
            @ApiResponse(code = HttpStatus.SC_INTERNAL_SERVER_ERROR, message = "INTERNAL_ERROR")
    })
    @Timed
    @ExceptionMetered
    public SubmitApplicationResponse submitNTBApplication(@Valid @ApiParam NTBSubmitApplicationRequest request,
                                                          @Valid @PathParam("financialProvider") @NotNull String financialProvider) throws Exception {
        log.info("POST API: Request for submitNTBApplication : {} for Financial Provider {}", request,financialProvider);
        return onboardingService.submitNTBApplication(financialProvider, request);
    }

    @Path("/account-details")
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "API for getting account details")
    @ApiResponses(value = {
            @ApiResponse(code = HttpStatus.SC_OK, message = "SUCCESS"),
            @ApiResponse(code = HttpStatus.SC_INTERNAL_SERVER_ERROR, message = "INTERNAL_ERROR")
    })
    @Timed
    @ExceptionMetered
    public AccountDetailsResponse getAccountDetails(@Valid @ApiParam AccountDetailsRequest request,
                                                    @Valid @PathParam("financialProvider") @NotNull String financialProvider) throws Exception {
        log.info("POST API: Request for submitNTBApplication : {} for Financial Provider {}", request,financialProvider);
        return onboardingService.getAccountDetails(financialProvider, request);
    }

    @Path("/kyc/initiate")
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "API for initiating KYC")
    @ApiResponses(value = {
            @ApiResponse(code = HttpStatus.SC_OK, message = "SUCCESS"),
            @ApiResponse(code = HttpStatus.SC_INTERNAL_SERVER_ERROR, message = "INTERNAL_ERROR")
    })
    @Timed
    @ExceptionMetered
    public PandoraResponseWrapper<WebviewDetailsResponse> initiateKyc(@Valid @ApiParam InitiateKycRequest request,
                                                                     @Valid @PathParam("financialProvider") @NotNull String financialProvider) throws Exception {
        log.info("POST API: Request for submitNTBApplication : {} for Financial Provider {}", request,financialProvider);
        return onboardingService.initiateKyc(financialProvider, request);
    }

    @Path("/kyc/status")
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "API for getting the kyc status")
    @ApiResponses(value = {
            @ApiResponse(code = HttpStatus.SC_OK, message = "SUCCESS"),
            @ApiResponse(code = HttpStatus.SC_INTERNAL_SERVER_ERROR, message = "INTERNAL_ERROR")
    })
    @Timed
    @ExceptionMetered
    public KycAppStatusResponse getKycStatus(@Valid @ApiParam ApplicationStatusRequest request,
                                             @Valid @PathParam("financialProvider") @NotNull String financialProvider) throws Exception {
        log.info("POST API: Request for submitNTBApplication : {} for Financial Provider {}", request,financialProvider);
        return onboardingService.getKycStatus(financialProvider, request);
    }

    @Path("/kyc/pan-ckyc-details")
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "API for getting ckyc details and validating pan")
    @ApiResponses(value = {
            @ApiResponse(code = HttpStatus.SC_OK, message = "SUCCESS"),
            @ApiResponse(code = HttpStatus.SC_INTERNAL_SERVER_ERROR, message = "INTERNAL_ERROR")
    })
    @Timed
    @ExceptionMetered
    public PanCkycDetailsResponse panValidationCkycDetails(@Valid @ApiParam PanCkycDetailsRequest request,
                                                          @Valid @PathParam("financialProvider") @NotNull String financialProvider) throws Exception {
        log.info("POST API: Request for submitNTBApplication : {} for Financial Provider {}", request,financialProvider);
        return onboardingService.panCkycDetails(financialProvider, request);
    }

    @Path("/validate-details")
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "API for getting ckyc details and validating pan")
    @ApiResponses(value = {
            @ApiResponse(code = HttpStatus.SC_OK, message = "SUCCESS"),
            @ApiResponse(code = HttpStatus.SC_INTERNAL_SERVER_ERROR, message = "INTERNAL_ERROR")
    })
    @Timed
    @ExceptionMetered
    public PandoraResponseWrapper<ValidateDetailsResponse> validateDetails(@Valid @ApiParam ValidateDetailsRequest request,
                                                                           @Valid @PathParam("financialProvider") @NotNull String financialProvider) throws Exception {
        log.debug("POST API: Request for fields validation : {} for Financial Provider {}", request, financialProvider);
        return onboardingService.validateDetails(financialProvider, request);
    }

}
