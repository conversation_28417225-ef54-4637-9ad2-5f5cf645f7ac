package com.flipkart.fintech.pandora.service.resources.cbc;

import com.codahale.metrics.annotation.ExceptionMetered;
import com.codahale.metrics.annotation.Timed;
import com.flipkart.fintech.pandora.api.model.response.cbc.upswing.UpswingAccountDetails;
import com.flipkart.fintech.pandora.service.core.cbc.manager.UpswingAccountDetailsManager;
import com.flipkart.fintech.pandora.service.plugins.Controller;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpStatus;

import javax.inject.Inject;
import javax.validation.constraints.NotNull;
import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.util.concurrent.ExecutionException;

@Slf4j
@Controller
@Api(value = "/upswing/account-details")
@Path("/v1/upswing/account-details")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public class UpswingAccountDetailsResource {

    private final UpswingAccountDetailsManager upswingAccountDetailsManager;

    @Inject
    public UpswingAccountDetailsResource(UpswingAccountDetailsManager upswingAccountDetailsManager) {
        this.upswingAccountDetailsManager = upswingAccountDetailsManager;
    }

    @GET
    @Path("/{userId}")
    @ApiOperation(value = "To fetch the Upswing Account status details")
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiResponses(value = {
            @ApiResponse(code = HttpStatus.SC_OK, message = "SUCCESS"),
            @ApiResponse(code = HttpStatus.SC_INTERNAL_SERVER_ERROR, message = "INTERNAL_ERROR")
    })
    @Timed
    @ExceptionMetered
    public UpswingAccountDetails getUpswingAccountDetailsResponse(@NotNull @PathParam(value = "userId") String userId) throws ExecutionException {

        log.info("Fetch Upswing Application Details request received for userId: {}", userId);
        UpswingAccountDetails accountDetailsResponse = upswingAccountDetailsManager.getAccountDetails(userId);
        log.info("Upswing Application Details response for userId: {} is: {}", userId, accountDetailsResponse);
        return accountDetailsResponse;
    }
}
