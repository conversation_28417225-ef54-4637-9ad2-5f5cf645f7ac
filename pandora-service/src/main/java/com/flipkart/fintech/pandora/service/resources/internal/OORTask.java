package com.flipkart.fintech.pandora.service.resources.internal;

import com.codahale.metrics.annotation.ExceptionMetered;
import com.codahale.metrics.annotation.Timed;
import com.flipkart.fintech.pandora.service.core.health.RotationManager;
import com.google.inject.Inject;
import io.dropwizard.servlets.tasks.Task;

import java.io.PrintWriter;
import java.util.Map;
import java.util.List;

/**
 * Created by aniruddha.sharma on 07/09/17.
 */
@Timed
@ExceptionMetered
public class OORTask extends Task {

    private final RotationManager rotationManager;

    @Inject
    public OORTask(RotationManager rotationManager) {
        super("oor");
        this.rotationManager = rotationManager;
    }

    @Override
    public void execute(Map<String, List<String>> immutableMultimap, PrintWriter printWriter) throws Exception {
        rotationManager.oor();
    }
}
