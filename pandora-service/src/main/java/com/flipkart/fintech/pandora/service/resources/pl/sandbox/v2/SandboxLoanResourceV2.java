package com.flipkart.fintech.pandora.service.resources.pl.sandbox.v2;

import calm.client.shade.com.nimbusds.jose.JOSEException;
import com.codahale.metrics.MetricRegistry;
import com.codahale.metrics.Timer;
import com.codahale.metrics.annotation.ExceptionMetered;
import com.codahale.metrics.annotation.Timed;
import com.flipkart.fintech.exception.ServiceErrorResponse;
import com.flipkart.fintech.exception.ServiceException;
import com.flipkart.fintech.pandora.api.model.pl.sandbox.response.AccountAggregatorResponse;
import com.flipkart.fintech.pandora.api.model.request.plOnboarding.sandbox.v2.GetApplicationStatusRequest;
import com.flipkart.fintech.pandora.api.model.request.plOnboarding.sandbox.v2.AccountAggregatorRequest;
import com.flipkart.fintech.pandora.api.model.request.plOnboarding.sandbox.v2.CreateApplicationRequest;
import com.flipkart.fintech.pandora.api.model.request.plOnboarding.sandbox.v2.OfferRequest;
import com.flipkart.fintech.pandora.api.model.request.plOnboarding.sandbox.v2.SubmitOfferRequest;
import com.flipkart.fintech.pandora.api.model.response.sandbox.v2.GetApplicationStatusResponse;
import com.flipkart.fintech.pandora.service.application.PandoraMetricRegistry;
import com.flipkart.fintech.pandora.service.client.exceptions.LenderException;
import com.flipkart.fintech.pandora.service.client.plOnboarding.exceptions.InvalidJweSignatureException;
import com.flipkart.fintech.pandora.service.client.sandbox.v2.response.*;
import com.flipkart.fintech.pandora.service.core.plUserOnboarding.PlConstants;
import com.flipkart.fintech.pandora.service.core.plUserOnboarding.SandboxPlManager;
import com.flipkart.fintech.pandora.service.core.plUserOnboarding.exceptions.PlOnboardingInvalidParamException;
import com.flipkart.fintech.pandora.service.plugins.Controller;
import com.flipkart.fintech.pinaka.client.ObjectMapperUtil;
import com.flipkart.fintech.pinaka.client.PinakaClientException;
import com.google.inject.Inject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;

import javax.ws.rs.client.Entity;
import javax.ws.rs.core.Response.Status;
import org.apache.http.HttpStatus;
import org.json.JSONException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.io.IOException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.UnrecoverableKeyException;
import java.security.cert.CertificateException;
import java.text.ParseException;

/**
 * <AUTHOR>
 * @date 14/02/24
 */
@Produces(MediaType.APPLICATION_JSON)
@Path("/loan/SME/V2")
@Api(value = "/Pl")
@Controller
public class SandboxLoanResourceV2 {

    private static final Logger logger = LoggerFactory.getLogger(SandboxLoanResourceV2.class);
    private final SandboxPlManager sandboxPlManager;
    private final MetricRegistry metricRegistry;


    @Inject
    public SandboxLoanResourceV2(SandboxPlManager sandboxPlManager){
        this.sandboxPlManager = sandboxPlManager;
        this.metricRegistry = PandoraMetricRegistry.getMetricRegistry();
    }

    @Path("/createApplication")
    @POST
    @ApiOperation(value = "Create application at lender")
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiResponses(value = {
            @ApiResponse(code = HttpStatus.SC_OK, message = "SUCCESS"),
            @ApiResponse(code = HttpStatus.SC_INTERNAL_SERVER_ERROR, message = "INTERNAL_ERROR")
    })
    @Timed
    @ExceptionMetered
    public Response createLoanApplicationAtLender(@Valid @NotNull CreateApplicationRequest request,
                                                  @HeaderParam(PlConstants.X_REQUEST_ID) @Valid @NotNull String requestId) throws LenderException {
        CreateApplicationResponse response;
        try (Timer.Context timer = metricRegistry
            .timer("SandboxLoanResourceV2_createLoanApplicationAtLender_".concat(request.getLender())).time()) {

            response = sandboxPlManager.getCreateApplicationResponse(request, requestId);
        } catch (LenderException e) {
            String logMsg = String.format(PlConstants.PL_RESOURCE_LOG_FORMAT_V2, request.getLender(), PlConstants.LSP_ID + request.getLspApplicationId(), PlConstants.CREATE_APPLICATION_AT_LENDER, e.getMessage());
            logger.error("Lender exception: {}", logMsg);
            metricRegistry.meter(MetricRegistry.name(SandboxLoanResourceV2.class,"CREATE_APPLICATION_EXCEPTION", request.getLender())).mark();
            throw e;
        }
        catch (Exception e) {
            String logMsg = String.format(PlConstants.PL_RESOURCE_LOG_FORMAT_V2, request.getLender(), PlConstants.LSP_ID + request.getLspApplicationId(), PlConstants.CREATE_APPLICATION_AT_LENDER, e.getMessage());
            logger.error(logMsg);
            metricRegistry.meter(MetricRegistry.name(SandboxLoanResourceV2.class,"CREATE_APPLICATION_EXCEPTION", request.getLender())).mark();
            throw new ServiceException(new ServiceErrorResponse(
                    Response.Status.INTERNAL_SERVER_ERROR, Response.Status.INTERNAL_SERVER_ERROR.getReasonPhrase(), PlConstants.LSP_ID + request.getLspApplicationId() + " " + e.getMessage()));
        }
        return Response.status(Response.Status.CREATED).entity(response).build();
    }

    @Path("/generateOffer")
    @POST
    @ApiOperation(value = "Generate Offer at lender")
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiResponses(value = {
            @ApiResponse(code = HttpStatus.SC_OK, message = "SUCCESS"),
            @ApiResponse(code = HttpStatus.SC_INTERNAL_SERVER_ERROR, message = "INTERNAL_ERROR")
    })
    @Timed
    @ExceptionMetered
    public Response generateOfferAtLender(@Valid @NotNull OfferRequest request,
                                                  @HeaderParam(PlConstants.X_REQUEST_ID) @Valid @NotNull String requestId){
        GenerateOfferResponse response;
        try (Timer.Context timer = metricRegistry
            .timer("SandboxLoanResourceV2_generateOfferAtLender_".concat(request.getLender())).time()) {
            response = sandboxPlManager.getGenerateOfferResponse(request, requestId);
            logger.info(ObjectMapperUtil.get().writeValueAsString(response));
        } catch (PlOnboardingInvalidParamException e) {
            String logMsg = String.format(PlConstants.PL_RESOURCE_LOG_FORMAT_V2, request.getLender(), PlConstants.LSP_ID + request.getLspApplicationId(), PlConstants.GENERATE_OFFER_AT_LENDER, e.getMessage());
            logger.error(logMsg);
            metricRegistry.meter(MetricRegistry.name(SandboxLoanResourceV2.class,"GENERATE_OFFER_LENDER", request.getLender())).mark();
            throw new ServiceException(new ServiceErrorResponse(
                    Response.Status.BAD_REQUEST, Response.Status.BAD_REQUEST.getReasonPhrase(), PlConstants.LSP_ID + request.getLspApplicationId() + " " + e.getMessage()));
        } catch (Exception e) {
            String logMsg = String.format(PlConstants.PL_RESOURCE_LOG_FORMAT_V2, request.getLender(), PlConstants.LSP_ID + request.getLspApplicationId(), PlConstants.GENERATE_OFFER_AT_LENDER, e.getMessage());
            logger.error(logMsg);
            metricRegistry.meter(MetricRegistry.name(SandboxLoanResourceV2.class,"GENERATE_OFFER_LENDER", request.getLender())).mark();
            throw new ServiceException(new ServiceErrorResponse(
                    Response.Status.INTERNAL_SERVER_ERROR, Response.Status.INTERNAL_SERVER_ERROR.getReasonPhrase(), PlConstants.LSP_ID + request.getLspApplicationId() + " " + e.getMessage()));
        }
        return Response.status(Response.Status.CREATED).entity(response).build();
    }

    @Path("/getOffer")
    @POST
    @ApiOperation(value = "Get Offer at lender")
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiResponses(value = {
            @ApiResponse(code = HttpStatus.SC_OK, message = "SUCCESS"),
            @ApiResponse(code = HttpStatus.SC_INTERNAL_SERVER_ERROR, message = "INTERNAL_ERROR")
    })
    @Timed
    @ExceptionMetered
    public Response getOfferAtLender(@Valid @NotNull OfferRequest request,
                                          @HeaderParam(PlConstants.X_REQUEST_ID) @Valid @NotNull String requestId){
        GetOfferResponse response;
        try (Timer.Context timer = metricRegistry
            .timer("SandboxLoanResourceV2_getOfferAtLender_".concat(request.getLender())).time()) {
            response = sandboxPlManager.getGetOfferResponse(request, requestId);
            logger.info(ObjectMapperUtil.get().writeValueAsString(response));
        } catch (PlOnboardingInvalidParamException e) {
            String logMsg = String.format(PlConstants.PL_RESOURCE_LOG_FORMAT_V2, request.getLender(), PlConstants.LSP_ID + request.getLspApplicationId(), PlConstants.GET_OFFER_FROM_LENDER, e.getMessage());
            logger.error(logMsg);
            metricRegistry.meter(MetricRegistry.name(SandboxLoanResourceV2.class,"GET_OFFER_LENDER", request.getLender())).mark();
            throw new ServiceException(new ServiceErrorResponse(
                    Response.Status.BAD_REQUEST, Response.Status.BAD_REQUEST.getReasonPhrase(), PlConstants.LSP_ID + request.getLspApplicationId() + " " + e.getMessage()));
        } catch (Exception e) {
            String logMsg = String.format(PlConstants.PL_RESOURCE_LOG_FORMAT_V2, request.getLender(), PlConstants.LSP_ID + request.getLspApplicationId(), PlConstants.GET_OFFER_FROM_LENDER, e.getMessage());
            logger.error(logMsg);
            metricRegistry.meter(MetricRegistry.name(SandboxLoanResourceV2.class,"GET_OFFER_LENDER", request.getLender())).mark();
            throw new ServiceException(new ServiceErrorResponse(
                    Response.Status.INTERNAL_SERVER_ERROR, Response.Status.INTERNAL_SERVER_ERROR.getReasonPhrase(), PlConstants.LSP_ID + request.getLspApplicationId() + " " + e.getMessage()));
        }
        return Response.status(Response.Status.CREATED).entity(response).build();
    }


    @Path("/submitOffer")
    @POST
    @ApiOperation(value = "Submit Offer at lender")
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiResponses(value = {
            @ApiResponse(code = HttpStatus.SC_OK, message = "SUCCESS"),
            @ApiResponse(code = HttpStatus.SC_INTERNAL_SERVER_ERROR, message = "INTERNAL_ERROR")
    })
    @Timed
    @ExceptionMetered
    public Response submitOfferToLender(@Valid @NotNull SubmitOfferRequest request,
                                     @HeaderParam(PlConstants.X_REQUEST_ID) @Valid @NotNull String requestId){
        SubmitOfferResponse response;
        try (Timer.Context timer = metricRegistry
            .timer("SandboxLoanResourceV2_submitOfferToLender_".concat(request.getLender())).time()) {
            response = sandboxPlManager.getSubmitOfferResponse(request, requestId);
            logger.info(ObjectMapperUtil.get().writeValueAsString(response));
        } catch (PlOnboardingInvalidParamException e) {
            String logMsg = String.format(PlConstants.PL_RESOURCE_LOG_FORMAT, PlConstants.LSP_ID + request.getLspApplicationId(), PlConstants.SUBMIT_OFFER_TO_LENDER, e.getMessage());
            logger.error(logMsg);
            metricRegistry.meter(MetricRegistry.name(SandboxLoanResourceV2.class,"SUBMIT_OFFER_LENDER", request.getLender())).mark();
            throw new ServiceException(new ServiceErrorResponse(
                    Response.Status.BAD_REQUEST, Response.Status.BAD_REQUEST.getReasonPhrase(), PlConstants.LSP_ID + request.getLspApplicationId() + " " + e.getMessage()));
        } catch (Exception e) {
            String logMsg = String.format(PlConstants.PL_RESOURCE_LOG_FORMAT, PlConstants.LSP_ID + request.getLspApplicationId(), PlConstants.SUBMIT_OFFER_TO_LENDER, e.getMessage());
            logger.error(logMsg);
            metricRegistry.meter(MetricRegistry.name(SandboxLoanResourceV2.class,"SUBMIT_OFFER_LENDER", request.getLender())).mark();
            throw new ServiceException(new ServiceErrorResponse(
                    Response.Status.INTERNAL_SERVER_ERROR, Response.Status.INTERNAL_SERVER_ERROR.getReasonPhrase(), PlConstants.LSP_ID + request.getLspApplicationId() + " " + e.getMessage()));
        }
        return Response.status(Response.Status.CREATED).entity(response).build();
    }

    @Path("/getApplication")
    @POST
    @ApiOperation(value = "Get Application")
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiResponses(value = {
            @ApiResponse(code = HttpStatus.SC_OK, message = "SUCCESS"),
            @ApiResponse(code = HttpStatus.SC_INTERNAL_SERVER_ERROR, message = "INTERNAL_ERROR")
    })
    @Timed
    @ExceptionMetered
    public Response getApplication(@Valid @NotNull GetApplicationStatusRequest request, @HeaderParam(PlConstants.X_REQUEST_ID) @Valid @NotNull String requestId){
        GetApplicationResponse response;
        try (Timer.Context timer = metricRegistry
            .timer("SandboxLoanResourceV2_getApplication_".concat(request.getLender())).time()) {
            response = sandboxPlManager.getApplicationResponse(request, requestId);
            logger.info(ObjectMapperUtil.get().writeValueAsString(response));
        } catch (PlOnboardingInvalidParamException e) {
            String logMsg = String.format(PlConstants.PL_RESOURCE_LOG_FORMAT, PlConstants.LSP_ID + request.getLenderApplicationId(), PlConstants.GET_LENDER_STATUS, e.getMessage());
            logger.error(logMsg);
            metricRegistry.meter(MetricRegistry.name(SandboxLoanResourceV2.class,"GET_APPLICATION", request.getLender())).mark();
            throw new ServiceException(new ServiceErrorResponse(
                    Response.Status.BAD_REQUEST, Response.Status.BAD_REQUEST.getReasonPhrase(), PlConstants.LSP_ID + request.getLenderApplicationId() + " " + e.getMessage()));
        } catch (Exception e) {
            String logMsg = String.format(PlConstants.PL_RESOURCE_LOG_FORMAT, PlConstants.LSP_ID + request.getLenderApplicationId(), PlConstants.GET_LENDER_STATUS, e.getMessage());
            logger.error(logMsg);
            metricRegistry.meter(MetricRegistry.name(SandboxLoanResourceV2.class,"GET_APPLICATION", request.getLender())).mark();
            throw new ServiceException(new ServiceErrorResponse(
                    Response.Status.INTERNAL_SERVER_ERROR, Response.Status.INTERNAL_SERVER_ERROR.getReasonPhrase(), PlConstants.LSP_ID + request.getLenderApplicationId() + " " + e.getMessage()));
        }
        return Response.status(Response.Status.OK).entity(response).build();
    }

    @Path("/getLenderStatus")
    @POST
    @ApiOperation(value = "Get Lender Status")
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiResponses(value = {
            @ApiResponse(code = HttpStatus.SC_OK, message = "SUCCESS"),
            @ApiResponse(code = HttpStatus.SC_INTERNAL_SERVER_ERROR, message = "INTERNAL_ERROR")
    })
    @Timed
    @ExceptionMetered
    public Response getApplicationStatus(@Valid @NotNull GetApplicationStatusRequest request, @HeaderParam(PlConstants.X_REQUEST_ID) @Valid @NotNull String requestId){
        GetApplicationStatusResponse response;
        try (Timer.Context timer = metricRegistry
            .timer("SandboxLoanResourceV2_getApplicationStatus_".concat(request.getLender())).time()) {
            response = sandboxPlManager.getApplicationStatusResponse(request, requestId);
            logger.info(ObjectMapperUtil.get().writeValueAsString(response));
        } catch (PlOnboardingInvalidParamException e) {
            String logMsg = String.format(PlConstants.PL_RESOURCE_LOG_FORMAT, PlConstants.LSP_ID + request.getLenderApplicationId(), PlConstants.GET_LENDER_STATUS, e.getMessage());
            logger.error(logMsg);
            metricRegistry.meter(MetricRegistry.name(SandboxLoanResourceV2.class,"GET_APPLICATION_STATUS", request.getLender())).mark();
            throw new ServiceException(new ServiceErrorResponse(
                    Response.Status.BAD_REQUEST, Response.Status.BAD_REQUEST.getReasonPhrase(), PlConstants.LSP_ID + request.getLenderApplicationId() + " " +request.getLender() + " " + e.getMessage()));
        } catch (Exception e) {
            String logMsg = String.format(PlConstants.PL_RESOURCE_LOG_FORMAT, PlConstants.LSP_ID + request.getLenderApplicationId(), PlConstants.GET_LENDER_STATUS, e.getMessage());
            metricRegistry.meter(MetricRegistry.name(SandboxLoanResourceV2.class,"GET_APPLICATION_STATUS", request.getLender())).mark();
            logger.error(logMsg);
            throw new ServiceException(new ServiceErrorResponse(
                    Response.Status.INTERNAL_SERVER_ERROR, Response.Status.INTERNAL_SERVER_ERROR.getReasonPhrase(), PlConstants.LSP_ID  + request.getLenderApplicationId() + " " +request.getLender()+ " " + e.getMessage()));
        }
        return Response.status(Response.Status.OK).entity(response).build();
    }

    @Path("/lsp/2.0/applicationEvent")
    @POST
    @ApiOperation(value = "Callback event from lender")
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiResponses(value = {
            @ApiResponse(code = HttpStatus.SC_OK, message = "SUCCESS"),
            @ApiResponse(code = HttpStatus.SC_INTERNAL_SERVER_ERROR, message = "INTERNAL_ERROR")
    })
    @Timed
    @ExceptionMetered
    public Response lenderCallbackStatusEventDeprecate(@NotNull String request,
                                              @HeaderParam(PlConstants.X_CLIENT_ID) @Valid @NotNull String clientId,
                                              @HeaderParam(PlConstants.X_REQUEST_ID) @Valid @NotNull String requestId){
        try {

            sandboxPlManager.consumeLenderPlatformEvent(request, clientId , requestId);

        } catch (JOSEException | ParseException | NoSuchAlgorithmException | CertificateException | KeyStoreException | IOException | UnrecoverableKeyException
                 | InvalidJweSignatureException | InternalServerErrorException | PinakaClientException |
                 JSONException e) {
            logger.error(e.getMessage(), e);
            throw new ServiceException(new ServiceErrorResponse(
                    Response.Status.INTERNAL_SERVER_ERROR, Response.Status.INTERNAL_SERVER_ERROR.getReasonPhrase(), e.getMessage()));
        }
        return Response.ok().entity(Entity.json("{}")).build();
    }

    @Path("/lsp/2.0/applicationStateEvent")
    @POST
    @ApiOperation(value = "Callback event from lender")
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiResponses(value = {
            @ApiResponse(code = HttpStatus.SC_OK, message = "SUCCESS"),
            @ApiResponse(code = HttpStatus.SC_INTERNAL_SERVER_ERROR, message = "INTERNAL_ERROR")
    })
    @Timed
    @ExceptionMetered
    public Response lenderCallbackStatusEvent(@NotNull String request,
                                              @HeaderParam(PlConstants.X_CLIENT_ID) @Valid @NotNull String clientId,
                                              @HeaderParam(PlConstants.X_REQUEST_ID) @Valid @NotNull String requestId){
        try {

            sandboxPlManager.consumeLenderPlatformEvent(request, clientId , requestId);

        } catch (JOSEException | ParseException | NoSuchAlgorithmException | CertificateException | KeyStoreException | IOException | UnrecoverableKeyException
                | InvalidJweSignatureException | InternalServerErrorException | PinakaClientException |JSONException e) {
            logger.error(e.getMessage(), e);
            throw new ServiceException(new ServiceErrorResponse(
                    Response.Status.INTERNAL_SERVER_ERROR, Response.Status.INTERNAL_SERVER_ERROR.getReasonPhrase(), e.getMessage()));
        }
        return Response.ok().entity(Entity.json("{}")).build();
    }


    @Path("/initAccountAggregator")
    @POST
    @ApiOperation(value = "Init Account Aggregator")
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiResponses(value = {
        @ApiResponse(code = HttpStatus.SC_OK, message = "SUCCESS"),
        @ApiResponse(code = HttpStatus.SC_INTERNAL_SERVER_ERROR, message = "INTERNAL_ERROR")
    })
    @Timed
    @ExceptionMetered
    public Response initAccountAggregator(@Valid @NotNull AccountAggregatorRequest request, @HeaderParam(PlConstants.X_REQUEST_ID) @Valid @NotNull String requestId){
        try (Timer.Context timer = metricRegistry
            .timer("SandboxLoanResourceV2_initAccountAggregator_".concat(request.getLender())).time()) {
          AccountAggregatorResponse accountAggregatorResponse = sandboxPlManager.initAccountAggregator(request, requestId);
          return Response.status(Status.OK).entity(accountAggregatorResponse).build();
        } catch (Exception e) {
            String logMsg = String.format(PlConstants.PL_RESOURCE_LOG_FORMAT, PlConstants.LSP_ID + request.getLspId(), PlConstants.ACCOUNT_AGGREGATOR_INIT, e.getMessage());
            logger.error(logMsg);
            metricRegistry.meter(MetricRegistry.name(SandboxLoanResourceV2.class,"INIT_ACCOUNT", request.getLender())).mark();
            throw new ServiceException(new ServiceErrorResponse(
                Response.Status.INTERNAL_SERVER_ERROR, Response.Status.INTERNAL_SERVER_ERROR.getReasonPhrase(), PlConstants.LSP_ID + request.getLspId() + " " + e.getMessage()));
        }
    }



}
