package com.flipkart.fintech.pandora.service.resources.user;

import com.flipkart.fintech.pandora.service.plugins.Controller;
import com.flipkart.fintech.user.data.client.UserDataClient;
import com.flipkart.fintech.user.data.models.AddressData;
import com.flipkart.fintech.user.data.models.UserData;
import com.flipkart.fintech.user.data.models.enums.Merchant;
import com.flipkart.fintech.user.data.models.enums.PIIDataType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import javax.inject.Inject;
import javax.validation.constraints.NotEmpty;
import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;

import java.util.Objects;

import static com.flipkart.fintech.pandora.service.utils.MetricUtils.pushMetricForSmAccountIdPresence;

@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@Slf4j
@Controller
@Path("users")
public class UserResource {

    private final UserDataClient userDataClient;

    @Inject
    public UserResource(UserDataClient userDataClient) {
        this.userDataClient = userDataClient;
    }


    @Path("/{userId}/addresses/{addressId}")
    @GET
    public AddressData fetchAddress(@PathParam("userId") @NotEmpty String userId,
                                    @PathParam("addressId") @NotEmpty String addressId,
                                    @QueryParam("smUserId") String smUserId,
                                    @QueryParam("merchant") String merchant) {
        pushMetricForSmAccountIdPresence("fetchAddress", StringUtils.isNotBlank(smUserId));
        return userDataClient.getAddressData(Merchant.getOrDefaultByValue(merchant), userId, smUserId, addressId,
                PIIDataType.PLAINTEXT);
    }

    @Path("/phone_fetch")
    @POST
    public UserData fetchPhone(@QueryParam("merchantAccountId") String merchantAccountId,
                               @QueryParam("smUserId") String smUserId,
                               @QueryParam("merchant") String merchant) {
        pushMetricForSmAccountIdPresence("fetchPhone", StringUtils.isNotBlank(smUserId));
        return userDataClient.getUserData(Merchant.getOrDefaultByValue(merchant), merchantAccountId, smUserId,
                PIIDataType.PLAINTEXT);
    }
}
