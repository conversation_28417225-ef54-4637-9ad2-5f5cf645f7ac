package com.flipkart.fintech.pandora.service.resources.verifier;

import com.flipkart.fintech.pandora.api.model.request.verification.PanNameVerificationRequest;
import com.flipkart.fintech.pandora.api.model.response.verification.PanNameVerificationResponse;
import com.flipkart.fintech.pandora.service.core.verification.PanNameVerificationService;
import com.flipkart.fintech.pandora.service.plugins.Controller;
import lombok.extern.slf4j.Slf4j;

import javax.inject.Inject;
import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
@Path("verifiers/{client}")
@Slf4j
@Controller
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public class PanNameVerificationResource {

    private final PanNameVerificationService panNameVerificationService;

    @Inject
    public PanNameVerificationResource(PanNameVerificationService panNameVerificationService){
        this.panNameVerificationService = panNameVerificationService;
    }

    @Path("/pan_name_verification")
    @POST
    public PanNameVerificationResponse panNameVerification(PanNameVerificationRequest request) {
        try {
            return panNameVerificationService.panNameVerification(request);
        } catch(Exception e) {
            log.error("Error in pan name verification", e);
            return PanNameVerificationResponse
                .builder()
                .nameMatch(false)
                .nameMatchScore(0)
                .build();
        }
    }
}
