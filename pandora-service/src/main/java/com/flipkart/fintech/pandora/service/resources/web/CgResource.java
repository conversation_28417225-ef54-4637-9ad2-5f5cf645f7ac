package com.flipkart.fintech.pandora.service.resources.web;


import com.codahale.metrics.annotation.ExceptionMetered;
import com.codahale.metrics.annotation.Timed;
import com.flipkart.fintech.pandora.api.model.request.cg.CgDispositionChange;
import com.flipkart.fintech.pandora.service.core.cg.CgManager;
import com.flipkart.fintech.pandora.service.plugins.Controller;
import io.dropwizard.hibernate.UnitOfWork;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.apache.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.inject.Inject;
import javax.validation.constraints.NotNull;
import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import static com.flipkart.fintech.pandora.service.client.utils.Constants.*;

@Produces(MediaType.APPLICATION_JSON)
@Path("/1")
@Api(value = "/cg")
@Controller
public class CgResource {

    private static final Logger logger = LoggerFactory.getLogger(CgResource.class);
    private CgManager cgManager;

    @Inject
    public CgResource(CgManager cgManager) {
        this.cgManager = cgManager;

    }

    @Path("/loan/{loan-id}")
    @POST
    @Consumes(MediaType.TEXT_PLAIN)
    @ApiOperation(value = "API for Loan create")
    @ApiResponses(value = {
            @ApiResponse(code = HttpStatus.SC_OK, message = "SUCCESS"),
            @ApiResponse(code = HttpStatus.SC_INTERNAL_SERVER_ERROR, message = "INTERNAL_ERROR")
    })
    @Timed
    @ExceptionMetered
    public Response cgCreateLoan(String payload, @NotNull @PathParam("loan-id") String loanId, @HeaderParam(AUTH_TOKEN) String authToken, @HeaderParam("base-url") String baseUrl, @QueryParam(COMPANY_ID) String companyId) {
        Response response = cgManager.createLoan(loanId, baseUrl, authToken, payload, companyId);
        return response;
    }



    @Path("/loan/{loan-id}")
    @PATCH
    @Consumes(MediaType.TEXT_PLAIN)
    @ApiOperation(value = "API for Updating CG loan")
    @ApiResponses(value = {
            @ApiResponse(code = HttpStatus.SC_OK, message = "SUCCESS"),
            @ApiResponse(code = HttpStatus.SC_INTERNAL_SERVER_ERROR, message = "INTERNAL_ERROR")
    })
    @Timed
    @ExceptionMetered
    public Response updateLoan(String payload, @NotNull @PathParam("loan-id") String loanId, @HeaderParam(AUTH_TOKEN) String authToken, @HeaderParam("base-url") String baseUrl, @QueryParam(COMPANY_ID) String companyId)
    {
        Response response = cgManager.updateLoan(loanId, baseUrl, authToken, payload, companyId);
        return response;
    }

    @Path("/payments/{loan-id}")
    @PATCH
    @Consumes(MediaType.TEXT_PLAIN)
    @ApiOperation(value = "API for Close CG loan")
    @ApiResponses(value = {
            @ApiResponse(code = HttpStatus.SC_OK, message = "SUCCESS"),
            @ApiResponse(code = HttpStatus.SC_INTERNAL_SERVER_ERROR, message = "INTERNAL_ERROR")
    })
    @Timed
    @ExceptionMetered
    public Response closeCgLoan(String payload, @NotNull @PathParam("loan-id") String loanId, @HeaderParam(AUTH_TOKEN) String authToken, @HeaderParam("base-url") String baseUrl, @QueryParam(COMPANY_ID) String companyId, @QueryParam(ALLOCATION_MONTH) String allocationMonth )  {
        Response response = cgManager.closeLoan(loanId, baseUrl, authToken, payload, companyId, allocationMonth);
        return response;
    }

    @Path("/cg/caas/dispositionChange")
    @POST
    @Timed
    @UnitOfWork
    @ExceptionMetered
    public Response processCgCaasDispositionChange(CgDispositionChange cgDispositionChange,
                                               @HeaderParam("X_CLIENT_ID") String xClientId)
    {
        logger.info("In processCgDispositionChange resource. cgDispositionChange : {}, xClientId : {}", cgDispositionChange, xClientId);
        return cgManager.processCgDispositionChange(cgDispositionChange, xClientId, CG_CAAS_DISPOSITION_PATH);
    }

    @Path("/cg/saas/dispositionChange")
    @POST
    @Timed
    @UnitOfWork
    @ExceptionMetered
    public Response processCgSaasDispositionChange(CgDispositionChange cgDispositionChange,
                                                   @HeaderParam("X_CLIENT_ID") String xClientId)
    {
        logger.info("In processCgDispositionChange resource. cgDispositionChange : {}, xClientId : {}", cgDispositionChange, xClientId);
        return cgManager.processCgDispositionChange(cgDispositionChange, xClientId, CG_SAAS_DISPOSITION_PATH);
    }

}
