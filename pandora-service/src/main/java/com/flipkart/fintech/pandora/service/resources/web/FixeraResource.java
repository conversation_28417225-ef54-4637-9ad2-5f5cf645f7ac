package com.flipkart.fintech.pandora.service.resources.web;

import com.codahale.metrics.annotation.ExceptionMetered;
import com.codahale.metrics.annotation.Timed;
import com.flipkart.fintech.pandora.api.model.cbc.response.kotak.PinServiceabilityResponse;
import com.flipkart.fintech.pandora.service.client.fixera.request.AuthCodeRequest;
import com.flipkart.fintech.pandora.service.client.fixera.request.CreateUserRequest;
import com.flipkart.fintech.pandora.service.client.fixera.request.FDJourneyRequest;
import com.flipkart.fintech.pandora.service.client.fixera.response.*;
import com.flipkart.fintech.pandora.service.core.experian.CheckBureauScoreByExperianService;
import com.flipkart.fintech.pandora.service.core.fixera.DepositAPIIntegrator;
import com.flipkart.fintech.pandora.service.core.fixera.DepositAPIIntegratorImpl;
import com.flipkart.fintech.pandora.service.plugins.Controller;
import com.flipkart.fintech.profile.api.request.BureauRequest;
import com.google.inject.Inject;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpStatus;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@Path("/fixera")
@Controller
@Slf4j
public class FixeraResource {

    private final DepositAPIIntegrator depositAPIIntegrator;
    @Inject
    public FixeraResource(DepositAPIIntegrator depositAPIIntegrator) {
        this.depositAPIIntegrator = depositAPIIntegrator;
    }

    @Path("/user/create")
    @POST
    public Response createUser(@Valid @NotNull CreateUserRequest request) {
        log.info("Creating user for request : {}", request.getPhone());
        CreateUserResponse createUserResponse = depositAPIIntegrator.createUser(request);
        return Response.status(Response.Status.OK).entity(createUserResponse).build();
    }

    @Path("/authCode/generate")
    @POST
    public Response getAuthCode(@Valid @NotNull AuthCodeRequest request) {
        log.info("fetching auth code : {}", request.getData());
        AuthCodeResponse authCodeResponse = depositAPIIntegrator.getAuthCode(request);
        return Response.status(Response.Status.OK).entity(authCodeResponse).build();
    }

    @Path("/get/journeyData")
    @POST
    public Response getJourneyData(@Valid @NotNull FDJourneyRequest journeyRequest) {
        log.info("fetching journey data : {}", journeyRequest.getFixeraUserId());
        UserFDDetails userFDDetails = depositAPIIntegrator.getFDJourney(journeyRequest.getFixeraUserId());
        return Response.status(Response.Status.OK).entity(userFDDetails).build();
    }


    @Path("/get/consolidatedPortfolioSummary")
    @GET
    public Response getConsolidatedPortfolioSummary(@QueryParam(value = "fixeraUserId") String fixeraUserId) {
        log.info("fetching consolidated portfolio summary for fixeraUserId : {}", fixeraUserId);
        ConsolidatedPortfolioSummary consolidatedPortfolioSummary = depositAPIIntegrator.getConsolidatedPortfolioSummary(fixeraUserId);
        return Response.status(Response.Status.OK).entity(consolidatedPortfolioSummary).build();
    }
}
