package com.flipkart.fintech.pandora.service.resources.web;

import com.codahale.metrics.annotation.ExceptionMetered;
import com.codahale.metrics.annotation.Timed;
import com.flipkart.fintech.pandora.api.model.common.FinancialProvider;
import com.flipkart.fintech.pandora.api.model.common.STATUS;
import com.flipkart.fintech.pandora.api.model.request.payback.PaybackForwardRequest;
import com.flipkart.fintech.pandora.api.model.request.payback.PaybackRefundRequest;
import com.flipkart.fintech.pandora.api.model.response.payback.PaybackForwardTransactionsResponse;
import com.flipkart.fintech.pandora.api.model.response.payback.PaybackRefundResponse;
import com.flipkart.fintech.pandora.service.client.kissht.KisshtServiceClientConfigEfa;
import com.flipkart.fintech.pandora.service.plugins.Controller;
import com.flipkart.fintech.pandora.service.router.PaybackManagerRouter;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.apache.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.inject.Inject;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.HeaderParam;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.util.Base64;

/**
 * Created by aniruddha.sharma on 26/12/17.
 */
@Path("/1")
@Produces(MediaType.APPLICATION_JSON)
@Api(value = "/payback", description = "Billing Service")
@Controller
public class PaybackResource {

    private static final Logger logger = LoggerFactory.getLogger(PaybackResource.class);

    private final PaybackManagerRouter paybackManagerRouter;

    private final KisshtServiceClientConfigEfa kisshtServiceClientConfigEfa;

    @Inject
    public PaybackResource(PaybackManagerRouter paybackManagerRouter, KisshtServiceClientConfigEfa
            kisshtServiceClientConfigEfa) {
        this.paybackManagerRouter = paybackManagerRouter;
        this.kisshtServiceClientConfigEfa = kisshtServiceClientConfigEfa;
    }

    @Path("/{financialProvider}/payback/forward")
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "API for forward payback transactions")
    @ApiResponses(value = {
            @ApiResponse(code = HttpStatus.SC_OK, message = "SUCCESS"),
            @ApiResponse(code = HttpStatus.SC_INTERNAL_SERVER_ERROR, message = "INTERNAL_ERROR")
    })
    @Timed
    @ExceptionMetered
    public Response getSummary(@ApiParam @NotNull PaybackForwardRequest paybackForwardRequest,
                               @Valid @PathParam("financialProvider") @NotNull FinancialProvider financialProvider) {
        logger.info("POST API: Request for /payback/forward : {} for Financial Provider {}",
                paybackForwardRequest, financialProvider);
        PaybackForwardTransactionsResponse paybackForwardTransactionsResponse = null;
        paybackForwardTransactionsResponse = paybackManagerRouter.getManager(financialProvider).paybackTransaction
                (paybackForwardRequest);
        return Response.ok(paybackForwardTransactionsResponse).build();
    }

    @Path("/{financialProvider}/payback/refund")
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "API for forward payback transactions")
    @ApiResponses(value = {
            @ApiResponse(code = HttpStatus.SC_OK, message = "SUCCESS"),
            @ApiResponse(code = HttpStatus.SC_INTERNAL_SERVER_ERROR, message = "INTERNAL_ERROR")
    })
    @Timed
    @ExceptionMetered
    public Response updateSummary(@ApiParam @NotNull PaybackRefundRequest paybackRefundRequest,
                               @Valid @PathParam("financialProvider") @NotNull FinancialProvider financialProvider,
                                  @NotNull @HeaderParam("X_CLIENT_ID") String xClientId) {
        StringBuilder stringBuilder = new StringBuilder(kisshtServiceClientConfigEfa.getReverseProxyKey());
        stringBuilder.append( ":" + stringBuilder.reverse());
        String base64encoded = Base64.getEncoder().encodeToString(stringBuilder.toString().getBytes());
        logger.info("POST API: Request for /payback/refund: {} for Financial Provider {} with X_CLIENT_ID: {}",
                paybackRefundRequest, financialProvider, xClientId);
        PaybackRefundResponse paybackRefundResponse = null;
        if(xClientId.equals(base64encoded)){
            paybackRefundResponse = paybackManagerRouter.getManager(financialProvider).refundTransaction
                    (paybackRefundRequest);
            if(STATUS.SUCCESS.equals(paybackRefundResponse.getStatus()))
                return Response.ok(paybackRefundResponse).build();
            else
                return Response.status(Response.Status.INTERNAL_SERVER_ERROR).entity(paybackRefundResponse).build();
        }
        else{
            paybackRefundResponse = new PaybackRefundResponse();
            paybackRefundResponse.setStatus(STATUS.INVALID_CLIENT_ID);
            return Response.status(Response.Status.FORBIDDEN).entity(paybackRefundResponse).build();
        }
    }
}
