package com.flipkart.fintech.pandora.service.resources.web;

import com.codahale.metrics.annotation.ExceptionMetered;
import com.codahale.metrics.annotation.Timed;
import com.flipkart.affordability.bnpl.tijori.api.enums.ProductCode;
import com.flipkart.fintech.pandora.api.model.common.FinancialProvider;
import com.flipkart.fintech.pandora.api.model.request.*;
import com.flipkart.fintech.pandora.api.model.response.*;
import com.flipkart.fintech.pandora.service.client.PandoraServiceClientException;
import com.flipkart.fintech.pandora.service.core.transaction.factory.TransactionFactory;
import com.flipkart.fintech.pandora.service.plugins.Controller;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpStatus;

import javax.inject.Inject;
import javax.validation.Valid;
import javax.ws.rs.*;

import com.flipkart.fintech.pandora.service.client.pl.response.PandoraFailureResponse;

import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

@Slf4j
@Path("/1")
@Produces(MediaType.APPLICATION_JSON)
@Api(value = "/transaction", description = "Transaction Related APIs")
@Controller
public class TransactionResource {

    private final TransactionFactory transactionFactory;

    @Inject
    public TransactionResource(TransactionFactory transactionFactory) {
        this.transactionFactory = transactionFactory;
    }

    @Path("/{financialProvider}/{product}/sale/debit")
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "API for sending sale debit transaction")
    @ApiResponses(value = {
            @ApiResponse(code = HttpStatus.SC_OK, message = "SUCCESS"),
            @ApiResponse(code = HttpStatus.SC_INTERNAL_SERVER_ERROR, message = "INTERNAL_ERROR")
    })
    @Timed
    @ExceptionMetered
    public Response processSaleDebitRequest(@HeaderParam("X-Tenant-Id") String tenantId,
                                            @PathParam("financialProvider") FinancialProvider financialProvider,
                                            @PathParam("product") ProductCode productCode,
                                            SaleForwardRequest saleForwardRequest) {
        try {
            log.info("sale forward request : {}", saleForwardRequest);
            SaleResponse saleResponse = transactionFactory.getTransactionManager(financialProvider).createSaleForwardTransaction(saleForwardRequest, productCode);
            log.info("sale forward response : {}", saleResponse);
            return Response.status(Response.Status.OK).entity(saleResponse).build();
        } catch (PandoraServiceClientException e) {
            log.error("Exception in SaleDebitRequest, downstreamRequest - {}, downstreamResponse - {}, errorMessage - {}", e.getDownstreamRequest(), e.getDownstreamResponse(), e.getMessage());
            return Response.status(e.getResponseCode()).entity(getPandoraFailureResponse(e)).build();
        }
    }


    @Path("/{financialProvider}/{product}/fee/{txnType}")
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "API for sending sale debit transaction")
    @ApiResponses(value = {
            @ApiResponse(code = HttpStatus.SC_OK, message = "SUCCESS"),
            @ApiResponse(code = HttpStatus.SC_INTERNAL_SERVER_ERROR, message = "INTERNAL_ERROR")
    })
    @Timed
    @ExceptionMetered
    public Response processFeeRequest(@HeaderParam("X-Tenant-Id") String tenantId,
                                      @PathParam("financialProvider") FinancialProvider financialProvider,
                                      @PathParam("product") ProductCode product,
                                      @PathParam("txnType") String txnType,
                                      FeeChargeRequest feeChargeRequest) {
        try {
            log.info("fee request: {}", feeChargeRequest);
            FeeChargeResponse feeChargeResponse = transactionFactory.getTransactionManagerV2(financialProvider,feeChargeRequest.getLenderCardNumber(), feeChargeRequest.getAppId(), false).createFeeChargeTransaction(feeChargeRequest, product, txnType);
            log.info("fee response: {}", feeChargeResponse);
            return Response.status(Response.Status.OK).entity(feeChargeResponse).build();
        } catch (PandoraServiceClientException e) {
            log.error("Exception in FeeDebitRequest, downstreamRequest - {}, downstreamResponse - {}, errorMessage - {}", e.getDownstreamRequest(), e.getDownstreamResponse(), e.getMessage());
            return Response.status(e.getResponseCode()).entity(getPandoraFailureResponse(e)).build();
        }
    }

    @Path("/{financialProvider}/{product}/sale/credit")
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "API for sending sale credit transaction")
    @ApiResponses(value = {
            @ApiResponse(code = HttpStatus.SC_OK, message = "SUCCESS"),
            @ApiResponse(code = HttpStatus.SC_INTERNAL_SERVER_ERROR, message = "INTERNAL_ERROR")
    })
    @Timed
    @ExceptionMetered
    public Response processSaleCreditRequest(@Valid @HeaderParam("X-Tenant-Id") String tenantId,
                                             @PathParam("financialProvider") FinancialProvider financialProvider,
                                             SaleReverseRequest saleReverseRequest,
                                             @PathParam("product") ProductCode product) {
        try {
            log.info("sale reverse request : {}", saleReverseRequest);
            SaleResponse saleResponse = transactionFactory.getTransactionManagerV2(financialProvider, saleReverseRequest.getLenderCardNumber(), saleReverseRequest.getAppId(), false).createSaleReverseTransaction(saleReverseRequest, product);
            log.info("sale reverse response : {}", saleResponse);
            return Response.status(Response.Status.OK).entity(saleResponse).build();
        } catch (PandoraServiceClientException e) {
            log.error("Exception in processSaleCreditRequest, downstreamRequest - {}, downstreamResponse - {}, errorMessage - {}", e.getDownstreamRequest(), e.getDownstreamResponse(), e.getMessage());
            return Response.status(e.getResponseCode()).entity(getPandoraFailureResponse(e)).build();
        }
    }

    private PandoraFailureResponse getPandoraFailureResponse(PandoraServiceClientException e) {
        return PandoraFailureResponse.builder()
                        .errorMessage(e.getMessage())
                        .lenderEndpoint(e.getDownstreamEndpoint()   )
                        .lenderRequest(e.getDownstreamRequest())
                        .lenderResponse(e.getDownstreamResponse())
                        .build();
    }


    @Path("/{financialProvider}/{product}/payback/credit")
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "API for sending payback debit transaction")
    @ApiResponses(value = {
            @ApiResponse(code = HttpStatus.SC_OK, message = "SUCCESS"),
            @ApiResponse(code = HttpStatus.SC_INTERNAL_SERVER_ERROR, message = "INTERNAL_ERROR")
    })
    @Timed
    @ExceptionMetered
    public Response processPaybackCreditRequest(@Valid @HeaderParam("X-Tenant-Id") String tenantId,
                                                @PathParam("financialProvider") FinancialProvider financialProvider,
                                                @PathParam("product") ProductCode product,
                                                PaybackForwardRequest paybackForwardRequest) {
        try {
            log.info("payback forward request: {}", paybackForwardRequest);
            if(paybackForwardRequest.getLenderAccountId() == null) {
                log.error("LAN is null for the customer {}", paybackForwardRequest);
                return Response.status(Response.Status.INTERNAL_SERVER_ERROR).entity(String.format("LAN is null for the customer %s ", paybackForwardRequest.getCustomerId())).build();
            }
            PaybackForwardResponse paybackForwardResponse =
                    transactionFactory.getTransactionManagerV2(financialProvider, paybackForwardRequest.getLenderCardNumber(), null, true).createPaybackForwardTransaction(paybackForwardRequest, product);
            log.info("payback forward response: {}", paybackForwardResponse);
            return Response.status(Response.Status.OK).entity(paybackForwardResponse).build();
        } catch (PandoraServiceClientException e) {
            log.error("Exception in processPaybackCreditRequest, downstreamRequest - {}, downstreamResponse - {}, errorMessage - {}", e.getDownstreamRequest(), e.getDownstreamResponse(), e.getMessage());
            return Response.status(e.getResponseCode()).entity(getPandoraFailureResponse(e)).build();
        }
    }

    @Path("/{financialProvider}/{product}/payback/debit")
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "API for sending payback credit transaction")
    @ApiResponses(value = {
            @ApiResponse(code = HttpStatus.SC_OK, message = "SUCCESS"),
            @ApiResponse(code = HttpStatus.SC_INTERNAL_SERVER_ERROR, message = "INTERNAL_ERROR")
    })
    @Timed
    @ExceptionMetered
    public Response processPaybackDebitRequest(@PathParam("financialProvider") FinancialProvider financialProvider,
                                               PaybackReverseRequest paybackReverseRequest,
                                               @PathParam("product") ProductCode product) {
        try {
            log.info("payback reverse request: {}", paybackReverseRequest);
            PaybackReverseResponse paybackReverseResponse =
                    transactionFactory.getTransactionManager(financialProvider).createPaybackReverseTransaction(paybackReverseRequest, product);
            log.info("payback reverse response: {}", paybackReverseResponse);
            return Response.status(Response.Status.OK).entity(paybackReverseResponse).build();
        } catch (UnsupportedOperationException e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR).entity("Temporarily Stopped.").build();
        } catch (PandoraServiceClientException e) {
            log.error("Exception in processPaybackDebitRequest, downstreamRequest - {}, downstreamResponse - {}, errorMessage - {}", e.getDownstreamRequest(), e.getDownstreamResponse(), e.getMessage());
            return Response.status(e.getResponseCode()).entity(getPandoraFailureResponse(e)).build();
        }
    }


    @Path("/{financialProvider}/{product}/generate/bill")
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "API for sending payback credit transaction")
    @ApiResponses(value = {
            @ApiResponse(code = HttpStatus.SC_OK, message = "SUCCESS"),
            @ApiResponse(code = HttpStatus.SC_INTERNAL_SERVER_ERROR, message = "INTERNAL_ERROR")
    })
    @Timed
    @ExceptionMetered
    public Response generateBill(@PathParam("financialProvider") FinancialProvider financialProvider,
                                               BillGenerationRequest billGenerationRequest,
                                               @PathParam("product") ProductCode product) {
        try {
            log.info("generate bill request: {}", billGenerationRequest);
            BillGenerationResponse billGenerationResponse = transactionFactory.getTransactionManager(financialProvider).billGeneration(billGenerationRequest);
            log.info("generate bill response: {}", billGenerationResponse);
            return Response.status(Response.Status.OK).entity(billGenerationResponse).build();
        } catch (UnsupportedOperationException e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR).entity("Temporarily Stopped.").build();
        } catch (PandoraServiceClientException e) {
            log.error("Exception in generateBill, downstreamRequest - {}, downstreamResponse - {}, errorMessage - {}", e.getDownstreamRequest(), e.getDownstreamResponse(), e.getMessage());
            return Response.status(e.getResponseCode()).entity(getPandoraFailureResponse(e)).build();
        }
    }

}
