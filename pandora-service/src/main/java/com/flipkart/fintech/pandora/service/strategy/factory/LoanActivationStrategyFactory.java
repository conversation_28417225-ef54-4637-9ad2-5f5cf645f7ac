package com.flipkart.fintech.pandora.service.strategy.factory;

import com.flipkart.fintech.pandora.api.model.common.FinancialProvider;
import com.flipkart.fintech.pandora.service.strategy.loan_activation.CheckEligibilityStrategy;
import com.flipkart.fintech.pandora.service.strategy.loan_activation.LoanCreationStrategy;
import com.flipkart.fintech.pandora.service.strategy.loan_activation.UnderwritingStrategy;
import com.google.inject.Inject;

import java.util.Map;

public class LoanActivationStrategyFactory {

    Map<FinancialProvider, CheckEligibilityStrategy> checkEligibilityStrategyMap;
    Map<FinancialProvider, LoanCreationStrategy> loanCreationStrategyMap;
    Map<FinancialProvider, UnderwritingStrategy> underwritingStrategyMap;

    @Inject
    public LoanActivationStrategyFactory(Map<FinancialProvider, CheckEligibilityStrategy> checkEligibilityStrategyMap,
                                         Map<FinancialProvider, LoanCreationStrategy> loanCreationStrategyMap,
                                         Map<FinancialProvider, UnderwritingStrategy> underwritingStrategyMap) {
        this.checkEligibilityStrategyMap = checkEligibilityStrategyMap;
        this.loanCreationStrategyMap = loanCreationStrategyMap;
        this.underwritingStrategyMap = underwritingStrategyMap;
    }


    public CheckEligibilityStrategy getCheckEligibilityStrategy(FinancialProvider financialProvider) {
        return checkEligibilityStrategyMap.get(financialProvider);
    }

    public LoanCreationStrategy getLoanCreationStrategy(FinancialProvider financialProvider) {
        return loanCreationStrategyMap.get(financialProvider);
    }

    public UnderwritingStrategy getUnderwritingStrategy(FinancialProvider financialProvider) {
        return underwritingStrategyMap.get(financialProvider);
    }
}
