package com.flipkart.fintech.pandora.service.strategy.kyc;

import com.flipkart.fintech.exception.ServiceErrorResponse;
import com.flipkart.fintech.exception.ServiceException;
import com.flipkart.fintech.filter.RequestContextThreadLocal;
import com.flipkart.fintech.pandora.api.model.request.ckyc.SearchCkycRequest;
import com.flipkart.fintech.pandora.api.model.response.ckyc.SearchCkycResponse;
import com.flipkart.fintech.pandora.service.client.pl.LenderConstants;
import com.flipkart.fintech.pandora.service.client.pl.IdfcUtil;
import com.flipkart.fintech.pandora.service.client.pl.kyc.IdfcClientV2;
import com.flipkart.fintech.pandora.service.client.pl.request.ckyc.SearchCkycRequestBody;
import com.flipkart.fintech.pandora.service.client.pl.request.ckyc.SearchCkycRequestDetail;
import com.flipkart.fintech.pandora.service.client.pl.response.ckyc.SearchCkycResponseDetail;
import com.flipkart.fintech.pandora.service.client.auth.Scope;
import com.flipkart.fintech.pandora.service.core.mock.MockService;
import com.flipkart.fintech.pandora.service.client.auth.AccessTokenProvider;
import com.flipkart.fintech.pandora.service.hystrix.PandoraHystrixNameConstants;
import com.flipkart.fintech.pandora.service.hystrix.PandoraHystrixProperties;
import com.flipkart.fintech.pandora.service.hystrix.idfcV2.IdfcSearchCkycCommand;
import com.flipkart.kloud.config.DynamicBucket;
import com.flipkart.sensitive.core.SensitiveUtil;
import com.google.inject.Inject;
import lombok.extern.slf4j.Slf4j;

import javax.ws.rs.core.Response;
import java.security.NoSuchAlgorithmException;
import java.util.Collections;
import java.util.UUID;

@Slf4j
public class IdfcCKYCSearchStrategy implements CKYCSearchStrategy {

    private IdfcUtil idfcUtil;
    private IdfcClientV2 idfcClient;
    private PandoraHystrixProperties pandoraHystrixProperties;
    private final DynamicBucket dynamicBucket;
    private AccessTokenProvider accessTokenProvider;

    @Inject
    public IdfcCKYCSearchStrategy (IdfcUtil idfcUtil, IdfcClientV2 idfcClient,
                             PandoraHystrixProperties pandoraHystrixProperties, DynamicBucket dynamicBucket, AccessTokenProvider accessTokenProvider) {
        this.idfcUtil = idfcUtil;
        this.idfcClient = idfcClient;
        this.pandoraHystrixProperties = pandoraHystrixProperties;
       this.dynamicBucket = dynamicBucket;
        this.accessTokenProvider = accessTokenProvider;
    }

    @Override
    public SearchCkycResponse execute(SearchCkycRequest ckycSearchRequest) {
        try {
            SearchCkycRequestDetail searchCkycRequestDetail = SearchCkycRequestDetail.builder()
                    .transactionId(UUID.randomUUID().toString())
                    .inputIdType(LenderConstants.CKYC_INPUT_ID_TYPE)
                    .inputIdNo(ckycSearchRequest.getPanNumber())
                    .build();

            SearchCkycRequestBody searchCkycRequestBody = SearchCkycRequestBody.builder()
                    .requestId(idfcUtil.generateIdfcRequestIdFromAccountId(ckycSearchRequest.getAccountId()))
                    .source(LenderConstants.IDFC_FLIPKART_SOURCE)
                    .details(Collections.singletonList(searchCkycRequestDetail))
                    .build();
            com.flipkart.fintech.pandora.service.client.pl.request.ckyc.SearchCkycRequest searchCkycRequest = com.flipkart.fintech.pandora.service.client.pl.request.ckyc.SearchCkycRequest.builder()
                    .searchCkycRequestBody(searchCkycRequestBody)
                    .build();

            log.info("SearchCkycRequest: {}", searchCkycRequest);

            if (dynamicBucket.getInt(PandoraHystrixNameConstants.HYSTRIX_IDFC_SEARCH_CKYC_TIMEOUT) != null) {
                pandoraHystrixProperties.setExecutionTimeoutInMilliseconds(PandoraHystrixNameConstants.IDFC_CKYC_SEARCH_KEY, dynamicBucket.getInt(PandoraHystrixNameConstants.HYSTRIX_IDFC_SEARCH_CKYC_TIMEOUT));
            }
            com.flipkart.fintech.pandora.service.client.pl.response.ckyc.SearchCkycResponse searchCkycResponse;
            if (RequestContextThreadLocal.get().isPerfRequest()) {
                searchCkycResponse = MockService.mockSearchCkyc(dynamicBucket.getInt("mockSearchCkycDelay"));
            } else {
                searchCkycResponse = new IdfcSearchCkycCommand(idfcClient, searchCkycRequest,
                        accessTokenProvider.getAccessToken(Scope.LENDING_IDFC), PandoraHystrixNameConstants.IDFC, PandoraHystrixNameConstants
                        .IDFC_CKYC_SEARCH_KEY, PandoraHystrixNameConstants.IDFC_CKYC_SEARCH_KEY, pandoraHystrixProperties).execute();
            }
            return   getCkycSearchResponseFromIdfcResponse(searchCkycResponse);
        } catch (NoSuchAlgorithmException e) {
            log.error("Exception while searching CKYC id for account - {}, - ", ckycSearchRequest.getAccountId(), e);
            throw new ServiceException(new ServiceErrorResponse(
                    Response.Status.INTERNAL_SERVER_ERROR, Response.Status.INTERNAL_SERVER_ERROR.getReasonPhrase(), e.getMessage()));
        }
    }

    private SearchCkycResponse getCkycSearchResponseFromIdfcResponse(com.flipkart.fintech.pandora.service.client.pl.response.ckyc.SearchCkycResponse searchCkycClientResponse) {
        SearchCkycResponseDetail searchCkycResponseDetail = searchCkycClientResponse.getSearchCkycResponseBody().getDetailList().get(0);
        SearchCkycResponse searchCkycResponse =  SearchCkycResponse.builder()
                .ckycId(searchCkycResponseDetail.getCkycId())
                .transactionStatus(searchCkycResponseDetail.getTransactionStatus())
                .transactionRejectionDescription(searchCkycResponseDetail.getTransactionRejectionDescription())
                .build();
        return SensitiveUtil.encryptSensitiveFields(searchCkycResponse);
    }
}
