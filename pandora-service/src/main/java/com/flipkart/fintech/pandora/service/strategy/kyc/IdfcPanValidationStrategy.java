package com.flipkart.fintech.pandora.service.strategy.kyc;

import com.flipkart.fintech.exception.ServiceErrorResponse;
import com.flipkart.fintech.exception.ServiceException;
import com.flipkart.fintech.filter.RequestContextThreadLocal;
import com.flipkart.fintech.pandora.api.model.request.onboarding.PanNumberRequest;
import com.flipkart.fintech.pandora.api.model.response.onboarding.PanNumberResponse;
import com.flipkart.fintech.pandora.service.client.PandoraServiceClientException;
import com.flipkart.fintech.pandora.service.client.pl.LenderConstants;
import com.flipkart.fintech.pandora.service.client.pl.IdfcUtil;
import com.flipkart.fintech.pandora.service.client.pl.kyc.IdfcClientV2;
import com.flipkart.fintech.pandora.service.client.pl.request.PanNumberVerificationRequest;
import com.flipkart.fintech.pandora.service.client.pl.response.PanNumberVerificationResponse;
import com.flipkart.fintech.pandora.service.client.pl.response.PanUserInfo;
import com.flipkart.fintech.pandora.service.client.auth.Scope;
import com.flipkart.fintech.pandora.service.core.mock.MockService;
import com.flipkart.fintech.pandora.service.client.auth.AccessTokenProvider;
import com.flipkart.fintech.pandora.service.hystrix.PandoraHystrixNameConstants;
import com.flipkart.fintech.pandora.service.hystrix.PandoraHystrixProperties;
import com.flipkart.fintech.pandora.service.hystrix.idfcV2.IdfcVerifyPanCommand;
import com.flipkart.kloud.config.DynamicBucket;
import com.google.inject.Inject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.ws.rs.core.Response;
import java.security.NoSuchAlgorithmException;
import java.util.function.Function;

import static com.flipkart.fintech.pandora.api.model.common.STATUS.SUCCESS;
import static com.flipkart.fintech.pandora.service.utils.Constants.INVALID_PAN_RESPONSE;
import static com.flipkart.fintech.pandora.service.utils.Constants.VALID_PAN_RESPONSE;

@Slf4j
public class IdfcPanValidationStrategy implements PanValidationStrategy {
    private final IdfcClientV2 idfcClient;
    private final IdfcUtil idfcUtil;
    private PandoraHystrixProperties pandoraHystrixProperties;
    private final DynamicBucket dynamicBucket;
    private final AccessTokenProvider accessTokenProvider;

    @Inject
    public IdfcPanValidationStrategy(IdfcClientV2 idfcClient, IdfcUtil idfcUtil, PandoraHystrixProperties pandoraHystrixProperties, DynamicBucket dynamicBucket, AccessTokenProvider accessTokenProvider) {
        this.idfcClient = idfcClient;
        this.idfcUtil = idfcUtil;
        this.pandoraHystrixProperties = pandoraHystrixProperties;
        this.dynamicBucket = dynamicBucket;
        this.accessTokenProvider = accessTokenProvider;
    }

    @Override
    public PanNumberResponse execute(PanNumberRequest panNumberRequest) {
        PanNumberVerificationRequest panNumberVerificationRequest  = new PanNumberVerificationRequest();
        PanNumberVerificationResponse panNumberVerificationResponse = null;
        panNumberVerificationRequest.setPanNumber(panNumberRequest.getPan());
        try {
            panNumberVerificationRequest.setReqId(idfcUtil.generateIdfcRequestIdFromAccountId(panNumberRequest.getAccId()));
        } catch (NoSuchAlgorithmException e) {
            log.error("Error while generating Idfc reqId from accountId: {}", panNumberRequest.getAccId(), e);
            throw new ServiceException(new ServiceErrorResponse(
                    Response.Status.INTERNAL_SERVER_ERROR, Response.Status.INTERNAL_SERVER_ERROR.getReasonPhrase(),
                    e.getMessage()));
        }
        log.info("Idfc verify pan, accountId: {}, reqId: {}", panNumberRequest.getAccId(), panNumberVerificationRequest.getReqId());

        if (dynamicBucket.getInt(PandoraHystrixNameConstants.HYSTRIX_IDFC_PAN_VERIFICATION_TIMEOUT) != null) {
            pandoraHystrixProperties.setExecutionTimeoutInMilliseconds(PandoraHystrixNameConstants.IDFC_PAN_VERIFICATION_KEY, dynamicBucket.getInt(PandoraHystrixNameConstants.HYSTRIX_IDFC_PAN_VERIFICATION_TIMEOUT));
        }
        if (RequestContextThreadLocal.get().isPerfRequest()) {
            panNumberVerificationResponse = MockService.mockVerifyPan(dynamicBucket.getInt("mockPanVerifyDelay"));
        } else {
            panNumberVerificationResponse = new IdfcVerifyPanCommand(idfcClient, panNumberVerificationRequest, accessTokenProvider.getAccessToken(Scope.LENDING_IDFC), PandoraHystrixNameConstants.IDFC,
                    PandoraHystrixNameConstants.IDFC_PAN_VERIFICATION_KEY, PandoraHystrixNameConstants.IDFC_PAN_VERIFICATION_KEY, pandoraHystrixProperties).execute();
        }
        return idfcPanToPandoraPanResponse.apply(panNumberVerificationResponse);
    }

    private Function<PanNumberVerificationResponse, PanNumberResponse> idfcPanToPandoraPanResponse = new Function<PanNumberVerificationResponse, PanNumberResponse>() {
        @Override
        public PanNumberResponse apply(PanNumberVerificationResponse panNumberVerificationResponse) {
            PanNumberResponse panNumberResponse = new PanNumberResponse();
            panNumberResponse.setPanVerifiedStatus(panNumberVerificationResponse.getPanVerificationResponse().getPanUserInfo().getPanStatus());
            if (LenderConstants.PanStatus.VALID_PAN_STATUS.equals(panNumberVerificationResponse.getPanVerificationResponse().getPanUserInfo().getPnStatus())) {
                panNumberResponse.setStatus(SUCCESS);
                panNumberResponse.setPanVerifiedStatus(VALID_PAN_RESPONSE);
                PanUserInfo panUserInfo = panNumberVerificationResponse.getPanVerificationResponse().getPanUserInfo();
                panNumberResponse.setPanVerifiedName(getFullnameFromFirstnameAndLastname(panUserInfo.getFirstName(), panUserInfo.getMiddleName(), panUserInfo.getLastName()));
                if (StringUtils.isEmpty(panNumberResponse.getPanVerifiedName())) {
                    throw new PandoraServiceClientException(String.format("reqId - %s, Empty name received from IDFC for valid pan status", panNumberVerificationResponse.getReqId()));
                }
            } else {
                panNumberResponse.setStatus(SUCCESS);
                panNumberResponse.setPanVerifiedStatus(INVALID_PAN_RESPONSE);
            }
            panNumberResponse.setExternalPanInfo(panNumberVerificationResponse.getPanVerificationResponse().getPanUserInfo().getPanStatus());
            panNumberResponse.setInfo(panNumberVerificationResponse.getPanVerificationResponse().getResponseId());
            return panNumberResponse;
        }
    };

    private String getFullnameFromFirstnameAndLastname(String firstName, String middleName, String lastName) {
        StringBuilder stringBuilder = new StringBuilder();
        if (StringUtils.isNotBlank(firstName)) {
            stringBuilder.append(firstName).append(" ");
        }
        if (StringUtils.isNotBlank(middleName)) {
            stringBuilder.append(middleName).append(" ");
        }
        if (StringUtils.isNotBlank(lastName)) {
            stringBuilder.append(lastName);
        }
        return stringBuilder.toString();
    }
}
