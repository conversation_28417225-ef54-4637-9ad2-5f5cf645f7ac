package com.flipkart.fintech.pandora.service.strategy.kyc;

import com.flipkart.fintech.pandora.api.model.request.ckyc.SearchCkycRequest;
import com.flipkart.fintech.pandora.api.model.response.ckyc.SearchCkycResponse;
import com.flipkart.fintech.pandora.service.client.auth.KotakAuthenticationClient;
import com.flipkart.fintech.pandora.service.client.kotak.constants.Constants;
import com.flipkart.fintech.pandora.service.client.kotak.kyc.KYCClient;
import com.flipkart.fintech.pandora.service.client.kotak.requests.CKYCSearchRequest;
import com.flipkart.fintech.pandora.service.client.kotak.responses.CKYCSearchResponse;
import com.flipkart.fintech.pandora.service.hystrix.PandoraHystrixNameConstants;
import com.flipkart.fintech.pandora.service.hystrix.PandoraHystrixProperties;
import com.flipkart.fintech.pandora.service.hystrix.kotak.KotakAuthTokenGenerationCommand;
import com.flipkart.fintech.pandora.service.hystrix.kotak.KotakCKYCSearchCommand;
import com.flipkart.fintech.pandora.service.utils.LenderRequestIdGenerator;
import com.google.inject.Inject;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class KotakCKYCSearchStrategy implements CKYCSearchStrategy {

    private final KYCClient kycClient;
    private final PandoraHystrixProperties pandoraHystrixProperties;
    private static final String OPERATION = "CKYC Search";
    private final KotakAuthenticationClient kotakAuthenticationClient;

    @Inject
    public KotakCKYCSearchStrategy(KYCClient kycClient, PandoraHystrixProperties pandoraHystrixProperties,
                                   KotakAuthenticationClient kotakAuthenticationClient) {
        this.kycClient = kycClient;
        this.pandoraHystrixProperties = pandoraHystrixProperties;
        this.kotakAuthenticationClient = kotakAuthenticationClient;
    }

    @Override
    public SearchCkycResponse execute(SearchCkycRequest searchCkycRequest) {
        CKYCSearchRequest ckycSearchRequest = new CKYCSearchRequest();
        ckycSearchRequest.setUniqueIdentifier(LenderRequestIdGenerator.generateNumericUniqueIdentifier(searchCkycRequest.getAccountId(),
                22));
        ckycSearchRequest.setSourceCode(Constants.SOURCE_CODE);
        String requestLogMessage = String.format(com.flipkart.fintech.pandora.service.utils.Constants.KOTAK_REQUEST_LOG_FORMAT,
                ckycSearchRequest.getUniqueIdentifier(), OPERATION);
        log.info(requestLogMessage);
        String accessToken = new KotakAuthTokenGenerationCommand(kotakAuthenticationClient, PandoraHystrixNameConstants.KOTAK,
                PandoraHystrixNameConstants.KOTAK_AUTH_TOKEN_GENERATION_KEY, PandoraHystrixNameConstants.KOTAK_AUTH_TOKEN_GENERATION_KEY,
                pandoraHystrixProperties).execute();
        CKYCSearchResponse ckycSearchResponse = new KotakCKYCSearchCommand(kycClient, ckycSearchRequest,
                accessToken, PandoraHystrixNameConstants.KOTAK,
                PandoraHystrixNameConstants.KOTAK_CKYC_SEARCH_KEY, PandoraHystrixNameConstants.KOTAK_CKYC_SEARCH_KEY,
                pandoraHystrixProperties).execute();
        String responseLogMessage = String.format(com.flipkart.fintech.pandora.service.utils.Constants.KOTAK_RESPONSE_LOG_FORMAT,
        ckycSearchRequest.getUniqueIdentifier(), OPERATION, ckycSearchResponse.getStatus(),
        ckycSearchResponse.getApiResult(), ckycSearchResponse.getResponseCode(), ckycSearchResponse.getResponseMessage(),
        ckycSearchResponse.getReason());
        log.info(responseLogMessage);
        SearchCkycResponse searchCkycResponse = new SearchCkycResponse();
        searchCkycResponse.setTransactionStatus(
                com.flipkart.fintech.pandora.service.utils.Constants.KOTAK_RESPONSE_STATUS_MAP.
                        get(ckycSearchResponse.getStatus()).name());
        return searchCkycResponse;
    }
}
