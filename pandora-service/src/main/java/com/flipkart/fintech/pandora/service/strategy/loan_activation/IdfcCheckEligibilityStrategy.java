package com.flipkart.fintech.pandora.service.strategy.loan_activation;

import com.flipkart.fintech.common.enums.Tenant;
import com.flipkart.fintech.exception.ServiceErrorResponse;
import com.flipkart.fintech.exception.ServiceException;
import com.flipkart.fintech.filter.RequestContextThreadLocal;
import com.flipkart.fintech.pandora.api.model.request.onboarding.CompressedUserRequest;
import com.flipkart.fintech.pandora.api.model.request.onboarding.UserRequest;
import com.flipkart.fintech.pandora.api.model.response.onboarding.UserResponse;
import com.flipkart.fintech.pandora.service.client.pl.LenderConstants;
import com.flipkart.fintech.pandora.service.client.pl.IdfcUtil;
import com.flipkart.fintech.pandora.service.client.pl.kyc.IdfcClientV2;
import com.flipkart.fintech.pandora.service.client.pl.requestV2.CheckApprovalStatusRequestV2;
import com.flipkart.fintech.pandora.service.client.pl.response.CheckApprovalStatusResponse;
import com.flipkart.fintech.pandora.service.client.auth.Scope;
import com.flipkart.fintech.pandora.service.core.mock.MockService;
import com.flipkart.fintech.pandora.service.client.auth.AccessTokenProvider;
import com.flipkart.fintech.pandora.service.hystrix.PandoraHystrixNameConstants;
import com.flipkart.fintech.pandora.service.hystrix.PandoraHystrixProperties;
import com.flipkart.fintech.pandora.service.hystrix.idfcV2.IdfcCheckEligibilityCommand;
import com.flipkart.fintech.pinaka.api.enums.Lender;
import com.flipkart.fintech.pinaka.api.response.v3.FetchLenderIdentifierResponse;
import com.flipkart.fintech.pinaka.client.v3.PinakaClientV3;
import com.flipkart.kloud.config.DynamicBucket;
import com.google.inject.Inject;
import lombok.extern.slf4j.Slf4j;

import javax.ws.rs.core.Response;
import java.util.Objects;

import static com.flipkart.fintech.pandora.api.model.common.STATUS.SUCCESS;

@Slf4j
public class IdfcCheckEligibilityStrategy implements CheckEligibilityStrategy {
    private final IdfcClientV2 idfcClient;
    private final IdfcUtil idfcUtil;
    private PandoraHystrixProperties pandoraHystrixProperties;
    private final PinakaClientV3 pinakaClientV3;
    private final DynamicBucket dynamicBucket;
    private final AccessTokenProvider accessTokenProvider;

    @Inject
    public IdfcCheckEligibilityStrategy(IdfcClientV2 idfcClient, IdfcUtil idfcUtil, PandoraHystrixProperties pandoraHystrixProperties, PinakaClientV3 pinakaClientV3, DynamicBucket dynamicBucket, AccessTokenProvider accessTokenProvider) {
        this.idfcClient = idfcClient;
        this.idfcUtil = idfcUtil;
        this.pandoraHystrixProperties = pandoraHystrixProperties;
        this.pinakaClientV3 = pinakaClientV3;
        this.dynamicBucket = dynamicBucket;
        this.accessTokenProvider = accessTokenProvider;
    }

    @Override
    public UserResponse execute(UserRequest userRequest) {
        CheckApprovalStatusRequestV2 checkApprovalStatusRequest = new CheckApprovalStatusRequestV2();
        UserResponse               userResponse               = null;
        CompressedUserRequest compressedUserRequest      = (CompressedUserRequest) userRequest;
        CheckApprovalStatusResponse checkApprovalStatusResponse = null;

        FetchLenderIdentifierResponse fetchLenderIdentifierResponse = null;
        try {
            checkApprovalStatusRequest.setReqId(idfcUtil.generateIdfcRequestIdFromAccountId(compressedUserRequest.getExternalReferenceId()));
            log.info("Idfc Check approval AccountId: {}, reqId: {}", compressedUserRequest.getExternalReferenceId(), checkApprovalStatusRequest.getReqId());
            Tenant tenant = RequestContextThreadLocal.get().getTenantId();
            if (tenant != Tenant.FFB && !RequestContextThreadLocal.get().isPerfRequest()) {
                fetchLenderIdentifierResponse = pinakaClientV3.fetchLenderIdentifierResponse(compressedUserRequest.getExternalReferenceId(), Lender.IDFC);
            }
        } catch (Exception e) {
            log.error("Exception in fetchLenderIdentifierResponse while activating checking eligibility accountId: {} ", compressedUserRequest.getExternalReferenceId(), e);
            throw new ServiceException(new ServiceErrorResponse(
                    Response.Status.INTERNAL_SERVER_ERROR, Response.Status.INTERNAL_SERVER_ERROR.getReasonPhrase(),
                    e.getMessage()));
        }
        log.info("Idfc Check fetchLenderIdentifierResponse {}",fetchLenderIdentifierResponse);
        // if user already has a valid account with the bank then skip the check status call to lender
        if (fetchLenderIdentifierResponse != null && fetchLenderIdentifierResponse.getLenderReferenceNumber() != null
                && !Objects.equals(fetchLenderIdentifierResponse.getLenderReferenceNumber(), "")) {
            checkApprovalStatusResponse = new CheckApprovalStatusResponse();
            checkApprovalStatusResponse.setCrn(fetchLenderIdentifierResponse.getLenderReferenceNumber());
            checkApprovalStatusResponse.setLoanApplicationNo(fetchLenderIdentifierResponse.getReferenceNumber());
            checkApprovalStatusResponse.setStatus(LenderConstants.CHECK_APPROVAL_SUCCESS);
        } else {
            log.info("HYSTRIX_IDFC_CHECK_ELIGIBILITY_TIMEOUT {}",dynamicBucket.getInt(PandoraHystrixNameConstants.HYSTRIX_IDFC_CHECK_ELIGIBILITY_TIMEOUT));
            if (dynamicBucket.getInt(PandoraHystrixNameConstants.HYSTRIX_IDFC_CHECK_ELIGIBILITY_TIMEOUT) != null) {
                pandoraHystrixProperties.setExecutionTimeoutInMilliseconds(PandoraHystrixNameConstants.IDFC_CHECK_ELIGIBILITY_KEY, dynamicBucket.getInt(PandoraHystrixNameConstants.HYSTRIX_IDFC_CHECK_ELIGIBILITY_TIMEOUT));
            }
            if(RequestContextThreadLocal.get().isPerfRequest()) {
                checkApprovalStatusResponse = MockService.mockCheckEligibility(dynamicBucket.getInt("mockCheckEligibilityDelay"));
            } else {
                checkApprovalStatusResponse = new IdfcCheckEligibilityCommand(idfcClient, checkApprovalStatusRequest,
                        accessTokenProvider.getAccessToken(Scope.LENDING_IDFC), PandoraHystrixNameConstants.IDFC, PandoraHystrixNameConstants.IDFC_CHECK_ELIGIBILITY_KEY,
                        PandoraHystrixNameConstants.IDFC_CHECK_ELIGIBILITY_KEY, pandoraHystrixProperties).execute();
            }
        }

        if (checkApprovalStatusResponse != null) {
            userResponse = new UserResponse();
            userResponse.setReferenceNumber(checkApprovalStatusResponse.getLoanApplicationNo());
            userResponse.setStatus(SUCCESS);
            userResponse.setLenderReferenceNumber(checkApprovalStatusResponse.getCrn());
            userResponse.setFicoStatus(checkApprovalStatusResponse.getFicoStatus());
            userResponse.setIdfcLimitAmount(checkApprovalStatusResponse.getIdfcLimitAmount());
            if (LenderConstants.CHECK_APPROVAL_SUCCESS.equals(checkApprovalStatusResponse.getStatus())) {
                userResponse.setEligible(Boolean.TRUE);
            } else if (LenderConstants.CHECK_APPROVAL_FAILURE.equals(checkApprovalStatusResponse.getStatus())) {
                userResponse.setEligible(Boolean.FALSE);
            } else {
                // For PENDING / FAILURE, status = INPROGRESS or status = null
                log.info("Check status call failed. Response {}", checkApprovalStatusResponse.getResult());
                throw new ServiceException(new ServiceErrorResponse(
                        Response.Status.INTERNAL_SERVER_ERROR, Response.Status.INTERNAL_SERVER_ERROR.getReasonPhrase(),
                        String.format("Error %s", checkApprovalStatusResponse.getResult().getMessage())));
            }
        }
        return userResponse;
    }
}
