package com.flipkart.fintech.pandora.service.strategy.loan_activation;

import com.flipkart.fintech.pandora.api.model.common.STATUS;
import com.flipkart.fintech.pandora.api.model.request.onboarding.CompressedUserRequest;
import com.flipkart.fintech.pandora.api.model.request.onboarding.UserRequest;
import com.flipkart.fintech.pandora.api.model.response.onboarding.UserResponse;
import com.flipkart.fintech.pandora.service.client.auth.KotakAuthenticationClient;
import com.flipkart.fintech.pandora.service.client.kotak.constants.Constants;
import com.flipkart.fintech.pandora.service.client.kotak.loan_activation.LoanActivationClient;
import com.flipkart.fintech.pandora.service.client.kotak.requests.CheckEligibilityRequest;
import com.flipkart.fintech.pandora.service.client.kotak.responses.CheckEligibilityResponse;
import com.flipkart.fintech.pandora.service.hystrix.PandoraHystrixNameConstants;
import com.flipkart.fintech.pandora.service.hystrix.PandoraHystrixProperties;
import com.flipkart.fintech.pandora.service.hystrix.kotak.KotakAuthTokenGenerationCommand;
import com.flipkart.fintech.pandora.service.hystrix.kotak.KotakCheckEligibilityCommand;
import com.flipkart.fintech.pandora.service.utils.LenderRequestIdGenerator;
import com.google.inject.Inject;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class KotakCheckEligibilityStrategy implements CheckEligibilityStrategy {

    private final LoanActivationClient loanActivationClient;
    private final PandoraHystrixProperties pandoraHystrixProperties;
    private static final String OPERATION = "Check Eligibility";
    private final KotakAuthenticationClient kotakAuthenticationClient;

    @Inject
    public KotakCheckEligibilityStrategy(LoanActivationClient loanActivationClient, PandoraHystrixProperties pandoraHystrixProperties,
                                         KotakAuthenticationClient kotakAuthenticationClient) {
        this.loanActivationClient = loanActivationClient;
        this.pandoraHystrixProperties = pandoraHystrixProperties;
        this.kotakAuthenticationClient = kotakAuthenticationClient;
    }

    @Override
    public UserResponse execute(UserRequest userRequest) {
        CompressedUserRequest compressedUserRequest = (CompressedUserRequest) userRequest;
        CheckEligibilityRequest checkEligibilityRequest =
                new com.flipkart.fintech.pandora.service.client.kotak.requests.CheckEligibilityRequest();
        checkEligibilityRequest.setSourceCode(Constants.SOURCE_CODE);
        checkEligibilityRequest.setUniqueIdentifier(LenderRequestIdGenerator.generateNumericUniqueIdentifier(compressedUserRequest.getExternalReferenceId(),
                22));
        String requestLogMessage = String.format(com.flipkart.fintech.pandora.service.utils.Constants.KOTAK_REQUEST_LOG_FORMAT,
                checkEligibilityRequest.getUniqueIdentifier(), OPERATION);
        log.info(requestLogMessage);
        String accessToken = new KotakAuthTokenGenerationCommand(kotakAuthenticationClient, PandoraHystrixNameConstants.KOTAK,
                PandoraHystrixNameConstants.KOTAK_AUTH_TOKEN_GENERATION_KEY, PandoraHystrixNameConstants.KOTAK_AUTH_TOKEN_GENERATION_KEY,
                pandoraHystrixProperties).execute();
        CheckEligibilityResponse checkEligibilityResponse = new KotakCheckEligibilityCommand(loanActivationClient, checkEligibilityRequest,
                accessToken, PandoraHystrixNameConstants.KOTAK,
                PandoraHystrixNameConstants.KOTAK_CHECK_ELIGIBILITY_KEY, PandoraHystrixNameConstants.KOTAK_CHECK_ELIGIBILITY_KEY,
                pandoraHystrixProperties).execute();
        String responseLogMessage = String.format(com.flipkart.fintech.pandora.service.utils.Constants.KOTAK_RESPONSE_LOG_FORMAT,
                checkEligibilityRequest.getUniqueIdentifier(), OPERATION, checkEligibilityResponse.getStatus(),
                checkEligibilityResponse.getApiResult(), checkEligibilityResponse.getResponseCode(),
                checkEligibilityResponse.getResponseMessage(), checkEligibilityResponse.getReason());
        log.info(responseLogMessage);
        UserResponse userResponse = new UserResponse();
        userResponse.setStatus(com.flipkart.fintech.pandora.service.utils.Constants.
                KOTAK_RESPONSE_STATUS_MAP.get(checkEligibilityResponse.getStatus()));
        if (STATUS.SUCCESS.equals(userResponse.getStatus()))
            userResponse.setEligible(true);
        return userResponse;
    }
}
