package com.flipkart.fintech.pandora.service.strategy.loan_activation;

import com.flipkart.fintech.pandora.api.model.request.onboarding.AccountActivationRequest;
import com.flipkart.fintech.pandora.api.model.response.onboarding.AccountActivationResponse;
import com.flipkart.fintech.pandora.service.client.auth.KotakAuthenticationClient;
import com.flipkart.fintech.pandora.service.client.kotak.constants.Constants;
import com.flipkart.fintech.pandora.service.client.kotak.loan_activation.LoanActivationClient;
import com.flipkart.fintech.pandora.service.client.kotak.requests.CreateLoanRequest;
import com.flipkart.fintech.pandora.service.client.kotak.responses.LoanCreationResponse;
import com.flipkart.fintech.pandora.service.hystrix.PandoraHystrixNameConstants;
import com.flipkart.fintech.pandora.service.hystrix.PandoraHystrixProperties;
import com.flipkart.fintech.pandora.service.hystrix.kotak.KotakAuthTokenGenerationCommand;
import com.flipkart.fintech.pandora.service.hystrix.kotak.KotakCreateLoanCommand;
import com.flipkart.fintech.pandora.service.utils.LenderRequestIdGenerator;
import com.google.inject.Inject;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class KotakLoanCreationStrategy implements LoanCreationStrategy {

    private final LoanActivationClient loanActivationClient;
    private final PandoraHystrixProperties pandoraHystrixProperties;
    private static final String OPERATION = "Loan Creation";
    private final KotakAuthenticationClient kotakAuthenticationClient;

    @Inject
    public KotakLoanCreationStrategy(LoanActivationClient loanActivationClient, PandoraHystrixProperties pandoraHystrixProperties,
                                     KotakAuthenticationClient kotakAuthenticationClient) {
        this.loanActivationClient = loanActivationClient;
        this.pandoraHystrixProperties = pandoraHystrixProperties;
        this.kotakAuthenticationClient = kotakAuthenticationClient;
    }

    @Override
    public AccountActivationResponse execute(AccountActivationRequest accountActivationRequest) {
        CreateLoanRequest createLoanRequest = new CreateLoanRequest();
        createLoanRequest.setSourceCode(Constants.SOURCE_CODE);
        createLoanRequest.setUniqueIdentifier(LenderRequestIdGenerator.generateNumericUniqueIdentifier(accountActivationRequest.getCustomerId(),
                22));
        // todo: add penny drop skipped information in request
        createLoanRequest.setPennyDropSkipped("");
        createLoanRequest.setLimitAmount(accountActivationRequest.getLoanAmount().toString());
        String requestLogMessage = String.format(com.flipkart.fintech.pandora.service.utils.Constants.KOTAK_REQUEST_LOG_FORMAT,
                createLoanRequest.getUniqueIdentifier(), OPERATION);
        log.info(requestLogMessage);
        String accessToken = new KotakAuthTokenGenerationCommand(kotakAuthenticationClient, PandoraHystrixNameConstants.KOTAK,
                PandoraHystrixNameConstants.KOTAK_AUTH_TOKEN_GENERATION_KEY, PandoraHystrixNameConstants.KOTAK_AUTH_TOKEN_GENERATION_KEY,
                pandoraHystrixProperties).execute();
        LoanCreationResponse loanCreationResponse = new KotakCreateLoanCommand(loanActivationClient, createLoanRequest,
                accessToken, PandoraHystrixNameConstants.KOTAK,
                PandoraHystrixNameConstants.KOTAK_CREATE_LOAN_KEY, PandoraHystrixNameConstants.KOTAK_CREATE_LOAN_KEY,
                pandoraHystrixProperties).execute();
        String responseLogMessage = String.format(com.flipkart.fintech.pandora.service.utils.Constants.KOTAK_RESPONSE_LOG_FORMAT,
                createLoanRequest.getUniqueIdentifier(), OPERATION, loanCreationResponse.getStatus(),
                loanCreationResponse.getApiResult(), loanCreationResponse.getResponseCode(),
                loanCreationResponse.getResponseMessage(), loanCreationResponse.getReason());
        log.info(responseLogMessage);
        AccountActivationResponse accountActivationResponse = new AccountActivationResponse();
        accountActivationResponse.setStatus(com.flipkart.fintech.pandora.service.utils.Constants.
                KOTAK_RESPONSE_STATUS_MAP.get(loanCreationResponse.getStatus()));
        return accountActivationResponse;
    }
}
