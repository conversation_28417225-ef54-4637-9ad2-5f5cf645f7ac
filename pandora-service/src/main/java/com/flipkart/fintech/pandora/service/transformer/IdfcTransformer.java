package com.flipkart.fintech.pandora.service.transformer;

import com.flipkart.fintech.pandora.api.model.pl.request.AutoDisbursalRequest;
import com.flipkart.fintech.pandora.api.model.pl.request.ComparePhoto;
import com.flipkart.fintech.pandora.api.model.pl.request.EmandateVerificationRequest;
import com.flipkart.fintech.pandora.api.model.pl.request.LivenessCheck;
import com.flipkart.fintech.pandora.api.model.pl.request.StartLoanRequest;
import com.flipkart.fintech.pandora.api.model.pl.request.UnderWriting;
import com.flipkart.fintech.pandora.api.model.pl.request.*;
import com.flipkart.fintech.pandora.api.model.pl.response.AutoDisbursalResponse;
import com.flipkart.fintech.pandora.api.model.pl.response.LoanOnboardingResponse;
import com.flipkart.fintech.pandora.api.model.pl.response.Metadata;
import com.flipkart.fintech.pandora.api.model.pl.response.SearchCkycResponse;
import com.flipkart.fintech.pandora.api.model.pl.response.*;
import com.flipkart.fintech.pandora.api.model.pl.response.VkycStatus.VkycStatusEnum;

import com.flipkart.fintech.pandora.idfc.client.request.Charge;
import com.flipkart.fintech.pandora.idfc.client.request.Consent;
import com.flipkart.fintech.pandora.idfc.client.request.CustomerDetail;
import com.flipkart.fintech.pandora.idfc.client.request.InitialOfferGenerationRequest;
import com.flipkart.fintech.pandora.idfc.client.request.LoanOnboardingRequest;
import com.flipkart.fintech.pandora.idfc.client.request.PennyDropRequest;
import com.flipkart.fintech.pandora.idfc.client.request.SearchCkycRequest;
import com.flipkart.fintech.pandora.idfc.client.request.*;
import com.flipkart.fintech.pandora.idfc.client.response.InitialOfferGenerationResponse;
import com.flipkart.fintech.pandora.idfc.client.response.KfsDetail;
import com.flipkart.fintech.pandora.idfc.client.response.PanVerificationResponse;
import com.flipkart.fintech.pandora.idfc.client.response.PennyDropResponse;
import com.flipkart.fintech.pandora.idfc.client.response.RepaySchedule;
import com.flipkart.fintech.pandora.idfc.client.response.ResourceDatum;
import com.flipkart.fintech.pandora.idfc.client.response.*;
import com.flipkart.fintech.pandora.service.client.document.request.DocumentInteractionRequest;
import com.flipkart.fintech.pandora.service.client.document.response.DocumentInteractionResponse;
import com.flipkart.fintech.pandora.service.client.pl.LenderConstants;
import com.flipkart.fintech.pandora.service.client.pl.scheme.SchemeFinder;
import com.flipkart.fintech.pandora.service.client.pl.scheme.SchemeFinder.Qualification;
import com.flipkart.fintech.pandora.service.client.pl.scheme.SchemeFinder.Serviceability;
import com.flipkart.fintech.pandora.service.core.document.DocumentService;
import com.flipkart.fintech.pandora.service.utils.IPAddressParser;
import com.flipkart.fintech.security.aes.AESService;
import com.flipkart.fintech.user.data.client.UserDataClient;
import com.flipkart.fintech.user.data.models.AddressData;
import com.flipkart.fintech.user.data.models.UserData;
import com.flipkart.fintech.user.data.models.enums.Merchant;
import com.flipkart.fintech.user.data.models.enums.PIIDataType;
import com.google.inject.Inject;
import com.google.inject.name.Named;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.LocalDate;
import java.time.Period;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Base64;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TimeZone;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.xml.bind.DatatypeConverter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.modelmapper.Converter;
import org.modelmapper.ModelMapper;
import org.modelmapper.TypeMap;
import org.modelmapper.convention.MatchingStrategies;
import org.modelmapper.spi.MappingContext;

import static com.flipkart.fintech.pandora.service.client.pl.LenderConstants.*;

@Slf4j
public class IdfcTransformer {

  private final ModelMapper mapper;

  private final UserDataClient userDataClient;

  private final String aadhaarEncryptionKey;
  private final DocumentService documentService;

  private final Map<String, VkycStatusEnum> statusEnumMap;
  private final SchemeFinder idfcSchemeFinder;

  private final String callerId;

  @Inject
  public IdfcTransformer(ModelMapper requestMapper,
                         UserDataClient userDataClient,
                         @Named("idfcAadhaarEncryptionKey") String aadhaarEncryptionKey,
                         DocumentService documentService,
                         Map<String, VkycStatusEnum> statusEnumMap,
                         SchemeFinder schemeFinder,
                         @Named("IdfcCallerId") String callerId) {
    this.mapper = requestMapper;
      this.userDataClient = userDataClient;
      this.aadhaarEncryptionKey = aadhaarEncryptionKey;
    this.documentService = documentService;
    this.statusEnumMap = statusEnumMap;
    this.idfcSchemeFinder = schemeFinder;
    this.callerId = callerId;
    log.info("callerId : {}", callerId);
    init();
  }

  private void init() {
    mapper.getConfiguration().setMatchingStrategy(MatchingStrategies.STRICT);
    mapper.addConverter(new PanVerificationRequestConverter());
    mapper.addConverter(new PanVerificationResponseConverter());
    mapper.addConverter(new InitialOfferGenerationRequestConverter());
    mapper.addConverter(new InitialOfferGenerationResponseConverter());
    mapper.addConverter(new InitialOfferGenerationStatusConverter());
    mapper.addConverter(new StatusCheckRequestConverter());
    mapper.addConverter(new SearchCkycRequestConverter());
    mapper.addConverter(new CkycResponseConverter(documentService));
    mapper.addConverter(new AadhaarOtpGenerationRequestConverter(aadhaarEncryptionKey));
    mapper.addConverter(new AadhaarOtpGenerationResponseConverter());
    mapper.addConverter(new AadhaarOtpValidationRequestConverter(aadhaarEncryptionKey));
    mapper.addConverter(new AadhaarOtpValidationResponseConverter(documentService));
    mapper.addConverter(new StartLoanRequestConverter());
    mapper.addConverter(new StartLoanResponseConverter());
    mapper.addConverter(new OfflineDataUploadRequestConverter(documentService));
    mapper.addConverter(new KfsGenerateOtpRequestConverter(userDataClient, callerId));
    mapper.addConverter(new KfsGenerateOtpResponseConverter());
    mapper.addConverter(new AutoDisbursalRequestConverter());
    mapper.addConverter(new AutoDisbursalResponseConverter());
    mapper.addConverter(new LoanOnboardingRequestConverter(idfcSchemeFinder, userDataClient));
    mapper.addConverter(new LoanOnboardingResponseConverter());
    mapper.addConverter(new IfscRequestConverter());
    mapper.addConverter(new IfscResponseConverter());
    mapper.addConverter(new EmandateVerificationRequestConverter());
    mapper.addConverter(new EmandateVerificationResponseConverter());
    mapper.addConverter(new VkycStatusConverter(statusEnumMap));
    mapper.addConverter(new PaymentDetailsUtilityResponseConverter());
    mapper.addConverter(new PaymentDetailsUtilityRequestConverter());
    mapper.createTypeMap(com.flipkart.fintech.pandora.api.model.pl.request.PennyDropRequest.class, PennyDropRequest.class)
        .addMappings(pdrMap -> pdrMap.map(com.flipkart.fintech.pandora.api.model.pl.request.PennyDropRequest::getIfscCode, PennyDropRequest::setIfscCode))
        .addMappings(pdrMap -> pdrMap.map(com.flipkart.fintech.pandora.api.model.pl.request.PennyDropRequest::getCustBankAccNo, PennyDropRequest::setCustBankAccNo))
        .addMappings(pdrMap -> pdrMap.map(com.flipkart.fintech.pandora.api.model.pl.request.PennyDropRequest::getApplicationId, PennyDropRequest::setReqId));
    mapper.createTypeMap(PennyDropResponse.class, com.flipkart.fintech.pandora.api.model.pl.response.PennyDropResponse.class)
        .addMappings(pdResponse -> pdResponse.map(PennyDropResponse::getStatus, com.flipkart.fintech.pandora.api.model.pl.response.PennyDropResponse::setStatus))
        .addMappings(pdResponse -> pdResponse.map(PennyDropResponse::getTransactionId, com.flipkart.fintech.pandora.api.model.pl.response.PennyDropResponse::setTransactionId))
        .addMappings(pdResponse -> pdResponse.map(PennyDropResponse::getTransactionId, com.flipkart.fintech.pandora.api.model.pl.response.PennyDropResponse::setTransactionId))
        .addMappings(pdResponse -> pdResponse.map(PennyDropResponse::getErrCode, com.flipkart.fintech.pandora.api.model.pl.response.PennyDropResponse::setErrCode))
        .addMappings(pdResponse -> pdResponse.map(PennyDropResponse::getErrMessage, com.flipkart.fintech.pandora.api.model.pl.response.PennyDropResponse::setErrMessage));
    mapper.createTypeMap(KfsSubmitOtpRequest.class, KfsOtpValidationRequest.class)
        .addMappings(kfsOtpValidationRequest -> kfsOtpValidationRequest.map(KfsSubmitOtpRequest::getLinkData, KfsOtpValidationRequest::setLinkData))
        .addMappings(kfsOtpValidationRequest -> kfsOtpValidationRequest.map(KfsSubmitOtpRequest::getReferenceNumber, KfsOtpValidationRequest::setReferenceNumber))
        .addMappings(kfsOtpValidationRequest -> kfsOtpValidationRequest.map(KfsSubmitOtpRequest::getOtp, KfsOtpValidationRequest::setOtpValue));
    mapper.createTypeMap(KfsOtpValidationResponse.class, KfsSubmitOtpResponse.class)
        .addMappings(kfsOtpValidationResponse -> kfsOtpValidationResponse.map(resp -> resp.getMetadata().getStatus(), KfsSubmitOtpResponse::setStatus))
        .addMappings(kfsOtpValidationResponse -> kfsOtpValidationResponse.map(resp -> resp.getMetadata().getMessage(), KfsSubmitOtpResponse::setMessage));
  }

  public PanNumberVerificationRequest transformPanRequest(PanVerificationRequest panVerificationRequest) {
    return mapper.map(panVerificationRequest, PanNumberVerificationRequest.class);
  }

  public com.flipkart.fintech.pandora.api.model.pl.response.PanVerificationResponse transformPanResponse(PanVerificationResponse response) {
    return mapper.map(response, com.flipkart.fintech.pandora.api.model.pl.response.PanVerificationResponse.class);
  }

  public InitialOfferGenerationRequest transformInitialOfferGenerationRequest(com.flipkart.fintech.pandora.api.model.pl.request.InitialOfferGenerationRequest initialOfferGenerationRequest) {
    return mapper.map(initialOfferGenerationRequest, InitialOfferGenerationRequest.class);
  }

  public com.flipkart.fintech.pandora.api.model.pl.response.InitialOfferGenerationResponse transformInitialOfferGenerationRespnose(InitialOfferGenerationResponse initialOfferGenerationResponse) {
    return mapper.map(initialOfferGenerationResponse, com.flipkart.fintech.pandora.api.model.pl.response.InitialOfferGenerationResponse.class);
  }

  public StatusCheckResponse transformStatusResponse(CheckApprovalStatusResponse checkApprovalStatusResponse) {
    if(checkApprovalStatusResponse != null && checkApprovalStatusResponse.getVkycStatus() != null) {
        return mapper.map(checkApprovalStatusResponse, VkycStatusCheckResponse.class);
    }
    return mapper.map(checkApprovalStatusResponse, InitialOfferGenerationStatusResponse.class);
  }

  public SearchCkycResponse transformCkycResponse(CkycDownloadResponse ckycDownloadResponse) {
    return mapper.map(ckycDownloadResponse, SearchCkycResponse.class);
  }

  public CheckApprovalStatusRequest transformStatusCheckRequest(StatusCheckRequest statusCheckRequest) {
    return mapper.map(statusCheckRequest, CheckApprovalStatusRequest.class);
  }

  public SearchCkycRequest transformCkycSearchRequest(com.flipkart.fintech.pandora.api.model.pl.request.SearchCkycRequest searchCkycRequest) {
    return mapper.map(searchCkycRequest, SearchCkycRequest.class);
  }

  public AadhaarOtpGenerationRequest transformAadhaarGenerateOtpRequest(AadhaarGenerateOtpRequest aadhaarGenerateOtpRequest) {
    return mapper.map(aadhaarGenerateOtpRequest, AadhaarOtpGenerationRequest.class);
  }

  public AadhaarGenerateOtpResponse transformAadhaarGenerateOtpResponse(AadhaarOtpGenerationResponse aadhaarOtpGenerationResponse) {
    return mapper.map(aadhaarOtpGenerationResponse.getGenerateOTPResponse(), AadhaarGenerateOtpResponse.class);

  }

  public AadhaarKycDetailsRequest transformAadhaarValidateOtpRequest(AadhaarValidateOtpRequest aadhaarValidateOtpRequest) {
    return mapper.map(aadhaarValidateOtpRequest, AadhaarKycDetailsRequest.class);
  }

  public AadhaarValidateOtpResponse transformAadhaarValidateOtpResponse(AadhaarKycDetailsResponse aadhaarKycDetailsResponse) {
    return mapper.map(aadhaarKycDetailsResponse, AadhaarValidateOtpResponse.class);
  }

  public com.flipkart.fintech.pandora.idfc.client.request.StartLoanRequest transformStartLoanRequest(StartLoanRequest startLoanRequest) {
    return mapper.map(startLoanRequest, com.flipkart.fintech.pandora.idfc.client.request.StartLoanRequest.class);
  }

  public GenericStatusResponse transformStartLoanResponse(StartLoanResponse startLoanResponse) {
    return mapper.map(startLoanResponse, GenericStatusResponse.class);
  }

  public OfflineUploadDataRequest transformOfflineDataUploadRequest(OfflineDataUploadRequest offlineDataUploadRequest) {
    return mapper.map(offlineDataUploadRequest, OfflineUploadDataRequest.class);
  }

  public GenericStatusResponse transformUploadDataResponse(OfflineDataUploadResponse offlineDataUploadResponse) {
    return mapper.map(offlineDataUploadResponse, GenericStatusResponse.class);
  }

  public PositiveConfirmationStatusRequest transformPositiveConfirmationRequest(StatusCheckRequest statusCheckRequest) {
    PositiveConfirmationStatusRequest positiveConfirmationStatusRequest = new PositiveConfirmationStatusRequest();
    positiveConfirmationStatusRequest.setSource(IDFC_SOURCE);
    positiveConfirmationStatusRequest.setReqId(statusCheckRequest.getApplicationId());
    return positiveConfirmationStatusRequest;
  }

  public PositiveConfirmationResponse transformPositiveConfirmationResponse(PositiveConfirmationStatusResponse clientResponse) {
    return mapper.map(clientResponse, PositiveConfirmationResponse.class);
  }

  public PennyDropRequest transformPennyDropRequest(com.flipkart.fintech.pandora.api.model.pl.request.PennyDropRequest pennyDropRequest) {
    PennyDropRequest result = mapper.map(pennyDropRequest, PennyDropRequest.class);
    result.setSource(IDFC_SOURCE);
    result.setIdempotentId(pennyDropRequest.getCustBankAccNo());
    return result;
  }

  public com.flipkart.fintech.pandora.api.model.pl.response.PennyDropResponse transformPennyDropResponse(PennyDropResponse response) {
    return mapper.map(response, com.flipkart.fintech.pandora.api.model.pl.response.PennyDropResponse.class);
  }

  public KfsOtpGenerationRequest transformKfsOtpRequest(KfsGenerateOtpRequest amsRequest) {
    return mapper.map(amsRequest, KfsOtpGenerationRequest.class);
  }

  public KfsGenerateOtpResponse transformKfsOtpResponse(KfsOtpGenerationResponse clientResponse) {
    return mapper.map(clientResponse, KfsGenerateOtpResponse.class);
  }

  public KfsOtpValidationRequest transformKfsSubmitOtpRequest(KfsSubmitOtpRequest amsRequest) {
    KfsOtpValidationRequest result = mapper.map(amsRequest, KfsOtpValidationRequest.class);
    result.setCallId(callerId);
    return result;
  }

  public KfsSubmitOtpResponse transformKfsSubmitOtpResponse(KfsOtpValidationResponse clientResponse) {
    return mapper.map(clientResponse, KfsSubmitOtpResponse.class);
  }

  public com.flipkart.fintech.pandora.idfc.client.request.AutoDisbursalRequest transformAutoDisbursalRequest(AutoDisbursalRequest amsRequest) {
    try {
      return mapper.map(amsRequest,
          com.flipkart.fintech.pandora.idfc.client.request.AutoDisbursalRequest.class);
    } catch (Exception e) {
      log.error("Error while transforming AutoDisbursalRequest", e);
      throw e;
    }
  }

  public AutoDisbursalResponse transformAutoDisbursalResponse(com.flipkart.fintech.pandora.idfc.client.response.AutoDisbursalResponse idfcResponse) {
    return mapper.map(idfcResponse, AutoDisbursalResponse.class);
  }

  public LoanOnboardingRequest transformLoanOnboardingRequest(com.flipkart.fintech.pandora.api.model.pl.request.LoanOnboardingRequest amsRequest) {
    return mapper.map(amsRequest, LoanOnboardingRequest.class);
  }

  public LoanOnboardingResponse transformLoanOnboardingResponse(com.flipkart.fintech.pandora.idfc.client.response.LoanOnboardingResponse idfcResponse) {
    return mapper.map(idfcResponse, LoanOnboardingResponse.class);
  }

  public IfscDetailsRequest transformIfscRequest(IfscRequest amsRequest) {
    return mapper.map(amsRequest, IfscDetailsRequest.class);
  }

  public IfscResponse transformIfscResponse(IfscDetailsResponse clientResponse) {
    return mapper.map(clientResponse, IfscResponse.class);
  }

  public com.flipkart.fintech.pandora.idfc.client.request.EmandateVerificationRequest transformEmandateVerificationRequest(EmandateVerificationRequest request) {
    return mapper.map(request, com.flipkart.fintech.pandora.idfc.client.request.EmandateVerificationRequest.class);
  }

  public EmandateVerificationResponse transformEmandateVerificationResponse(IdfcEmandateVerificationResponse idfcEmandateVerificationResponse) {
    return mapper.map(idfcEmandateVerificationResponse, EmandateVerificationResponse.class);
  }

  public LoanUtilityResponse transformGetPaymentDetailsResponse(IdfcLoanUtilityResponse loanUtility) {
    return mapper.map(loanUtility, LoanUtilityResponse.class);
  }

  public IdfcLoanUtilityRequest transformGetPaymentDetailsRequest(LoanUtilityRequest request) {
    return mapper.map(request, IdfcLoanUtilityRequest.class);
  }

  private static class PaymentDetailsUtilityRequestConverter implements Converter<LoanUtilityRequest, IdfcLoanUtilityRequest> {

    @Override
    public IdfcLoanUtilityRequest convert(MappingContext<LoanUtilityRequest, IdfcLoanUtilityRequest> context) {
      LoanUtilityRequest source = context.getSource();
      IdfcLoanUtilityRequest target = new IdfcLoanUtilityRequest();
      target.setEntityName(IDFC_SOURCE);
      target.setRequestCode(IDFC_PAYMENT_DETAILS_REQUEST_CODE);
      target.setAgreementId(source.getAgreementId());
      return target;
    }
  }

  private static class PaymentDetailsUtilityResponseConverter implements Converter<IdfcLoanUtilityResponse, LoanUtilityResponse> {

    @Override
    public LoanUtilityResponse convert(MappingContext<IdfcLoanUtilityResponse, LoanUtilityResponse> context) {
      IdfcLoanUtilityResponse source = context.getSource();
      LoanUtilityResponse target = new LoanUtilityResponse();
      target.setStatus(source.getMetaData().getStatus());
      target.setVersion(source.getMetaData().getVersion());
      target.setTime(source.getMetaData().getTime());
      target.setMessage(source.getMetaData().getMessage());
      if(source.getResourceData() != null) {
        List<LoanUtilityResource> paymentDetailResource = source.getResourceData()
            .stream()
            .map(this::convertResourceData)
            .collect(Collectors.toList());
        target.setResourceData(paymentDetailResource.get(paymentDetailResource.size() - 1));
      }
      return target;
    }

    private LoanUtilityResource convertResourceData(IdfcLoanUtilityResourceData idfcLoanUtilityResourceData) {
      LoanUtilityResource result = new LoanUtilityResource();
      result.setUtrNo(idfcLoanUtilityResourceData.getUtrNo());
      result.setPaymentAmount(idfcLoanUtilityResourceData.getPaymentAmt());
      result.setPaymentDate(idfcLoanUtilityResourceData.getPaymentDate());
      result.setPaymentStatus(idfcLoanUtilityResourceData.getPaymentStatus());
      return result;
    }
  }

  private static class EmandateVerificationResponseConverter implements Converter<IdfcEmandateVerificationResponse, EmandateVerificationResponse> {

    @Override
    public EmandateVerificationResponse convert(MappingContext<IdfcEmandateVerificationResponse, EmandateVerificationResponse> context) {
      Set<String> retryStatusCodes = Stream
              .of("0601", "0602")
              .collect(Collectors.toSet());
      IdfcEmandateVerificationResponse source = context.getSource();
      EmandateVerificationResponse target = new EmandateVerificationResponse();
      target.setStatus(source.getStatus());
      target.setMessage(source.getTransactionMessage());
      target.setTimestamp(source.getTimestamp());
      if (retryStatusCodes.contains(source.getTransactionCode()) || noResponseFound(source)) {
        target.setEMandateStatus("retry");
      } else {
        target.setEMandateStatus(source.getTransactionStatus());
      }
      target.setEMandateStatusCode(source.getTransactionCode());
      return target;
    }

    private boolean noResponseFound(IdfcEmandateVerificationResponse source) {
      return "Failure".equals(source.getStatus()) && "No Response Found !!!".equals(source.getResponseMessage());
    }
  }

  private static class EmandateVerificationRequestConverter implements Converter<EmandateVerificationRequest, com.flipkart.fintech.pandora.idfc.client.request.EmandateVerificationRequest> {

    @Override
    public com.flipkart.fintech.pandora.idfc.client.request.EmandateVerificationRequest convert(MappingContext<EmandateVerificationRequest, com.flipkart.fintech.pandora.idfc.client.request.EmandateVerificationRequest> context) {
      EmandateVerificationRequest source = context.getSource();
      com.flipkart.fintech.pandora.idfc.client.request.EmandateVerificationRequest target = new com.flipkart.fintech.pandora.idfc.client.request.EmandateVerificationRequest();
      target.setConsumerId(source.getApplicationId());
      target.setMerchantCode(IDFC_SOURCE);
      target.setMerchantTransactionId(source.getMerchantTxnId());
      return target;
    }
  }

  private static class IfscResponseConverter implements Converter<IfscDetailsResponse, IfscResponse> {

    @Override
    public IfscResponse convert(MappingContext<IfscDetailsResponse, IfscResponse> context) {
        IfscDetailsResponse source = context.getSource();
        IfscResponse target = new IfscResponse();
        IfscBankDetails bankDetails = new IfscBankDetails();
        bankDetails.setName(source.getBankname());
        bankDetails.setBranch(source.getBankbranch());
        bankDetails.setCity(source.getCity());
        target.setBankDetails(bankDetails);

        IfscAvailableModes availableModes = new IfscAvailableModes();
        if(source.getDebitCardCode() != null) {
          availableModes.setDebitCard(source.getDebitCardCode());
        }
        if(source.getNetBankCode() != null) {
          availableModes.setNetBanking(source.getNetBankCode());
        }
        target.setAvailableModes(availableModes);
        return target;
    }
  }

  private static class IfscRequestConverter implements Converter<IfscRequest, IfscDetailsRequest> {

    @Override
    public IfscDetailsRequest convert(MappingContext<IfscRequest, IfscDetailsRequest> context) {
      IfscRequest source = context.getSource();
      IfscDetailsRequest target = new IfscDetailsRequest();
      Attributes attributes = new Attributes();
      attributes.setType("Bank_Detail__c");
      target.setAttributes(attributes);
      String id = UUID.randomUUID().toString().replaceAll("-", "");
      target.setId(id);
      target.setIFSCCodeC(source.getIfscCode());
      return target;
    }
  }

  private static class LoanOnboardingResponseConverter implements Converter<com.flipkart.fintech.pandora.idfc.client.response.LoanOnboardingResponse, LoanOnboardingResponse> {

    private final ModelMapper loanOnboardingMapper;

    LoanOnboardingResponseConverter() {
      loanOnboardingMapper = new ModelMapper();
      loanOnboardingMapper.getConfiguration().setMatchingStrategy(MatchingStrategies.STRICT);
      loanOnboardingMapper.createTypeMap(KfsDetail.class, com.flipkart.fintech.pandora.api.model.pl.response.KfsDetail.class);
      loanOnboardingMapper.createTypeMap(RepaySchedule.class, com.flipkart.fintech.pandora.api.model.pl.response.RepaySchedule.class);
    }

    @Override
    public LoanOnboardingResponse convert(MappingContext<com.flipkart.fintech.pandora.idfc.client.response.LoanOnboardingResponse, LoanOnboardingResponse> context) {
      try {
        com.flipkart.fintech.pandora.idfc.client.response.LoanOnboardingResponse source = context.getSource();
        LoanOnboardingResponse target = new LoanOnboardingResponse();
        OnboardingResult result = new OnboardingResult();
        OnboardingResponse response = new OnboardingResponse();
        if("ERROR".equalsIgnoreCase(source.getStatus())) {
          result.setStatus("failure");
          result.setMsg(source.getMessage());
          target.setResult(result);
          return target;
        }
        String chargeAmount = source.getResponse().getCharges().get(0).getChargeAmount();
        response.setKfsDetails(source.getResponse().getKfsDetails()
            .stream()
            .filter(Objects::nonNull)
            .map(k -> loanOnboardingMapper.map(k,
                com.flipkart.fintech.pandora.api.model.pl.response.KfsDetail.class))
            .peek(kfs -> kfs.setProcessFee(chargeAmount))
            .peek(this::setStaticValues)
            .collect(Collectors.toList()));
        response.setRepaySchedule(
            source.getResponse().getRepaySchedule()
                .stream()
                .filter(Objects::nonNull)
                .map(rs -> loanOnboardingMapper.map(rs,
                    com.flipkart.fintech.pandora.api.model.pl.response.RepaySchedule.class))
                .collect(Collectors.toList())
        );
        response.setEntityReqId(source.getSourceRequestId());
        response.setLoanId(source.getResponse().getRepaySchedule().get(0).getLoanId());
        result.setResponse(response);
        result.setMsg(source.getMessage());
        result.setStatus("success");
        target.setResult(result);
        return target;
      } catch (Exception e) {
        log.error("Error while converting LoanOnboardingResponse", e);
        throw e;
      }
    }

    private void setStaticValues(com.flipkart.fintech.pandora.api.model.pl.response.KfsDetail kfs) {
      kfs.setBankName("IDFC FIRST Bank Ltd.");
      kfs.setBankAdd("KRM Tower, 7th Floor, No. 1, Harrington Road, Chetpet, Chennai - 600031");
      kfs.setBankDetails(kfs.getBankName() + ", " + kfs.getBankAdd());
      kfs.setPreEmiCharges("0");
      kfs.setBounceCharge("500+gst");
      kfs.setLatePayCharge("2% of unpaid EMI + GST");
      kfs.setForeclosureCharge("5% of POS exclusive of GST");
      kfs.setOverdueCharges("NA");
      kfs.setCoolOffPeriod("3 days");
      kfs.setLspDetails("SCAPIC INNOVATIONS PRIVATE LIMITED");
      kfs.setNodalDetails(kfs.getNameNodal() + ", "  + kfs.getDesignationNodal() + ", " + kfs.getAddNodal() + ", " + kfs.getPhoneNodal());
    }
  }

  private static class LoanOnboardingRequestConverter implements Converter<com.flipkart.fintech.pandora.api.model.pl.request.LoanOnboardingRequest, LoanOnboardingRequest> {

    private final ModelMapper loanOnboardingRequestMapper;

    private final SchemeFinder schemeFinder;

    private final Map<String, String> kycTypes;

    private final Map<String, DateTimeFormatter> dateOfBirthConverter;
    private final Converter<String, String> epochToDateTimeConverter;
    private final UserDataClient userDataClient;

    LoanOnboardingRequestConverter(SchemeFinder schemeFinder, UserDataClient userDataClient) {
        this.userDataClient = userDataClient;
        epochToDateTimeConverter = context -> {
        long epochMilli = Long.parseLong(context.getSource());
        Instant instant = Instant.ofEpochMilli(epochMilli);
        ZonedDateTime utc = ZonedDateTime.ofInstant(instant, java.time.ZoneOffset.UTC);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS");
        return utc.format(formatter);
      };
      loanOnboardingRequestMapper = new ModelMapper();
      loanOnboardingRequestMapper.getConfiguration().setMatchingStrategy(MatchingStrategies.STRICT);
      loanOnboardingRequestMapper
          .typeMap(LoanOnboardingConsents.class, CustomerConsents.class)
          .addMappings(mapper -> {
            mapper.using(epochToDateTimeConverter).map(LoanOnboardingConsents::getPanVerifyConsentTimestamp, CustomerConsents::setPanVerifyConsentTimestamp);
            mapper.using(epochToDateTimeConverter).map(LoanOnboardingConsents::getBureauPullConsentTimestamp, CustomerConsents::setBureauPullConsentTimestamp);
            mapper.using(epochToDateTimeConverter).map(LoanOnboardingConsents::getUwDataSharingConsentTimestamp, CustomerConsents::setUwDataSharingConsentTimestamp);
            mapper.using(epochToDateTimeConverter).map(LoanOnboardingConsents::getKycInitConsentTimestamp, CustomerConsents::setKycInitConsentTimestamp);
            mapper.using(epochToDateTimeConverter).map(LoanOnboardingConsents::getKycAcceptConsentTimestamp, CustomerConsents::setKycAcceptConsentTimestamp);
            mapper.using(epochToDateTimeConverter).map(LoanOnboardingConsents::getEMandateConsentTimestamp, CustomerConsents::setEMandateConsentTimestamp);
          });
      this.schemeFinder = schemeFinder;
      kycTypes = new HashMap<>();
      dateOfBirthConverter = new HashMap<>();
        initKycTypes();
      initDateOfBirthConverter();
    }

    private void initDateOfBirthConverter() {
      dateOfBirthConverter.put("ckyc", DateTimeFormatter.ofPattern("dd-MMM-yyyy"));
      dateOfBirthConverter.put("ekyc", DateTimeFormatter.ofPattern("dd-MM-yyyy"));
      dateOfBirthConverter.put("binh", DateTimeFormatter.ofPattern("dd/MM/yyyy"));
    }

    private void initKycTypes() {
      kycTypes.put("ckyc", "C");
      kycTypes.put("ekyc", "E");
      kycTypes.put("okyc", "O");
      kycTypes.put("binh", "B");
    }


    @Override
    public LoanOnboardingRequest convert(MappingContext<com.flipkart.fintech.pandora.api.model.pl.request.LoanOnboardingRequest, LoanOnboardingRequest> context) {
      try {
        com.flipkart.fintech.pandora.api.model.pl.request.LoanOnboardingRequest source = context.getSource();
        LoanOnboardingRequest target = new LoanOnboardingRequest();

        CustomerConsents consents = loanOnboardingRequestMapper.map(source.getCustomerConsents(), CustomerConsents.class);

        Section section = new Section();
        section.setCustomerConsents(consents);

        Application application = new Application();
        application.setSection(section);

        target.setApplication(application);

        Request request = new Request();
        request.setRequestType(LenderConstants.IDFC_REQUEST_TYPE);
        request.setSourceSystem(IDFC_SOURCE);
        request.setSourceRequestId(source.getApplicationId());
        section.setRequest(request);

        Sourcing sourcing = getSourcing(source);
        section.setSourcing(sourcing);

        LoanDetails loanDetails = getLoanDetails(source);

        section.setLoanDetails(loanDetails);

        CustomerDetail customerDetail = getCustomerDetail(source);

        section.setCustomerDetails(Collections.singletonList(customerDetail));

        List<Charge> chargeList = source.getCharges().stream()
            .filter(Objects::nonNull)
            .map(c -> getCharge(c, source.getLoanAmount()))
            .collect(Collectors.toList());
        chargeList.forEach(c -> c.setFundedFlag("Y"));
        target.getApplication().getSection().setCharges(chargeList);

        section.setAddresses(Collections.singletonList(source.getAddress()));

        LoanOnboardingAddress addressForUser = getAddressForUser(source);

        section.setAddresses(Collections.singletonList(addressForUser));

        return target;
      } catch (Exception e) {
        log.error("Error while converting LoanOnboardingRequest", e);
        throw e;
      }
    }

    private LoanOnboardingAddress getAddressForUser(com.flipkart.fintech.pandora.api.model.pl.request.LoanOnboardingRequest source) {
      UserData userData = userDataClient.getUserData(Merchant.getOrDefaultByValue(source.getMerchantId()), source.getAccountId(), source.getSmUserId(), PIIDataType.PLAINTEXT);
      AddressData addressData = userDataClient.getAddressData(Merchant.getOrDefaultByValue(source.getMerchantId()), source.getAccountId(), source.getSmUserId(), source.getAddressId(), PIIDataType.PLAINTEXT);
      LoanOnboardingAddress address = new LoanOnboardingAddress();
      address.setAddressType(LenderConstants.ADDRESS_TYPE);
      address.setAddressLine1(addressData.getAddressLine1());
      address.setCountry(addressData.getCountry());
      address.setPincode(addressData.getPincode());
      String primaryPhone = userData.getPrimaryPhone();
      if(primaryPhone.startsWith("+91")) {
        primaryPhone = primaryPhone.replaceFirst("\\+91", "");
      }
      address.setMobileNo(primaryPhone);
      address.setApplicantCount("1");
      return address;
    }

    private Sourcing getSourcing(com.flipkart.fintech.pandora.api.model.pl.request.LoanOnboardingRequest source) {
      Sourcing sourcing = new Sourcing();
      sourcing.setCustType(getCustomerType(source.getKycType().toLowerCase()));
      sourcing.setBranchID(IDFC_BRANCH_ID);
      sourcing.setProduct(IDFC_LOAN_PRODUCT);
      sourcing.setApplicationID(source.getLoanApplicationNo());
      sourcing.setCrnNo(source.getCrn());
      sourcing.setScheme(getSchemeFromRequest(source));
      ZonedDateTime utc = ZonedDateTime.ofInstant(Instant.now(), ZoneOffset.UTC);
      DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm:ss");
      formatter = formatter.withZone(TimeZone.getTimeZone("Asia/Kolkata").toZoneId());
      sourcing.setTimeStamp(utc.format(formatter));
      // TODO: Check if null is an okay scenario for IP address
      sourcing.setIpAddress(IPAddressParser.parseIpAddress(source.getIpAddress()).orElse(null));
      return sourcing;
    }

    private String getCustomerType(String kycType) {
      return "binh".equals(kycType) ? ETB_IDFC_CUST_TYPE : NTB_IDFC_CUST_TYPE;
    }

    private static LoanDetails getLoanDetails(com.flipkart.fintech.pandora.api.model.pl.request.LoanOnboardingRequest source) {
      LoanDetails loanDetails = new LoanDetails();
      loanDetails.setLoanCategory(IDFC_LOAN_CATEGORY);
      loanDetails.setLoanAmount(source.getLoanAmount());
      loanDetails.setTenure(source.getTenure());
      loanDetails.setRateEMIFlag(IDFC_RATE_EMI_FLAG);
      loanDetails.setInterestRate(source.getInterestRate());
      loanDetails.setPurposeOfLoan(IDFC_LOAN_PURPOSE);
      return loanDetails;
    }

    private CustomerDetail getCustomerDetail(com.flipkart.fintech.pandora.api.model.pl.request.LoanOnboardingRequest source) {
      CustomerDetail customerDetail = new CustomerDetail();
      customerDetail.setApplicantCount("1");
      customerDetail.setApplicantType("P");
      customerDetail.setCustomerId(source.getCrn());
      customerDetail.setTypeOfApplicantCode("I");
      customerDetail.setFirstName(source.getFullName());
      customerDetail.setGender(source.getGender());
      customerDetail.setKycType(getKycType(source.getKycType().toLowerCase()));
      customerDetail.setDateOfBirth(getDateOfBirth(source.getDob(), dateOfBirthConverter.get(source.getKycType().toLowerCase())));
      customerDetail.setFieldAge(getAge(customerDetail.getDateOfBirth()));
      customerDetail.setConstitution("1");
      customerDetail.setCkycNumber(source.getCkycNumber());
      if(source.getPan() != null) {
        customerDetail.setPanNo(source.getPan());
        customerDetail.setPanAvailable("Yes");
      }
      return customerDetail;
    }

    private String getKycType(String kycType) {
      if(kycTypes.containsKey(kycType)) {
        return kycTypes.get(kycType);
      }
      throw new IllegalArgumentException("Invalid kyc type");
    }

    private String getAge(String dateOfBirth) {
      LocalDate dob = LocalDate.parse(dateOfBirth, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
      LocalDate now = LocalDate.now();
      Period period = Period.between(dob, now);
      return String.valueOf(period.getYears());
    }

    private String getDateOfBirth(String dob, DateTimeFormatter inputDateFormat) {
      DateTimeFormatter outputDateFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd");
      return LocalDate.parse(dob, inputDateFormat).format(outputDateFormat);
    }

    private String getSchemeFromRequest(com.flipkart.fintech.pandora.api.model.pl.request.LoanOnboardingRequest source) {
      String eligibilityType = source.getCustomerEligibilityType();
      String pinType = source.getPinType();
      String kycType = source.getKycType().toLowerCase();
      if(eligibilityType != null && pinType != null) {
        Qualification qualification = Qualification.fromValue(eligibilityType.trim());
        Serviceability serviceability = Serviceability.fromValue(pinType.trim());
        if(kycType!=null && "binh".equals(kycType)){
           return String.valueOf(schemeFinder.getEtbId(qualification, serviceability));
        }
        return String.valueOf(schemeFinder.getId(qualification, serviceability));
      }
      throw new IllegalArgumentException("Invalid eligibility type or pin type");
    }

    private Charge getCharge(String chargeRate, String loanAmount) {
      Charge charge = new Charge();
      charge.setChargeId(LenderConstants.IDFC_CHARGE_ID);
      double currentCharge = Double.parseDouble(chargeRate);
      currentCharge /= 100.0;
      charge.setChargeAmount(String.valueOf(currentCharge * Double.parseDouble(loanAmount)));
      return charge;
    }
  }

  private static class AutoDisbursalResponseConverter implements Converter<com.flipkart.fintech.pandora.idfc.client.response.AutoDisbursalResponse, AutoDisbursalResponse> {

    private final ModelMapper autoDisbursalMapper;

    AutoDisbursalResponseConverter() {
      autoDisbursalMapper = new ModelMapper();
      autoDisbursalMapper.getConfiguration().setMatchingStrategy(MatchingStrategies.STRICT);
      autoDisbursalMapper.createTypeMap(KfsDetail.class, com.flipkart.fintech.pandora.api.model.pl.response.KfsDetail.class);
      autoDisbursalMapper.createTypeMap(AutoDisbursalCharge.class, com.flipkart.fintech.pandora.api.model.pl.response.Charge.class);
      autoDisbursalMapper.createTypeMap(RepaySchedule.class, com.flipkart.fintech.pandora.api.model.pl.response.RepaySchedule.class)
          .addMappings(mapper -> {
            mapper.map(RepaySchedule::getInstalmentNumber, com.flipkart.fintech.pandora.api.model.pl.response.RepaySchedule::setInstallmentNumber);
          });
      autoDisbursalMapper.createTypeMap(AutoDisbursalMetadata.class, Metadata.class);
    }

    @Override
    public AutoDisbursalResponse convert(MappingContext<com.flipkart.fintech.pandora.idfc.client.response.AutoDisbursalResponse, AutoDisbursalResponse> context) {
      com.flipkart.fintech.pandora.idfc.client.response.AutoDisbursalResponse source = context.getSource();
      AutoDisbursalResponse target = new AutoDisbursalResponse();
      target.setMetadata(autoDisbursalMapper.map(source.getMetadata(), Metadata.class));
      List<com.flipkart.fintech.pandora.api.model.pl.response.ResourceDatum> resourceData = source.getResourceData().stream()
          .filter(Objects::nonNull)
          .map(this::getResourceDatum)
          .collect(Collectors.toList());
      target.setResourceData(resourceData);
      return target;
    }

    private com.flipkart.fintech.pandora.api.model.pl.response.ResourceDatum getResourceDatum(ResourceDatum resourceDatum) {
      String processingFee = resourceDatum.getCharges().get(0).getChargeAmount();
      com.flipkart.fintech.pandora.api.model.pl.response.ResourceDatum result = autoDisbursalMapper.map(resourceDatum, com.flipkart.fintech.pandora.api.model.pl.response.ResourceDatum.class);
      result.setKfsDetails(resourceDatum.getKfsDetails().stream()
          .filter(Objects::nonNull)
          .map(k -> autoDisbursalMapper.map(k, com.flipkart.fintech.pandora.api.model.pl.response.KfsDetail.class))
          .peek(kfs -> kfs.setProcessFee(processingFee))
          .collect(Collectors.toList()));
      result.setCharges(resourceDatum.getCharges().stream()
          .filter(Objects::nonNull)
          .map(c -> autoDisbursalMapper.map(c, com.flipkart.fintech.pandora.api.model.pl.response.Charge.class))
          .collect(Collectors.toList()));
      result.setRepaySchedule(resourceDatum.getRepaySchedule().stream()
          .filter(Objects::nonNull)
          .map(rs -> autoDisbursalMapper.map(rs, com.flipkart.fintech.pandora.api.model.pl.response.RepaySchedule.class))
          .collect(Collectors.toList()));
      return result;
    }
  }

  private static class AutoDisbursalRequestConverter implements Converter<AutoDisbursalRequest, com.flipkart.fintech.pandora.idfc.client.request.AutoDisbursalRequest> {

    private final ModelMapper autoDisbursalMapper;
    AutoDisbursalRequestConverter() {
      Converter<String, String> epochToDateTimeConverter = new Converter<String, String>() {
        @Override
        public String convert(MappingContext<String, String> context) {
          long epochMilli = Long.parseLong(context.getSource());
          Instant instant = Instant.ofEpochMilli(epochMilli);
          ZonedDateTime utc = ZonedDateTime.ofInstant(instant, java.time.ZoneOffset.UTC);
          DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS");
          return utc.format(formatter);
        }
      };
      autoDisbursalMapper = new ModelMapper();
      autoDisbursalMapper.getConfiguration().setMatchingStrategy(MatchingStrategies.STRICT);
      autoDisbursalMapper
          .typeMap(AutoDisbursalConsent.class, Consent.class)
          .addMappings(mapper -> {
            mapper.using(epochToDateTimeConverter).map(AutoDisbursalConsent::getTncConsentTimestamp, Consent::setTncConsentTimestamp);
          });
    }

    @Override
    public com.flipkart.fintech.pandora.idfc.client.request.AutoDisbursalRequest convert(MappingContext<AutoDisbursalRequest, com.flipkart.fintech.pandora.idfc.client.request.AutoDisbursalRequest> context) {
      AutoDisbursalRequest source = context.getSource();
      com.flipkart.fintech.pandora.idfc.client.request.AutoDisbursalRequest target = new com.flipkart.fintech.pandora.idfc.client.request.AutoDisbursalRequest();
      target.setEntityCode(IDFC_SOURCE);
      target.setLoanId(source.getLoanId());
      target.setReqType(source.getReqType());
      target.setEntityReqId(source.getEntityReqId());
      target.setPreAuthId(source.getEntityReqId());
      if(source.getConsent().getTncConsent() != null) {
        AutoDisbursalConsent consent = source.getConsent();
        target.setConsent(autoDisbursalMapper.map(consent, Consent.class));
      }
      return target;
    }
  }

  private static class KfsGenerateOtpResponseConverter implements Converter<KfsOtpGenerationResponse, KfsGenerateOtpResponse> {

    @Override
    public KfsGenerateOtpResponse convert(MappingContext<KfsOtpGenerationResponse, KfsGenerateOtpResponse> context) {
      KfsOtpGenerationResponse source = context.getSource();
      KfsGenerateOtpResponse target = new KfsGenerateOtpResponse();
      target.setStatus(source.getMetadata().getStatus());
      target.setMessage(source.getMetadata().getMessage());
      target.setTime(source.getMetadata().getTime());
      return target;
    }
  }

  private static class KfsGenerateOtpRequestConverter implements Converter<KfsGenerateOtpRequest, KfsOtpGenerationRequest> {

    private final UserDataClient userDataClient;
    private final String callerId;

    KfsGenerateOtpRequestConverter(UserDataClient userDataClient, String callerId) {
        this.userDataClient = userDataClient;
        this.callerId = callerId;
    }

    @Override
    public KfsOtpGenerationRequest convert(MappingContext<KfsGenerateOtpRequest, KfsOtpGenerationRequest> context) {
      KfsGenerateOtpRequest source = context.getSource();
      KfsOtpGenerationRequest target = new KfsOtpGenerationRequest();
      target.setLinkData(source.getLinkData());
      target.setReferenceNumber(source.getReferenceNumber());
      target.setCallId(callerId);
      log.info("accountId for KFS: " + source.getAccountId());
      UserData userData = userDataClient.getUserData(Merchant.getOrDefaultByValue(source.getMerchantId()), source.getAccountId(), source.getSmUserId(), PIIDataType.PLAINTEXT);
      SendSms sendSms = new SendSms();
      sendSms.setText(getTextFromId());
      String primaryPhone = userData.getPrimaryPhone();
      if(Objects.nonNull(source.getSendSms()) && StringUtils.isNotBlank(source.getSendSms().getMobileNumber())){
        primaryPhone = source.getSendSms().getMobileNumber();
      }
      if(primaryPhone.startsWith("+91")) {
        primaryPhone = primaryPhone.replaceFirst("\\+91", "");
      }
      sendSms.setMobileNumber(Long.valueOf(primaryPhone));
      target.setSendSms(sendSms);
      if (StringUtils.isNotBlank(source.getVerifyMobileNumber())) {
        target.setVerifyMobileNumber(source.getVerifyMobileNumber());
      }
      return target;
    }

    private String getTextFromId() {
      return "Dear Customer, One Time Password (OTP) to complete your verification transaction is {OTP}. It is valid for {EXPIRY_MINS} minutes.";
    }
  }

  private static class OfflineDataUploadRequestConverter implements Converter<OfflineDataUploadRequest, OfflineUploadDataRequest> {

    private final DocumentService documentService;
    private final ModelMapper offlineDataUploadMapper;
    OfflineDataUploadRequestConverter(DocumentService documentService) {
      offlineDataUploadMapper = new ModelMapper();
      offlineDataUploadMapper.getConfiguration().setMatchingStrategy(MatchingStrategies.STRICT);
      offlineDataUploadMapper.createTypeMap(UploadDocument.class, OfflineUploadDocument.class);
      this.documentService = documentService;
    }

    @Override
    public OfflineUploadDataRequest convert(MappingContext<OfflineDataUploadRequest, OfflineUploadDataRequest> context) {
      OfflineDataUploadRequest source = context.getSource();
      OfflineUploadDataRequest target = new OfflineUploadDataRequest();
      target.setRequestId(source.getApplicationId());
      target.setSource(IDFC_SOURCE);
      target.setLoanId(source.getLoanApplicationNo());
      target.setKycMode(source.getKycMode());
      List<com.flipkart.fintech.pandora.idfc.client.request.OfflineUploadDocument> documents = source.getUploadDocuments().stream()
          .filter(Objects::nonNull)
          .map(this::getOfflineUploadDocument)
          .collect(Collectors.toList());
      target.setDocuments(documents);
      target.setData(Collections.singletonList(new OfflineUploadData("KYC", "livelinessResponse", "HYPERVERGE", "")));
      return target;
    }

    private OfflineUploadDocument getOfflineUploadDocument(UploadDocument document) {
      OfflineUploadDocument result = offlineDataUploadMapper.map(document, OfflineUploadDocument.class);
      result.setDocumentContent(getDocumentContent(document.getDocumentId()));
      result.setFileExtension("png");
      result.setDocumentName(getDocumentName(document.getDocumentId()));
      result.setSource(IDFC_SOURCE);
      return result;
    }

    private String getDocumentName(String documentId) {
      log.info("documentId length: " + documentId);
      String applicationId = documentId.split("/")[1];
      return "selfie_" + applicationId;
    }

    private String getDocumentContent(String documentId) {
      DocumentInteractionResponse documentInteractionResponse = documentService.getDocument(documentId);
      return Base64.getEncoder().encodeToString(documentInteractionResponse.getDocument());
    }
  }

  private static class StartLoanResponseConverter implements Converter<StartLoanResponse, GenericStatusResponse> {

    @Override
    public GenericStatusResponse convert(MappingContext<StartLoanResponse, GenericStatusResponse> context) {
      StartLoanResponse source = context.getSource();
      GenericStatusResponse target = new GenericStatusResponse();
      target.setStatus(source.getStatus());
      return target;
    }
  }

  private static class StartLoanRequestConverter implements Converter<StartLoanRequest, com.flipkart.fintech.pandora.idfc.client.request.StartLoanRequest> {
    private final ModelMapper startLoanMapper;

    StartLoanRequestConverter() {
      this.startLoanMapper = new ModelMapper();
      startLoanMapper.getConfiguration().setMatchingStrategy(MatchingStrategies.STRICT);
      TypeMap<UnderWriting, com.flipkart.fintech.pandora.idfc.client.request.UnderWriting> typeMap = startLoanMapper.createTypeMap(UnderWriting.class, com.flipkart.fintech.pandora.idfc.client.request.UnderWriting.class);
      TypeMap<ComparePhoto, com.flipkart.fintech.pandora.idfc.client.request.ComparePhoto> comparePhotoTypeMap = startLoanMapper.createTypeMap(ComparePhoto.class, com.flipkart.fintech.pandora.idfc.client.request.ComparePhoto.class);
      TypeMap<LivenessCheck, com.flipkart.fintech.pandora.idfc.client.request.LivenessCheck> livenessCheckTypeMap = startLoanMapper.createTypeMap(LivenessCheck.class, com.flipkart.fintech.pandora.idfc.client.request.LivenessCheck.class);
    }

    @Override
    public com.flipkart.fintech.pandora.idfc.client.request.StartLoanRequest convert(MappingContext<StartLoanRequest, com.flipkart.fintech.pandora.idfc.client.request.StartLoanRequest> context) {
      StartLoanRequest source = context.getSource();
      com.flipkart.fintech.pandora.idfc.client.request.StartLoanRequest target = new com.flipkart.fintech.pandora.idfc.client.request.StartLoanRequest();
      target.setReqId(source.getApplicationId());
      target.setSource(IDFC_SOURCE);
      target.setKycMode(source.getKycMode());
      if(source.getUnderWriting()!=null && !StringUtils.isBlank(source.getUnderWriting().getLoanAmount()) && !StringUtils.isBlank(source.getUnderWriting().getEligibleLoanAmount()) && !StringUtils.isBlank(source.getUnderWriting().getTenure()) && !StringUtils.isBlank(source.getUnderWriting().getInterestRate())) {
        target.setUnderWriting(startLoanMapper.map(source.getUnderWriting(), com.flipkart.fintech.pandora.idfc.client.request.UnderWriting.class));
      }
      if(source.getComparePhoto()!=null && !StringUtils.isBlank(source.getComparePhoto().getSource()) && !StringUtils.isBlank(source.getComparePhoto().getMatch()) && !StringUtils.isBlank(source.getComparePhoto().getMatchScore()) && !StringUtils.isBlank(source.getComparePhoto().getFinalPhotoMatchStatus())) {
        ComparePhoto sourceComparePhoto = getComparePhoto(source);
        target.setComparePhoto(startLoanMapper.map(sourceComparePhoto, com.flipkart.fintech.pandora.idfc.client.request.ComparePhoto.class));
      }
      if(source.getLivenessCheck()!=null && !StringUtils.isBlank(source.getLivenessCheck().getSource()) && !StringUtils.isBlank(source.getLivenessCheck().getLivenessScore()) && !StringUtils.isBlank(source.getLivenessCheck().getFinalLiveCheckStatus())) {

        LivenessCheck sourceLivenessCheck = getLivenessCheck(source);
        com.flipkart.fintech.pandora.idfc.client.request.LivenessCheck livenessCheck = startLoanMapper.map(sourceLivenessCheck, com.flipkart.fintech.pandora.idfc.client.request.LivenessCheck.class);
        livenessCheck.setIsLive("Y");
        target.setLivenessCheck(livenessCheck);
      }
      return target;
    }

    private ComparePhoto getComparePhoto(StartLoanRequest source) {
      ComparePhoto sourceComparePhoto = source.getComparePhoto() == null ? new ComparePhoto() : source.getComparePhoto();
      sourceComparePhoto.setSource(sourceComparePhoto.getSource());
      sourceComparePhoto.setMatch("yes".equalsIgnoreCase(sourceComparePhoto.getMatch()) ? "Y" : "N");
      sourceComparePhoto.setFinalPhotoMatchStatus(getFinalPhotoMatchStatus(sourceComparePhoto));
      sourceComparePhoto.setMatchScore(sourceComparePhoto.getMatchScore());
      return sourceComparePhoto;
    }

    private String getFinalPhotoMatchStatus(ComparePhoto sourceComparePhoto) {
      return "pass".equalsIgnoreCase(sourceComparePhoto.getFinalPhotoMatchStatus()) ? "PASS": "FAIL";
    }

    private LivenessCheck getLivenessCheck(StartLoanRequest source) {
      LivenessCheck sourceLivenessCheck = source.getLivenessCheck() == null ? new LivenessCheck() : source.getLivenessCheck();
      sourceLivenessCheck.setSource(sourceLivenessCheck.getSource());
      sourceLivenessCheck.setIsLive(sourceLivenessCheck.getIsLive());
      sourceLivenessCheck.setFinalLiveCheckStatus(getFinalLiveCheckStatus(sourceLivenessCheck));
      sourceLivenessCheck.setLivenessScore(sourceLivenessCheck.getLivenessScore());
      return sourceLivenessCheck;
    }

    private String getFinalLiveCheckStatus(LivenessCheck sourceLivenessCheck) {
      return "auto_approved".equalsIgnoreCase(sourceLivenessCheck.getFinalLiveCheckStatus()) ? "PASS" : "FAIL";
    }
  }

  private static class AadhaarOtpValidationResponseConverter implements Converter<AadhaarKycDetailsResponse, AadhaarValidateOtpResponse> {

    private final ModelMapper validateOtpMapper;
    private final DocumentService documentService;

    AadhaarOtpValidationResponseConverter(DocumentService documentService) {
      validateOtpMapper = new ModelMapper();
      validateOtpMapper.getConfiguration().setMatchingStrategy(MatchingStrategies.STRICT);
      this.documentService = documentService;
      TypeMap<AadhaarKycDetailsAddress, AadhaarValidateOtpAddress> typeMap = validateOtpMapper.createTypeMap(AadhaarKycDetailsAddress.class, AadhaarValidateOtpAddress.class);

    }

    @Override
    public AadhaarValidateOtpResponse convert(MappingContext<AadhaarKycDetailsResponse, AadhaarValidateOtpResponse> context) {
      AadhaarKycDetailsResponse source = context.getSource();
      AadhaarValidateOtpResponse target = new AadhaarValidateOtpResponse();
      if(source.getGetKYCDetailServiceResponse().getResult() != null && "ERROR".equals(source.getGetKYCDetailServiceResponse().getResult())) {
        target.setStatus(source.getGetKYCDetailServiceResponse().getResult());
        target.setMessage(source.getGetKYCDetailServiceResponse().getReason());
        return target;
      }
      AadhaarValidateOtpKyc aadhaarValidateOtpKyc = new AadhaarValidateOtpKyc();
      aadhaarValidateOtpKyc.setName(source.getGetKYCDetailServiceResponse().getName());
      aadhaarValidateOtpKyc.setDob(source.getGetKYCDetailServiceResponse().getDob());
      aadhaarValidateOtpKyc.setGender(source.getGetKYCDetailServiceResponse().getGender());
      AadhaarValidateOtpAddress mapped = validateOtpMapper.map(source.getGetKYCDetailServiceResponse().getAddress(), AadhaarValidateOtpAddress.class);
      aadhaarValidateOtpKyc.setAddress(mapped);
      String image = source.getGetKYCDetailServiceResponse().getPht();
      String imageId = getImageIdFromImage(image, source);
      aadhaarValidateOtpKyc.setImageId(imageId);
      target.setKyc(aadhaarValidateOtpKyc);
      return target;
    }

    private String getImageIdFromImage(String image, AadhaarKycDetailsResponse source) {
      try {
        DocumentInteractionRequest documentRequest = getDocumentInteractionRequest(image, source);
        DocumentInteractionResponse documentInteractionResponse = documentService.uploadDocument(documentRequest);
        return documentInteractionResponse.getDocumentId();
      } catch(Exception e) {
        log.error("Error while uploading document, ", e);
        return "imageId";
      }
    }

    private static DocumentInteractionRequest getDocumentInteractionRequest(String image, AadhaarKycDetailsResponse source) {
      DocumentInteractionRequest documentRequest = new DocumentInteractionRequest();
      documentRequest.setAccountId(Optional.ofNullable(source.getAccountId()).orElse(""));
      documentRequest.setSmUserId(Optional.ofNullable(source.getSmUserId()).orElse(""));
      documentRequest.setDocumentType("KYC_IMAGE");
      log.info("Request Id for ekyc image: {}", source.getAmsId());
      documentRequest.setAmsId(source.getAmsId());
      documentRequest.setKey("");
      documentRequest.setPassword("");
      documentRequest.setPublicKeyRefId("");
      documentRequest.setContent(image.getBytes(StandardCharsets.UTF_8));
      return documentRequest;
    }

  }

  private static class AadhaarOtpValidationRequestConverter implements Converter<AadhaarValidateOtpRequest, AadhaarKycDetailsRequest> {

    private final String key;

    AadhaarOtpValidationRequestConverter(String key) {
      this.key = key;
    }

    @Override
    public AadhaarKycDetailsRequest convert(MappingContext<AadhaarValidateOtpRequest, AadhaarKycDetailsRequest> context) {
      AadhaarValidateOtpRequest source = context.getSource();
      AadhaarKycDetailsRequest target = new AadhaarKycDetailsRequest();
      GetKYCDetailServiceRequest getKYCDetailServiceRequest = new GetKYCDetailServiceRequest();
      getKYCDetailServiceRequest.setUid(encrypt(source.getAadhaarNo()));
      getKYCDetailServiceRequest.setOtp(source.getOtp());
      getKYCDetailServiceRequest.setOtpTxn(source.getTransactionId());
      getKYCDetailServiceRequest.setReqId(source.getApplicationId());
      getKYCDetailServiceRequest.setMobNo(
          LenderConstants.aadhaarNoVsPhoneNo.getOrDefault(source.getAadhaarNo(), "9189583407"));
      target.setGetKYCDetailServiceRequest(getKYCDetailServiceRequest);
      return target;
    }

    private String encrypt(String value) {
      try {
        byte[] encrypted = AESService.encryptEcb(key, value.getBytes(StandardCharsets.UTF_8));
        return DatatypeConverter.printBase64Binary(encrypted);
      } catch (Exception ex) {
        log.error("Error while encrypting aadhaar, ", ex);
        throw new RuntimeException(ex);
      }
    }
  }

  private static class AadhaarOtpGenerationResponseConverter implements Converter<GenerateOTPResponse, AadhaarGenerateOtpResponse> {

    @Override
    public AadhaarGenerateOtpResponse convert(MappingContext<GenerateOTPResponse, AadhaarGenerateOtpResponse> context) {
      GenerateOTPResponse source = context.getSource();
      AadhaarGenerateOtpResponse target = new AadhaarGenerateOtpResponse();
      target.setStatus(source.getSts());
      target.setTransactionId(source.getOtpTxn());
      return target;
    }
  }

  private static class AadhaarOtpGenerationRequestConverter implements Converter<AadhaarGenerateOtpRequest, AadhaarOtpGenerationRequest> {

    private final String key;

    AadhaarOtpGenerationRequestConverter(String key) {
      this.key = key;
    }

    @Override
    public AadhaarOtpGenerationRequest convert(MappingContext<AadhaarGenerateOtpRequest, AadhaarOtpGenerationRequest> context) {
      AadhaarGenerateOtpRequest source = context.getSource();
      AadhaarOtpGenerationRequest target = new AadhaarOtpGenerationRequest();
      GenerateOTPRequest generateOtpRequest = new GenerateOTPRequest();
      generateOtpRequest.setUid(encrypt(source.getAadhaarNo()));
      generateOtpRequest.setReqId(source.getApplicationId());
      target.setGenerateOTPRequest(generateOtpRequest);
      return target;
    }

    private String encrypt(String value) {
      try {
        byte[] encrypted = AESService.encryptEcb(key, value.getBytes(StandardCharsets.UTF_8));
        return DatatypeConverter.printBase64Binary(encrypted);
      } catch (Exception ex) {
        log.error("Error while encrypting aadhaar, ", ex);
        throw new RuntimeException(ex);
      }
    }
  }

  private static class SearchCkycRequestConverter implements Converter<com.flipkart.fintech.pandora.api.model.pl.request.SearchCkycRequest, SearchCkycRequest> {

    @Override
    public SearchCkycRequest convert(MappingContext<com.flipkart.fintech.pandora.api.model.pl.request.SearchCkycRequest, SearchCkycRequest> context) {
      com.flipkart.fintech.pandora.api.model.pl.request.SearchCkycRequest source = context.getSource();
      SearchCkycRequest target = new SearchCkycRequest();
      SearchCkycRequestBody searchCkycRequestBody = new SearchCkycRequestBody();
      searchCkycRequestBody.setRequestId(source.getApplicationId());
      searchCkycRequestBody.setSource(IDFC_SOURCE);
      List<SearchCkycRequestDetail> kycDetails = source.getDetails().stream()
          .filter(Objects::nonNull)
          .map(this::getCkycDetail)
          .collect(Collectors.toList());
      searchCkycRequestBody.setDetails(kycDetails);
      target.setSearchCkycRequestBody(searchCkycRequestBody);
      return target;
    }

    private SearchCkycRequestDetail getCkycDetail(CkycDetail detail) {
      SearchCkycRequestDetail searchCkycRequestDetail = new SearchCkycRequestDetail();
      searchCkycRequestDetail.setInputIdNo(detail.getPanNo());
      searchCkycRequestDetail.setInputIdType("C");
      searchCkycRequestDetail.setTransactionId(detail.getTransactionId());
      return searchCkycRequestDetail;
    }
  }

  private static class StatusCheckRequestConverter implements Converter<StatusCheckRequest, CheckApprovalStatusRequest> {

    @Override
    public CheckApprovalStatusRequest convert(MappingContext<StatusCheckRequest, CheckApprovalStatusRequest> context) {
      StatusCheckRequest source = context.getSource();
      CheckApprovalStatusRequest target = new CheckApprovalStatusRequest();
      target.setReqId(source.getApplicationId());
      return target;
    }
  }

  private static class CkycResponseConverter implements Converter<CkycDownloadResponse, SearchCkycResponse> {

      private final TypeMap<CKYCPersonalDetail, PersonalDetail> personalDetailTypeMap;
    private final TypeMap<CkycImage, ImageDetail> imageDetailTypeMap;
    private final TypeMap<CKYCRelatedPerson, RelatedPerson> relatedPersonDetailTypeMap;

    private final DocumentService documentService;

    CkycResponseConverter(DocumentService documentService) {
      this.documentService = documentService;
      ModelMapper ckycMapper = new ModelMapper();
      ckycMapper.getConfiguration().setMatchingStrategy(MatchingStrategies.STRICT);
      imageDetailTypeMap = ckycMapper.createTypeMap(CkycImage.class, ImageDetail.class);
      imageDetailTypeMap.addMappings(imageMapper -> imageMapper.map(CkycImage::getCKYCImageExtension, ImageDetail::setExtension));
      imageDetailTypeMap.addMappings(imageMapper -> imageMapper.map(CkycImage::getCKYCImageType, ImageDetail::setType));
      imageDetailTypeMap.addMappings(imageMapper -> imageMapper.map(CkycImage::getCKYCImageSequence, ImageDetail::setSequence));
      personalDetailTypeMap = ckycMapper.createTypeMap(CKYCPersonalDetail.class, PersonalDetail.class);
      personalDetailTypeMap.addMappings(pdMapper -> pdMapper.map(CKYCPersonalDetail::getCKYCFatherFullName, PersonalDetail::setCKycFatherFullName));
      personalDetailTypeMap.addMappings(pdMapper -> pdMapper.map(CKYCPersonalDetail::getCKYCFullName, PersonalDetail::setCKycFullName));
      personalDetailTypeMap.addMappings(pdMapper -> pdMapper.map(CKYCPersonalDetail::getCKYCNumber, PersonalDetail::setCKycNumber));
      personalDetailTypeMap.addMappings(pdMapper -> pdMapper.map(CKYCPersonalDetail::getCkycdob, PersonalDetail::setCkycdob));
      personalDetailTypeMap.addMappings(pdMapper -> pdMapper.map(CKYCPersonalDetail::getCKYCCorAddPinType, PersonalDetail::setCKycCorAddPinType));
      personalDetailTypeMap.addMappings(pdMapper -> pdMapper.map(CKYCPersonalDetail::getCKYCGender, PersonalDetail::setCKycGender));
      relatedPersonDetailTypeMap = ckycMapper.createTypeMap(CKYCRelatedPerson.class, RelatedPerson.class);
      relatedPersonDetailTypeMap.addMappings(rpdMapper -> rpdMapper.map(CKYCRelatedPerson::getCKYCRPSequence, RelatedPerson::setCkycRPSequence));
      relatedPersonDetailTypeMap.addMappings(rpdMapper -> rpdMapper.map(CKYCRelatedPerson::getCKYCRPRelation, RelatedPerson::setCkycRPRelation));
      relatedPersonDetailTypeMap.addMappings(rpdMapper -> rpdMapper.map(CKYCRelatedPerson::getCKYCRPCKYCNumber, RelatedPerson::setCkycRPckycNumber));
      relatedPersonDetailTypeMap.addMappings(rpdMapper -> rpdMapper.map(CKYCRelatedPerson::getCKYCRPNamePrefix, RelatedPerson::setCkycRPNamePrefix));
      relatedPersonDetailTypeMap.addMappings(rpdMapper -> rpdMapper.map(CKYCRelatedPerson::getCKYCRPFirstName, RelatedPerson::setCkycRPFirstName));
      relatedPersonDetailTypeMap.addMappings(rpdMapper -> rpdMapper.map(CKYCRelatedPerson::getCKYCRPMiddleName, RelatedPerson::setCkycRPMiddleName));
      relatedPersonDetailTypeMap.addMappings(rpdMapper -> rpdMapper.map(CKYCRelatedPerson::getCKYCRPLastName, RelatedPerson::setCkycRPLastName));
      relatedPersonDetailTypeMap.addMappings(rpdMapper -> rpdMapper.map(CKYCRelatedPerson::getCkycrppan, RelatedPerson::setCkycrppan));
      relatedPersonDetailTypeMap.addMappings(rpdMapper -> rpdMapper.map(CKYCRelatedPerson::getCKYCRPAadhar, RelatedPerson::setCkycRPAadhar));
      relatedPersonDetailTypeMap.addMappings(rpdMapper -> rpdMapper.map(CKYCRelatedPerson::getCKYCRPVoterId, RelatedPerson::setCkycRPVoterId));
      relatedPersonDetailTypeMap.addMappings(rpdMapper -> rpdMapper.map(CKYCRelatedPerson::getCkycrpnrega, RelatedPerson::setCkycrpnrega));
      relatedPersonDetailTypeMap.addMappings(rpdMapper -> rpdMapper.map(CKYCRelatedPerson::getCKYCRPPassportNumber, RelatedPerson::setCkycRPPassportNumber));
      relatedPersonDetailTypeMap.addMappings(rpdMapper -> rpdMapper.map(CKYCRelatedPerson::getCKYCRPPassportExpiryDate, RelatedPerson::setCkycRPPassportExpiryDate));
      relatedPersonDetailTypeMap.addMappings(rpdMapper -> rpdMapper.map(CKYCRelatedPerson::getCKYCRPDrivingLicenceNumber, RelatedPerson::setCkycRPDrivingLicenceNumber));
      relatedPersonDetailTypeMap.addMappings(rpdMapper -> rpdMapper.map(CKYCRelatedPerson::getCKYCRPDrivingLicenceExpiryDate, RelatedPerson::setCkycRPDrivingLicenceExpiryDate));
      relatedPersonDetailTypeMap.addMappings(rpdMapper -> rpdMapper.map(CKYCRelatedPerson::getCKYCRPProofOfIdName, RelatedPerson::setCkycRPProofOfIdName));
      relatedPersonDetailTypeMap.addMappings(rpdMapper -> rpdMapper.map(CKYCRelatedPerson::getCKYCRPProofOfIdNumber, RelatedPerson::setCkycRPProofOfIdNumber));
      relatedPersonDetailTypeMap.addMappings(rpdMapper -> rpdMapper.map(CKYCRelatedPerson::getCKYCRPSimplifiedIDType, RelatedPerson::setCkycRPSimplifiedIDType));
      relatedPersonDetailTypeMap.addMappings(rpdMapper -> rpdMapper.map(CKYCRelatedPerson::getCKYCRPSimplifiedIDNumber, RelatedPerson::setCkycRPSimplifiedIDNumber));
      relatedPersonDetailTypeMap.addMappings(rpdMapper -> rpdMapper.map(CKYCRelatedPerson::getCKYCRPDateofDeclaration, RelatedPerson::setCkycRPDateofDeclaration));
      relatedPersonDetailTypeMap.addMappings(rpdMapper -> rpdMapper.map(CKYCRelatedPerson::getCKYCRPPlaceofDeclaration, RelatedPerson::setCkycRPPlaceofDeclaration));
      relatedPersonDetailTypeMap.addMappings(rpdMapper -> rpdMapper.map(CKYCRelatedPerson::getCKYCRPKYCVerificationDate, RelatedPerson::setCkycRPKYCVerificationDate));
      relatedPersonDetailTypeMap.addMappings(rpdMapper -> rpdMapper.map(CKYCRelatedPerson::getCKYCRPTypeofDocSubmitted, RelatedPerson::setCkycRPTypeofDocSubmitted));
      relatedPersonDetailTypeMap.addMappings(rpdMapper -> rpdMapper.map(CKYCRelatedPerson::getCKYCRPKYCVerificationDate, RelatedPerson::setCkycRPKYCVerificationDate));
      relatedPersonDetailTypeMap.addMappings(rpdMapper -> rpdMapper.map(CKYCRelatedPerson::getCKYCRPKYCVerificationName, RelatedPerson::setCkycRPKYCVerificationName));
      relatedPersonDetailTypeMap.addMappings(rpdMapper -> rpdMapper.map(CKYCRelatedPerson::getCKYCRPKYCVerificationDesg, RelatedPerson::setCkycRPKYCVerificationDesg));
      relatedPersonDetailTypeMap.addMappings(rpdMapper -> rpdMapper.map(CKYCRelatedPerson::getCKYCRPKYCVerificationBranch, RelatedPerson::setCkycRPKYCVerificationBranch));
      relatedPersonDetailTypeMap.addMappings(rpdMapper -> rpdMapper.map(CKYCRelatedPerson::getCKYCRPKYCVerificationEmpcode, RelatedPerson::setCkycRPKYCVerificationEmpcode));
    }

    @Override
    public SearchCkycResponse convert(MappingContext<CkycDownloadResponse, SearchCkycResponse> context) {
      CkycDownloadResponse source = context.getSource();
      SearchCkycResponse target = new SearchCkycResponse();
      target.setPositiveConfirmation(source.getDownloadCkycResponse().getDetails().getPositiveConfirmation());
      target.setTransactionId(source.getDownloadCkycResponse().getDetails().getTransactionId());
      target.setTransactionStatus(source.getDownloadCkycResponse().getDetails().getTransactionStatus());

      SearchKyc searchKyc = new SearchKyc();
      List<PersonalDetail> personalDetails = source.getDownloadCkycResponse().getDetails()
          .getCKYCPersonalDetail().stream()
          .filter(Objects::nonNull)
          .map(personalDetailTypeMap::map)
          .collect(Collectors.toList());
      searchKyc.setPersonalDetails(personalDetails);
      List<ImageDetail> imageDetails = source.getDownloadCkycResponse().getDetails()
          .getCKYCImageDetails()
          .stream()
          .filter(ckycImageDetail -> ckycImageDetail != null && ckycImageDetail.getCkycImage() != null)
          .flatMap(ckycImageDetail -> ckycImageDetail.getCkycImage().stream())
          .filter(ckycImage -> ckycImage != null && "Photograph".equals(ckycImage.getCKYCImageType()))
          .map(ckycImage -> getImageDetail(ckycImage, source))
          .collect(Collectors.toList());
      searchKyc.setImages(imageDetails);
      List<RelatedPerson> relatedPersons = Optional.ofNullable(source.getDownloadCkycResponse())
          .map(DownloadCkycResponse::getDetails)
          .map(Details::getCKYCRelatedPersonDetails)
          .orElse(Collections.emptyList())
          .stream()
          .filter(Objects::nonNull)
          .map(CKYCRelatedPersonDetail::getCKYCRelatedPerson)
          .map(relatedPersonDetailTypeMap::map)
          .collect(Collectors.toList());
      searchKyc.setRelatedPersons(relatedPersons);
      target.setAddress(getCkycAddress(source));
      target.setKyc(searchKyc);
      return target;
    }

    private ImageDetail getImageDetail(CkycImage ckycImage, CkycDownloadResponse source) {
      ImageDetail result = imageDetailTypeMap.map(ckycImage);
      String imageId = getImageId(ckycImage, source);
      result.setId(imageId);
      return result;
    }

    @NotNull
    private static CkycAddress getCkycAddress(CkycDownloadResponse source) {
      CkycAddress ckycAddress = new CkycAddress();
      ckycAddress.setAddressLine1(
          source.getDownloadCkycResponse().getDetails().getCKYCPersonalDetail().get(0).getCKYCCorAdd1());
      ckycAddress.setCity(
          source.getDownloadCkycResponse().getDetails().getCKYCPersonalDetail().get(0).getCKYCCorAddCity());
      ckycAddress.setState(
          source.getDownloadCkycResponse().getDetails().getCKYCPersonalDetail().get(0).getCKYCCorAddState());
      ckycAddress.setCountry(
          source.getDownloadCkycResponse().getDetails().getCKYCPersonalDetail().get(0).getCKYCCorAddCountry());
      ckycAddress.setPincode(
          source.getDownloadCkycResponse().getDetails().getCKYCPersonalDetail().get(0).getCKYCCorAddPin());
      return ckycAddress;
    }

    private String getImageId(CkycImage image, CkycDownloadResponse source) {
      try {
        DocumentInteractionRequest documentRequest = getDocumentInteractionRequest(image, source);
        DocumentInteractionResponse documentInteractionResponse = documentService.uploadDocument(documentRequest);
        return documentInteractionResponse.getDocumentId();
      } catch(Exception e) {
        log.error("Error while uploading document, ", e);
        throw new RuntimeException(e);
      }
    }

    private static DocumentInteractionRequest getDocumentInteractionRequest(CkycImage image, CkycDownloadResponse source) {
      DocumentInteractionRequest documentRequest = new DocumentInteractionRequest();
      documentRequest.setAccountId(Optional.ofNullable(source.getAccountId()).orElse(""));
      documentRequest.setSmUserId(Optional.ofNullable(source.getSmUserId()).orElse(""));
      documentRequest.setDocumentType("KYC_IMAGE");
      log.info("Request Id for kyc image: {}", source.getRequestId());
      documentRequest.setAmsId(source.getRequestId());
      documentRequest.setKey("");
      documentRequest.setPassword("");
      documentRequest.setPublicKeyRefId("");
      documentRequest.setContent(image.getCKYCImageData().getBytes(StandardCharsets.UTF_8));
      return documentRequest;
    }
  }

  private static class VkycStatusConverter implements Converter<CheckApprovalStatusResponse, VkycStatusCheckResponse> {

    private final Map<String, VkycStatusEnum> statusMap;

    VkycStatusConverter(Map<String, VkycStatusEnum> statusMap) {
      this.statusMap = statusMap;
    }

    @Override
    public VkycStatusCheckResponse convert(MappingContext<CheckApprovalStatusResponse, VkycStatusCheckResponse> context) {
      CheckApprovalStatusResponse source = context.getSource();
      VkycStatusCheckResponse target = new VkycStatusCheckResponse();
      if(source.getResult() != null) {
        target.setStatus("REJECT");
        target.setRejectReason(source.getResult().getMessage());
        return target;
      }
      target.setStatus(source.getStatus());
      target.setCrn(source.getCrn());
      target.setLoanApplicationNo(source.getLoanApplicationNo());
      target.setReferenceId(source.getReferenceId());
      VkycStatusEnum vkycStatusEnum = statusMap.getOrDefault(source.getVkycStatus(), VkycStatusEnum.REJECTED);
      VkycStatus vkycStatus = new VkycStatus();
      vkycStatus.setStatus(vkycStatusEnum.getValue());
      vkycStatus.setMessage(source.getVkycStatus());
      target.setVkycStatus(vkycStatus);
      return target;
    }
  }

  private static class InitialOfferGenerationStatusConverter implements Converter<CheckApprovalStatusResponse, InitialOfferGenerationStatusResponse> {

    @Override
    public InitialOfferGenerationStatusResponse convert(MappingContext<CheckApprovalStatusResponse, InitialOfferGenerationStatusResponse> context) {
      CheckApprovalStatusResponse source = context.getSource();
      InitialOfferGenerationStatusResponse target = new InitialOfferGenerationStatusResponse();
      if(source.getResult() != null) {
        return getRejectReasonAndStatus(target, source);
      }

      String status = getApprovalStatus(source);
      target.setStatus(status);
      target.setInitialStatus(status);
      target.setCrn(source.getCrn());
      target.setLoanApplicationNo(source.getLoanApplicationNo());
      target.setReferenceId(source.getReferenceId());
      target.setCustomerEligibilityType(source.getCustomerEligibilityType());
      target.setOffers(getOffers(source));
      target.setRejectReason(source.getRejectReason());
      if (!StringUtils.isBlank(source.getKycWaiver())) {
        target.setKycWaiver(source.getKycWaiver());
      }
      if (!StringUtils.isBlank(source.getPinType())) {
        target.setPinType(source.getPinType());
      }
      if(!Objects.isNull(source.getPhoneNumbers())) {
        target.setPhoneNumberList(source.getPhoneNumbers());
      }
      return target;
    }

    private InitialOfferGenerationStatusResponse getRejectReasonAndStatus(InitialOfferGenerationStatusResponse target, CheckApprovalStatusResponse source) {
      boolean noPosidexCall = source.getResult().getMessage().startsWith("No POSIDEX call");
      target.setStatus(noPosidexCall ? "INPROGRESS" : "REJECT");
      target.setInitialStatus(noPosidexCall ? "INPROGRESS" : "REJECT");
      target.setRejectReason(source.getResult().getMessage());
      return target;
    }

    private static String getApprovalStatus(CheckApprovalStatusResponse source) {
      if(source.getFicoStatus() != null && "REJECT".equals(source.getFicoStatus())) {
        return "REJECT";
      }

      if(source.getInitialStatus() != null) {
        return source.getInitialStatus();
      }
      return source.getStatus();
    }

    private List<InitialOffer> getOffers(CheckApprovalStatusResponse source) {
      return Optional.ofNullable(source)
          .map(CheckApprovalStatusResponse::getOffers)
          .orElseGet(Collections::emptyList)
          .stream()
          .filter(Objects::nonNull)
          .map(this::getInitialOfferFromOffer)
          .collect(Collectors.toList());
    }


    private InitialOffer getInitialOfferFromOffer(Offer offer) {
      InitialOffer result = new InitialOffer();
      result.setMaxEligibleAmount(offer.getMaxEligibleAmount());
      result.setMinEligibleAmount(offer.getMinEligibleAmount());
      result.setRoi(offer.getRoi());
      result.setMaxTenure(offer.getMaxTenure());
      result.setMinTenure(offer.getMinTenure());
      result.setProcessingFee(offer.getProcessingFee());
      return result;
    }
  }

  private static class InitialOfferGenerationResponseConverter implements Converter<InitialOfferGenerationResponse, com.flipkart.fintech.pandora.api.model.pl.response.InitialOfferGenerationResponse> {

    @Override
    public com.flipkart.fintech.pandora.api.model.pl.response.InitialOfferGenerationResponse convert(MappingContext<InitialOfferGenerationResponse, com.flipkart.fintech.pandora.api.model.pl.response.InitialOfferGenerationResponse> context) {
      InitialOfferGenerationResponse source = context.getSource();
      com.flipkart.fintech.pandora.api.model.pl.response.InitialOfferGenerationResponse target = new com.flipkart.fintech.pandora.api.model.pl.response.InitialOfferGenerationResponse();
      target.setStatus(source.getStatus());
      return target;
    }
  }

  private static class InitialOfferGenerationRequestConverter implements Converter<com.flipkart.fintech.pandora.api.model.pl.request.InitialOfferGenerationRequest, InitialOfferGenerationRequest> {

    @Override
    public InitialOfferGenerationRequest convert(MappingContext<com.flipkart.fintech.pandora.api.model.pl.request.InitialOfferGenerationRequest, InitialOfferGenerationRequest> context) {
      com.flipkart.fintech.pandora.api.model.pl.request.InitialOfferGenerationRequest source = context.getSource();
      InitialOfferGenerationRequest target = new InitialOfferGenerationRequest();
      target.setReqId(source.getApplicationId());
      target.setPanNumber(source.getPanNumber());
      String formattedDob = getFormattedDob(source.getDob(), DateTimeFormatter.ofPattern("dd/MM/yyyy"));
      target.setDateOfBirth(formattedDob);
      target.setOfferId(source.getApplicationId());
      target.setAddress(source.getAddress());
      target.setMobileNo(source.getPhoneNumber());
      if (source.getPartnerScore()!=null) {
        target.setPartnerScore(source.getPartnerScore());
      }
      if (source.getEmploymentType()!=null) {
        if("SelfEmployed".equals(source.getEmploymentType())){
          target.setEmploymentType("Self Employed");
        }
        else {
          target.setEmploymentType(source.getEmploymentType());
        }
      }

      return target;
    }
  }

  public static String getFormattedDob(String dob, DateTimeFormatter inputFormat) {
    DateTimeFormatter outputFormat = DateTimeFormatter.ofPattern("dd-MM-yyyy");
    return LocalDate.parse(dob, inputFormat).format(outputFormat);
  }

  private static class PanVerificationResponseConverter implements Converter<PanVerificationResponse, com.flipkart.fintech.pandora.api.model.pl.response.PanVerificationResponse> {

    @Override
    public com.flipkart.fintech.pandora.api.model.pl.response.PanVerificationResponse convert(MappingContext<PanVerificationResponse, com.flipkart.fintech.pandora.api.model.pl.response.PanVerificationResponse> context) {
      PanVerificationResponse source = context.getSource();
      com.flipkart.fintech.pandora.api.model.pl.response.PanVerificationResponse target = new com.flipkart.fintech.pandora.api.model.pl.response.PanVerificationResponse();
      target.setStatus(source.getStatus());
      target.setPanStatus(source.getPanUserInfo().getPanStatus());
      return target;
    }
  }

  private static class PanVerificationRequestConverter implements Converter<PanVerificationRequest, PanNumberVerificationRequest> {

    @Override
    public PanNumberVerificationRequest convert(MappingContext<PanVerificationRequest, PanNumberVerificationRequest> context) {
      PanVerificationRequest source = context.getSource();
      PanNumberVerificationRequest target = new PanNumberVerificationRequest();
      target.setPanNumber(source.getPan());
      target.setReqId(source.getApplicationId());
      target.setDob(source.getDob());
      if(StringUtils.isNotEmpty(source.getFirstName()) && StringUtils.isNotEmpty(source.getLastName())) {
        target.setCustomerName(source.getFirstName() + " " + source.getLastName());
      }
      return target;
    }
  }
}
