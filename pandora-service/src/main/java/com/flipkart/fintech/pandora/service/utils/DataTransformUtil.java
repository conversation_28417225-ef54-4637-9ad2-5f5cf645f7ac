package com.flipkart.fintech.pandora.service.utils;

import com.flipkart.fintech.exception.ServiceErrorResponse;
import com.flipkart.fintech.exception.ServiceException;
import com.flipkart.fintech.pandora.service.application.configuration.BucketingConfiguration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.ws.rs.core.Response;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by sushan<PERSON><PERSON>.b on 17/07/18
 */
public class DataTransformUtil {

    private static final Logger logger = LoggerFactory.getLogger(DataTransformUtil.class);

    public static Map bucketizeSurrogates(BucketingConfiguration bucketingConfiguration, Map surrogates) {

        if (Boolean.FALSE.equals(bucketingConfiguration.getEnabled())) {
            return surrogates;
        }

        Map bucketizedSurrogates = new HashMap();
        for( Object key : surrogates.keySet() ) {
            String keyString = key.toString();
            Object value = surrogates.get(key);

            switch(keyString) {
                case Constants.Surrogates.ACCOUNT_VINTAGE:
                    value = getBucketValue(Double.parseDouble(value.toString()), bucketingConfiguration.getAccountVintage());
                break;

                case Constants.Surrogates.DAYS_SINCE_LAST_ORDER:
                    value = getBucketValue(Double.parseDouble(value.toString()), bucketingConfiguration.getDaysSinceOrder());
                    break;

                case Constants.Surrogates.ORDERS_FULFILLED:
                    value = getBucketValue(Double.parseDouble(value.toString()), bucketingConfiguration.getOrdersFulfilled());
                break;

                case Constants.Surrogates.GMV_TILL_DATE:
                    value = getBucketValue(Double.parseDouble(value.toString()), bucketingConfiguration.getGmvTillDate());
                break;

                case Constants.Surrogates.PERCENTAGE_ORDERS_RETURNED:
                    value = getBucketValue(Double.parseDouble(value.toString()), bucketingConfiguration.getPercentageReturned());
                break;

                case Constants.Surrogates.ORDERS_AT_COMMON_ADDRESS:
                    value = getBucketValue(Double.parseDouble(value.toString()), bucketingConfiguration.getOrdersAtCommonAddress());
                break;

                case Constants.Surrogates.PREPAID_ORDERS:
                    value = Integer.parseInt(value.toString())>0?2:1;
                break;

                case Constants.Surrogates.HIGHEST_GMV_IN_MONTH:
                case Constants.Surrogates.SECOND_HIGHEST_GMV_IN_MONTH:
                    Double d = Double.parseDouble(value.toString());
                    Long oldValue = d.longValue();
                    Long newValue = ((oldValue+500)/1000)*1000;
                    value = BigDecimal.valueOf(newValue).setScale(1);
                break;

                default:
                break;
            }

            bucketizedSurrogates.put(key, value);
        }

        //TODO remove logging later
        for( Object key:bucketizedSurrogates.keySet()){
            logger.info( "Key {} Original {} Bucket {}", key, surrogates.get(key), bucketizedSurrogates.get(key));
        }
        return bucketizedSurrogates;
    }

    private static Object getBucketValue(Double value, List<Double> buckets) {
        Integer bucket = 1;
        for( Double range:buckets ) {
            if( value<range ) {
                return bucket;
            }
            bucket++;
        }
        return bucket;
    }

    public static void throw5XX(Exception e) {
        throw new ServiceException(new ServiceErrorResponse(
                Response.Status.INTERNAL_SERVER_ERROR, Response.Status.INTERNAL_SERVER_ERROR.getReasonPhrase(), e.getMessage()));
    }
}
