package com.flipkart.fintech.pandora.service.utils;

import com.flipkart.fintech.pandora.api.model.enums.ebc.EbcCallbackRequestStatus;
import com.flipkart.fintech.pandora.api.model.request.ebc.EbcCallbackRequest;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class EbcCallbackRequestValidator implements ConstraintValidator<ValidateEbcCallbackRequest, EbcCallbackRequest> {

    @Override
    public void initialize(ValidateEbcCallbackRequest ebcCallbackRequest) {
    }

    @Override
    public boolean isValid(EbcCallbackRequest request,
                            ConstraintValidatorContext constraintValidatorContext) {
        if(request.getStatus() == EbcCallbackRequestStatus.APPROVED) {
            return request.getLimit() > 0;
        }
        else {
            return true;
        }
    }
}
