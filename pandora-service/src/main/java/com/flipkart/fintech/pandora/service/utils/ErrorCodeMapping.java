package com.flipkart.fintech.pandora.service.utils;

import com.flipkart.fintech.pandora.api.model.common.STATUS;
import com.flipkart.fintech.pandora.service.external.LenderErrorConfig;
import com.flipkart.fintech.pinaka.api.enums.Lender;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;
import com.google.inject.Inject;
import org.apache.commons.lang3.EnumUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

public class ErrorCodeMapping {

    private Table<Lender, String, STATUS> lenderErrorCodeMapping;
    private final LenderErrorConfig lenderErrorConfig;
    private static Logger log = LoggerFactory.getLogger(ErrorCodeMapping.class);

    @Inject
    public ErrorCodeMapping(LenderErrorConfig lenderErrorConfig) {
        this.lenderErrorConfig = lenderErrorConfig;
        Map<String, List<String>> errorMapping = this.lenderErrorConfig.getErrorMapping();

        lenderErrorCodeMapping = HashBasedTable.create();
        errorMapping.forEach((k,v) -> {

            if (EnumUtils.isValidEnum(Lender.class, k)) {
                for (String errorStatusMap : v) {
                    String[] errorStatusSplit = errorStatusMap.split(Constants.LenderErrorStatusSplitDelimiter);
                    List<String> errorStatusSplitList = new LinkedList<>(Arrays.asList(errorStatusSplit));
                    errorStatusSplitList.replaceAll(String::trim);
                    if (errorStatusSplitList.size()==2 && EnumUtils.isValidEnum(STATUS.class, errorStatusSplitList.get(1))) {
                        Lender lender = Lender.valueOf(k);
                        STATUS status = STATUS.valueOf(errorStatusSplitList.get(1));
                        String errorMessage = errorStatusSplitList.get(0);

                        lenderErrorCodeMapping.put(lender, errorMessage, status);
                    }
                }
            }


            /*String[] lenderErrorSplit = k.split(Constants.LenderErrorSplitDelimiter);
            List<String> lenderErrorSplitList = new LinkedList<String>(Arrays.asList(lenderErrorSplit));
            //lenderErrorSplitList.removeAll(Arrays.asList("", null));

            if (EnumUtils.isValidEnum(Lender.class, lenderErrorSplitList.get(0)) && EnumUtils.isValidEnum(STATUS.class, v)) {
                Lender lender = Lender.valueOf(lenderErrorSplitList.get(0));
                STATUS status = STATUS.valueOf(v);
                String errorMessage = lenderErrorSplitList.get(1);

                lenderErrorCodeMapping.put(lender, errorMessage, status);
            }*/
        });

    }

    public STATUS getStatusForLenderAndError(Lender lender, String errorMessage) {  //works for empty or null errorMessage (returns failure status)
        STATUS status = lenderErrorCodeMapping.get(lender, errorMessage);
        if(Objects.isNull(status)) {
            status = STATUS.FAILURE;
        }

        return status;
    }

    public Map<String, STATUS> getStatusesForLender(Lender lender) {
        return lenderErrorCodeMapping.row(lender);
    }

}
