package com.flipkart.fintech.pandora.service.utils;

import org.apache.commons.lang3.StringUtils;

import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

public class LenderRequestIdGenerator {

    public static final String ALPHA_NUMERIC = "ALPHA_NUMERIC";
    public static final String NUMERIC = "NUMERIC";

    private LenderRequestIdGenerator() {
    }


    public static String generateNumericUniqueIdentifier(String accountId, int length) {
        return generateNumericHash(accountId, length);
    }

    public static String generateAlphaNumericUniqueIdentifier(String accountId, String hashingSalt, int length)
            throws NoSuchAlgorithmException {
        String hashedAccountId = generateHash(accountId, hashingSalt);
        return hashedAccountId.substring(0, length);
    }

    private static String generateHash(String stringToEncrypt, String hashingSalt) throws NoSuchAlgorithmException {
        byte[] bytes = stringToEncrypt.getBytes(StandardCharsets.UTF_8);
        MessageDigest m = MessageDigest.getInstance("SHA-256");
        if (!StringUtils.isBlank(hashingSalt)) {
            m.update(hashingSalt.getBytes());
        }
        byte[] digest = m.digest(bytes);
        return new BigInteger(1, digest).toString(16);
    }

    private static String generateNumericHash(String input, int length) {
        int hashValue = input.hashCode();
        hashValue = Math.abs(hashValue);
        String numericHash = String.valueOf(hashValue);
        return StringUtils.rightPad(numericHash, length, '0');
    }
}
