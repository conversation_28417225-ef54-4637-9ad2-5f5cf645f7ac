{"error": false, "statusCode": 200, "statusMessage": "OK", "data": {"id": 1, "customer_id": 1, "case_reference_id": "c1-0004", "c1_number": "ICC-00003432345-Procesed", "emp_firstname": "Axis", "emp_lastname": "Employee - 2", "contact_no": "9994058531", "schedule_time": "10AM - 12PM", "schedule_date": "2020-06-04", "schedule_status": "Scheduled", "full_name": "<PERSON><PERSON><PERSON>", "contact_number": "9994058530", "alternate_contact_number": "9994058530", "email": "<EMAIL>", "communication_flag": "residential", "residential_address": {"address": "address", "city": "city", "state": "state", "pincode": 641042}, "alternate_address": null, "work_address": null, "pincode": "641042", "document_type": "Address Proof, ID proof, Income proof", "extra_param": null}, "responseTime": 1591969499}