{"error": false, "statusCode": 200, "statusMessage": "OK", "data": {"id": 23, "case_reference_id": "ICC-0000004901-Process", "slot_id": 5, "schedule_date": "2020-07-03", "pincode": "641042", "employee_id": 1, "customer_id": 1, "schedule_status": 1, "alternate_contact_number": "9990340675", "communication_flag": "residential", "residential_address": "{\"address\":\"golden residency\",\"city\":\"Bangalore\",\"pincode\":\"641042\",\"state\":\"Karnataka\"}", "alternate_address": "{\"address\":\"golden residency\",\"city\":\"Bangalore\",\"pincode\":\"641042\",\"state\":\"Karnataka\"}", "work_address": "{\"address\":\"golden residency\",\"city\":\"Bangalore\",\"pincode\":\"641042\",\"state\":\"Karnataka\"}", "document_type": "Address Proof, ID proof, Income proof", "extra_param": "\"{}\"", "updated_at": "2020-06-26T08:36:19.000000Z", "message": "Successfully lead was created and schedule information will update later."}, "responseTime": 1593160579}