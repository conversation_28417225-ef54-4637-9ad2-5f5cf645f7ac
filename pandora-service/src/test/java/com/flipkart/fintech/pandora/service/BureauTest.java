package com.flipkart.fintech.pandora.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.flipkart.fintech.pandora.api.model.pl.request.PanVerificationRequest;
import com.flipkart.fintech.pandora.api.model.pl.request.PennyDropRequest;
import com.flipkart.fintech.pandora.service.core.decryption.FormDataDecryption;
import com.flipkart.fintech.profile.api.request.BureauRequest;
import com.flipkart.fintech.profile.api.response.BureauResponse;
import org.apache.commons.lang.StringEscapeUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.io.InputStream;

public class BureauTest {
    private BureauResponse bureauResponse;
    private ObjectMapper objectMapper = new ObjectMapper();

    @BeforeEach
    public void setUp() throws Exception {
        InputStream inputStream = FormDataDecryption.class.getClassLoader().getResourceAsStream("XmlReport.json");
        bureauResponse = objectMapper.readValue(inputStream, BureauResponse.class);
    }

    @Test
    public void convertXmlToJsonTest() throws JsonProcessingException {
        System.out.println("before escaping xml : "+bureauResponse.getRawData());
        String xml = StringEscapeUtils.unescapeXml(bureauResponse.getRawData());
        System.out.println("after escaping xml :"+ xml );
//        ObjectMapper xmlMapper = new XmlMapper();
//        JsonNode jsonNode = xmlMapper.readTree(xml);
//
//        ObjectMapper jsonMapper = new ObjectMapper();
//        System.out.println(jsonMapper.writeValueAsString(jsonNode));
    }
}
