package com.flipkart.fintech.pandora.service;

import com.flipkart.fintech.pandora.service.core.decryption.FormDataDecryption;
import liquibase.util.csv.CSVReader;
import liquibase.util.csv.CSVWriter;
import org.junit.Ignore;
import org.junit.Test;

import java.io.FileNotFoundException;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.util.List;


public class ImportCsvtest {

    @Ignore
    @Test
    public void decryptAllData() throws FileNotFoundException {
        String inputCsv = "src/test/java/com/flipkart/fintech/pandora/service/input.csv";
        String outputCsv = "src/test/java/com/flipkart/fintech/pandora/service/output.csv";
        int[] columnsToDecrypt = {0, 1, 2}; // pan, email, phone_number

        FormDataDecryption decryption = new FormDataDecryption();

        try (CSVReader reader = new CSVReader(new FileReader(inputCsv));
             CSVWriter writer = new CSVWriter(new FileWriter(outputCsv))) {
            List<String[]> allRows = reader.readAll();
            writer.writeNext(allRows.get(0));
            for (int i = 1; i < allRows.size(); i++) {
                String[] row = allRows.get(i);
                for (int col : columnsToDecrypt) {
                    if (row.length > col && row[col] != null && !row[col].trim().isEmpty()) {
                        try {
                            row[col] = decryption.decrypt(row[col].trim());
                        } catch (IllegalArgumentException e) {
                            row[col] = "INVALID_BASE64";
                        }
                    }
                }
                writer.writeNext(row);
            }
            System.out.println("Done");
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

}
