package com.flipkart.fintech.pandora.service;

import com.flipkart.fintech.pandora.service.client.exceptions.LenderException;
import com.flipkart.fintech.pandora.service.client.mapper.exceptions.LenderExceptionMapper;
import com.flipkart.fintech.pandora.service.client.pl.response.ErrorResponse;
import com.flipkart.fintech.pandora.service.client.sandbox.v2.response.CreateApplicationResponse;
import com.flipkart.fintech.pandora.service.client.mapper.sandbox.SandboxErrorMapper;
import org.junit.jupiter.api.Test;

import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import static org.junit.jupiter.api.Assertions.*;

class LenderExceptionMapperTest {

    @Test
    void testToResponse() {
        // Create a dummy error value.
        ErrorResponse dummyError = new ErrorResponse();
        // Create a LenderException with the dummy error.
        LenderException exception = new LenderException(dummyError);

        // Instantiate the mapper and get the response.
        LenderExceptionMapper mapper = new LenderExceptionMapper();
        Response response = mapper.toResponse(exception);

        // Determine expected HTTP status from the SandboxErrorMapper.
        int expectedStatus = SandboxErrorMapper.getHttpStatusCode(dummyError);

        // Assert status code and content type.
        assertEquals(expectedStatus, response.getStatus(), "Status codes should match");
        assertEquals(MediaType.APPLICATION_JSON, response.getMediaType().toString(), "MediaType should be application/json");

        // Assert that the entity is of type CreateApplicationResponse and contains the given error.
        Object entity = response.getEntity();
        assertNotNull(entity, "Response entity should not be null");
        assertTrue(entity instanceof CreateApplicationResponse, "Entity should be an instance of CreateApplicationResponse");

        CreateApplicationResponse createApplicationResponse = (CreateApplicationResponse) entity;
        // Assuming CreateApplicationResponse has an accessor for the error.
        assertEquals(dummyError, createApplicationResponse.getError(), "Error in response should match the given error");
    }
}