package com.flipkart.fintech.pandora.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.affordability.common.ObjectMapperFactory;
import com.flipkart.fintech.pandora.service.client.kotak.responses.AadhaarOTPVerificationResponse;
import com.flipkart.fintech.pandora.service.client.kotak.responses.CKYCDownloadResponse;
import com.flipkart.fintech.pandora.service.hystrix.PandoraHystrixProperties;

import java.io.IOException;

import static io.dropwizard.testing.FixtureHelpers.fixture;

/**
 * <AUTHOR>
 * @since 27/04/21.
 */
public class TestHelper {

    private final ObjectMapper objectMapper;
    private static final String REQUEST_PATH = "request/";
    private static final String RESPONSE_PATH = "response/";
    private static final String CONFIG_PATH = "config/";

    public TestHelper() {
        objectMapper = ObjectMapperFactory.OBJECT_MAPPER;
    }


    public PandoraHystrixProperties getPandoraHystrixProperties() throws IOException {
        String requestStr = fixture(CONFIG_PATH + "pandora_hystrix_properties.json");
        return objectMapper.readValue(requestStr, PandoraHystrixProperties.class);
    }

    public CKYCDownloadResponse getCKYCDownloadResponse() throws IOException {
        String response = fixture(RESPONSE_PATH + "ckyc_download_response.json");
        return objectMapper.readValue(response, CKYCDownloadResponse.class);
    }

    public AadhaarOTPVerificationResponse getAadhaarOTPVerificationResponse() throws IOException {
        String response = fixture(RESPONSE_PATH + "ekyc_verify_otp_response.json");
        return objectMapper.readValue(response, AadhaarOTPVerificationResponse.class);
    }
}
