package com.flipkart.fintech.pandora.service.client.axisCbc;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.pandora.api.model.common.STATUS;
import com.flipkart.fintech.pandora.api.model.request.onboarding.AxisApiConfigurationKeys;
import com.flipkart.fintech.pandora.service.application.configuration.AxisCbcApiConfigurations;
import com.flipkart.fintech.pandora.service.application.configuration.AxisCbcConfiguration;
import com.flipkart.fintech.pandora.service.core.lifeCycleManagement.AxisCardLifeCycleMangementClient;
import com.flipkart.fintech.pandora.service.external.dataModels.axisLifeCycleMangement.request.encrypted.LCMGenerateAuthOTPRequest;
import com.flipkart.fintech.pandora.service.external.dataModels.axisLifeCycleMangement.response.encrypted.LCMGenerateAuthOTPResponse;
import com.flipkart.fintech.pandora.service.external.dataModels.axisUserOnboarding.SubHeader;
import com.flipkart.fintech.pandora.service.external.dataModels.axisLifeCycleMangement.request.encrypted.LCMValidateAuthOTPRequest;
import com.flipkart.fintech.pandora.service.external.dataModels.axisLifeCycleMangement.response.encrypted.LCMValidateAuthOTPResponse;
import com.flipkart.fintech.pandora.service.hystrix.PandoraHystrixNameConstants;
import com.flipkart.fintech.pandora.service.hystrix.PandoraHystrixProperties;
import com.flipkart.fintech.pandora.service.hystrix.axisCbc.lifeCycleManagement.GenerateAuthOtpCommand;
import com.flipkart.fintech.pandora.service.utils.Constants;
import com.flipkart.fintech.pandora.service.utils.EncryptionUtil;
import com.flipkart.fintech.pandora.service.utils.PandoraContext;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Spy;
import org.mockito.runners.MockitoJUnitRunner;

import javax.ws.rs.client.Client;
import javax.ws.rs.client.Entity;
import javax.ws.rs.core.HttpHeaders;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.MultivaluedHashMap;
import javax.ws.rs.core.MultivaluedMap;
import java.util.HashMap;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class AxisCardLifeCycleMangementClientTest {
    private final PandoraHystrixProperties pandoraHystrixProperties = new PandoraHystrixProperties();

    private AxisCbcConfiguration axisCbcConfiguration = new AxisCbcConfiguration();

    private ObjectMapper objectMapper = new ObjectMapper();
    private EncryptionUtil aesEncryptionUtil = new EncryptionUtil();
    private javax.ws.rs.client.Client client = mock(Client.class);
    @InjectMocks
    @Spy
    private AxisCardLifeCycleMangementClient clientService =
            spy(new AxisCardLifeCycleMangementClient(objectMapper, client, axisCbcConfiguration));
    private MultivaluedMap<String, Object> map = new MultivaluedHashMap<>();

    @BeforeEach
    public void setUp() throws Exception {

        MultivaluedMap<String, String> headers = PandoraContext.getHeaders();
        if (null != headers) {
            headers.add("X-Perf-Test", "True");
        }
        else {
            headers = new MultivaluedHashMap<>();
            headers.add("X-Perf-Test", "True");
        }
        PandoraContext.setHeaders(headers);
        axisCbcConfiguration.setChannelId("FK");
        AxisCbcApiConfigurations axisCbcConfig = new AxisCbcApiConfigurations();
        axisCbcConfig.setApiPath("abc");
        axisCbcConfig.setServiceRequestId("123");
        axisCbcConfig.setServiceRequestVersion("1");
        axisCbcConfiguration.getAxisCbcApiConfigurationsMap()
                .put(AxisApiConfigurationKeys.LCM_GENERATE_AUTH_OTP.getKey(), axisCbcConfig);
        axisCbcConfiguration.getAxisCbcApiConfigurationsMap()
                .put(AxisApiConfigurationKeys.LCM_VALIDATE_AUTH_OTP.getKey(), axisCbcConfig);
        axisCbcConfiguration.setEncryptionKey("53EACE72CD83D6B60754C2F3959168EA");
        map.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON);
        map.add(Constants.AxisCbc.X_IBM_CLIENT_ID, axisCbcConfiguration.getClient());
        map.add(Constants.AxisCbc.X_IBM_CLIENT_SECRET, axisCbcConfiguration.getSecretKey());
        pandoraHystrixProperties.setCommandCircuitBreakerProperties(new HashMap<>());
        pandoraHystrixProperties.setCommandExecutionProperties(new HashMap<>());
        pandoraHystrixProperties.setThreadPoolProperties(new HashMap<>());
    }

    @Test
    public void generateAuthOtpHappyCase() throws Exception {
        LCMGenerateAuthOTPRequest lcmGenerateAuthOTPRequest = new LCMGenerateAuthOTPRequest();
        String encryptedString = "ABC1235";
        LCMGenerateAuthOTPResponse lcmGenerateAuthOTPResponse = new LCMGenerateAuthOTPResponse();
        SubHeader subHeader = new SubHeader();
        subHeader.setRequestUUID("ABC123");
        subHeader.setServiceRequestVersion("1.0");
        subHeader.setChannelId("FK");
        subHeader.setServiceRequestId("CheckCardEligibility");
        lcmGenerateAuthOTPRequest.setSubHeader(subHeader);
        lcmGenerateAuthOTPRequest.setLcmGenerateAuthOTPRequestBodyEncrypted(encryptedString);
        lcmGenerateAuthOTPResponse.setLcmGenerateAuthOTPResponseBodyEncrypted(encryptedString);
        map.add(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON);
        doReturn(lcmGenerateAuthOTPResponse).when(clientService)
                .getPostResponse(String.format(axisCbcConfiguration.getAxisCbcApiConfigurationsMap()
                        .get(AxisApiConfigurationKeys.LCM_GENERATE_AUTH_OTP.getKey())
                        .getApiPath()), Entity.json(lcmGenerateAuthOTPRequest), map, LCMGenerateAuthOTPResponse.class);
        LCMGenerateAuthOTPResponse response = new LCMGenerateAuthOTPResponse();
        response = new GenerateAuthOtpCommand(PandoraHystrixNameConstants.AXIS_CBC,
                PandoraHystrixNameConstants.AXIS_CBC_CHECK_COHORT_ELIGIBILITY,
                PandoraHystrixNameConstants.AXIS_CBC_CHECK_COHORT_ELIGIBILITY, pandoraHystrixProperties,
                lcmGenerateAuthOTPRequest, clientService).execute();
        assertThat(response.getLcmGenerateAuthOTPResponseBodyEncrypted()).isEqualTo(encryptedString);
    }

    @Test
    public void generateAuthOtpErrorCase() throws Exception {
        LCMGenerateAuthOTPRequest lcmGenerateAuthOTPRequest = new LCMGenerateAuthOTPRequest();
        String encryptedString = "ABC1235";
        LCMGenerateAuthOTPResponse lcmGenerateAuthOTPResponse = new LCMGenerateAuthOTPResponse();
        SubHeader subHeader = new SubHeader();
        subHeader.setRequestUUID("ABC123");
        subHeader.setServiceRequestVersion("1.0");
        subHeader.setChannelId("FK");
        subHeader.setServiceRequestId("CheckCardEligibility");
        lcmGenerateAuthOTPRequest.setSubHeader(subHeader);
        lcmGenerateAuthOTPRequest.setLcmGenerateAuthOTPRequestBodyEncrypted(encryptedString);
        lcmGenerateAuthOTPResponse.setLcmGenerateAuthOTPResponseBodyEncrypted(encryptedString);
        doThrow(new RuntimeException("ABC124")).when(clientService)
                .getPostResponse(String.format(axisCbcConfiguration.getAxisCbcApiConfigurationsMap()
                        .get(AxisApiConfigurationKeys.LCM_GENERATE_AUTH_OTP.getKey())
                        .getApiPath()), Entity.json(lcmGenerateAuthOTPRequest), map, LCMGenerateAuthOTPResponse.class);
        LCMGenerateAuthOTPResponse response = new LCMGenerateAuthOTPResponse();
        response = new GenerateAuthOtpCommand(PandoraHystrixNameConstants.AXIS_CBC,
                PandoraHystrixNameConstants.AXIS_CBC_CHECK_COHORT_ELIGIBILITY,
                PandoraHystrixNameConstants.AXIS_CBC_CHECK_COHORT_ELIGIBILITY, pandoraHystrixProperties,
                lcmGenerateAuthOTPRequest, clientService).execute();
        Assertions.assertThat(response.getHttpMessage()).isEqualTo(STATUS.LCM_GENERATE_AUTH_OTP_EXCEPTION.name());
        Assertions.assertThat(response.getHttpCode()).isEqualTo(500);
    }

    @Test
    public void validateAuthOtpHappyCase() throws Exception {
        LCMValidateAuthOTPRequest lcmValidateAuthOTPRequest = new LCMValidateAuthOTPRequest();
        String encryptedString = "ABC1235";
        String validationToken = "validationToken";
        LCMValidateAuthOTPResponse lcmValidateAuthOTPResponse = new LCMValidateAuthOTPResponse();
        SubHeader subHeader = new SubHeader();
        subHeader.setRequestUUID("ABC123");
        subHeader.setServiceRequestVersion("1.0");
        subHeader.setChannelId("FK");
        subHeader.setServiceRequestId("CheckCardEligibility");
        lcmValidateAuthOTPRequest.setSubHeader(subHeader);
        lcmValidateAuthOTPRequest.setLCMValidateAuthOTPRequestBodyEncrypted(encryptedString);
        lcmValidateAuthOTPResponse.setLcmValidateAuthOtpResponseBodyEncrypted(encryptedString);
        MultivaluedMap<String, Object> mapWithValidation = new MultivaluedHashMap<>();
        map.add(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON);
        mapWithValidation.putAll(map);
        mapWithValidation.add(Constants.AxisCbc.AUTHORIZATION, "Bearer " + validationToken);
        doReturn(lcmValidateAuthOTPResponse).when(clientService)
                .getPostResponse(String.format(axisCbcConfiguration.getAxisCbcApiConfigurationsMap()
                        .get(AxisApiConfigurationKeys.LCM_VALIDATE_AUTH_OTP.getKey())
                        .getApiPath()), Entity.json(lcmValidateAuthOTPRequest), mapWithValidation, LCMValidateAuthOTPResponse.class);
        LCMValidateAuthOTPResponse response = new LCMValidateAuthOTPResponse();
        response = new com.flipkart.fintech.pandora.service.hystrix.axisCbc.lifeCycleManagement.ValidateAuthOtpCommand(
                PandoraHystrixNameConstants.AXIS_CBC, PandoraHystrixNameConstants.AXIS_CBC_CHECK_COHORT_ELIGIBILITY,
                PandoraHystrixNameConstants.AXIS_CBC_CHECK_COHORT_ELIGIBILITY, pandoraHystrixProperties,
                lcmValidateAuthOTPRequest, clientService, validationToken).execute();
        assertThat(response.getLcmValidateAuthOtpResponseBodyEncrypted()).isEqualTo(encryptedString);
    }

    @Test
    public void validateAuthOtpErrorCase() throws Exception {
        LCMValidateAuthOTPRequest lcmValidateAuthOTPRequest = new LCMValidateAuthOTPRequest();
        String encryptedString = "ABC1235";
        String validationToken = "validationToken";
        LCMValidateAuthOTPResponse lcmValidateAuthOTPResponse = new LCMValidateAuthOTPResponse();
        SubHeader subHeader = new SubHeader();
        subHeader.setRequestUUID("ABC123");
        subHeader.setServiceRequestVersion("1.0");
        subHeader.setChannelId("FK");
        subHeader.setServiceRequestId("CheckCardEligibility");
        lcmValidateAuthOTPRequest.setSubHeader(subHeader);
        lcmValidateAuthOTPRequest.setLCMValidateAuthOTPRequestBodyEncrypted(encryptedString);
        lcmValidateAuthOTPResponse.setLcmValidateAuthOtpResponseBodyEncrypted(encryptedString);
        doThrow(new RuntimeException("ABC124")).when(clientService)
                .getPostResponse(String.format(axisCbcConfiguration.getAxisCbcApiConfigurationsMap()
                        .get(AxisApiConfigurationKeys.LCM_VALIDATE_AUTH_OTP.getKey())
                        .getApiPath()), Entity.json(lcmValidateAuthOTPRequest), map, LCMValidateAuthOTPResponse.class);
        LCMValidateAuthOTPResponse response = new LCMValidateAuthOTPResponse();
        response = new com.flipkart.fintech.pandora.service.hystrix.axisCbc.lifeCycleManagement.ValidateAuthOtpCommand(
                PandoraHystrixNameConstants.AXIS_CBC, PandoraHystrixNameConstants.AXIS_CBC_CHECK_COHORT_ELIGIBILITY,
                PandoraHystrixNameConstants.AXIS_CBC_CHECK_COHORT_ELIGIBILITY, pandoraHystrixProperties,
                lcmValidateAuthOTPRequest, clientService, validationToken).execute();
        Assertions.assertThat(response.getHttpMessage()).isEqualTo(STATUS.LCM_VALIDATE_AUTH_OTP_EXCEPTION.name());
        Assertions.assertThat(response.getHttpCode()).isEqualTo(500);
    }
}
