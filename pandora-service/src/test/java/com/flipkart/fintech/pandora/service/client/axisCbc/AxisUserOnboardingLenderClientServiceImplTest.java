package com.flipkart.fintech.pandora.service.client.axisCbc;


import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.pandora.api.model.common.STATUS;
import com.flipkart.fintech.pandora.api.model.request.onboarding.AxisApiConfigurationKeys;
import com.flipkart.fintech.pandora.service.application.configuration.AxisCbcApiConfigurations;
import com.flipkart.fintech.pandora.service.application.configuration.AxisCbcConfiguration;
import com.flipkart.fintech.pandora.service.core.UserOnboarding.AxisUserOnboardingLenderClientServiceImpl;
import com.flipkart.fintech.pandora.service.exception.AxisUserOnboardingServiceException;
import com.flipkart.fintech.pandora.service.external.dataModels.axisUserOnboarding.SubHeader;
import com.flipkart.fintech.pandora.service.external.dataModels.axisUserOnboarding.request.CheckCohortEligibilityRequestBody;
import com.flipkart.fintech.pandora.service.external.dataModels.axisUserOnboarding.request.OTPGenerationRequest;
import com.flipkart.fintech.pandora.service.external.dataModels.axisUserOnboarding.request.encrypted.*;
import com.flipkart.fintech.pandora.service.external.dataModels.axisUserOnboarding.response.encrypted.*;
import com.flipkart.fintech.pandora.service.hystrix.PandoraHystrixNameConstants;
import com.flipkart.fintech.pandora.service.hystrix.PandoraHystrixProperties;
import com.flipkart.fintech.pandora.service.hystrix.axisCbc.userOnboarding.*;
import com.flipkart.fintech.pandora.service.utils.Constants;
import com.flipkart.fintech.pandora.service.utils.EncryptionUtil;
import com.flipkart.fintech.pandora.service.utils.PandoraContext;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Spy;
import org.mockito.runners.MockitoJUnitRunner;

import javax.ws.rs.client.Client;
import javax.ws.rs.client.Entity;
import javax.ws.rs.core.HttpHeaders;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.MultivaluedHashMap;
import javax.ws.rs.core.MultivaluedMap;
import java.util.HashMap;

import static org.assertj.core.api.Java6Assertions.assertThat;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 * @since 06/05/19.
 */
@RunWith(MockitoJUnitRunner.class)
public class AxisUserOnboardingLenderClientServiceImplTest {

    private final PandoraHystrixProperties pandoraHystrixProperties = new PandoraHystrixProperties();

    private AxisCbcConfiguration axisCbcConfiguration = new AxisCbcConfiguration();

    private ObjectMapper objectMapper = new ObjectMapper();
    private EncryptionUtil encryptionUtil = new EncryptionUtil();
    private Client client = mock(Client.class);
    @Spy
    @InjectMocks
    private AxisUserOnboardingLenderClientServiceImpl clientService =
            spy(new AxisUserOnboardingLenderClientServiceImpl(objectMapper, axisCbcConfiguration, client));
    private MultivaluedMap<String, Object> mapWithoutValidation = new MultivaluedHashMap<>();
    private MultivaluedMap<String, Object> mapWithValidation = new MultivaluedHashMap<>();
    private String validationToken = "1212323";

    @BeforeEach
    public void setUp() throws Exception {

        MultivaluedMap<String, String> headers = PandoraContext.getHeaders();
        if (null != headers) {
            headers.add("X-Perf-Test", "True");
        }
        else {
            headers = new MultivaluedHashMap<>();
            headers.add("X-Perf-Test", "True");
        }
        PandoraContext.setHeaders(headers);
        axisCbcConfiguration.setChannelId("FK");
        AxisCbcApiConfigurations axisCbcConfig = new AxisCbcApiConfigurations();
        axisCbcConfig.setApiPath("abc");
        axisCbcConfig.setServiceRequestId("123");
        axisCbcConfig.setServiceRequestVersion("1");
        axisCbcConfiguration.getAxisCbcApiConfigurationsMap()
                .put(AxisApiConfigurationKeys.CHECK_COHORT_ELIGIBILTIY.getKey(), axisCbcConfig);
        axisCbcConfiguration.getAxisCbcApiConfigurationsMap()
                .put(AxisApiConfigurationKeys.DEMO_CARD_CONSENT.getKey(), axisCbcConfig);
        axisCbcConfiguration.getAxisCbcApiConfigurationsMap()
                .put(AxisApiConfigurationKeys.APPLY_CARD_CONSENT.getKey(), axisCbcConfig);
        axisCbcConfiguration.getAxisCbcApiConfigurationsMap()
                .put(AxisApiConfigurationKeys.FETCH_CUSTOMER_DEMOGRAPHICS.getKey(), axisCbcConfig);
        axisCbcConfiguration.getAxisCbcApiConfigurationsMap()
                .put(AxisApiConfigurationKeys.PROCESS_NEW_CARD_CASE.getKey(), axisCbcConfig);
        axisCbcConfiguration.getAxisCbcApiConfigurationsMap()
                .put(AxisApiConfigurationKeys.CHECK_CASE_STATUS.getKey(), axisCbcConfig);
        axisCbcConfiguration.getAxisCbcApiConfigurationsMap()
                .put(AxisApiConfigurationKeys.CHECK_CASE_STATUS_MOBILE.getKey(), axisCbcConfig);
        axisCbcConfiguration.setEncryptionKey("53EACE72CD83D6B60754C2F3959168EA");
        mapWithoutValidation.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON);
        mapWithoutValidation.add(Constants.AxisCbc.X_IBM_CLIENT_ID, axisCbcConfiguration.getClient());
        mapWithoutValidation.add(Constants.AxisCbc.X_IBM_CLIENT_SECRET, axisCbcConfiguration.getSecretKey());
        mapWithValidation.putAll(mapWithoutValidation);
        mapWithValidation.add(Constants.AxisCbc.AUTHORIZATION, "Bearer " + validationToken);
        pandoraHystrixProperties.setCommandCircuitBreakerProperties(new HashMap<>());
        pandoraHystrixProperties.setCommandExecutionProperties(new HashMap<>());
        pandoraHystrixProperties.setThreadPoolProperties(new HashMap<>());
    }

    @Test
    public void checkCardEligibiltyHappyCase() throws Exception {
        CheckCohortEligibilityRequest request = new CheckCohortEligibilityRequest();
        String encryptedString = "ABC1235";
        CheckCohortEligibilityResponse checkCohortEligibilityResponse = new CheckCohortEligibilityResponse();
        SubHeader subHeader = new SubHeader();
        subHeader.setRequestUUID("ABC123");
        subHeader.setServiceRequestVersion("1.0");
        subHeader.setChannelId("FK");
        subHeader.setServiceRequestId("CheckCardEligibility");
        request.setSubHeader(subHeader);
        request.setCheckCohortEligibilityRequestBodyEncrypted(encryptedString);
        mapWithoutValidation.add(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON);
        checkCohortEligibilityResponse.setCheckCohortEligibilityResponseBodyEncrypted(encryptedString);
        doReturn(checkCohortEligibilityResponse).when(clientService)
                .getPostResponse(String.format(axisCbcConfiguration.getAxisCbcApiConfigurationsMap()
                        .get(AxisApiConfigurationKeys.CHECK_COHORT_ELIGIBILTIY.getKey())
                        .getApiPath()), Entity.json(request), mapWithoutValidation, CheckCohortEligibilityResponse.class);
        CheckCohortEligibilityResponse response = new CheckCohortEligibilityResponse();
        response = new CheckCohortEligibilityCommand(PandoraHystrixNameConstants.AXIS_CBC,
                PandoraHystrixNameConstants.AXIS_CBC_CHECK_COHORT_ELIGIBILITY,
                PandoraHystrixNameConstants.AXIS_CBC_CHECK_COHORT_ELIGIBILITY, pandoraHystrixProperties, request,
                clientService).execute();
        assertThat(response.getCheckCohortEligibilityResponseBodyEncrypted()).isEqualTo(encryptedString);
    }

    @Test
    public void checkCardEligibiltyErrorCase() throws Exception {
        CheckCohortEligibilityRequestBody checkCardEligibilityRequest = new CheckCohortEligibilityRequestBody();
        checkCardEligibilityRequest.setDeviceId("8888");
        checkCardEligibilityRequest.setMobileNumber("************");
        CheckCohortEligibilityRequest request = new CheckCohortEligibilityRequest();
        String encryptedString = "ABC1235";
        CheckCohortEligibilityResponse checkCohortEligibilityResponse = new CheckCohortEligibilityResponse();
        SubHeader subHeader = new SubHeader();
        subHeader.setRequestUUID("ABC123");
        subHeader.setServiceRequestVersion("1.0");
        subHeader.setChannelId("FK");
        subHeader.setServiceRequestId("CheckCardEligibility");
        request.setSubHeader(subHeader);
        request.setCheckCohortEligibilityRequestBodyEncrypted(encryptedString);
        checkCohortEligibilityResponse.setCheckCohortEligibilityResponseBodyEncrypted(encryptedString);
        doThrow(new RuntimeException("ABC124")).when(clientService)
                .getPostResponse(String.format(axisCbcConfiguration.getAxisCbcApiConfigurationsMap()
                        .get(AxisApiConfigurationKeys.CHECK_COHORT_ELIGIBILTIY.getKey())
                        .getApiPath()), Entity.json(request), mapWithoutValidation, CheckCohortEligibilityResponse.class);
        CheckCohortEligibilityResponse response = new CheckCohortEligibilityResponse();
        response = new CheckCohortEligibilityCommand(PandoraHystrixNameConstants.AXIS_CBC,
                PandoraHystrixNameConstants.AXIS_CBC_CHECK_COHORT_ELIGIBILITY,
                PandoraHystrixNameConstants.AXIS_CBC_CHECK_COHORT_ELIGIBILITY, pandoraHystrixProperties, request,
                clientService).execute();
        assertThat(response.getHttpMessage()).isEqualTo(STATUS.CHECK_CARD_ELIGBILITY_EXCEPTION.name());
        assertThat(response.getHttpCode()).isEqualTo(500);
    }

    @Test
    public void GenerateDemoCardConsentHappyCase() throws AxisUserOnboardingServiceException {
        OTPGenerationRequest otpGenerationRequest = new OTPGenerationRequest();
        otpGenerationRequest.setDeviceId("8888");
        otpGenerationRequest.setMobileNumber("************");
        otpGenerationRequest.setApplicationReferenceId("123245");
        otpGenerationRequest.setOtpReferenceId("54321");
        DemoConsentOTPGenerationRequest request = new DemoConsentOTPGenerationRequest();
        String encryptedString = "ABC1235";
        SubHeader subHeader = new SubHeader();
        subHeader.setRequestUUID("ABC123");
        subHeader.setServiceRequestVersion("1.0");
        subHeader.setChannelId("FK");
        subHeader.setServiceRequestId("123");
        request.setSubHeader(subHeader);
        request.setDemoConsentOTPGenerationRequestBodyEncrypted(encryptedString);
        DemoConsentOTPGenerationResponse demoConsentOTPGenerationResponse = new DemoConsentOTPGenerationResponse();
        demoConsentOTPGenerationResponse.setDemoConsentOTPGenerationResponseBodyEncrypted(encryptedString);
        mapWithValidation.add(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON);
        doReturn(demoConsentOTPGenerationResponse).when(clientService)
                .getPostResponse(String.format(axisCbcConfiguration.getAxisCbcApiConfigurationsMap()
                                .get(AxisApiConfigurationKeys.DEMO_CARD_CONSENT.getKey())
                                .getApiPath()), Entity.json(request), mapWithValidation,
                        DemoConsentOTPGenerationResponse.class);
        DemoConsentOTPGenerationResponse response = new DemoConsentOtpCommand(PandoraHystrixNameConstants.AXIS_CBC,
                PandoraHystrixNameConstants.AXIS_CBC_DEMO_CARD_CONSENT,
                PandoraHystrixNameConstants.AXIS_CBC_DEMO_CARD_CONSENT, pandoraHystrixProperties, request,
                clientService, validationToken,AxisApiConfigurationKeys.DEMO_CARD_CONSENT.getKey()).execute();
        assertThat(response.getDemoConsentOTPGenerationResponseBodyEncrypted()).isEqualTo(encryptedString);
    }

    @Test
    public void GenerateDemoCardConsentErrorCase() throws AxisUserOnboardingServiceException {
        OTPGenerationRequest otpGenerationRequest = new OTPGenerationRequest();
        otpGenerationRequest.setDeviceId("8888");
        otpGenerationRequest.setMobileNumber("************");
        otpGenerationRequest.setApplicationReferenceId("123245");
        otpGenerationRequest.setOtpReferenceId("54321");
        DemoConsentOTPGenerationRequest request = new DemoConsentOTPGenerationRequest();
        String encryptedString = "ABC1235";
        SubHeader subHeader = new SubHeader();
        subHeader.setRequestUUID("ABC123");
        subHeader.setServiceRequestVersion("1.0");
        subHeader.setChannelId("FK");
        subHeader.setServiceRequestId("123");
        request.setSubHeader(subHeader);
        request.setDemoConsentOTPGenerationRequestBodyEncrypted(encryptedString);
        DemoConsentOTPGenerationResponse demoConsentOTPGenerationResponse = new DemoConsentOTPGenerationResponse();
        demoConsentOTPGenerationResponse.setDemoConsentOTPGenerationResponseBodyEncrypted(encryptedString);
        doThrow(new RuntimeException("ABC124")).when(clientService)
                .getPostResponse(String.format(axisCbcConfiguration.getAxisCbcApiConfigurationsMap()
                                .get(AxisApiConfigurationKeys.DEMO_CARD_CONSENT.getKey())
                                .getApiPath()), Entity.json(request), mapWithValidation,
                        DemoConsentOTPGenerationResponse.class);
        DemoConsentOTPGenerationResponse response = new DemoConsentOtpCommand(PandoraHystrixNameConstants.AXIS_CBC,
                PandoraHystrixNameConstants.AXIS_CBC_DEMO_CARD_CONSENT,
                PandoraHystrixNameConstants.AXIS_CBC_DEMO_CARD_CONSENT, pandoraHystrixProperties, request,
                clientService, validationToken, AxisApiConfigurationKeys.DEMO_CARD_CONSENT.getKey()).execute();
        assertThat(response.getHttpMessage()).isEqualTo(STATUS.DEMO_CARD_CONSENT_OTP_EXCEPTION.name());
        assertThat(response.getHttpCode()).isEqualTo(500);
    }

    @Test
    public void ApplyCardConsentHappyCase() throws AxisUserOnboardingServiceException {
        OTPGenerationRequest otpGenerationRequest = new OTPGenerationRequest();
        otpGenerationRequest.setDeviceId("8888");
        otpGenerationRequest.setMobileNumber("************");
        otpGenerationRequest.setApplicationReferenceId("123245");
        otpGenerationRequest.setOtpReferenceId("54321");
        ApplyCardConsentOTPGenRequest request = new ApplyCardConsentOTPGenRequest();
        String encryptedString = "ABC1235";
        SubHeader subHeader = new SubHeader();
        subHeader.setRequestUUID("ABC123");
        subHeader.setServiceRequestVersion("1.0");
        subHeader.setChannelId("FK");
        subHeader.setServiceRequestId("123");
        request.setSubHeader(subHeader);
        request.setApplyCardConsentOTPGenRequestBodyEncrypted(encryptedString);
        ApplyCardConsentOTPGenResponse applyCardConsentOTPGenResponse = new ApplyCardConsentOTPGenResponse();
        applyCardConsentOTPGenResponse.setApplyCardConsentOTPGenResponseBodyEncrypted(encryptedString);
        mapWithValidation.add(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON);
        doReturn(applyCardConsentOTPGenResponse).when(clientService)
                .getPostResponse(String.format(axisCbcConfiguration.getAxisCbcApiConfigurationsMap()
                        .get(AxisApiConfigurationKeys.APPLY_CARD_CONSENT.getKey())
                        .getApiPath()), Entity.json(request), mapWithValidation, ApplyCardConsentOTPGenResponse.class);
        ApplyCardConsentOTPGenResponse response = new ApplyCardConsentOtpCommand(PandoraHystrixNameConstants.AXIS_CBC,
                PandoraHystrixNameConstants.AXIS_CBC_DEMO_CARD_CONSENT,
                PandoraHystrixNameConstants.AXIS_CBC_DEMO_CARD_CONSENT, pandoraHystrixProperties, request,
                clientService, validationToken, AxisApiConfigurationKeys.APPLY_CARD_CONSENT.getKey()).execute();
        assertThat(response.getApplyCardConsentOTPGenResponseBodyEncrypted()).isEqualTo(encryptedString);
    }

    @Test
    public void ApplyCardConsentErrorCase() throws AxisUserOnboardingServiceException {
        OTPGenerationRequest otpGenerationRequest = new OTPGenerationRequest();
        otpGenerationRequest.setDeviceId("8888");
        otpGenerationRequest.setMobileNumber("************");
        otpGenerationRequest.setApplicationReferenceId("123245");
        otpGenerationRequest.setOtpReferenceId("54321");
        DemoConsentOTPGenerationRequest request = new DemoConsentOTPGenerationRequest();
        String encryptedString = "ABC1235";
        SubHeader subHeader = new SubHeader();
        subHeader.setRequestUUID("ABC123");
        subHeader.setServiceRequestVersion("1.0");
        subHeader.setChannelId("FK");
        subHeader.setServiceRequestId("123");
        request.setSubHeader(subHeader);
        request.setDemoConsentOTPGenerationRequestBodyEncrypted(encryptedString);
        DemoConsentOTPGenerationResponse demoConsentOTPGenerationResponse = new DemoConsentOTPGenerationResponse();
        demoConsentOTPGenerationResponse.setDemoConsentOTPGenerationResponseBodyEncrypted(encryptedString);
        doThrow(new RuntimeException("ABC124")).when(clientService)
                .getPostResponse(String.format(axisCbcConfiguration.getAxisCbcApiConfigurationsMap()
                                .get(AxisApiConfigurationKeys.APPLY_CARD_CONSENT.getKey())
                                .getApiPath()), Entity.json(request), mapWithValidation,
                        DemoConsentOTPGenerationResponse.class);
        DemoConsentOTPGenerationResponse response = new DemoConsentOtpCommand(PandoraHystrixNameConstants.AXIS_CBC,
                PandoraHystrixNameConstants.AXIS_CBC_DEMO_CARD_CONSENT,
                PandoraHystrixNameConstants.AXIS_CBC_DEMO_CARD_CONSENT, pandoraHystrixProperties, request,
                clientService, validationToken, AxisApiConfigurationKeys.APPLY_CARD_CONSENT.getKey()).execute();
        assertThat(response.getHttpMessage()).isEqualTo(STATUS.DEMO_CARD_CONSENT_OTP_EXCEPTION.name());
        assertThat(response.getHttpCode()).isEqualTo(500);
    }

    @Test
    public void FetchCustDemogsHappyCase() throws AxisUserOnboardingServiceException {
        FetchCustDemographicsRequest request = new FetchCustDemographicsRequest();
        SubHeader subHeader = new SubHeader();
        subHeader.setRequestUUID("ABC123");
        subHeader.setServiceRequestVersion("1.0");
        subHeader.setChannelId("FK");
        subHeader.setServiceRequestId("OtpGen");
        request.setSubHeader(subHeader);
        String encryptedString = "ABC1235";
        request.setFetchCustDemographicsRequestBodyEncrypted(encryptedString);
        FetchCustDemographicsResponse response = new FetchCustDemographicsResponse();
        response.setFetchCustomerDemographicsResponseBodyEncrypted(encryptedString);
        mapWithValidation.add(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON);
        doReturn(response).when(clientService)
                .getPostResponse(String.format(axisCbcConfiguration.getAxisCbcApiConfigurationsMap()
                        .get(AxisApiConfigurationKeys.FETCH_CUSTOMER_DEMOGRAPHICS.getKey())
                        .getApiPath()), Entity.json(request), mapWithValidation, FetchCustDemographicsResponse.class);
        response = new FetchCustDemographicsCommand(PandoraHystrixNameConstants.AXIS_CBC,
                PandoraHystrixNameConstants.AXIS_CBC_FETCH_CUST_DEMOGS,
                PandoraHystrixNameConstants.AXIS_CBC_FETCH_CUST_DEMOGS, pandoraHystrixProperties, request,
                clientService, validationToken, AxisApiConfigurationKeys.FETCH_CUSTOMER_DEMOGRAPHICS.getKey()).execute();
        assertThat(response.getFetchCustomerDemographicsResponseBodyEncrypted()).isEqualTo(encryptedString);
    }

    @Test
    public void FetchCustDemogsErrorCase() throws AxisUserOnboardingServiceException {
        FetchCustDemographicsRequest request = new FetchCustDemographicsRequest();
        SubHeader subHeader = new SubHeader();
        subHeader.setRequestUUID("ABC123");
        subHeader.setServiceRequestVersion("1.0");
        subHeader.setChannelId("FK");
        subHeader.setServiceRequestId("OtpGen");
        request.setSubHeader(subHeader);
        String encryptedString = "ABC1235";
        request.setFetchCustDemographicsRequestBodyEncrypted(encryptedString);
        FetchCustDemographicsResponse response = new FetchCustDemographicsResponse();
        response.setFetchCustomerDemographicsResponseBodyEncrypted(encryptedString);
        doThrow(new RuntimeException("ABC124")).when(clientService)
                .getPostResponse(String.format(axisCbcConfiguration.getAxisCbcApiConfigurationsMap()
                        .get(AxisApiConfigurationKeys.FETCH_CUSTOMER_DEMOGRAPHICS.getKey())
                        .getApiPath()), Entity.json(request), mapWithValidation, FetchCustDemographicsResponse.class);
        response = new FetchCustDemographicsCommand(PandoraHystrixNameConstants.AXIS_CBC,
                PandoraHystrixNameConstants.AXIS_CBC_FETCH_CUST_DEMOGS,
                PandoraHystrixNameConstants.AXIS_CBC_FETCH_CUST_DEMOGS, pandoraHystrixProperties, request,
                clientService, validationToken, AxisApiConfigurationKeys.FETCH_CUSTOMER_DEMOGRAPHICS.getKey()).execute();
        assertThat(response.getHttpMessage()).isEqualTo(STATUS.FETCH_CUST_DEMOGRAPHICS_EXCEPTION.name());
        assertThat(response.getHttpCode()).isEqualTo(500);
    }

//    @Test
//    public void CheckCaseStatusHappyCase() throws Exception {
//        CheckCaseStatusRequest request = new CheckCaseStatusRequest();
//        SubHeader subHeader = new SubHeader();
//        subHeader.setRequestUUID("ABC123");
//        subHeader.setServiceRequestVersion("1.0");
//        subHeader.setChannelId("FK");
//        subHeader.setServiceRequestId("OtpGen");
//        String encryptedString = "ABC1235";
//        request.setCheckCaseStatusRequestBodyEncrypted(encryptedString);
//        CheckCaseStatusResponse response = new CheckCaseStatusResponse();
//        response.setCheckCaseStatusResponseBodyEncrypted(encryptedString);
//        doReturn(response).when(clientService)
//                .getPostResponse(String.format(axisCbcConfiguration.getAxisCbcApiConfigurationsMap()
//                                .get(AxisApiConfigurationKeys.CHECK_CASE_STATUS.getKey())
//                                .getApiPath()), mapWithoutValidation, CheckCaseStatusResponse.class);
//        response = new CheckCaseStatusCommand(PandoraHystrixNameConstants.AXIS_CBC,
//                PandoraHystrixNameConstants.AXIS_CBC_PROCESS_NEW_CARD,
//                PandoraHystrixNameConstants.AXIS_CBC_PROCESS_NEW_CARD, pandoraHystrixProperties, request, clientService).execute();
//        assertThat(response.getCheckCaseStatusResponseBodyEncrypted()).isEqualTo("ABC1235");
//    }
//
//    @Test
//    public void CheckCaseStatusErrorCase() throws Exception {
//        CheckCaseStatusRequest request = new CheckCaseStatusRequest();
//        SubHeader subHeader = new SubHeader();
//        subHeader.setRequestUUID("ABC123");
//        subHeader.setServiceRequestVersion("1.0");
//        subHeader.setChannelId("FK");
//        subHeader.setServiceRequestId("OtpGen");
//        String encryptedString = "ABC1235";
//        request.setCheckCaseStatusRequestBodyEncrypted(encryptedString);
//        CheckCaseStatusResponse response = new CheckCaseStatusResponse();
//        response.setCheckCaseStatusResponseBodyEncrypted(encryptedString);
//        doThrow(new RuntimeException("ABC124")).when(clientService)
//                .getResponse(String.format(axisCbcConfiguration.getAxisCbcApiConfigurationsMap()
//                                .get(AxisApiConfigurationKeys.CHECK_CASE_STATUS.getKey())
//                                .getApiPath()), mapWithoutValidation, CheckCaseStatusResponse.class);
//        response = new CheckCaseStatusCommand(PandoraHystrixNameConstants.AXIS_CBC,
//                PandoraHystrixNameConstants.AXIS_CBC_PROCESS_NEW_CARD,
//                PandoraHystrixNameConstants.AXIS_CBC_PROCESS_NEW_CARD, pandoraHystrixProperties, request, clientService).execute();
//        assertThat(response.getHttpMessage()).isEqualTo(STATUS.CASE_STATUS_CHECK_EXCEPTION.name());
//        assertThat(response.getHttpCode()).isEqualTo(500);
//    }
    @Test
    public void checkCaseStatusMobile(){
        CheckCaseStatusRequest request = new CheckCaseStatusRequest();
        SubHeader subHeader = new SubHeader();
        subHeader.setRequestUUID("123");
        subHeader.setServiceRequestVersion("1.0");
        subHeader.setChannelId("FK");
        subHeader.setServiceRequestId("CheckCaseStatus");
        String encryptedString = "ABC1235";
        request.setCheckCaseStatusRequestBodyEncrypted(encryptedString);
        CheckCaseStatusResponse response = new CheckCaseStatusResponse();
        response.setCheckCaseStatusResponseBodyEncrypted(encryptedString);
        mapWithValidation.add(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON);
        doReturn(response).when(clientService)
                .getPostResponse(String.format(axisCbcConfiguration.getAxisCbcApiConfigurationsMap()
                        .get(AxisApiConfigurationKeys.CHECK_CASE_STATUS_MOBILE.getKey())
                        .getApiPath()), Entity.json(request), mapWithValidation, CheckCaseStatusResponse.class);


        response = new CheckCaseStatusMobileCommand(PandoraHystrixNameConstants.AXIS_CBC,
                PandoraHystrixNameConstants.AXIS_CBC_CHECK_CASE_STATUS_MOBILE,
                PandoraHystrixNameConstants.AXIS_CBC_CHECK_CASE_STATUS_MOBILE, pandoraHystrixProperties, request,
                clientService, validationToken, AxisApiConfigurationKeys.CHECK_CASE_STATUS_MOBILE.getKey()).execute();
        assertThat(response.getCheckCaseStatusResponseBodyEncrypted()).isEqualTo(encryptedString);
        assertThat(response.getCheckCaseStatusResponseBodyEncrypted()).isEqualTo("ABC1235");

    }

}
