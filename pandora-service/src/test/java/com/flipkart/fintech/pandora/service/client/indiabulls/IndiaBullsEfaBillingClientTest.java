package com.flipkart.fintech.pandora.service.client.indiabulls;

import com.flipkart.fintech.pandora.service.client.indiabulls.billing.DashboardRequest;
import org.junit.jupiter.api.BeforeEach;

/**
 * <AUTHOR> H <PERSON>vi
 */
public class IndiaBullsEfaBillingClientTest {
	
	private IndiaBullsBillingClient indiaBullsBillingClient;
	
	@BeforeEach
	public void setUp() throws Exception {
		this.indiaBullsBillingClient = new IndiaBullsBillingClient(new IndiaBullsClientConfig());
	}

	public void cardBalanceEnquiry() {
	}

	public void dashboardInformation() {
		DashboardRequest dashboardRequest = new DashboardRequest();
		dashboardRequest.setTransactionId("123456789");
		dashboardRequest.setCifNo("12312312");
		
		indiaBullsBillingClient.dashboardInformation(dashboardRequest);
	}
}