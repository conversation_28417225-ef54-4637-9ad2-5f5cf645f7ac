package com.flipkart.fintech.pandora.service.client.mapper.sandbox;

import static org.junit.jupiter.api.Assertions.*;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.pandora.service.client.pl.response.ErrorResponse;
import com.flipkart.fintech.pandora.service.client.utils.ObjectMapperUtil;
import org.junit.jupiter.api.Test;

class ErrorMapperTest {

  private final ObjectMapper objectMapper = ObjectMapperUtil.get();

  @Test
  void happyPathWhenClassifyErrorReturnsErrorResponse() throws Exception {
    String errorMessage =
        "Sorry, this pincode is not serviced by us currently. We will get in touch as soon as we start our operations here";
    String json =
        "{\"journeyState\":{\"applicationState\":\"APPLICATION_CREATION\",\"subState\":\""
            + errorMessage
            + "\",\"ts\":\"1743482167000\"}}";
    JsonNode node = objectMapper.readTree(json);
    ErrorResponse expectedError =
        ErrorResponse.builder()
            .code("ERR_SVR_UNSERVICEABLE_PINCODE")
            .reason("This pincode is not serviced yet")
            .detail(errorMessage.toLowerCase())
            .build();
    ErrorMapper mapper = new SandboxErrorMapper();
    ErrorResponse result = mapper.toObservedError(node);
    assertEquals(expectedError, result);
  }

  @Test
  void returnsEmptyErrorResponseWhenClassifyErrorReturnsNull() throws Exception {
    String errorMessage = "nonexistent error";
    String json =
        "{\"journeyState\":{\"applicationState\":\"APPLICATION_CREATION\",\"subState\":\""
            + errorMessage
            + "\",\"ts\":\"1743482167000\"}}";
    JsonNode node = objectMapper.readTree(json);
    ErrorMapper mapper = new SandboxErrorMapper();
    ErrorResponse result = mapper.toObservedError(node);
    assertEquals("ERR_SVR_UNKNOWN", result.getCode());
    assertNull(result.getReason());
    assertEquals(errorMessage.toLowerCase(), result.getDetail());
  }

  @Test
  void handleErrorResponseWhenJourneyHasSubStatus() throws Exception {
    String errorMessage = "";
    String json =
        "{\"lenderApplicationId\":\"\",\"validTill\":\"\",\"customerType\":\"\",\"applicationStatus\":\"FAILED\",\"journeyState\":{\"applicationState\":\"DATA_COLLECTION\",\"status\":\"FAILED\",\"subState\":\"" + errorMessage + "\",\"ts\":\"1745392069\",\"redirectionUrl\":\"\"}}";
    JsonNode node = objectMapper.readTree(json);
    ErrorMapper mapper = new SandboxErrorMapper();
    ErrorResponse result = mapper.toObservedError(node);
    assertEquals("ERR_SVR_UNKNOWN", result.getCode());
    assertNull(result.getReason());
    assertEquals(errorMessage.toLowerCase(), result.getDetail());
  }

  @Test
  void usesMessageFieldWhenSubstateMissing() throws Exception {
    String errorMessage = "Exception while parsing oauthToken";
    String json = "{\"Message\":\"" + errorMessage + "\"}";
    JsonNode node = objectMapper.readTree(json);
    ErrorMapper mapper = new SandboxErrorMapper();
    ErrorResponse result = mapper.toObservedError(node);
    assertEquals("ERR_CLI_AUTH_FAILED", result.getCode());
    assertEquals("Invalid auth code", result.getReason());
    assertEquals(errorMessage.toLowerCase(), result.getDetail());
  }

  @Test
  void happyPathWhenClassifyErrorReturnsInvalidPanErrorResponse() throws Exception {
    String errorMessage = "UserService CreatePAN non 2XX : The pan format is invalid.";
    String json =
        "{\"trace_id\":\"070f980721503aa218914b7db1484c27\",\"error\":{\"code\":\"2500\",\"message\":\"Internal Server Error\",\"details\":[{\"field\":\"Not Applicable\",\"message\":\""
            + errorMessage
            + "\"}]}}";
    JsonNode node = objectMapper.readTree(json);
    ErrorResponse expectedError =
        ErrorResponse.builder()
            .code("ERR_CLI_PAN_INVALID")
            .reason("Invalid PAN")
            .detail(errorMessage.toLowerCase())
            .build();
    ErrorMapper mapper = new SandboxErrorMapper();
    ErrorResponse result = mapper.toObservedError(node);
    assertEquals(expectedError, result);
  }
}
