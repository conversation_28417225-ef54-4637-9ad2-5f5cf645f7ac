package com.flipkart.fintech.pandora.service.client.mapper.sandbox;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

import com.flipkart.fintech.pandora.service.client.pl.response.ErrorResponse;
import javax.ws.rs.core.Response.Status;
import org.junit.jupiter.api.Test;

class SandboxErrorMapperTest {

  @Test
  void classifyErrorShouldReturnPanInvalidError() {
    String message = "invalid pan number provided";
    ErrorResponse error = SandboxErrorMapper.classifyError(message);
    assertEquals("ERR_CLI_PAN_INVALID", error.getCode());
    assertEquals("Invalid PAN", error.getReason());
  }

  @Test
  void classifyErrorShouldReturnPincodeError() {
    String message = "sorry, this pincode is not serviced by us currently. we will get in touch as soon as we start our operations here";
    ErrorResponse error = SandboxErrorMapper.classifyError(message);
    assertEquals("ERR_SVR_UNSERVICEABLE_PINCODE", error.getCode());
    assertEquals("This pincode is not serviced yet", error.getReason());
  }

  @Test
  void classifyErrorShouldReturnInvalidEmploymentTypeError() {
    String message = "employment type invalid";
    ErrorResponse error = SandboxErrorMapper.classifyError(message);
    assertEquals("ERR_CLI_INVALID_EMPLOYMENT_TYPE", error.getCode());
    assertEquals("Invalid employment type", error.getReason());
  }

  @Test
  void classifyErrorShouldReturnInvalidNameError() {
    String message = "invalid name provided";
    ErrorResponse error = SandboxErrorMapper.classifyError(message);
    assertEquals("ERR_CLI_INVALID_FIRST_NAME", error.getCode());
    assertEquals("Invalid first or last name", error.getReason());
  }

  @Test
  void classifyErrorShouldReturnPanFailedError() {
    String message = "userservice createpan non 2xx : pan verification failed";
    ErrorResponse error = SandboxErrorMapper.classifyError(message);
    assertEquals("ERR_SVR_VERIFY_PAN_FAILED", error.getCode());
    assertEquals("PAN verification failed at upstream. Retry.", error.getReason());
  }

  @Test
  void classifyErrorShouldReturnAuthError() {
    String message = "exception while parsing oauthtoken";
    ErrorResponse error = SandboxErrorMapper.classifyError(message);
    assertEquals("ERR_CLI_AUTH_FAILED", error.getCode());
    assertEquals("Invalid auth code", error.getReason());
  }

  @Test
  void classifyErrorShouldReturnTsError() {
    String message = "exceptions.RequestValidationException: For the consent: BUREAU_PULL, ts can't be greater than current timestamp\"";
    ErrorResponse error = SandboxErrorMapper.classifyError(message);
    assertEquals("ERR_CLI_TS_IN_FUTURE", error.getCode());
    assertEquals("Timestamp is greater than current timestamp", error.getReason());
  }

  @Test
  void classifyErrorShouldReturnDefaultErrorForUnknownMessage() {
    String message = "an unhandled error occurred";
    ErrorResponse error = SandboxErrorMapper.classifyError(message);
    assertNull(error.getCode());
    assertNull(error.getReason());
    assertEquals(message, error.getDetail());
  }

  @Test
  void getHttpStatusCodeShouldReturnBadRequestForMissingCode() {
    ErrorResponse error = ErrorResponse.builder().detail("Some detail").build();
    int status = SandboxErrorMapper.getHttpStatusCode(error);
    assertEquals(Status.BAD_REQUEST.getStatusCode(), status);
  }

  @Test
  void getHttpStatusCodeShouldReturnUnauthorizedForAuthCode() {
    ErrorResponse error = ErrorResponse.builder()
        .detail("Auth error")
        .code("ERR_CLI_AUTH_SOMETHING")
        .build();
    int status = SandboxErrorMapper.getHttpStatusCode(error);
    assertEquals(Status.UNAUTHORIZED.getStatusCode(), status);
  }

  @Test
  void getHttpStatusCodeShouldReturnServiceUnavailableForServerErrorCode() {
    ErrorResponse error = ErrorResponse.builder()
        .detail("Server error occurred")
        .code("ERR_SVR_NETWORK")
        .build();
    int status = SandboxErrorMapper.getHttpStatusCode(error);
    assertEquals(Status.SERVICE_UNAVAILABLE.getStatusCode(), status);
  }
}