package com.flipkart.fintech.pandora.service.client.mpockket;

import com.flipkart.fintech.pandora.service.client.pl.request.mpockket.MpockketEncryptedRequest;
import com.flipkart.fintech.pandora.service.client.cfa.ConfigHelper;
import com.flipkart.fintech.pandora.service.client.pl.client.MpockketClient;
import com.flipkart.fintech.pandora.service.client.pl.client.MpockketClientImpl;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.Mock;
import org.mockito.Mockito;

public class MpockketClientTest {
    @Mock
    private ConfigHelper configHelper;
    private static final String PRIVATE_KEY = "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDVlG+OuSsS3Z4Z" +
            "2QgEM65yFfK1uF5/nx9f8GphG0Wiu/NZir9tLNZGJYvXEQd35blJSGPWYUR8D1uk" +
            "o+8X354LxEPAowXO5keSqfU/OXci1fjUlB68iHxb1ZCV/nv5WBppsg3ukmccu+D6" +
            "H/g5FnMn0W3VDogs/EMXEGlGDkqwbKzpCYpWH8XIfD70OZD2GE/PxRohYxhlGlhK" +
            "GZlrY+N9/NVx42Wz/begM0FI/LlR5LSLZTfs8aTBa9CwDYzsypfEnq4WImBzYzsU" +
            "L1pRclNRhMQ5iSAk6HwCrt2WKJ2dCzb6Pcaon/XR8L3GTX/ZyWwcUL54y7cmd8LF" +
            "jFc71B15AgMBAAECggEAYAb/KXKDtVXDMefsvMbn1g+cMqwUgNjwaFhV3BdA9ev/" +
            "e/DwJIwWQy+6chYL/5Jf/lz31nmhKw5Uq++xHlDCZJP3ynMBt4a+A2k7BRQ3Rohh" +
            "+mImd6n+D+Qzv9zNrMw6A++0Z+GXTsoCWzNojXJUkECPW03PWDqLh6EvU/o1BqDj" +
            "n4G5diYfR7HuXvuUBvmjae1DkmF4AIXnZWM/GZxb/q9I9BWteYhOAsVZ3E97sU1u" +
            "4nAOWokvxQKpxiWgEeeWwnpUME1ZVUTjpNcf6jqhO1sJG0QFkF2Ni+J6mMLSQ3P2" +
            "lYOovjkWsHub99lPX35i9i57aWSs65Fu0mKc9UWnyQKBgQD0Em5Z++MQC5H0tpYw" +
            "qA2UvEM7ixoDDzoYxhlZ1IeWwFjE9MxTt4TXQxiUtCLCT+KjgHq9pzR1mkIjz+Po" +
            "e0IceyqibusfD0/oSjyuLR4NPCYk3E94V0DysRmixVanhFa6uNwR29xbzoAuENRa" +
            "DfJsgzmm3Xu/I/J+I0s7GWeZlwKBgQDgBITnjF7S6e0ABSY+PkAE+tzABpys/i4p" +
            "oMmP4EvJj4OTEw55FatSotJ0RNdBEyGIZjN3l2SDE8AFzHsdyK0w87X7Qx3xG86T" +
            "f9C/fKnxIe4b8nmTDZaYebQsEfNHjXgwwurVx8vHG5b1VASLWgv5SR0OQm7/4Qi2" +
            "vkli9bpDbwKBgEmIuAiSVDHEWqAQ1qUoLJdQNS1eVmLvloBaRs8CsisgHl6QpnLe" +
            "ir5UL7a1ovdKHC1IizJSiwKXjC+/9ZlYcW2lSg4QPvRnp5qDq06Kt1AjfBaE7ciC" +
            "+UVlSCT7MYVc4Mh2447Z38UlLQcz+NZ89znsO1Vjy2GmEewg9AyFNepXAoGBALJ+" +
            "h1NkAm413BSHBdjUivgs1tXertocUbXzmoccZea9hk4bAg4y2OaoO95Ot+DvGGnx" +
            "XRLuOzVB+F/mdTbfHjiCB7+j0+i4iln2nuLktxgeO/ZitsIYMqVdW9U9nEWoEFty" +
            "A+HBJ7qHXjot7s0GO8IuoospHf6eVO4fIqRKPVbbAoGBAOW2Y0t6v3x3Q0yntaKm" +
            "8rWUDsSK5VK+jeU6coThwlX3igzrRt50PUdCC+UIJ6voEp6sAKwYrTXRINnN9YwJ" +
            "jw3AHu0LFUKf0XF+9qbi1qSxdzZoLUWrN3paMnFrtFmv3BA1tPnk/bntIfHmXPcK" +
            "tfE/vTFpWzI8+QpjpkkLChP3";
    private static final String PUBLIC_KEY = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA1ZRvjrkrEt2eGdkIBDOu" +
            "chXytbhef58fX/BqYRtForvzWYq/bSzWRiWL1xEHd+W5SUhj1mFEfA9bpKPvF9+e" +
            "C8RDwKMFzuZHkqn1Pzl3ItX41JQevIh8W9WQlf57+VgaabIN7pJnHLvg+h/4ORZz" +
            "J9Ft1Q6ILPxDFxBpRg5KsGys6QmKVh/FyHw+9DmQ9hhPz8UaIWMYZRpYShmZa2Pj" +
            "ffzVceNls/23oDNBSPy5UeS0i2U37PGkwWvQsA2M7MqXxJ6uFiJgc2M7FC9aUXJT" +
            "UYTEOYkgJOh8Aq7dliidnQs2+j3GqJ/10fC9xk1/2clsHFC+eMu3JnfCxYxXO9Qd" +
            "eQIDAQAB";
    private MpockketClientImpl mpockketClient;
    @BeforeEach
    void setUp(){
        configHelper = Mockito.mock(ConfigHelper.class);
        mpockketClient = new MpockketClientImpl(configHelper);
    }
    @Test
    public void getEncryptedRequest(){
        try {
            String jsonString = "{\"data\":\"Hello World\"}";
            MpockketEncryptedRequest mpockketEncryptedRequest = mpockketClient.getEncryptedRequest(jsonString, PUBLIC_KEY);
            assert mpockketEncryptedRequest != null;
            assert mpockketEncryptedRequest.getEncryptedData() != null;
            assert mpockketEncryptedRequest.getEncryptedAesKey() != null;
            assert !mpockketEncryptedRequest.getEncryptedData().isEmpty();
            assert !mpockketEncryptedRequest.getEncryptedAesKey().isEmpty();
        }catch (Exception e){
            e.printStackTrace();
        }
    }
}
