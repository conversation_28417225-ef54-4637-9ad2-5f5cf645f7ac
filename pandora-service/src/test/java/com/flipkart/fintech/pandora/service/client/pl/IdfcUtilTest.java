package com.flipkart.fintech.pandora.service.client.pl;

import org.assertj.core.util.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.security.NoSuchAlgorithmException;
import java.util.List;

import static com.flipkart.fintech.pandora.service.TestConstants.IDFC_HASHING_SALT;

/**
 * <AUTHOR>
 * @since 13/09/19.
 */
public class IdfcUtilTest {

    @Mock
    private IdfcConfiguration idfcConfiguration;
    private IdfcUtil idfcUtil;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        idfcUtil = new IdfcUtil(idfcConfiguration);
    }

    @Test
    public void generateIdfcRequestIdFromAccountId() throws NoSuchAlgorithmException {
        Mockito.when(idfcConfiguration.getIdfcHashingSalt()).thenReturn(IDFC_HASHING_SALT);
        List<String> accountList = Lists.newArrayList("ACC13544417982806160","ACC13802163684106785","ACC14063669346407269","ACC14081760001777513","ACC35F3092FBB1649A1AC34AFEA8D75632EV","ACC13623648850319675","ACC4A87B36BC977421F9D47B41B681E85485","ACC3DCF7C88DE9747318A0416E4A579EAB3Y");
        for (String account : accountList) {
            String reqId = idfcUtil.generateIdfcRequestIdFromAccountId(account);
            System.out.printf(String.format("%s\t%s\n", account, reqId));
        }

    }
}
