package com.flipkart.fintech.pandora.service.client.pl;

import com.flipkart.fintech.pandora.service.utils.TijoriPandoraQueueUtil;
import org.junit.jupiter.api.Test;

import java.util.HashMap;

import static org.junit.Assert.*;

public class TijoriPandoraQueueUtilTest {

    @Test
    public void testPztMapping() {
        String pztMapFilePath = "src/main/resources/pztMap.json";
        HashMap<String, String> stuckPztMap = TijoriPandoraQueueUtil.getMapFromJsonFile(pztMapFilePath);
        assertEquals(stuckPztMap.size(), 955);
        assertFalse(stuckPztMap.keySet().contains("PZT2005301216T631J01"));
        assertTrue(stuckPztMap.keySet().contains("PZT2005301216T631J08"));
        assertEquals(stuckPztMap.get("PZT2005301216T631J08"), "PZT2005301216T631J07");
    }

    @Test
    public void testPztMapNotFound() {
        String pztMapFilePath = "src/main/resources/pztMap1.json";
        HashMap<String, String> stuckPztMap = TijoriPandoraQueueUtil.getMapFromJsonFile(pztMapFilePath);
        assertEquals(stuckPztMap.size(), 0);
    }
}
