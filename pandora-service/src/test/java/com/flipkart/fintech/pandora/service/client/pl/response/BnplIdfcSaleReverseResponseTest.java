package com.flipkart.fintech.pandora.service.client.pl.response;

import org.junit.Assert;
import org.junit.jupiter.api.Test;

public class BnplIdfcSaleReverseResponseTest {

    @Test
    public void isSuccess() {
        BnplIdfcSaleReverseResponse bnplIdfcSaleReverseResponse = new BnplIdfcSaleReverseResponse();
        Assert.assertFalse(bnplIdfcSaleReverseResponse.isSuccess());
        bnplIdfcSaleReverseResponse.setChequeId("0");
        Assert.assertFalse(bnplIdfcSaleReverseResponse.isSuccess());

        bnplIdfcSaleReverseResponse.setChequeId("null");
        Assert.assertFalse(bnplIdfcSaleReverseResponse.isSuccess());

        bnplIdfcSaleReverseResponse.setChequeId("123");
        Assert.assertTrue(bnplIdfcSaleReverseResponse.isSuccess());

        bnplIdfcSaleReverseResponse.setRespDesc("EOD is in progress...");
        Assert.assertFalse(bnplIdfcSaleReverseResponse.isSuccess());

    }
}
