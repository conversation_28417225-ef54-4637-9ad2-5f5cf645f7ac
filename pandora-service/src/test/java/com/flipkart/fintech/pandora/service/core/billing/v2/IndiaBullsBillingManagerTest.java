package com.flipkart.fintech.pandora.service.core.billing.v2;

import com.flipkart.fintech.pandora.api.model.CardPaymentInstrument;
import com.flipkart.fintech.pandora.api.model.billing.CreditUsageInformation;
import com.flipkart.fintech.pandora.api.model.billing.request.BillInfoRequest;
import com.flipkart.fintech.pandora.api.model.billing.request.CreditUsageRequest;
import com.flipkart.fintech.pandora.service.client.indiabulls.IndiaBullsBillingClient;
import com.flipkart.fintech.pandora.service.client.indiabulls.IndiaBullsBillingMockClient;
import com.flipkart.fintech.pandora.service.client.indiabulls.IndiaBullsClientConfig;
import com.flipkart.fintech.pandora.service.hystrix.PandoraHystrixProperties;
import com.flipkart.fintech.pandora.service.utils.PandoraContext;
import org.junit.jupiter.api.AfterEach;
import org.junit.Assert;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import javax.ws.rs.core.MultivaluedHashMap;
import javax.ws.rs.core.MultivaluedMap;
import java.math.BigDecimal;
import java.time.Month;
import java.time.Year;

/**
 * <AUTHOR> H Adavi
 */
public class IndiaBullsBillingManagerTest {
	
	private IndiaBullsBillingManager indiaBullsBillingManager;
	private IndiaBullsBillingClient indiaBullsBillingClient;
	private IndiaBullsBillingMockClient indiaBullsBillingMockClient;
	private PandoraHystrixProperties pandoraHystrixProperties;
	private IndiaBullsClientConfig indiaBullsClientConfig;
	
	@BeforeEach
	public void setUp() throws Exception {
		
		indiaBullsClientConfig = new IndiaBullsClientConfig();
		indiaBullsBillingClient = new IndiaBullsBillingClient(indiaBullsClientConfig);
		
		indiaBullsBillingMockClient = new IndiaBullsBillingMockClient();
		
		pandoraHystrixProperties = new PandoraHystrixProperties();
		
		indiaBullsBillingManager = new IndiaBullsBillingManager(indiaBullsBillingClient,indiaBullsBillingMockClient,pandoraHystrixProperties);
	}
	
	@AfterEach
	public void tearDown() throws Exception {
	}
	
	@Test
	public void fetchCreditUsage() {
		
		MultivaluedMap<String, String> headers = PandoraContext.getHeaders();
		if(null != headers){
			headers.add("X-Perf-Test","True");
		} else {
			headers = new MultivaluedHashMap<>();
			headers.add("X-Perf-Test","True");
		}
		PandoraContext.setHeaders(headers);
		
		CardPaymentInstrument cardPaymentInstrument = new CardPaymentInstrument();
		cardPaymentInstrument.setCardNumber("2867471011003713");
		
		CreditUsageRequest creditUsageRequest = new CreditUsageRequest();
		creditUsageRequest.setPaymentInstrument(cardPaymentInstrument);
		
		CreditUsageInformation creditUsageInformation = indiaBullsBillingManager.fetchCreditUsage(creditUsageRequest);
		
		Assert.assertNotNull(creditUsageInformation);
		Assert.assertEquals(new BigDecimal(60000),creditUsageInformation.getTotalCreditLimit());
		Assert.assertEquals(new BigDecimal(40000),creditUsageInformation.getTotalCreditUsage());
		Assert.assertNull(creditUsageInformation.getCreditUsageDetail());
	}
	
	
	//@Test
	public void fetchCreditUsageDetails() {
		
		MultivaluedMap<String, String> headers = PandoraContext.getHeaders();
		if(null != headers){
			headers.add("X-Perf-Test","True");
		} else {
			headers = new MultivaluedHashMap<>();
			headers.add("X-Perf-Test","True");
		}
		PandoraContext.setHeaders(headers);
		
		CardPaymentInstrument cardPaymentInstrument = new CardPaymentInstrument();
		cardPaymentInstrument.setCardNumber("2867471011003713");
		
		CreditUsageRequest creditUsageRequest = new CreditUsageRequest();
		creditUsageRequest.setPaymentInstrument(cardPaymentInstrument);
		creditUsageRequest.setLenderAccId("12312312");
		
		CreditUsageInformation creditUsageInformation = indiaBullsBillingManager.fetchCreditUsageDetails(creditUsageRequest);
		
		Assert.assertNotNull(creditUsageInformation);
	}
	
	@Test
	public void fetchBillInformation() {
		
		MultivaluedMap<String, String> headers = PandoraContext.getHeaders();
		if(null != headers){
			headers.add("X-Perf-Test","True");
		} else {
			headers = new MultivaluedHashMap<>();
			headers.add("X-Perf-Test","True");
		}
		PandoraContext.setHeaders(headers);
		
		CardPaymentInstrument cardPaymentInstrument = new CardPaymentInstrument();
		cardPaymentInstrument.setCardNumber("12312312");
		
		BillInfoRequest billInfoRequest = new BillInfoRequest();
		billInfoRequest.setMonth(Month.APRIL);
		billInfoRequest.setYear(Year.now());
		
	}
}