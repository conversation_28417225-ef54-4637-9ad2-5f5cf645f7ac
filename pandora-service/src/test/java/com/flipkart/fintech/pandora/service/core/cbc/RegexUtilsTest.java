package com.flipkart.fintech.pandora.service.core.cbc;

import com.flipkart.fintech.pandora.service.core.cbc.utils.RegexUtils;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;


class RegexUtilsTest {

    @Test
    void checkStringWithMultipleRegex() {
        List<String> regexs = new ArrayList<>();
        regexs.add("^[a-zA-Z\\s]{1,19}$");
        regexs.add("^(?!.*\\s{2})[\\s\\S]*$");
        regexs.add("^(?=.*[a-zA-Z]{2,})[a-zA-Z]+(?:\\s[a-zA-Z]+){1,2}$");

        boolean result = RegexUtils.checkStringWithMultipleRegex(regexs, "Lorem");
        assertFalse(result);
        result = RegexUtils.checkStringWithMultipleRegex(regexs, "Lorem Ipsum");
        assertTrue(result);
        result = RegexUtils.checkStringWithMultipleRegex(regexs, "Lorem  Ipsum");
        assertFalse(result);
        result = RegexUtils.checkStringWithMultipleRegex(regexs, "Lorem IpsumDolar SitAmet");
        assertFalse(result);
        result = RegexUtils.checkStringWithMultipleRegex(regexs, "Lorem I Dolar");
        assertTrue(result);
        result = RegexUtils.checkStringWithMultipleRegex(regexs, "Lorem I");
        assertTrue(result);
        result = RegexUtils.checkStringWithMultipleRegex(regexs, "L Ipsum Dolar");
        assertTrue(result);
        result = RegexUtils.checkStringWithMultipleRegex(regexs, "L I Dolar");
        assertTrue(result);
        result = RegexUtils.checkStringWithMultipleRegex(regexs, "Lorem Ipsum Dolar");
        assertTrue(result);
        result = RegexUtils.checkStringWithMultipleRegex(regexs, "Lo Ip");
        assertTrue(result);
        result = RegexUtils.checkStringWithMultipleRegex(regexs, "Lo Ip ");
        assertFalse(result);
        result = RegexUtils.checkStringWithMultipleRegex(regexs, "1234");
        assertFalse(result);
    }
}