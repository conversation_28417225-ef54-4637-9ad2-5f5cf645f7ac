package com.flipkart.fintech.pandora.service.core.cbc;

import static org.mockito.Mockito.*;
import static org.junit.jupiter.api.Assertions.*;

import com.flipkart.fintech.pandora.api.model.enums.cbc.CardForm;
import com.flipkart.fintech.pandora.api.model.enums.cbc.PhysicalCardEligibilityStatus;
import com.flipkart.fintech.pandora.api.model.enums.cbc.UpswingCardStatus;
import com.flipkart.fintech.pandora.api.model.response.cbc.upswing.UpswingAccountDetails;
import com.flipkart.fintech.pandora.service.client.cbc.exceptions.ErrorResponse;
import com.flipkart.fintech.pandora.service.core.cbc.manager.impl.UpswingAccountDetailsManagerImpl;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import com.flipkart.fintech.pandora.service.client.cbc.upswing.response.UpswingCreditCardAccountDetails;
import com.flipkart.fintech.pandora.service.client.cbc.upswing.services.UpswingServiceClient;
import com.flipkart.fintech.pandora.service.client.cbc.exceptions.CbcException;

import java.time.LocalDate;
import java.util.concurrent.ExecutionException;

@ExtendWith(MockitoExtension.class)
public class UpswingAccountDetailsManagerImplTest {

    @Mock
    private UpswingServiceClient upswingServiceClient;

    @InjectMocks
    private UpswingAccountDetailsManagerImpl upswingAccountDetailsManager;

    @Test
    void UserWithVirtualCardWithoutFees() throws ExecutionException, CbcException {
        String userId = "validUserId";
        UpswingCreditCardAccountDetails mockDetails = new UpswingCreditCardAccountDetails();
        mockDetails.setShouldNudgeForAdditionalFD(true);
        mockDetails.setBillGenerated(true);
        mockDetails.setBillPaid(true);
        mockDetails.setPaymentDueDate(LocalDate.of(2023, 12, 31));
        mockDetails.setOverdue(false);
        mockDetails.setCardStatus(UpswingCardStatus.ACTIVE);
        mockDetails.setCardForm(CardForm.VIRTUAL);
        mockDetails.setPhysicalCardEligibilityStatus(PhysicalCardEligibilityStatus.ELIGIBLE_WITHOUT_FEES);

        when(upswingServiceClient.fetchAccountDetails(userId)).thenReturn(mockDetails);

        UpswingAccountDetails result = upswingAccountDetailsManager.getAccountDetails(userId);

        assertNotNull(result);
        assertTrue(result.getShouldNudgeForAdditionalFD());
        assertTrue(result.getBillGenerated());
        assertTrue(result.getBillPaid());
        assertEquals(LocalDate.of(2023, 12, 31), result.getPaymentDueDate());
        assertFalse(result.getOverdue());
        assertEquals(UpswingCardStatus.ACTIVE, result.getCardStatus());
        assertEquals(CardForm.VIRTUAL, result.getCardForm());
        assertEquals(PhysicalCardEligibilityStatus.ELIGIBLE_WITHOUT_FEES, result.getPhysicalCardEligibilityStatus());

        verify(upswingServiceClient).fetchAccountDetails(userId);
    }

    @Test
    void UserWithPhysicalCard() throws ExecutionException, CbcException {
        String userId = "invalidUserId";
        UpswingCreditCardAccountDetails mockDetails = new UpswingCreditCardAccountDetails();
        mockDetails.setShouldNudgeForAdditionalFD(true);
        mockDetails.setBillGenerated(true);
        mockDetails.setBillPaid(true);
        mockDetails.setPaymentDueDate(LocalDate.of(2023, 12, 31));
        mockDetails.setOverdue(false);
        mockDetails.setCardStatus(UpswingCardStatus.ACTIVE);
        mockDetails.setCardForm(CardForm.PHYSICAL);
        mockDetails.setPhysicalCardEligibilityStatus(PhysicalCardEligibilityStatus.ELIGIBLE_WITHOUT_FEES);

        when(upswingServiceClient.fetchAccountDetails(userId)).thenReturn(mockDetails);

        UpswingAccountDetails result = upswingAccountDetailsManager.getAccountDetails(userId);

        assertNotNull(result);
        assertTrue(result.getShouldNudgeForAdditionalFD());
        assertTrue(result.getBillGenerated());
        assertTrue(result.getBillPaid());
        assertEquals(LocalDate.of(2023, 12, 31), result.getPaymentDueDate());
        assertFalse(result.getOverdue());
        assertEquals(UpswingCardStatus.ACTIVE, result.getCardStatus());
        assertEquals(CardForm.VIRTUAL, result.getCardForm());
        assertEquals(PhysicalCardEligibilityStatus.ELIGIBLE_WITHOUT_FEES, result.getPhysicalCardEligibilityStatus());

        when(upswingServiceClient.fetchAccountDetails(userId)).thenThrow(new ExecutionException(new Exception("Service failure")));

        assertThrows(ExecutionException.class, () -> upswingAccountDetailsManager.getAccountDetails(userId));
    }

    @Test
    void UserWithVirtualCardWithFees() throws ExecutionException, CbcException {
        String userId = "validUserIdWithoutFees";
        UpswingCreditCardAccountDetails mockDetails = new UpswingCreditCardAccountDetails();
        mockDetails.setShouldNudgeForAdditionalFD(true);
        mockDetails.setBillGenerated(true);
        mockDetails.setBillPaid(true);
        mockDetails.setPaymentDueDate(LocalDate.of(2023, 12, 31));
        mockDetails.setOverdue(false);
        mockDetails.setCardStatus(UpswingCardStatus.ACTIVE);
        mockDetails.setCardForm(CardForm.VIRTUAL);
        mockDetails.setPhysicalCardEligibilityStatus(PhysicalCardEligibilityStatus.ELIGIBLE_WITH_FEES);

        when(upswingServiceClient.fetchAccountDetails(userId)).thenReturn(mockDetails);

        UpswingAccountDetails result = upswingAccountDetailsManager.getAccountDetails(userId);

        assertNotNull(result);
        assertTrue(result.getShouldNudgeForAdditionalFD());
        assertTrue(result.getBillGenerated());
        assertTrue(result.getBillPaid());
        assertEquals(LocalDate.of(2023, 12, 31), result.getPaymentDueDate());
        assertFalse(result.getOverdue());
        assertEquals(UpswingCardStatus.ACTIVE, result.getCardStatus());
        assertEquals(CardForm.VIRTUAL, result.getCardForm());
        assertEquals(PhysicalCardEligibilityStatus.ELIGIBLE_WITH_FEES, result.getPhysicalCardEligibilityStatus());

        when(upswingServiceClient.fetchAccountDetails(userId)).thenThrow(new ExecutionException(new Exception("Service failure")));

        assertThrows(ExecutionException.class, () -> upswingAccountDetailsManager.getAccountDetails(userId));
    }
}