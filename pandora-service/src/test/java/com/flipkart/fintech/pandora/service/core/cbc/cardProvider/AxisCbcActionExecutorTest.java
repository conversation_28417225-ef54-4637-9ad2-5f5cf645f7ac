package com.flipkart.fintech.pandora.service.core.cbc.cardProvider;

import com.axis.model.GetAppStatusInputGetAppStatusRequest;
import com.axis.model.GetAppStatusOutput;
import com.axis.model.KycStatusReqGetKycStatusRequest;
import com.axis.model.KycStatusRes;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.pandora.api.model.cbc.enums.axis.CustomerType;
import com.flipkart.fintech.pandora.api.model.cbc.request.axis.ApplicationStatusRequest;
import com.flipkart.fintech.pandora.api.model.cbc.request.axis.AxisDisplayNameValidationRequest;
import com.flipkart.fintech.pandora.api.model.cbc.request.common.ValidateDetailsRequest;
import com.flipkart.fintech.pandora.api.model.cbc.request.common.ValidateDetailsRequestType;
import com.flipkart.fintech.pandora.api.model.cbc.response.PandoraResponseWrapper;
import com.flipkart.fintech.pandora.api.model.cbc.response.axis.AxisDisplayNameValidationResponse;
import com.flipkart.fintech.pandora.api.model.cbc.response.axis.KycAppStatusResponse;
import com.flipkart.fintech.pandora.api.model.cbc.response.common.ValidateDetailsResponse;
import com.flipkart.fintech.pandora.service.client.cbc.axis.SmAxisCbcConfiguration;
import com.flipkart.fintech.pandora.service.client.cbc.axis.client.onboarding.AxisOnboardingClient;
import com.flipkart.fintech.pandora.service.client.cbc.exceptions.CbcException;
import com.flipkart.fintech.pandora.service.client.cbc.exceptions.ErrorResponse;
import com.flipkart.fintech.pandora.service.core.cbc.fieldValidators.FieldValidatorFactory;
import com.flipkart.fintech.pandora.service.core.cbc.fieldValidators.axis.AxisDisplayNameFieldValidator;
import com.flipkart.fintech.pandora.service.core.cbc.transformers.AxisTransformers;
import org.elasticsearch.common.collect.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;

@ExtendWith(MockitoExtension.class)
class AxisCbcActionExecutorTest {
    ObjectMapper objectMapper = new ObjectMapper();

    @Mock
    AxisOnboardingClient axisOnboardingClient;

    @Spy
    AxisTransformers axisTransformers = new AxisTransformers(new SmAxisCbcConfiguration(), objectMapper, null);

    @Spy
    FieldValidatorFactory fieldValidatorFactory = new FieldValidatorFactory(
            Map.of(ValidateDetailsRequestType.AXIS_DISPLAY_NAME_VALIDATION, new AxisDisplayNameFieldValidator(new SmAxisCbcConfiguration()))
    );

    @InjectMocks
    AxisCbcActionExecutor executor;

    @BeforeEach
    void setUp() {
        executor = new AxisCbcActionExecutor(axisOnboardingClient, axisTransformers, fieldValidatorFactory);
    }

    @Test
    void expectToValidateDetailsReturnTrue() throws CbcException {
        ValidateDetailsRequest request = new ValidateDetailsRequest();
        request.setType(ValidateDetailsRequestType.AXIS_DISPLAY_NAME_VALIDATION);
        AxisDisplayNameValidationRequest validationRequest = AxisDisplayNameValidationRequest.builder().oldName("Karan").newName("Karan Modh").build();
        request.setFieldValidationRequest(validationRequest);
        PandoraResponseWrapper<ValidateDetailsResponse> response = executor.validateDetails(request);
        System.out.println(response);

        AxisDisplayNameValidationResponse expectedValidationResponse = AxisDisplayNameValidationResponse.builder().isNameValid(true).build();

        assertNotNull(response.getResponse());
        assertEquals(ValidateDetailsRequestType.AXIS_DISPLAY_NAME_VALIDATION, response.getResponse().getType());
        assertEquals(expectedValidationResponse.toString(), response.getResponse().getFieldValidationResponse().toString());
    }

    @Test
    void expectToGetKycAppStatus() throws CbcException, JsonProcessingException {
        ApplicationStatusRequest applicationStatusRequest = new ApplicationStatusRequest();
        applicationStatusRequest.setApplicationId("APP12345");
        applicationStatusRequest.setCustType(CustomerType.NTB);
        applicationStatusRequest.setPhoneNumber("**********");

        GetAppStatusOutput appStatusOutput = objectMapper.readValue("{\"GetAppStatusResponse\":{\"SubHeader\":{\"requestUUID\":\"8ee3695d-1844-4ba9-a31e-0985e\",\"serviceRequestId\":\"AX.GEN.ICC.MAIN.APP.STATUS\",\"serviceRequestVersion\":\"1.0\",\"channelId\":\"XXX\"},\"GetAppStatusResponseBody\":{\"data\":{\"applicationState\":\"ACCOUNT_CREATED\",\"accountToken\":\"123456\"}}}}", GetAppStatusOutput.class);
        KycStatusRes kycStatusRes = objectMapper.readValue("{\"GetKycStatusResponse\":{\"SubHeader\":{\"requestUUID\":\"d71c3427-93b0-4b7d-9803-76da5c\",\"serviceRequestId\":\"AX.GEN.ICC.GET.KYC.STATUS\",\"serviceRequestVersion\":\"1.0\",\"channelId\":\"XXX\"},\"GetKycStatusResponseBody\":{\"data\":{\"kyc\":{\"eKyc\":{\"lastUpdateTimestamp\":{\"epochMillis\":*************},\"expirationTimestamp\":{\"epochMillis\":*************},\"notStartedStateContext\":{}},\"vKyc\":{\"lastUpdateTimestamp\":{\"epochMillis\":*************},\"expirationTimestamp\":{\"epochMillis\":*************},\"notStartedStateContext\":{}},\"offlineKyc\":{\"lastUpdateTimestamp\":{\"epochMillis\":*************},\"notStartedStateContext\":{}},\"state\":\"NOT_STARTED\"}}}}}", KycStatusRes.class);

        Mockito.when(axisOnboardingClient.fetchApplicationStatus(any(GetAppStatusInputGetAppStatusRequest.class))).thenReturn(appStatusOutput);
        Mockito.when(axisOnboardingClient.fetchKycStatus(any(KycStatusReqGetKycStatusRequest.class))).thenReturn(kycStatusRes);

        KycAppStatusResponse kycAppStatusResponse = executor.getKycAppStatus(applicationStatusRequest);

        assertNotNull(kycAppStatusResponse);
    }

    @Test
    void expectToGetKycAppStatusThrowException() throws CbcException, JsonProcessingException {
        ApplicationStatusRequest applicationStatusRequest = new ApplicationStatusRequest();
        applicationStatusRequest.setApplicationId("APP12345");
        applicationStatusRequest.setCustType(CustomerType.NTB);
        applicationStatusRequest.setPhoneNumber("**********");

        GetAppStatusOutput appStatusOutput = objectMapper.readValue("{\"GetAppStatusResponse\":{\"SubHeader\":{\"requestUUID\":\"8ee3695d-1844-4ba9-a31e-0985e\",\"serviceRequestId\":\"AX.GEN.ICC.MAIN.APP.STATUS\",\"serviceRequestVersion\":\"1.0\",\"channelId\":\"XXX\"},\"GetAppStatusResponseBody\":{\"data\":{\"applicationState\":\"ACCOUNT_CREATED\",\"accountToken\":\"123456\"}}}}", GetAppStatusOutput.class);
        KycStatusRes kycStatusRes = objectMapper.readValue("{\"GetKycStatusResponse\":{\"SubHeader\":{\"requestUUID\":\"d71c3427-93b0-4b7d-9803-76da5c\",\"serviceRequestId\":\"AX.GEN.ICC.GET.KYC.STATUS\",\"serviceRequestVersion\":\"1.0\",\"channelId\":\"XXX\"},\"GetKycStatusResponseBody\":{\"data\":{\"kyc\":{\"eKyc\":{\"lastUpdateTimestamp\":{\"epochMillis\":*************},\"expirationTimestamp\":{\"epochMillis\":*************},\"notStartedStateContext\":{}},\"vKyc\":{\"lastUpdateTimestamp\":{\"epochMillis\":*************},\"expirationTimestamp\":{\"epochMillis\":*************},\"notStartedStateContext\":{}},\"offlineKyc\":{\"lastUpdateTimestamp\":{\"epochMillis\":*************},\"notStartedStateContext\":{}},\"state\":\"NOT_STARTED\"}}}}}", KycStatusRes.class);

        Mockito.when(axisOnboardingClient.fetchApplicationStatus(any(GetAppStatusInputGetAppStatusRequest.class))).thenThrow(new CbcException(ErrorResponse.builder().errorCode("CCIC01").errorDescription("Invalid OTP").build()));
        Mockito.verify(axisOnboardingClient, Mockito.never()).fetchKycStatus(any(KycStatusReqGetKycStatusRequest.class));

        assertThrows(CbcException.class, () -> executor.getKycAppStatus(applicationStatusRequest));
    }

}