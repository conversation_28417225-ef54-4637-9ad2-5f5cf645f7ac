package com.flipkart.fintech.pandora.service.core.cbc.fieldValidators.axis;

import com.flipkart.fintech.pandora.api.model.cbc.request.axis.AxisDisplayNameValidationRequest;
import com.flipkart.fintech.pandora.api.model.cbc.request.common.FieldValidationRequest;
import com.flipkart.fintech.pandora.api.model.cbc.response.axis.AxisDisplayNameValidationResponse;
import com.flipkart.fintech.pandora.service.client.cbc.axis.SmAxisCbcConfiguration;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class AxisDisplayNameFieldValidatorTest {

    @Mock
    SmAxisCbcConfiguration smAxisCbcConfiguration;

    AxisDisplayNameFieldValidator axisDisplayNameFieldValidator;

    @BeforeEach
    void setUp() {
        axisDisplayNameFieldValidator = new AxisDisplayNameFieldValidator(smAxisCbcConfiguration);
    }

    @Test
    void testNameValidationForOnlyNewName() {
        FieldValidationRequest request = AxisDisplayNameValidationRequest.builder().newName("Karan").build();
        AxisDisplayNameValidationResponse result = axisDisplayNameFieldValidator.validate(request);
        assertFalse(result.getIsNameValid());
        request = AxisDisplayNameValidationRequest.builder().newName("Karan Modh").build();
        result = axisDisplayNameFieldValidator.validate(request);
        assertTrue(result.getIsNameValid());
    }

    @Test
    void testNameValidationForOnlyOldName() {
        FieldValidationRequest request = AxisDisplayNameValidationRequest.builder().oldName("Karan").build();
        AxisDisplayNameValidationResponse result = axisDisplayNameFieldValidator.validate(request);
        assertFalse(result.getIsNameValid());
        request = AxisDisplayNameValidationRequest.builder().oldName("Karan Modh").build();
        result = axisDisplayNameFieldValidator.validate(request);
        assertTrue(result.getIsNameValid());
    }

    @Test
    void testNameValidationForBothNameEmpty() {
        FieldValidationRequest request = AxisDisplayNameValidationRequest.builder().build();
        AxisDisplayNameValidationResponse result = axisDisplayNameFieldValidator.validate(request);
        assertFalse(result.getIsNameValid());
    }

    @Test
    void testNameValidationForBothNamePresent() {
        Mockito.when(smAxisCbcConfiguration.getDisplayNameMatchThreshold()).thenReturn(70);
        FieldValidationRequest request = AxisDisplayNameValidationRequest.builder().newName("Karan Modh").oldName("Karan").build();
        AxisDisplayNameValidationResponse result = axisDisplayNameFieldValidator.validate(request);
        assertTrue(result.getIsNameValid());

        request = AxisDisplayNameValidationRequest.builder().newName("Karan Sanjay Modh").oldName("Karan").build();
        result = axisDisplayNameFieldValidator.validate(request);
        assertFalse(result.getIsNameValid());
    }

}