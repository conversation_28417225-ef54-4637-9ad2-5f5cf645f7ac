package com.flipkart.fintech.pandora.service.core.cbc.manager;

import com.flipkart.fintech.pandora.api.model.request.cbc.upswing.*;
import com.flipkart.fintech.pandora.service.core.cbc.manager.impl.UpswingWebhookEventsManagerImpl;
import com.flipkart.fintech.sm.card.gateway.client.CbcWebhookEventsCardGatewayClient;
import com.flipkart.kloud.config.DynamicBucket;
import com.sumo.bff.card.client.CbcWebhookClient;
import java.util.List;
import org.junit.Before;
import org.junit.Test;
import org.mockito.*;

import java.util.Collections;

import static org.mockito.Mockito.*;

public class UpswingWebhookEventsManagerImplTest {

    @Mock
    private CbcWebhookEventsCardGatewayClient cardGatewayClient;

    @Mock
    private CbcWebhookClient cbcWebhookClient;

    @Mock
    private DynamicBucket dynamicBucket;

    @InjectMocks
    private UpswingWebhookEventsManagerImpl upswingWebhookEventsManager;

    @Captor
    private ArgumentCaptor<com.sumo.bff.webhook.CommunicationRequest> communicationRequestCaptor;

    @Captor
    private ArgumentCaptor<com.sumo.bff.webhook.AnalyticsRequest> analyticsRequestArgumentCaptor;

    @Captor
    private ArgumentCaptor<com.sumo.bff.webhook.RewardsRequest> rewardsRequestArgumentCaptor;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testSendCommunicationEvent_MigratedToCardService() throws Exception {
        // Arrange
        CommRequest commRequest = new CommRequest();
        commRequest.setUserId("user123");
        commRequest.setEventType("EVENT_TYPE_X");
        commRequest.setChannelDataList(Collections.singletonList(new CommChannelData()));

        when(dynamicBucket.getStringArray("migrateToCardServiceForUpswingCBCWebhookEvents")).thenReturn(Collections.singletonList("communication"));

        UpswingWebhookEventsManagerImpl spyManager = Mockito.spy(upswingWebhookEventsManager);

        // Act
        spyManager.sendComm(commRequest);

        verify(cbcWebhookClient, times(1)).sendCommunicationWebhookRequest(communicationRequestCaptor.capture());
        verify(cardGatewayClient, never()).sendCommunication(any());
    }

    @Test
    public void testSendAnalyticsEvent_MigratedToCardService() throws Exception {
        // Arrange
        AnalyticsRequest analyticsRequest = new AnalyticsRequest();

        when(dynamicBucket.getStringArray("migrateToCardServiceForUpswingCBCWebhookEvents")).thenReturn(Collections.singletonList("analytics"));

        UpswingWebhookEventsManagerImpl spyManager = Mockito.spy(upswingWebhookEventsManager);

        // Act
        spyManager.sendAnalytics(analyticsRequest);

        verify(cbcWebhookClient, times(1)).sendAnalyticsWebhookRequest(analyticsRequestArgumentCaptor.capture());
        verify(cardGatewayClient, never()).sendAnalytics(any());
    }

    @Test
    public void testSendRewardsEvent_MigratedToCardService() throws Exception {
        // Arrange
        UpswingRewardsRequestV2 rewardsRequestV2 = new UpswingRewardsRequestV2();
        UpswingUpdatedRewardDetailsV2 updatedRewardDetailsV2 = new UpswingUpdatedRewardDetailsV2();
        updatedRewardDetailsV2.setUpswingRedeemableReward(new UpswingRewardDetailsV2());
        updatedRewardDetailsV2.setUpswingPotentialReward(new UpswingRewardDetailsV2());

        rewardsRequestV2.setUpdatedRewardDetails(updatedRewardDetailsV2);

        when(dynamicBucket.getStringArray("migrateToCardServiceForUpswingCBCWebhookEvents")).thenReturn(Collections.singletonList("rewards"));

        UpswingWebhookEventsManagerImpl spyManager = Mockito.spy(upswingWebhookEventsManager);

        // Act
        spyManager.sendRewardsV2(rewardsRequestV2);

        verify(cbcWebhookClient, times(1)).sendRewardsWebhookRequest(rewardsRequestArgumentCaptor.capture());
        verify(cardGatewayClient, never()).sendRewardsEventV2(any());
    }

}
