package com.flipkart.fintech.pandora.service.core.cbc.transformers;

import com.axis.model.GetAppStatusOutput;
import com.axis.model.InitiateChallengeInputInitiateChallengeRequest;
import com.axis.model.KycStatusRes;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.pandora.api.model.cbc.enums.axis.State;
import com.flipkart.fintech.pandora.api.model.cbc.request.axis.InitiateChallengeRequest;
import com.flipkart.fintech.pandora.api.model.cbc.response.axis.KycAppStatusResponse;
import com.flipkart.fintech.pandora.api.model.cbc.response.axis.KycStatusResponse;
import com.flipkart.fintech.pandora.service.client.cbc.axis.SmAxisCbcConfiguration;
import com.flipkart.fintech.pandora.service.client.cbc.exceptions.CbcException;
import com.flipkart.kloud.config.DynamicBucket;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.FileNotFoundException;
import java.io.InputStream;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class AxisTransformersTest {

    @Mock
    SmAxisCbcConfiguration smAxisCbcConfig;
    @Mock
    DynamicBucket featureFlagBucket;
    @Spy
    ObjectMapper objectMapper;

    AxisTransformers axisTransformer;

    @BeforeEach
    void setup() {
        this.axisTransformer = new AxisTransformers(smAxisCbcConfig, objectMapper, featureFlagBucket);
        Mockito.lenient().when(featureFlagBucket.getBoolean("cbc.piiData.isEncrypted")).thenReturn(true);
        Mockito.lenient().when(featureFlagBucket.getString("cbc.piiData.encryptionKey")).thenReturn("cGlpRW5jcnlwdGlvbktleQ==");
    }

    @AfterEach
    void teardown() {
        axisTransformer = null;
    }

    @ParameterizedTest
    @MethodSource("provideArgsForKycStatus")
    void expectToTestKycStatusResponseFromAxis(String testCaseKey, boolean incomeIsNull, boolean incomeRequired, State incomeState, String incomeStatus, State finalState, String finalStatus, State kycState, String kycStatus) {

        Map<String, Object> statusRes = readJson("axisCbcKycStatusResponse.json", Map.class);
        KycStatusRes response = objectMapper.convertValue(statusRes.get(testCaseKey), KycStatusRes.class);
        KycStatusResponse outResponse = axisTransformer.axisKycStatusToKycStatusRes(response);
        System.out.println(outResponse);
        assertNotNull(outResponse);

        // Income Verification checks
        assertEquals(incomeIsNull, Objects.isNull(outResponse.getIncomeVerification()));
        assertEquals(incomeRequired, outResponse.getIncomeVerificationRequired());
        if (outResponse.getIncomeVerificationRequired()) {
            assertEquals(incomeState, outResponse.getIncomeVerification().getState());
            assertEquals(incomeStatus, outResponse.getIncomeVerification().getStatus());
        }

        // Final Status checks
        assertEquals(finalStatus, outResponse.getStatus());
        assertEquals(finalState, outResponse.getState());

        // kyc checks
        assertEquals(kycState, outResponse.getKyc().getState());
        assertEquals(kycStatus, outResponse.getKyc().getStatus());
    }

    @ParameterizedTest
    @MethodSource("provideArgsForInitiateChallenge")
    void expectToTestInitiateChallengeRequest(String testCaseKey, String decryptedPan) throws CbcException {

        Map<String, Object> reqJson = readJson("axisCbcInitiateChallengeRequest.json", Map.class);
        InitiateChallengeRequest request = objectMapper.convertValue(reqJson.get(testCaseKey), InitiateChallengeRequest.class);
        InitiateChallengeInputInitiateChallengeRequest outResponse = axisTransformer.initiateChallengeRequestToAxisInitiateChallengeRequest(request);
        System.out.println(outResponse);
        assertNotNull(outResponse);

        assertNotNull(outResponse.getInitiateChallengeRequestBody().getData().getPan());
        assertEquals(decryptedPan, outResponse.getInitiateChallengeRequestBody().getData().getPan());
    }

    @Test
    void expectToTestAxisKycAppStatusResToKycAppStatusResponse() throws JsonProcessingException {
        GetAppStatusOutput appStatusOutput = objectMapper.readValue("{\"GetAppStatusResponse\":{\"SubHeader\":{\"requestUUID\":\"8ee3695d-1844-4ba9-a31e-0985e\",\"serviceRequestId\":\"AX.GEN.ICC.MAIN.APP.STATUS\",\"serviceRequestVersion\":\"1.0\",\"channelId\":\"XXX\"},\"GetAppStatusResponseBody\":{\"data\":{\"applicationState\":\"ACCOUNT_CREATED\",\"accountToken\":\"123456\"}}}}", GetAppStatusOutput.class);
        KycStatusRes kycStatusRes = objectMapper.readValue("{\"GetKycStatusResponse\":{\"SubHeader\":{\"requestUUID\":\"d71c3427-93b0-4b7d-9803-76da5c\",\"serviceRequestId\":\"AX.GEN.ICC.GET.KYC.STATUS\",\"serviceRequestVersion\":\"1.0\",\"channelId\":\"XXX\"},\"GetKycStatusResponseBody\":{\"data\":{\"kyc\":{\"eKyc\":{\"lastUpdateTimestamp\":{\"epochMillis\":*************},\"expirationTimestamp\":{\"epochMillis\":*************},\"notStartedStateContext\":{}},\"vKyc\":{\"lastUpdateTimestamp\":{\"epochMillis\":*************},\"expirationTimestamp\":{\"epochMillis\":*************},\"notStartedStateContext\":{}},\"offlineKyc\":{\"lastUpdateTimestamp\":{\"epochMillis\":*************},\"notStartedStateContext\":{}},\"state\":\"NOT_STARTED\"}}}}}", KycStatusRes.class);

        KycAppStatusResponse kycAppStatusResponse = axisTransformer.axisKycAppStatusResToKycAppStatusRes(appStatusOutput, kycStatusRes);

        assertNotNull(kycAppStatusResponse);
        assertNotNull(kycAppStatusResponse.getAppStatusResponse());
        assertNotNull(kycAppStatusResponse.getKycStatusResponse());
    }

    @Test
    void shouldReturnFullNameWhenValid() {
        String fullName = "John Doe";
        String fallback = "Fallback Name"; // shouldn't be used

        String result = AxisTransformers.validateFullNameAndFallbackIfRequired(fullName, fallback);
        assertEquals(fullName, result);
    }

    @Test
    void shouldFallbackWhenFullNameIsNull() {
        String fallback = "Valid Fallback";

        String result = AxisTransformers.validateFullNameAndFallbackIfRequired(null, fallback);
        assertEquals(fallback, result);
    }

    @Test
    void shouldTruncateFallbackIfTooLong() {
        String longFallback = "ThisFallbackDisplayNameIsWayTooLongToBeValid";
        String expected = longFallback.substring(0, 30);

        String result = AxisTransformers.validateFullNameAndFallbackIfRequired(null, longFallback);
        assertEquals(expected, result);
    }

    @Test
    void shouldFallbackWhenFullNameDoesNotMatchRegex() {
        String fullName = "J0hn D0e"; // Invalid because it has digits
        String fallback = "Valid Name";

        String result = AxisTransformers.validateFullNameAndFallbackIfRequired(fullName, fallback);
        assertEquals(fallback, result);
    }

    @Test
    void shouldTruncateFullNameIfTooLongAndFallbackIsNull() {
        String longFullName = "ThisFullNameIsExcessivelyAndRidiculouslyLong";
        String expected = longFullName.substring(0, 30);

        String result = AxisTransformers.validateFullNameAndFallbackIfRequired(longFullName, null);
        assertEquals(expected, result);
    }

    @Test
    void shouldReturnNullWhenBothAreNull() {
        String result = AxisTransformers.validateFullNameAndFallbackIfRequired(null, null);
        assertNull(result);
    }

    @Test
    void shouldReturnTruncatedFallbackIfFullNameInvalidAndFallbackTooLong() {
        String fullName = "Invalid123 Name"; // Invalid due to digits
        String longFallback = "ValidFallbackNameThatIsDefinitelyTooLong";
        String expected = longFallback.substring(0, 30);

        String result = AxisTransformers.validateFullNameAndFallbackIfRequired(fullName, longFallback);
        assertEquals(expected, result);
    }

    @Test
    void shouldReturnFullNameWithThreeWords() {
        String fullName = "John Middle Doe"; // Valid 3-word name
        String fallback = "Fallback";

        String result = AxisTransformers.validateFullNameAndFallbackIfRequired(fullName, fallback);
        assertEquals(fullName, result);
    }

    @Test
    void shouldRejectNameWithExtraSpaces() {
        String fullName = "John  Doe"; // Two spaces → invalid
        String fallback = "Backup Name";

        String result = AxisTransformers.validateFullNameAndFallbackIfRequired(fullName, fallback);
        assertEquals(fallback, result);
    }

    private static Stream<Arguments> provideArgsForKycStatus() {
        return Stream.of(
                Arguments.of("ekyc_approved_vkyc_in_progress_without_income", true, false, null, null, State.IN_PROGRESS, "VKYC__IN_PROGRESS", State.IN_PROGRESS, "VKYC__IN_PROGRESS"),
                Arguments.of("ekyc_approved_vkyc_awaiting_approval_without_income", true, false, null, null, State.AWAITING_APPROVAL, "VKYC__AWAITING_APPROVAL", State.AWAITING_APPROVAL, "VKYC__AWAITING_APPROVAL"),
                Arguments.of("ekyc_approved_vkyc_rejected_offline_in_progress_without_income", true, false, null, null, State.OFFLINE, "PHYSICAL__IN_PROGRESS", State.IN_PROGRESS, "PHYSICAL__IN_PROGRESS"),
                Arguments.of("ekyc_approved_vkyc_offline_rejected_without_income", true, false, null, null, State.REJECTED, "PHYSICAL__REJECTED", State.REJECTED, "PHYSICAL__REJECTED"),
                Arguments.of("ekyc_approved_vkyc_rejected_without_offline", true, false, null, null, State.REJECTED, "VKYC__REJECTED", State.REJECTED, "VKYC__REJECTED"),
                Arguments.of("ekyc_vkyc_approved_income_in_progress", false, true, State.IN_PROGRESS, "IA_ONLINE__IN_PROGRESS", State.IN_PROGRESS, "IA_ONLINE__IN_PROGRESS", State.APPROVED, "VKYC__APPROVED"),
                Arguments.of("ekyc_vkyc_approved_income_online_rejected_offline_in_progress", false, true, State.IN_PROGRESS, "IA_OFFLINE__IN_PROGRESS", State.OFFLINE, "IA_OFFLINE__IN_PROGRESS", State.APPROVED, "VKYC__APPROVED"),
                Arguments.of("ekyc_vkyc_approved_income_online_approved", false, true, State.APPROVED, "IA_ONLINE__APPROVED", State.APPROVED, "IA_ONLINE__APPROVED", State.APPROVED, "VKYC__APPROVED"),
                Arguments.of("ekyc_vkyc_approved_income_not_started", false, true, State.NOT_STARTED, "IA_ONLINE__NOT_STARTED", State.NOT_STARTED, "IA_ONLINE__NOT_STARTED", State.APPROVED, "VKYC__APPROVED"),
                Arguments.of("ekyc_vkyc_approved_income_offline_in_progress", false, true, State.IN_PROGRESS, "IA_OFFLINE__IN_PROGRESS", State.OFFLINE, "IA_OFFLINE__IN_PROGRESS", State.APPROVED, "VKYC__APPROVED"),
                Arguments.of("ekyc_approved_vkyc_not_started_in_progress_without_income", true, false, null, null, State.IN_PROGRESS, "VKYC__NOT_STARTED", State.IN_PROGRESS, "VKYC__NOT_STARTED")
        );
    }

    private static Stream<Arguments> provideArgsForInitiateChallenge() {
        return Stream.of(
                Arguments.of("initiate_challenge_request", "AMIPI1234A")
        );
    }

    private <T> T readJson(String jsonFilePath, Class<T> clazz) {
        try {
            InputStream is = getClass().getClassLoader().getResourceAsStream(jsonFilePath);
            if (is == null) {
                throw new FileNotFoundException("File not found: " + jsonFilePath);
            }

            return objectMapper.readValue(is, clazz);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

}