package com.flipkart.fintech.pandora.service.core.cbc.utils;

import org.junit.Test;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;

public class StringValidationUtilsTest {

    @Test
    public void testSanitizeName_withNullInput() {
        assertNull(StringValidationUtils.sanitizeName(null));
    }

    @Test
    public void testSanitizeName_withOnlyValidCharacters() {
        String input = "John Doe";
        String expected = "John Doe";
        assertEquals(expected, StringValidationUtils.sanitizeName(input));
    }

    @Test
    public void testSanitizeName_removesSpecialCharacters() {
        String input = "John@Doe!#";
        String expected = "JohnDoe";
        assertEquals(expected, StringValidationUtils.sanitizeName(input));
    }

    @Test
    public void testSanitizeName_removesStandaloneNullWords() {
        String input = "null John NULL Doe";
        String expected = "John Doe";
        assertEquals(expected, StringValidationUtils.sanitizeName(input));
    }

    @Test
    public void testSanitizeName_collapsesMultipleSpaces() {
        String input = "John   Do<PERSON>   Smith";
        String expected = "<PERSON>";
        assertEquals(expected, StringValidationUtils.sanitizeName(input));
    }

    @Test
    public void testSanitizeName_trimsWhitespace() {
        String input = "   John Doe   ";
        String expected = "John Doe";
        assertEquals(expected, StringValidationUtils.sanitizeName(input));
    }

    @Test
    public void testSanitizeName_combinedCase() {
        String input = " null!@#   John  NULL     Doe##$% ";
        String expected = "John Doe";
        assertEquals(expected, StringValidationUtils.sanitizeName(input));
    }

    @Test
    public void testSanitizeName_withOnlyNulls() {
        String input = "null NULL null";
        String expected = "";
        assertEquals(expected, StringValidationUtils.sanitizeName(input));
    }

    @Test
    public void testSanitizeName_withNonEnglishLetters() {
        String input = "Jöhn Dœé 😊";
        String expected = "Jhn D";
        assertEquals(expected, StringValidationUtils.sanitizeName(input));
    }
}
