package com.flipkart.fintech.pandora.service.core.events;


import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.flipkart.fintech.pandora.service.exception.PandoraException;
import com.sumo.bff.card.client.GupshupWebhookClient;
import com.sumo.bff.card.exception.CardClientException;
import com.sumo.bff.comms.GupshupWAWebhookRequest;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pandora.external.models.request.GupshupRequest;

@ExtendWith(MockitoExtension.class)
class GupshupEventManagerImplTest {
    @Mock
    private GupshupWebhookClient gupshupWebhookClient;
    @InjectMocks
    private GupshupEventManagerImpl gupshupEventManager;


    @Test
    @DisplayName("handleIncomingMessageEvent should send event successfully when request is valid")
    void handleIncomingMessageEventShouldSendEventSuccessfully() {
        GupshupRequest request = new GupshupRequest();
        request.setWaNumber("12345");
        request.setMessageId("msg123");
        request.setMobile("9876543210");
        request.setName("Test User");
        request.setReplyId("reply123");
        request.setTimestamp(1745390755);
        request.setType("text");
        request.setButtonJson("{\"payload\":\"YES\",\"text\":\"YES\"}");
        gupshupEventManager.handleIncomingMessageEvent(request);

        verify(gupshupWebhookClient, times(1)).sendIncomingMessageEvent(any(GupshupWAWebhookRequest.class));
    }

    @Test
    @DisplayName("handleIncomingMessageEvent should throw PandoraException when CardClientException occurs")
    void handleIncomingMessageEventShouldThrowPandoraExceptionOnCardClientException() {
        GupshupRequest request = new GupshupRequest();
        request.setWaNumber("12345");

        doThrow(new CardClientException("Client error")).when(gupshupWebhookClient).sendIncomingMessageEvent(any());

        PandoraException exception = assertThrows(PandoraException.class, () -> {
            gupshupEventManager.handleIncomingMessageEvent(request);
        });

        assertEquals("Client error", exception.getMessage());
    }

    @Test
    @DisplayName("handleIncomingMessageEvent should handle null button in request")
    void handleIncomingMessageEventShouldHandleNullButton() {
        GupshupRequest request = new GupshupRequest();
        request.setWaNumber("12345");
        request.setMessageId("msg123");
        request.setMobile("9876543210");
        request.setName("Test User");
        request.setReplyId("reply123");
        request.setTimestamp(1745390755);
        request.setType("text");
        request.setButtonJson(null);

        gupshupEventManager.handleIncomingMessageEvent(request);

        verify(gupshupWebhookClient, times(1)).sendIncomingMessageEvent(any(GupshupWAWebhookRequest.class));
    }
}