package com.flipkart.fintech.pandora.service.core.lenders;

import com.flipkart.affordability.model.userservice.UserProfile;
import com.flipkart.fintech.pandora.api.model.pl.request.SearchCkycRequest;
import com.flipkart.fintech.pandora.idfc.client.response.CkycDownloadResponse;
import com.flipkart.fintech.pandora.idfc.client.response.SearchCkycResponse;
import com.flipkart.fintech.pandora.service.annotations.JsonFile;
import com.flipkart.fintech.pandora.service.client.cfa.MockHelper;
import com.flipkart.fintech.pandora.service.client.exceptions.LenderException;
import com.flipkart.fintech.pandora.service.client.pl.client.IdfcPlClient;
import com.flipkart.fintech.pandora.service.extensions.JsonFileParameterResolver;
import com.flipkart.fintech.pandora.service.external.BQIngestionHelper;
import com.flipkart.fintech.pandora.service.transformer.IdfcTransformer;
import com.flipkart.fintech.user.data.client.UserDataClient;
import com.flipkart.fintech.winterfell.client.WinterfellClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@ExtendWith(JsonFileParameterResolver.class)
class IdfcUserActionExecutorTest {

  private IdfcUserActionExecutor idfcUserActionExecutor;

  @Mock
  private IdfcPlClient idfcPlClient;

  @Mock
  private IdfcTransformer transformer;

  @Mock
  private MockHelper flags;

  @Mock
  UserDataClient userDataClient;

  @Mock
  BQIngestionHelper bqIngestionHelper;

  @Mock
  WinterfellClient winterfellClient;
  @BeforeEach
  void setUp() {
    idfcUserActionExecutor = new IdfcUserActionExecutor(idfcPlClient, transformer, flags, userDataClient, bqIngestionHelper, winterfellClient);
  }

  @Test
  void rejectApplicationOnSearchCkycFailure(
      @JsonFile("errors/searchCkycErrorResponse.json") SearchCkycResponse searchCkycErrorResponse,
      @JsonFile("requests/searchCkycRequest.json") SearchCkycRequest validSearchCkycRequest
  ) throws LenderException {
    assertNotNull(searchCkycErrorResponse);
    when(idfcPlClient.searchCkyc(any())).thenReturn(searchCkycErrorResponse);
    com.flipkart.fintech.pandora.api.model.pl.response.SearchCkycResponse response = idfcUserActionExecutor.searchCkyc(validSearchCkycRequest);
    assertNotNull(response);
    assertEquals("REJECT", response.getTransactionStatus());
  }

  @Test
  void rejectApplicationOnDownloadCkycFailure(
      @JsonFile("response/validSearchCkycResponse.json") SearchCkycResponse successFullSearchCkycResponse,
      @JsonFile("errors/downloadCkycErrorResponse.json") CkycDownloadResponse downloadCkycErrorResponse,
      @JsonFile("requests/searchCkycRequest.json") SearchCkycRequest searchCkycRequest
  ) throws LenderException {
    assertNotNull(successFullSearchCkycResponse);
    assertNotNull(downloadCkycErrorResponse);
    when(idfcPlClient.searchCkyc(any())).thenReturn(successFullSearchCkycResponse);
    when(idfcPlClient.downloadCkyc(any())).thenReturn(downloadCkycErrorResponse);

    UserProfile currentUserProfile = new UserProfile();
    currentUserProfile.setFirstName("firstName");
    currentUserProfile.setLastName("lastName");
    currentUserProfile.setPrimaryPhone("1234567890");

    com.flipkart.fintech.pandora.api.model.pl.response.SearchCkycResponse response = idfcUserActionExecutor.searchCkyc(searchCkycRequest);
    assertNotNull(response);
    assertEquals("REJECT", response.getTransactionStatus());
  }

}
