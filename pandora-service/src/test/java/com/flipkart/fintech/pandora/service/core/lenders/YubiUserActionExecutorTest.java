package com.flipkart.fintech.pandora.service.core.lenders;

import com.flipkart.fintech.pandora.api.model.pl.request.*;
import com.flipkart.fintech.pandora.api.model.pl.response.*;
import com.flipkart.fintech.pandora.service.client.creditline.yubi.YubiCreditLineClient;
import com.flipkart.fintech.pandora.service.exception.PandoraException;
import com.flipkart.fintech.pandora.service.transformer.YubiTransformer;
import com.flipkart.fintech.pandora.yubi.request.*;
import com.flipkart.fintech.pandora.yubi.response.*;
import com.flipkart.fintech.pandora.yubi.response.kyc.UploadKYCResponse;
import com.flipkart.usercluster.auth.model.entity.request.FlowInstancePerformActionRequest;
import com.flipkart.usercluster.auth.model.entity.response.AuthFlowInstanceResponse;
import com.flipkart.usercluster.chowkidaar.client.ChowkidaarClient;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;
import java.util.Date;
import java.util.Optional;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;


@RunWith(MockitoJUnitRunner.class)
public class YubiUserActionExecutorTest {

    @Mock
    private YubiTransformer transformer;

    @Mock
    private ChowkidaarClient chowkidaarClient;

    @Mock
    private YubiCreditLineClient yubiCreditLineClient;

    @InjectMocks
    private YubiUserActionExecutor yubiUserActionExecutor;

    @Test
    public void testCustomerIdentification_Success() throws Exception {
        // Arrange
        CustomerIdentificationRequest request = new CustomerIdentificationRequest();
        com.flipkart.fintech.pandora.yubi.request.CreateApplicationRequest createAppRequest =
                new com.flipkart.fintech.pandora.yubi.request.CreateApplicationRequest();
        com.flipkart.fintech.pandora.yubi.response.CreateApplicationResponse createAppResponse =
                new com.flipkart.fintech.pandora.yubi.response.CreateApplicationResponse();
        PanVerificationResponse expectedResponse = new PanVerificationResponse();

        when(transformer.transformCustomerIdentificationRequest(request)).thenReturn(createAppRequest);
        when(yubiCreditLineClient.createApplication(createAppRequest)).thenReturn(createAppResponse);
        when(transformer.transformCreateApplicationResponse(createAppResponse)).thenReturn(expectedResponse);

        // Act
        PanVerificationResponse response = yubiUserActionExecutor.customerIdentification(request);

        // Assert
        assertNotNull(response);
        assertEquals(expectedResponse, response);
        verify(transformer).transformCustomerIdentificationRequest(request);
        verify(yubiCreditLineClient).createApplication(createAppRequest);
        verify(transformer).transformCreateApplicationResponse(createAppResponse);
    }

    @Test
    public void testGenerateKfsOtp_Success() throws Exception {
        KfsGenerateOtpRequest request = new KfsGenerateOtpRequest();
        KfsOtpSms kfsOtpSms = new KfsOtpSms();
        kfsOtpSms.setMobileNumber("1234567890");
        request.setSendSms(kfsOtpSms);

        FlowInstancePerformActionRequest fiActionRequest = new FlowInstancePerformActionRequest();
        AuthFlowInstanceResponse authFlowInstanceResponse = new AuthFlowInstanceResponse();
        KfsGenerateOtpResponse expectedResponse = new KfsGenerateOtpResponse();

        when(transformer.transformFlowInstancePerformActionRequestGenerateOtp(request)).thenReturn(fiActionRequest);
        when(chowkidaarClient.getAndVerifyOTP(eq(Optional.empty()), eq(fiActionRequest))).thenReturn(authFlowInstanceResponse);
        when(transformer.transformKfsGenerateOtpResponse(authFlowInstanceResponse)).thenReturn(expectedResponse);

        KfsGenerateOtpResponse response = yubiUserActionExecutor.generateKfsOtp(request);

        assertNotNull(response);
        assertEquals(expectedResponse, response);
        verify(transformer).transformFlowInstancePerformActionRequestGenerateOtp(request);
        verify(chowkidaarClient).getAndVerifyOTP(eq(Optional.empty()), eq(fiActionRequest));
        verify(transformer).transformKfsGenerateOtpResponse(authFlowInstanceResponse);
    }

    @Test
    public void testGenerateKfsOtp_ValidationFailure() {
        KfsGenerateOtpRequest request = new KfsGenerateOtpRequest();
        PandoraException exception = assertThrows(PandoraException.class,
                () -> yubiUserActionExecutor.generateKfsOtp(request));
        assertTrue(exception.getMessage().contains("Missing required field"));
    }

    @Test
    public void testSubmitKfsOtp_ValidationFailure() {
        KfsSubmitOtpRequest request = new KfsSubmitOtpRequest();
        PandoraException exception = assertThrows(PandoraException.class,
                () -> yubiUserActionExecutor.submitKfsOtp(request));
        assertEquals("Missing required field", exception.getMessage());
    }

    @Test
    public void testGetStatus_ApplicationCreationStatus() throws Exception {
        // Arrange
        StatusCheckRequest request = new StatusCheckRequest();
        request.setCheckApplicationCreationStatus(true);
        request.setAccountId("test-account");

        LoanStatusRequest loanStatusRequest = new LoanStatusRequest();
        ApplicationStatusResponse appStatusResponse = new ApplicationStatusResponse();
        StatusCheckResponse expectedResponse = new StatusCheckResponse();

        when(transformer.transformStatusCheckRequest(request)).thenReturn(loanStatusRequest);
        when(yubiCreditLineClient.fetchApplicationStatus(loanStatusRequest)).thenReturn(appStatusResponse);
        when(transformer.transformApplicationCreationResponse(appStatusResponse)).thenReturn(expectedResponse);

        // Act
        StatusCheckResponse response = yubiUserActionExecutor.getStatus(request);

        // Assert
        assertNotNull(response);
        assertEquals(expectedResponse, response);
        verify(yubiCreditLineClient).fetchApplicationStatus(loanStatusRequest);
    }

    @Test
    public void testGetStatus_ApprovalStatus() throws Exception {
        // Arrange
        StatusCheckRequest request = new StatusCheckRequest();
        request.setCheckApplicationCreationStatus(false);
        request.setAccountId("test-account");

        LoanStatusRequest loanStatusRequest = new LoanStatusRequest();
        com.flipkart.fintech.pandora.yubi.response.LoanStatusResponse loanStatusResponse =
                new com.flipkart.fintech.pandora.yubi.response.LoanStatusResponse();
        StatusCheckResponse expectedResponse = new StatusCheckResponse();

        when(transformer.transformStatusCheckRequest(request)).thenReturn(loanStatusRequest);
        when(yubiCreditLineClient.checkApprovalStatus(loanStatusRequest)).thenReturn(loanStatusResponse);
        when(transformer.transformStatusCheckResponse(loanStatusResponse)).thenReturn(expectedResponse);

        // Act
        StatusCheckResponse response = yubiUserActionExecutor.getStatus(request);

        // Assert
        assertNotNull(response);
        assertEquals(expectedResponse, response);
        verify(yubiCreditLineClient).checkApprovalStatus(loanStatusRequest);
    }

    @Test
    public void testUploadOfflineData_Selfie() throws Exception {
        // Arrange
        OfflineDataUploadRequest request = new OfflineDataUploadRequest();
        request.setKycMode("selfie");

        UploadKYCRequest uploadKYCRequest = new UploadKYCRequest();
        UploadKYCRequest.CustomerDetail customerDetail = new UploadKYCRequest.CustomerDetail();
        customerDetail.setExtension("jpg");
        customerDetail.setSelfieLink("selfie-data");
        uploadKYCRequest.setCustomerDetails(Collections.singletonList(customerDetail));

        SignedURLResponse signedURLResponse = new SignedURLResponse();
        signedURLResponse.setPresignedUrl("[http://s3-presigned-url.com](http://s3-presigned-url.com)");

        UploadKYCResponse uploadKYCResponse = new UploadKYCResponse();
        GenericStatusResponse expectedResponse = new GenericStatusResponse();

        when(transformer.transformKYCRequest(request)).thenReturn(uploadKYCRequest);
        when(yubiCreditLineClient.generateSignedURL(anyString(), anyString(), anyString())).thenReturn(signedURLResponse);
        when(yubiCreditLineClient.uploadKYC(any(UploadKYCRequest.class), anyBoolean())).thenReturn(uploadKYCResponse);
        when(transformer.transformKYCResponse(uploadKYCResponse)).thenReturn(expectedResponse);

        // Act
        GenericStatusResponse response = yubiUserActionExecutor.uploadOfflineData(request);

        // Assert
        assertNotNull(response);
        assertEquals(expectedResponse, response);
        verify(yubiCreditLineClient).generateSignedURL(anyString(), eq("jpg"), eq("KYC_SELFIE"));
        verify(yubiCreditLineClient).uploadKYC(any(UploadKYCRequest.class), eq(true));
    }

    @Test
    public void testUploadOfflineData_AadhaarXml() throws Exception {
        // Arrange
        OfflineDataUploadRequest request = new OfflineDataUploadRequest();
        request.setKycMode("okyc");  // Not selfie mode

        UploadKYCRequest uploadKYCRequest = new UploadKYCRequest();
        UploadKYCRequest.CustomerDetail customerDetail = new UploadKYCRequest.CustomerDetail();
        customerDetail.setExtension("xml");
        customerDetail.setAadhaarXml("aadhaar-xml-data");
        uploadKYCRequest.setCustomerDetails(Collections.singletonList(customerDetail));
        uploadKYCRequest.setKycTriggerType("UPDATE");

        UploadKYCResponse uploadKYCResponse = new UploadKYCResponse();
        GenericStatusResponse expectedResponse = new GenericStatusResponse();

        when(transformer.transformKYCRequest(request)).thenReturn(uploadKYCRequest);
        when(yubiCreditLineClient.uploadKYC(any(UploadKYCRequest.class), eq(false))).thenReturn(uploadKYCResponse);
        when(transformer.transformKYCResponse(uploadKYCResponse)).thenReturn(expectedResponse);

        // Act
        GenericStatusResponse response = yubiUserActionExecutor.uploadOfflineData(request);

        // Assert
        assertNotNull(response);
        assertEquals(expectedResponse, response);
        verify(yubiCreditLineClient).uploadKYC(any(UploadKYCRequest.class), eq(false));
        verifyNoMoreInteractions(yubiCreditLineClient);
    }

//    @Test
//    public void testGenerateKfsOtp_ValidationFailure() {
//        // Arrange
//        KfsGenerateOtpRequest request = new KfsGenerateOtpRequest();
//        // Don't set required fields to trigger validation
//
//        // Act & Assert
//        PandoraException exception = assertThrows(PandoraException.class,
//                () -> yubiUserActionExecutor.generateKfsOtp(request));
//
//        assertTrue(exception.getMessage().contains("Missing required field"));
//    }

//    @Test
//    public void testSubmitKfsOtp_ValidationFailure() {
//        // Arrange
//        KfsSubmitOtpRequest request = new KfsSubmitOtpRequest();
//        // Don't set required fields to trigger validation
//
//        // Act & Assert
//        PandoraException exception = assertThrows(PandoraException.class,
//                () -> yubiUserActionExecutor.submitKfsOtp(request));
//
//        assertEquals("Missing required field", exception.getMessage());
//    }

//    @Test
//    public void testGenerateKfsOtp_Success() throws Exception {
//        // Arrange
//        KfsGenerateOtpRequest request = new KfsGenerateOtpRequest();
//        // Set KfsOtpSms instead of SendSms
//        KfsOtpSms kfsOtpSms = new KfsOtpSms();
//        kfsOtpSms.setMobileNumber("1234567890");
//        request.setSendSms(kfsOtpSms);
//
//        FlowInstancePerformActionRequest fiActionRequest = new FlowInstancePerformActionRequest();
//        AuthFlowInstanceResponse authFlowInstanceResponse = new AuthFlowInstanceResponse();
//        KfsGenerateOtpResponse expectedResponse = new KfsGenerateOtpResponse();
//
//        when(transformer.transformFlowInstancePerformActionRequestGenerateOtp(request)).thenReturn(fiActionRequest);
//        when(chowkidaarClient.getAndVerifyOTP(eq(Optional.empty()), eq(fiActionRequest))).thenReturn(authFlowInstanceResponse);
//        when(transformer.transformKfsGenerateOtpResponse(authFlowInstanceResponse)).thenReturn(expectedResponse);
//
//        // Act
//        KfsGenerateOtpResponse response = yubiUserActionExecutor.generateKfsOtp(request);
//
//        // Assert
//        assertNotNull(response);
//        assertEquals(expectedResponse, response);
//        verify(transformer).transformFlowInstancePerformActionRequestGenerateOtp(request);
//        verify(chowkidaarClient).getAndVerifyOTP(eq(Optional.empty()), eq(fiActionRequest));
//        verify(transformer).transformKfsGenerateOtpResponse(authFlowInstanceResponse);
//    }

    @Test
    public void testUpdateBankDetails_Success() throws Exception {
        // Arrange
        BankUpdateRequest request = new BankUpdateRequest();
        BankRequest bankRequest = new BankRequest();
        BankResponse bankResponse = new BankResponse();
        LoanResponse expectedResponse = new LoanResponse();

        when(transformer.transformBankRequest(request)).thenReturn(bankRequest);
        when(yubiCreditLineClient.updateBankStatus(bankRequest)).thenReturn(bankResponse);
        when(transformer.transformBankResponse(bankResponse)).thenReturn(expectedResponse);

        // Act
        LoanResponse response = yubiUserActionExecutor.updateBankDetails(request);

        // Assert
        assertNotNull(response);
        assertEquals(expectedResponse, response);
        verify(yubiCreditLineClient).updateBankStatus(bankRequest);
    }

    @Test
    public void testGenerateInitialOffer_Success() throws Exception {
        // Arrange
        InitialOfferGenerationRequest request = new InitialOfferGenerationRequest();
        GetOfferRequest getOfferRequest = new GetOfferRequest();
        OfferResponse offerResponse = new OfferResponse();
        InitialOfferGenerationResponse expectedResponse = new InitialOfferGenerationResponse();

        when(transformer.transformInitialOfferGenerationRequest(request)).thenReturn(getOfferRequest);
        when(yubiCreditLineClient.getOffer(getOfferRequest)).thenReturn(offerResponse);
        when(transformer.transformInitialOfferGenerationResponse(offerResponse)).thenReturn(expectedResponse);

        // Act
        InitialOfferGenerationResponse response = yubiUserActionExecutor.generateInitialOffer(request);

        // Assert
        assertNotNull(response);
        assertEquals(expectedResponse, response);
        verify(transformer).transformInitialOfferGenerationRequest(request);
        verify(yubiCreditLineClient).getOffer(getOfferRequest);
        verify(transformer).transformInitialOfferGenerationResponse(offerResponse);
    }

    @Test
    public void testStartLoan_Success() throws Exception {
        // Arrange
        StartLoanRequest request = new StartLoanRequest();
        ConfirmCommercialRequest confirmCommercialRequest = new ConfirmCommercialRequest();
        ConfirmCommercialResponse confirmCommercialResponse = new ConfirmCommercialResponse();
        GenericStatusResponse expectedResponse = new GenericStatusResponse();

        when(transformer.transformStartLoanRequest(request)).thenReturn(confirmCommercialRequest);
        when(yubiCreditLineClient.confirmCommercial(confirmCommercialRequest)).thenReturn(confirmCommercialResponse);
        when(transformer.transformConfirmCommercialResponse(confirmCommercialResponse)).thenReturn(expectedResponse);

        // Act
        GenericStatusResponse response = yubiUserActionExecutor.startLoan(request);

        // Assert
        assertNotNull(response);
        assertEquals(expectedResponse, response);
        verify(transformer).transformStartLoanRequest(request);
        verify(yubiCreditLineClient).confirmCommercial(confirmCommercialRequest);
        verify(transformer).transformConfirmCommercialResponse(confirmCommercialResponse);
    }

    @Test
    public void testFetchKfsDocument_Success() throws Exception {
        // Arrange
        FetchKfsDocumentRequest request = new FetchKfsDocumentRequest();
        GetDocumentRequest getDocumentRequest = new GetDocumentRequest();
        FetchDocumentResponse fetchDocumentResponse = new FetchDocumentResponse();
        FetchKfsDocumentResponse expectedResponse = new FetchKfsDocumentResponse();

        when(transformer.transformKfsDocumentRequest(request)).thenReturn(getDocumentRequest);
        when(yubiCreditLineClient.fetchDocument(getDocumentRequest)).thenReturn(fetchDocumentResponse);
        when(transformer.transformGetDocumentResponse(fetchDocumentResponse)).thenReturn(expectedResponse);

        // Act
        FetchKfsDocumentResponse response = yubiUserActionExecutor.fetchKfsDocument(request);

        // Assert
        assertNotNull(response);
        assertEquals(expectedResponse, response);
        verify(transformer).transformKfsDocumentRequest(request);
        verify(yubiCreditLineClient).fetchDocument(getDocumentRequest);
        verify(transformer).transformGetDocumentResponse(fetchDocumentResponse);
    }

    @Test
    public void testGenerateKfsDocument_Success() throws Exception {
        // Arrange
        GenerateKfsDocumentRequest request = new GenerateKfsDocumentRequest();
        GenerateDocumentRequest generateDocumentRequest = new GenerateDocumentRequest();
        GenerateDocumentResponse generateDocumentResponse = new GenerateDocumentResponse();
        FetchKfsDocumentResponse expectedResponse = new FetchKfsDocumentResponse();

        when(transformer.transformGenerateKfsDocumentRequest(request)).thenReturn(generateDocumentRequest);
        when(yubiCreditLineClient.generateDocument(generateDocumentRequest)).thenReturn(generateDocumentResponse);
        when(transformer.transformGenerateDocumentResponse(generateDocumentResponse)).thenReturn(expectedResponse);

        // Act
        FetchKfsDocumentResponse response = yubiUserActionExecutor.generateKfsDocument(request);

        // Assert
        assertNotNull(response);
        assertEquals(expectedResponse, response);
        verify(transformer).transformGenerateKfsDocumentRequest(request);
        verify(yubiCreditLineClient).generateDocument(generateDocumentRequest);
        verify(transformer).transformGenerateDocumentResponse(generateDocumentResponse);
    }

    @Test
    public void testLoanEsigning_Success() throws Exception {
        // Arrange
        EsigningRequest request = new EsigningRequest();
        EsignRequest esignRequest = new EsignRequest();
        EsignResponse esignResponse = new EsignResponse();
        EsigningResponse expectedResponse = new EsigningResponse();

        when(transformer.transformEsigningRequest(request)).thenReturn(esignRequest);
        when(yubiCreditLineClient.initiateEsign(esignRequest)).thenReturn(esignResponse);
        when(transformer.transformEsigningResponse(esignResponse)).thenReturn(expectedResponse);

        // Act
        EsigningResponse response = yubiUserActionExecutor.loanEsigning(request);

        // Assert
        assertNotNull(response);
        assertEquals(expectedResponse, response);
        verify(transformer).transformEsigningRequest(request);
        verify(yubiCreditLineClient).initiateEsign(esignRequest);
        verify(transformer).transformEsigningResponse(esignResponse);
    }

    @Test
    public void testGetPaymentDetails_Success() throws Exception {
        // Arrange
        LoanUtilityRequest request = new LoanUtilityRequest();
        LoanStatusRequest loanStatusRequest = new LoanStatusRequest();
        RepaymentDetailsResponse repaymentDetailsResponse = new RepaymentDetailsResponse();
        LoanUtilityResponse expectedResponse = new LoanUtilityResponse();

        when(transformer.transformGetRepaymentDetailsRequest(request)).thenReturn(loanStatusRequest);
        when(yubiCreditLineClient.getPaymentDetails(loanStatusRequest)).thenReturn(repaymentDetailsResponse);
        when(transformer.transformRepaymentDetailsResponse(repaymentDetailsResponse)).thenReturn(expectedResponse);

        // Act
        LoanUtilityResponse response = yubiUserActionExecutor.getPaymentDetails(request);

        // Assert
        assertNotNull(response);
        assertEquals(expectedResponse, response);
        verify(transformer).transformGetRepaymentDetailsRequest(request);
        verify(yubiCreditLineClient).getPaymentDetails(loanStatusRequest);
        verify(transformer).transformRepaymentDetailsResponse(repaymentDetailsResponse);
    }

    @Test
    public void testGetDisbursementDetails_Success() throws Exception {
        // Arrange
        StatusCheckRequest request = new StatusCheckRequest();
        LoanStatusRequest loanStatusRequest = new LoanStatusRequest();
        DisbursementDetailResponse disbursementDetailResponse = new DisbursementDetailResponse();
        DisbursementDetailsResponse expectedResponse = new DisbursementDetailsResponse();

        when(transformer.transformStatusCheckRequest(request)).thenReturn(loanStatusRequest);
        when(yubiCreditLineClient.getDisbursementDetails(loanStatusRequest)).thenReturn(disbursementDetailResponse);
        when(transformer.transformDisbursementDetailsResponse(disbursementDetailResponse)).thenReturn(expectedResponse);

        // Act
        DisbursementDetailsResponse response = yubiUserActionExecutor.getDisbursementDetails(request);

        // Assert
        assertNotNull(response);
        assertEquals(expectedResponse, response);
        verify(transformer).transformStatusCheckRequest(request);
        verify(yubiCreditLineClient).getDisbursementDetails(loanStatusRequest);
        verify(transformer).transformDisbursementDetailsResponse(disbursementDetailResponse);
    }

    @Test
    public void testFetchCustomerDetails_Success() throws Exception {
        // Arrange
        CustomerStatusRequest request = new CustomerStatusRequest();
        LoanStatusRequest loanStatusRequest = new LoanStatusRequest();
        CustomerDetailResponse customerDetailResponse = new CustomerDetailResponse();
        CustomerDetailsResponse expectedResponse = new CustomerDetailsResponse();

        when(transformer.transformCustomerStatusCheckRequest(request)).thenReturn(loanStatusRequest);
        when(yubiCreditLineClient.fetchCustomerDetails(loanStatusRequest)).thenReturn(customerDetailResponse);
        when(transformer.transformCustomerDetailsResponse(customerDetailResponse)).thenReturn(expectedResponse);

        // Act
        CustomerDetailsResponse response = yubiUserActionExecutor.fetchCustomerDetails(request);

        // Assert
        assertNotNull(response);
        assertEquals(expectedResponse, response);
        verify(transformer).transformCustomerStatusCheckRequest(request);
        verify(yubiCreditLineClient).fetchCustomerDetails(loanStatusRequest);
        verify(transformer).transformCustomerDetailsResponse(customerDetailResponse);
    }

    @Test
    public void testUpdateMandateDetails_Success() throws Exception {
        // Arrange
        MandateUpdateRequest request = new MandateUpdateRequest();
        MandateRequest mandateRequest = new MandateRequest();
        MandateResponse mandateResponse = new MandateResponse();
        LoanResponse expectedResponse = new LoanResponse();

        when(transformer.transformMandateRequest(request)).thenReturn(mandateRequest);
        when(yubiCreditLineClient.updateMandateStatus(mandateRequest)).thenReturn(mandateResponse);
        when(transformer.transformMandateResponse(mandateResponse)).thenReturn(expectedResponse);

        // Act
        LoanResponse response = yubiUserActionExecutor.updateMandateDetails(request);

        // Assert
        assertNotNull(response);
        assertEquals(expectedResponse, response);
        verify(transformer).transformMandateRequest(request);
        verify(yubiCreditLineClient).updateMandateStatus(mandateRequest);
        verify(transformer).transformMandateResponse(mandateResponse);
    }

    @Test
    public void testGetLoanRepaymentSchedule_LoanRepaymentScheduleRequest() throws Exception {
        // Arrange
        LoanRepaymentScheduleRequest request = new LoanRepaymentScheduleRequest();

        // Act
        LoanRepaymentScheduleResponse response = yubiUserActionExecutor.getLoanRepaymentSchedule(request);

        // Assert
        assertNull(response); // Method returns null
    }

    @Test
    public void testGetLoanRepaymentSchedule_LoanRepaymentAmountRequest() throws Exception {
        // Arrange
        // Fix for constructor requiring parameters
        LoanRepaymentAmountRequest request = new LoanRepaymentAmountRequest("test-loan-id", new Date());

        // Act
        LoanRepaymentAmountResponse response = yubiUserActionExecutor.getLoanRepaymentSchedule(request);

        // Assert
        assertNull(response); // Method returns null
    }

    @Test
    public void testUpdateLoanPayment() throws Exception {
        // Arrange
        LoanPaymentUpdateRequest request = new LoanPaymentUpdateRequest();

        // Act
        LoanPaymentUpdateResponse response = yubiUserActionExecutor.updateLoanPayment(request);

        // Assert
        assertNull(response); // Method returns null
    }

    // Tests for methods that throw UnsupportedOperationException

    @Test
    public void testVerifyPan_UnsupportedOperation() {
        // Arrange
        PanVerificationRequest request = new PanVerificationRequest();

        // Act & Assert
        assertThrows(UnsupportedOperationException.class,
                () -> yubiUserActionExecutor.verifyPan(request));
    }

    @Test
    public void testSearchCkyc_UnsupportedOperation() {
        // Arrange
        SearchCkycRequest request = new SearchCkycRequest();

        // Act & Assert
        assertThrows(UnsupportedOperationException.class,
                () -> yubiUserActionExecutor.searchCkyc(request));
    }

    @Test
    public void testGenerateAadhaarOtp_UnsupportedOperation() {
        // Arrange
        AadhaarGenerateOtpRequest request = new AadhaarGenerateOtpRequest();

        // Act & Assert
        assertThrows(UnsupportedOperationException.class,
                () -> yubiUserActionExecutor.generateAadhaarOtp(request));
    }

    @Test
    public void testValidateAadhaarOtp_UnsupportedOperation() {
        // Arrange
        AadhaarValidateOtpRequest request = new AadhaarValidateOtpRequest();

        // Act & Assert
        assertThrows(UnsupportedOperationException.class,
                () -> yubiUserActionExecutor.validateAadhaarOtp(request));
    }

    @Test
    public void testGetPositiveConfirmation_UnsupportedOperation() {
        // Arrange
        StatusCheckRequest request = new StatusCheckRequest();

        // Act & Assert
        assertThrows(UnsupportedOperationException.class,
                () -> yubiUserActionExecutor.getPositiveConfirmation(request));
    }

    @Test
    public void testOnBoardLoan_UnsupportedOperation() {
        // Arrange
        LoanOnboardingRequest request = new LoanOnboardingRequest();

        // Act & Assert
        assertThrows(UnsupportedOperationException.class,
                () -> yubiUserActionExecutor.onBoardLoan(request));
    }

    @Test
    public void testDisburseLoan_UnsupportedOperation() {
        // Arrange
        AutoDisbursalRequest request = new AutoDisbursalRequest();

        // Act & Assert
        assertThrows(UnsupportedOperationException.class,
                () -> yubiUserActionExecutor.disburseLoan(request));
    }

    @Test
    public void testInitiatePennyDrop_UnsupportedOperation() {
        // Arrange
        PennyDropRequest request = new PennyDropRequest();

        // Act & Assert
        assertThrows(UnsupportedOperationException.class,
                () -> yubiUserActionExecutor.initiatePennyDrop(request));
    }

    @Test
    public void testGetIfscDetails_UnsupportedOperation() {
        // Arrange
        IfscRequest request = new IfscRequest();

        // Act & Assert
        assertThrows(UnsupportedOperationException.class,
                () -> yubiUserActionExecutor.getIfscDetails(request));
    }

    @Test
    public void testVerifyEmandate_UnsupportedOperation() {
        // Arrange
        EmandateVerificationRequest request = new EmandateVerificationRequest();

        // Act & Assert
        assertThrows(UnsupportedOperationException.class,
                () -> yubiUserActionExecutor.verifyEmandate(request));
    }

    @Test
    public void testFetchApplicationStatus_UnsupportedOperation() {
        // Arrange
        LoanStatusRequest request = new LoanStatusRequest();

        // Act & Assert
        assertThrows(UnsupportedOperationException.class,
                () -> yubiUserActionExecutor.fetchApplicationStatus(request));
    }

    @Test
    public void testGetLoanDetails_UnsupportedOperation() {
        // Arrange
        // Fix for constructor requiring parameters
        LoanDataRequest request = new LoanDataRequest("test-loan-id");

        // Act & Assert
        assertThrows(UnsupportedOperationException.class,
                () -> yubiUserActionExecutor.getLoanDetails(request));
    }

    @Test
    public void testGetTransactionDetails_UnsupportedOperation() {
        // Arrange
        LoanTransactionRequest request = new LoanTransactionRequest();

        // Act & Assert
        assertThrows(UnsupportedOperationException.class,
                () -> yubiUserActionExecutor.getTransactionDetails(request));
    }

    @Test
    public void testGetCreditLineData_UnsupportedOperation() {
        // Arrange
        LoanCreditDataRequest request = new LoanCreditDataRequest();

        // Act & Assert
        assertThrows(UnsupportedOperationException.class,
                () -> yubiUserActionExecutor.getCreditLineData(request));
    }
}