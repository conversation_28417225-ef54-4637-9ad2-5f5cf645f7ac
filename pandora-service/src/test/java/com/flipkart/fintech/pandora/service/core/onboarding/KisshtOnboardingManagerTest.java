package com.flipkart.fintech.pandora.service.core.onboarding;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;

import com.codahale.metrics.MetricRegistry;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.pandora.api.model.common.CustomerBucket;
import com.flipkart.fintech.pandora.api.model.common.Gender;
import com.flipkart.fintech.pandora.api.model.common.STATUS;
import com.flipkart.fintech.pandora.api.model.request.onboarding.AccountActivationRequest;
import com.flipkart.fintech.pandora.api.model.request.onboarding.AdditionalData;
import com.flipkart.fintech.pandora.api.model.request.onboarding.Address;
import com.flipkart.fintech.pandora.api.model.request.onboarding.AddressType;
import com.flipkart.fintech.pandora.api.model.request.onboarding.ExpandedUserRequest;
import com.flipkart.fintech.pandora.api.model.request.onboarding.MobileNumberUpdationRequest;
import com.flipkart.fintech.pandora.api.model.request.onboarding.PanNumberRequest;
import com.flipkart.fintech.pandora.service.application.configuration.BucketingConfiguration;
import com.flipkart.fintech.pandora.service.client.kissht.client.onboarding.KisshtOnboardingServiceClient;
import com.flipkart.fintech.pandora.service.client.kissht.request.CheckEligibilityRequest;
import com.flipkart.fintech.pandora.service.client.kissht.response.CheckEligibilityResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

public class KisshtOnboardingManagerTest {

    private KisshtOnboardingServiceClient kisshtOnboardingServiceClient;
    private KisshtOnboardingManager kisshtOnboardingManager;
    private BucketingConfiguration bucketingConfiguration;

    @BeforeEach
    public void setUp() throws Exception {
        kisshtOnboardingServiceClient = Mockito.mock(KisshtOnboardingServiceClient.class);
        bucketingConfiguration = new BucketingConfiguration();
        bucketingConfiguration.setEnabled(false);
        kisshtOnboardingManager = new KisshtOnboardingManager(kisshtOnboardingServiceClient, bucketingConfiguration);
    }

    @Test
    public void testCreateLoan() throws Exception {
        CheckEligibilityResponse checkEligibilityResponse = new CheckEligibilityResponse();
        checkEligibilityResponse.setSuccess(true);
        ExpandedUserRequest userRequest = new ExpandedUserRequest();
        userRequest.setFirstName("Aniruddha");
        userRequest.setLastName("Sharma");
        userRequest.setMobileNumber("**********");
        userRequest.setDateOfBirth("2017-09-13");
        userRequest.setEmail("<EMAIL>");
        userRequest.setGender(Gender.M);
        userRequest.setAddressLine1("Flipkart test 1");
        userRequest.setAddressLine2("Flipkart test 2");
        userRequest.setState("Karnataka");
        userRequest.setCity("Bangalore");
        userRequest.setPincode("560037");
        userRequest.setMobileVerificationFlag(true);
        userRequest.setPanNumber("**********");
        userRequest.setAadhaarReferenceNumber("123");
        userRequest.setKycCompleted(true);
        AdditionalData additionalData = new AdditionalData();
        Map surrogates = new HashMap();
        surrogates.put("accountVintage",70);
        surrogates.put("daysSinceLastOrder",23);
        surrogates.put("noOfOrdersFulfilled",17);
        additionalData.setCustomerBucket(CustomerBucket.DEFAULT);
        additionalData.setSurrogates(surrogates);
        List<Address> addresses = new ArrayList<>();
        Address address = Mockito.mock(Address.class);
        Mockito.when(address.getAddressType()).thenReturn(AddressType.HOME);
        addresses.add(address);
        additionalData.setAddresses(addresses);
        userRequest.setAdditionalData(additionalData);

        Mockito.when(kisshtOnboardingServiceClient.checkEligibility(any(CheckEligibilityRequest.class)))
                .thenReturn(checkEligibilityResponse);

        assertEquals(kisshtOnboardingManager.createLoan(userRequest).getStatus(), STATUS.FAILURE);
    }

    @Test
    public void testUpdateMobileNumber() throws Exception {
        String accountNumber = "accountNumber";
        MobileNumberUpdationRequest mobileNumberUpdationRequest = new MobileNumberUpdationRequest();
        com.flipkart.fintech.pandora.service.client.kissht.response.MobileNumberUpdationResponse
                mobileNumberUpdationResponseClient = new com.flipkart.fintech.pandora.service.client.kissht.response
                .MobileNumberUpdationResponse();
        mobileNumberUpdationResponseClient.setSuccess(true);
        mobileNumberUpdationRequest.setNumberVerified(true);
        mobileNumberUpdationRequest.setNewMobileNumber("**********");
        mobileNumberUpdationRequest.setOldMobileNumber("**********");
        Mockito.when(kisshtOnboardingServiceClient.updateMobileNumber(eq(accountNumber), any
                (com.flipkart.fintech.pandora.service.client.kissht.request.MobileNumberUpdationRequest.class)))
                .thenReturn(mobileNumberUpdationResponseClient);
        assertEquals(kisshtOnboardingManager.updateMobileNumber(accountNumber,mobileNumberUpdationRequest).getStatus
                (), STATUS.SUCCESS);

    }

    @Test
    public void testActivateAccount() throws Exception {
        String accountNumber = "accountNumber";
        AccountActivationRequest accountActivationRequest = new AccountActivationRequest();
        com.flipkart.fintech.pandora.service.client.kissht.response.AccountActivationResponse
                accountActivationResponseClient = new com.flipkart.fintech.pandora.service.client.kissht.response
                .AccountActivationResponse();
        accountActivationResponseClient.setSuccess(true);
        accountActivationRequest.setActivated(true);
        Mockito.when(kisshtOnboardingServiceClient.activateAccount(any
                (com.flipkart.fintech.pandora.service.client.kissht.request.AccountActivationRequest.class), eq
                (accountNumber)))
                .thenReturn(accountActivationResponseClient);
        assertEquals(kisshtOnboardingManager.activateAccount(accountActivationRequest, accountNumber).getStatus
                (), STATUS.SUCCESS);
    }

    @Test
    public void testVerifyPan() throws Exception {
        PanNumberRequest panNumberRequest = new PanNumberRequest();
        panNumberRequest.setPan("**********");
        com.flipkart.fintech.pandora.service.client.kissht.response.PanNumberResponse
                panNumberResponseClient = new com.flipkart.fintech.pandora.service.client.kissht.response
                .PanNumberResponse();
        panNumberResponseClient.setSuccess(true);
        Mockito.when(kisshtOnboardingServiceClient.verifyPan(any
                (com.flipkart.fintech.pandora.service.client.kissht.request.PanNumberRequest.class)))
                .thenReturn(panNumberResponseClient);
        assertEquals(kisshtOnboardingManager.verifyPan(panNumberRequest).getStatus
                (), STATUS.SUCCESS);
    }
}