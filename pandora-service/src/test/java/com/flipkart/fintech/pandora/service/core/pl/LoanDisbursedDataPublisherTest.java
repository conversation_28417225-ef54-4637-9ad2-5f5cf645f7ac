package com.flipkart.fintech.pandora.service.core.pl;

import com.flipkart.fintech.pandora.api.model.pl.request.LoanDisbursedData;
import com.flipkart.fintech.pandora.service.application.configuration.LoanDisbursedDataPublisherConfig;
import com.google.api.core.ApiFuture;
import com.google.cloud.pubsub.v1.Publisher;
import com.google.pubsub.v1.PubsubMessage;
import org.junit.Before;
import org.junit.Test;
import org.mockito.MockedConstruction;
import org.mockito.Mockito;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

public class LoanDisbursedDataPublisherTest {

    private LoanDisbursedDataPublisherConfig config;
    private LoanDisbursedDataPublisher publisher;

    @Before
    public void setUp() {
        config = mock(LoanDisbursedDataPublisherConfig.class);
        when(config.getTopic()).thenReturn("test-topic");
        when(config.getProjectId()).thenReturn("test-project");
        publisher = new LoanDisbursedDataPublisher(config);
    }

    @Test
    public void testPublishDisbursalData_Success() throws Exception {
        LoanDisbursedData data = mock(LoanDisbursedData.class);
        when(data.getApplicationId()).thenReturn("app-123");

        try (MockedConstruction<Publisher> mocked = Mockito.mockConstruction(Publisher.class,
                (mock, context) -> {
                    ApiFuture<String> future = mock(ApiFuture.class);
                    when(future.get()).thenReturn("message-id-1");
                    when(mock.publish(any(PubsubMessage.class))).thenReturn(future);
                })) {

            String result = publisher.publishDisbursalData(data);
            assertEquals("message-id-1", result);
        }
    }

    @Test
    public void testPublishDisbursalData_Exception() throws Exception {
        LoanDisbursedData data = mock(LoanDisbursedData.class);
        when(data.getApplicationId()).thenReturn("app-123");

        try (MockedConstruction<Publisher> mocked = Mockito.mockConstruction(Publisher.class,
                (mock, context) -> {
                    when(mock.publish(any(PubsubMessage.class))).thenThrow(new RuntimeException("PubSub error"));
                })) {

            try {
                publisher.publishDisbursalData(data);
                fail("Expected Exception was not thrown");
            } catch (Exception ex) {
                assertTrue(ex.getMessage().contains("PubSub error"));
            }
        }
    }
}