package com.flipkart.fintech.pandora.service.core.pl;

import com.flipkart.fintech.pandora.api.model.pl.response.EmandateVerificationResponse;
import java.util.Optional;

import com.flipkart.fintech.pandora.service.client.cfa.MockHelper;
import org.junit.Before;
import org.junit.Test;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

public class MandateDataParserTest {
  private final static String emandateSecretKey = "Sp2oyMONs2zEx9Y=";
  private final static String emandateInitializationVector = "08SM7aUgkNEygQ==";
  private final static String successData = "CK5oiGXoFbkQmPQhAeZB4EQpZEIfEwfoeCIkICOy/Tg/bzliLyemlmgrlXHsqBoRfSzXokpC5an1mav1g+FFY9QNdMPOQzsrnIgowYURrDV5aImBkFZe/KAilgZvBNEAl3aTmUr75/rra0NO8USGSxUyarTG9734mfj3xf+vJBqx/yXeslL5HjKoWbAMA5y64FLjEjQs5+Hz+1yopNgFQEdcbsTt9Pw3VYTMbwOk6dHuQ+U2c/qR6VueperO3oWXzI460o9idIf4Oi534kA4JH7GYPu1dY4tDX3Jis9XA82IOArd58pSlfFQiF8F1zgpSCVtqGNsHl/3fvXE+C9dm48pQdwz0wmFIoTGqzdzTIP56Szxn4pPS04ebp8NWvUnNoplbrjwIGjlTNF0NIFU3GuQYUt5y/M4ozGnSYdlx/ypeNenYehYkCDcg/NoO8oB";
  private final static String failureData = "CK5oiGXoFbkQmPQhAeZB4EQpZEIfEwfoeCIkICOy/Tg/bzliLyemlmgrlXHsqBoRfSzXokpC5an1mav1g+FFY4nIobH5c4HhNyNdir0IgiqVBXYeX9TB25vCZKOKPjFGevUw2RN7xmkNo/jqqVckzLSDioxYTWFjHZpeGckt7LHVbyR5J+OYsUaY2tS3DAO2JhYnf21lcUHReOnvleqVBxY7LbpXc7rXyLGpFmjcLmtF3TcDBiD6o851vqkADAHfNPcvEWwRU8wYJOizkfWcwNEIgEDpwwNc/M2KbtnG+U7cRFgW2o7NlXgyYXvIE/9M8yBhfkFJFaNfneSbh47WGbbatutRWHG8rcAWuq11/Ds=";
  private final static String invalidData = "CK5oiGXoFbkQmPQhAeZB4EQpZEIfEwfoeCIkICOy/Tg/bzliLyemlmgrlXHsqBoRfSzXokpC5an1mav1g+FFY4nIobH5c4HhNyNdir0IgiqVBXYeX9TB25vCZKOKPjFGxjspxSnfG82+fy8cm6f0o/24lfoGv618WV1L2c6EhUmjP5mbIT40Sqfdxw2HkVUmEpZ3yXUzVszc75RDi3ZdYkTZr5GvqpTQKy5Vap5dBq5QVTUMZG9w31wLjazZ3fruto9p3091FG5ZStMaLXQiLw==";

  private final static String transactionId = "f6edc7c2d52749cbbcf0c9e9703e8842";

  private MandateDataParser parser;
  private MockHelper flags;


  @Before
  public void setUp() throws Exception {
    EncryptionService encryptionService = new EncryptionService(emandateSecretKey,
        emandateInitializationVector, "", "",flags);
    this.parser = new MandateDataParser(encryptionService);
  }

  @Test
  public void eMandateSuccess() {
    Optional<EmandateVerificationResponse> responseOptional = parser.parse(successData, transactionId);
    assertTrue(responseOptional.isPresent());
    EmandateVerificationResponse response = responseOptional.get();
    assertEquals("Success", response.getStatus());
    assertEquals("success", response.getEMandateStatus());
  }

  @Test
  public void eMandateFailure() {
    Optional<EmandateVerificationResponse> responseOptional = parser.parse(failureData, transactionId);
    assertTrue(responseOptional.isPresent());
    EmandateVerificationResponse response = responseOptional.get();
    assertEquals("Success", response.getStatus());
    assertEquals("failure", response.getEMandateStatus());
  }

  @Test
  public void invalidResponse() {
    Optional<EmandateVerificationResponse> responseOptional = parser.parse(invalidData, transactionId);
    assertFalse(responseOptional.isPresent());
  }

  @Test
  public void emptyResponse() {
    Optional<EmandateVerificationResponse> responseOptional = parser.parse("", transactionId);
    assertFalse(responseOptional.isPresent());
  }

  @Test(expected = SecurityException.class)
  public void randomResponse() {
    Optional<EmandateVerificationResponse> responseOptional = parser.parse("aad", transactionId);
    assertFalse(responseOptional.isPresent());
  }

  @Test
  public void inCorrectTransactionId() {
    Optional<EmandateVerificationResponse> responseOptional = parser.parse(successData, "random");
    assertFalse(responseOptional.isPresent());
  }
}