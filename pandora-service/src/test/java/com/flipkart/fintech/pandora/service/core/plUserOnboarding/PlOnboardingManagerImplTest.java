package com.flipkart.fintech.pandora.service.core.plUserOnboarding;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.cryptex.LocalDynamicBucket;
import com.flipkart.fintech.pandora.api.model.request.plOnboarding.EncryptedRequest;
import com.flipkart.fintech.pandora.service.client.pl.client.MpockketClient;
import com.flipkart.fintech.pandora.service.client.sandbox.ApiParamModel;
import com.flipkart.fintech.pandora.service.client.pl.client.SandboxLendersPlClient;
import com.flipkart.fintech.pandora.service.client.plOnboarding.GenericLenderClient;
import com.flipkart.fintech.pandora.service.core.plUserOnboarding.adapters.LenderAdapterFactory;
import com.flipkart.fintech.pandora.service.core.plUserOnboarding.exceptions.PlOnboardingInvalidParamException;
import com.flipkart.fintech.pandora.service.external.BQIngestionHelper;
import com.flipkart.fintech.pandora.service.external.ElasticSearchClient;
import com.flipkart.fintech.pandora.service.external.coreLogistics.client.CoreLogisticsClient;
import com.flipkart.fintech.pandora.service.hystrix.PandoraHystrixProperties;
import com.flipkart.fintech.pandora.service.transformer.UserDataTransformer;
import com.flipkart.fintech.pinaka.client.PinakaClientException;
import com.flipkart.fintech.pinaka.client.v7.PinakaClientV7;
import com.flipkart.fintech.user.data.client.UserDataClient;
import com.flipkart.fintech.winterfell.client.WinterfellClient;
import com.flipkart.kloud.config.DynamicBucket;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.Mockito;

class PlOnboardingManagerImplTest {

    private PlOnboardingManager plOnboardingManager;

    private LenderAdapterFactory lenderAdapterFactory;
    private PandoraHystrixProperties pandoraHystrixProperties;
    private PlUserOnboardingConfig plUserOnboardingConfig;
    private GenericLenderClient genericLenderClient;
    private CoreLogisticsClient coreLogisticsClient;
    private PinakaClientV7 pinakaClientV7;
    private SandboxLendersPlClient sandboxLendersPlClient;
    private ApiParamModel apiParamModel;
    private ElasticSearchClient elasticSearchClient;
    @Mock
    private UserDataClient userDataClient;
    @Mock
    private UserDataTransformer userDataTransformer;
    @Mock
    private BQIngestionHelper bqIngestionHelper;
    @Mock
    private WinterfellClient winterfellClient;
    @Mock
    private MpockketClient mpockketClient;




    @BeforeEach
    void setUp() {
        lenderAdapterFactory = Mockito.mock(LenderAdapterFactory.class);
        pandoraHystrixProperties = Mockito.mock(PandoraHystrixProperties.class);
        DynamicBucket dynamicBucket = new LocalDynamicBucket("pandora-local");
        plUserOnboardingConfig = new PlUserOnboardingConfig(dynamicBucket);
        genericLenderClient = Mockito.mock(GenericLenderClient.class);
        coreLogisticsClient = Mockito.mock(CoreLogisticsClient.class);
        pinakaClientV7 = Mockito.mock(PinakaClientV7.class);
        sandboxLendersPlClient = Mockito.mock(SandboxLendersPlClient.class);
        apiParamModel = Mockito.mock(ApiParamModel.class);
        elasticSearchClient = Mockito.mock(ElasticSearchClient.class);
        bqIngestionHelper = Mockito.mock(BQIngestionHelper.class);
        winterfellClient = Mockito.mock(WinterfellClient.class);
        mpockketClient = Mockito.mock(MpockketClient.class);
        plOnboardingManager = new PlOnboardingManagerImpl(lenderAdapterFactory, pandoraHystrixProperties, plUserOnboardingConfig,
                                genericLenderClient, coreLogisticsClient, pinakaClientV7, sandboxLendersPlClient, apiParamModel, elasticSearchClient, userDataClient, userDataTransformer, bqIngestionHelper, winterfellClient, mpockketClient);


    }

    @Test
    void consumeLenderPlatformEvent() {
        String encryptedRequest = "{\"data\":\"eyJhbGciOiJSUzI1NiJ9.ZXlKbGJtTWlPaUpCTWpVMlIwTk5JaXdpWVd4bklqb2lVbE5CTFU5QlJWQXRNalUySW4wLms0V0o4NFZDOE1UcXg0ZEFKWjJPYU5oQWJhT19IdHBxQzNsT2Z6WFFSX19sSGxTWENyOExaNkJZd1l0T3lhTnlRaGpYRWVSSnhHaC0tTnFKSE9fR25aT0JZZjJOb3RrbV9LenBWQ0xHYVJNM01NODExOTkyUjc3OHVmMEdYTllsaVIyRWxwdUNZRGxLQkNnQU5HS0ZKTzhPdDR1NGowRTAzN0NCaEJOd3lvZ1lGb1ZKaElNVTFxTEM0aThteEJpUGFScEJ0UURtWjhnblFhYU9PX1Fib1QzSFpKWER5VDNhQUI0YnZtVXlsTW94OXZsWmNOdEZkYmVFc1ZmNnZLdjk4TFIzd1hZT0kwdVdwZ19XNUpIVFdOVG9nYjdOV0NGLUlsamE3d3ByV0lRTDBOTV9qVkF6SUZSb0gyZmozc1d2MWtvNm9DcWxGTGlDWlJ3WENXSlNBdy41UDllTUlzYzVKT2w5dHlCLkhuWnRnTmRXV1RIT0NCQ1o4YnZWUzZpTDhKaFJSWUlhcng5MjdXOGk3T211ay1LdktzYkNoTGMxZTJ1ZzhXVDFPNDN3Tk1iRG1kN1JwcERXNklVdUVxb1JQclUzWHpiSC1GcmE5SkVDQ3E3M0JDVld4Z3hYUWZfU2tDNFVkMnFJLXRESzVsRTB2YWM3dTF1T3d5bFV0cERFdHRxTG8zbXVOeXlvRG1xdFlTYkRVdm1OeFptRFBtTzBoS09XSFhOTXBxY0NCNWJkVTVwa3ZjRm9QbnlTZ2I5bWdZekw3Q1Rmel9DVkR3ZnJSZ20xaFNVSzlTTGpNRFF4Y0h0MUxQUlo4clUzV2lXNndqaHhiMFFWVDJ1UDhaeThaTVppdEctZHBvMjdRYkd1TG40akVxSVF6THk0cTVHT2RMcWY5ck9pbDFHcmlKay5NelVKWGdOQVNVeDE3VnlHWHdZZE5B.VaOnFJH_4Mp0WjY-IekL7swcd7cGGlKOJv0psIs69ihQB-n48Cb0WqsJY4122TxNPAhsWLqcMM9FMC_7f7kS4ElyD0wOTO0Q631hW6Dx32cPhpSXxdIPLLpXCmVX0RgNVMnthG97lTVxfd1oSe266BjoaTbx94nn8n6Iry1-VULrA4w8osNYwQBZUAbbglJB5_rf9y7BRXOxGbFUi7x5IxCQqtW-IwHd1qa1JyNRfs0_he9Vw3zAMA_QbylKaXnuxPjKyJcgYjGgvbnfteJ6gM54t2GrViTD6n_Q2O-OjLIC1DYkbWNR3jIEkXHrl2r2JpHslUuigTvRjp1OQ9eyqg\"}\n" +
                "\"{\\\"lspApplicationId\\\":\\\"APP2312281407153923492965723286878112976\\\",\\\"lenderApplicationId\\\":\\\"LD2023122822927000\\\",\\\"applicationStatus\\\":\\\"INPROGRESS\\\",\\\"stage\\\":{\\\"step\\\":\\\"VKYC\\\",\\\"subStep\\\":\\\"OVERALL_VKYC\\\",\\\"status\\\":\\\"SUCCESS\\\"},\\\"stateTransitionTime\\\":\\\"1704808255936\\\"}\"\n" +
                "{\"data\":\"eyJhbGciOiJSUzI1NiJ9.ZXlKbGJtTWlPaUpCTWpVMlIwTk5JaXdpWVd4bklqb2lVbE5CTFU5QlJWQXRNalUySW4wLllpSkMzN1RWR1d3dkhmMmR1eG8zYm1Oc1l1Y2hXaTh3SnlaV3c1dXFkY0pfZG9ubFRmd2tOTnVMZTAwNDJYZXM3UHJrdWVRNlc2enFOZUdMLVNlSmRaUDFHdHdFVjBCZkxBa0dHZFZVSUtmdUg4NTJHbWp3Y01HRXBMWmlvTGR1anNiWURBbFljVGRZZHJSNmFMaVVpcGljaldPVFFUNUpiVjNnZTBmN05jTjJtNWJiVUhBN200TGVzQW1ONWw4NWVnOXNDZVdWNE9LTjJONktNWE16MWRFdW1LRm95b2FCQUxFX01UZU1IUVVSbXZ0SXRsLWhkeE1xc0tXeWlKZXhzMC12LXNFQ0tlQXlFbFlSc1dvOTZleHo3NkN5bUNndTlieTRBaU9Ka04wU1YzWUpsMThuUUlibkhHZ3Y4R0hBS2VjZHZaWWRMQ2ZDZVJrLTVRbmRFQS5hZHdPWjhUc0hOaEZyTDFaLlVmdXNFN0ZWVGNBNDJUQW5YZGNmaUZaZi1hSVZsbUtTeWlRVHlWOHdYckthVEN1eVhRd01qRGhKS1RuQzZsdDhXRWE2TFBGclFZNkM0RmdZVEh6Y3ZqdzRycXUxSmxKRGxlOFFRWHZkclJtcHJ0QXh3V1ZfdGstUzNlOXA3bzRmUW9FbG9VVG1PWms5azZyNzFyNE5IUFQ5cHQzYXBSS1llT1V3cFA2TU5Qa3BXNFBDRkN1YWItbnRmcTdFUjJjQ2pHSmFhVGVnS0hUX2RYN1AtVXAzWmtNXzYwdEdTblBPcVBvZzR5TDlvRHg2MTY1b25tVVlzRzVkR1RuenlLanB6bWpSaDROTk1MbnljNndYajNuNUJFcTdJTV9iU05oQzZXS2s4S1RHaC1mTnhYZ2k5U2I0TnZkdHpsSVp1ZTBVbGxUeGFfeV9fcVdTQlNZMG9nLk1ud0d4RmVnSzg5ZzV1ck56NmpvRlE.eL9m-Sqd44-paHgr_dl48rRXzVrRNibpLo0Xlhs7twIByXUwWIakxkz7JmZlypa1fdag-oz-F2GMBiHLdiZ4mO_NSYPBHWZ9d8L1idPBre3rcN-kkKOB5LG5g_gW5cQUaPZkw_l74g_ss9WG-ql_fM99O4P8t6Q5-RtjRVF8hlI-y7poeZ3SYvJIG6gamtr8ecnBDlkWOr-otCg87nnJnO6P7iejSLvLggz_6FSavIaynBN-7pl-x1mkRejRpiXKVWW_lJIm205xwsrexWfDmlODcbRwIRxKkww3m-5YOPLn_rtjKhJnUX_dxkuYu7yfYMJx9-gt1exMOKSh9bRHgg\"}\n" +
                "{\"lsp_application_id\":null,\"lender_application_id\":null,\"application_status\":null,\"state_transition_time\":null,\"stage\":{\"step\":\"VKYC\",\"sub_step\":\"OVERALL_VKYC\",\"status\":\"SUCCESS\",\"lender\":\"AXIS\",\"failure_code\":null}}\n";

        try {
            plOnboardingManager.consumeLenderPlatformEvent(new ObjectMapper().readValue(encryptedRequest, EncryptedRequest.class), "axispl", "abc", "ts");
        } catch (PlOnboardingInvalidParamException | PinakaClientException | JsonProcessingException e) {
            e.printStackTrace();
        }
    }
}
