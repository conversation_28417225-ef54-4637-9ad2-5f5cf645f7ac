package com.flipkart.fintech.pandora.service.core.plUserOnboarding.adapters.sandbox;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.pandora.api.model.request.plOnboarding.sandbox.v2.CreateApplicationRequest;
import com.flipkart.fintech.pandora.service.application.configuration.EnvironmentConfiguration;
import com.flipkart.fintech.pandora.service.application.configuration.PandoraConfiguration;
import com.flipkart.fintech.pandora.service.client.configuration.SandboxLendersPlConfiguration;
import com.flipkart.fintech.pandora.service.client.sandbox.ApiParamModel;
import com.flipkart.fintech.pandora.service.core.plUserOnboarding.exceptions.PlOnboardingInvalidParamException;
import com.flipkart.fintech.user.data.client.UserDataClient;
import com.flipkart.fintech.user.data.models.UserData;
import com.flipkart.kloud.config.DynamicBucket;
import edu.emory.mathcs.backport.java.util.Arrays;
import org.junit.jupiter.api.Assertions;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.IOException;
import java.io.InputStream;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;

@RunWith(MockitoJUnitRunner.class)
public class CreateApplicationV2AdapterTest {

    @Mock
    private UserDataClient usClient;

    @Mock
    private ApiParamModel apiParamModel;

    @Mock
    private PandoraConfiguration pandoraConfiguration;

    @Mock
    private EnvironmentConfiguration environmentConfiguration;

    @Mock
    private DynamicBucket dynamicBucket;

    private CreateApplicationV2Adapter createApplicationV2Adapter;

    private CreateApplicationRequest createApplicationRequest;

    private com.flipkart.fintech.pandora.service.client.sandbox.v2.request.CreateApplicationRequest expectedRequest;

    @Before
    public void setUp() throws IOException {
        Mockito.when(pandoraConfiguration.getEnvironmentConfig()).thenReturn(environmentConfiguration);
        Mockito.when(environmentConfiguration.getEnv()).thenReturn("preprod");

        InputStream inputStream = getClass().getClassLoader().getResourceAsStream("requests/CreateApplicationRequest.json");
        createApplicationRequest = new ObjectMapper().readValue(inputStream, CreateApplicationRequest.class);

        inputStream = getClass().getClassLoader().getResourceAsStream("requests/CreateApplicationRequest_expected.json");
        expectedRequest = new ObjectMapper().readValue(inputStream, com.flipkart.fintech.pandora.service.client.sandbox.v2.request.CreateApplicationRequest.class);

        createApplicationV2Adapter = new CreateApplicationV2Adapter(usClient, pandoraConfiguration, apiParamModel, dynamicBucket);
    }

    @Test
    public void testGenerateLenderRequestBody_validRequest_success() throws PlOnboardingInvalidParamException, IOException {
        UserData userData = UserData.builder().primaryPhone("+911234567890").build();
        Mockito.when(usClient.getUserData(any(), anyString(), anyString(), any())).thenReturn(userData);
        Mockito.when(dynamicBucket.getStringArray(any())).thenReturn(Arrays.asList(new String[]{"MONEYVIEWOPENMKT"}));
        SandboxLendersPlConfiguration config = SandboxLendersPlConfiguration.builder()
                .cremoEnabled(true)
                .granularCremoEnabled(true)
                .needShippingAddress(false)
                .build();
        Mockito.when(apiParamModel.getPlConfigurationForLender(any())).thenReturn(config);
        com.flipkart.fintech.pandora.service.client.sandbox.v2.request.CreateApplicationRequest request =
                createApplicationV2Adapter.generateLenderRequestBody(createApplicationRequest);

        Assertions.assertNotNull(request);
        Assertions.assertEquals(request, expectedRequest);
    }
}
