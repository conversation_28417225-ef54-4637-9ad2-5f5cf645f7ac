package com.flipkart.fintech.pandora.service.core.plUserOnboarding.adapters.sandbox;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.pandora.api.model.pl.sandbox.dto.*;
import com.flipkart.fintech.pandora.api.model.pl.sandbox.dto.enums.FeeType;
import com.flipkart.fintech.pandora.api.model.pl.sandbox.dto.enums.TenureUnit;
import com.flipkart.fintech.pandora.api.model.request.plOnboarding.ChargeBreakUp;
import com.flipkart.fintech.pandora.api.model.request.plOnboarding.sandbox.v2.SubmitOfferRequest;
import com.flipkart.fintech.pandora.service.client.sandbox.v2.response.GetOfferResponse;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.fintech.winterfell.client.WinterfellClient;
import lombok.var;
import org.junit.Before;
import org.junit.jupiter.api.Assertions;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class SubmitOfferAdapterTest {

    private WinterfellClient winterfellClient;

    private SubmitOfferAdapter submitOfferAdapter;

    @Before
    public void setUp() {
        this.winterfellClient = Mockito.mock(WinterfellClient.class);
        submitOfferAdapter = new SubmitOfferAdapter(winterfellClient);
    }

    @Test
    public void testGenerateLenderRequestBody_whenFinancialProviderIsABFL() throws Exception {
        SubmitOfferRequest input = buildRequest("ABFL", "UPGRADED");
        Map<String, Object> offerMap = new ObjectMapper().convertValue(buildGetOfferResponse(), Map.class);

        ApplicationDataResponse response = new ApplicationDataResponse();
        Map<String, Object> data = new HashMap<>();
        data.put("financial_provider", "ABFL");
        data.put("getOffer", offerMap);
        response.setApplicationData(data);

        when(winterfellClient.getApplication(any(), eq("LSP-APP-ID"), any(), any())).thenReturn(response);

        com.flipkart.fintech.pandora.service.client.sandbox.v2.request.SubmitOfferRequest result = submitOfferAdapter.generateLenderRequestBody(input);

        assertNotNull(result.getSubmittedOffer());
        assertEquals("LENDER-APP-ID", result.getLenderApplicationId());
        assertTrue(result.getAdditionalDataFlowsInvoked().contains("AA"));
    }

    @Test
    public void testGenerateLenderRequestBody_whenFinancialProviderIsNotABFL() throws Exception {
        SubmitOfferRequest input = buildRequest("XYZ", "INITIAL");

        ApplicationDataResponse response = new ApplicationDataResponse();
        Map<String, Object> data = new HashMap<>();
        data.put("financial_provider", "XYZ");
        response.setApplicationData(data);

        when(winterfellClient.getApplication(any(), any(), any(), any())).thenReturn(response);

        com.flipkart.fintech.pandora.service.client.sandbox.v2.request.SubmitOfferRequest result = submitOfferAdapter.generateLenderRequestBody(input);

        assertNotNull(result.getSubmittedOffer().getLoanParams().getCharges());
        assertEquals("INITIAL", input.getOfferType());
        assertEquals(1, result.getSubmittedOffer().getLoanParams().getCharges().size());
    }

    @Test
    public void testGetEmiCalculation() {
        Tenure tenure = new Tenure(2, TenureUnit.YEAR);
        Double result = submitOfferAdapter.getEmi(100000.0, tenure, 12.0);
        assertNotNull(result);
        assertTrue(result > 0);
    }

    @Test
    public void testRoundToFixedDecimal() {
        Double rounded = invokeRoundToFixedDecimal(123.45678, 2);
        assertEquals(Double.valueOf(123.46), rounded);
    }

    // Helper to test private roundToFixedDecimal
    private Double invokeRoundToFixedDecimal(Double input, int places) {
        try {
            var method = SubmitOfferAdapter.class.getDeclaredMethod("roundToFixedDecimal", Double.class, int.class);
            method.setAccessible(true);
            return (Double) method.invoke(submitOfferAdapter, input, places);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    // === Helper Methods ===

    private SubmitOfferRequest buildRequest(String provider, String offerType) {
        Map<ChargeType, ChargeBreakUp> charges = new HashMap<>();
        charges.put(ChargeType.PROCESSING_FEE,
                ChargeBreakUp.builder().amount(100.0).gstAmount(BigDecimal.valueOf(18.0)).totalAmount(BigDecimal.valueOf(118.0)).build());

        return SubmitOfferRequest.builder()
                .loanAmount(1000.0)
                .roi(12.0)
                .tenure(new Tenure(1, TenureUnit.YEAR))
                .charges(charges)
                .offerId("OFFER-123")
                .lenderApplicationId("LENDER-APP-ID")
                .lspApplicationId("LSP-APP-ID")
                .lspId("LSP-001")
                .offerType(offerType)
                .build();
    }

    private GetOfferResponse buildGetOfferResponse() {
        OfferEntity offerEntity = new OfferEntity();
        offerEntity.setRoi(12.0);
        offerEntity.setTenure(new Tenure(1, TenureUnit.YEAR));
        offerEntity.setPf(new LoanCharge(FeeType.PERCENTAGE, 2.0, 18.0));

        List<OfferEntity> offerTable = Collections.singletonList(offerEntity);

        Offer offer = new Offer();
        offer.setMaxSanctionedAmount(100000.0);
        offer.setOfferTable(offerTable);

        GeneratedOffer generatedOffer = new GeneratedOffer();
        generatedOffer.setOffer(offer);

        return GetOfferResponse.builder()
                .generatedOffer(generatedOffer)
                .build();
    }

    @Test
    public void getEmiTest1() {
        Double loanAmount = 10000D;
        Double roi = 36D;
        Tenure tenure = new Tenure();
        tenure.setUnit(TenureUnit.MONTH);
        tenure.setValue(2);
        double emi = submitOfferAdapter.getEmi(loanAmount, tenure, roi);
        Assertions.assertEquals(emi, 5226.11, 0.5);
    }

    @Test
    public void getEmiTest2() {
        Double loanAmount = 20000D;
        Double roi = 24.45D;
        Tenure tenure = new Tenure();
        tenure.setUnit(TenureUnit.MONTH);
        tenure.setValue(5);
        double emi = submitOfferAdapter.getEmi(loanAmount, tenure, roi);
        Assertions.assertEquals(emi, 4247.79, 0.5);
    }



}