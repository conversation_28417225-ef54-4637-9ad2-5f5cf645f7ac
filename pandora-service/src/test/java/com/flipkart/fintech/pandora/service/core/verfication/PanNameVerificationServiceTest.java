package com.flipkart.fintech.pandora.service.core.verfication;

import com.flipkart.fintech.pandora.api.model.request.verification.PanNameVerificationRequest;
import com.flipkart.fintech.pandora.service.annotations.JsonFile;
import com.flipkart.fintech.pandora.service.client.digitapAi.DigitapClient;
import com.flipkart.fintech.pandora.service.core.verification.PanNameVerificationService;
import com.flipkart.fintech.pandora.service.extensions.JsonFileParameterResolver;
import com.flipkart.fintech.user.data.client.UserDataClient;
import com.supermoney.schema.PandoraService.DigitapAiPanValResponseV2;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import pandora.external.models.response.DigitapAiPanValResponse;
import com.supermoney.publisher.EventPublisher;

import static org.asynchttpclient.util.Assertions.assertNotNull;
import static org.junit.Assert.assertEquals;
import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
@ExtendWith(JsonFileParameterResolver.class)
public class PanNameVerificationServiceTest {
    @InjectMocks
    private PanNameVerificationService panNameVerificationService;

    @Mock
    private DigitapClient digitapClient;

    @Mock
    private UserDataClient userDataClient;

    @Mock
    private EventPublisher eventPublisher;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        panNameVerificationService = new PanNameVerificationService(digitapClient, userDataClient, eventPublisher);
    }

    @Test
    public void testGetDigitapEvent_WithResult(@JsonFile("requests/digitapRequest.json")PanNameVerificationRequest panNameVerificationRequest,
                                               @JsonFile("response/DigitapAiPanValResponse1.json")DigitapAiPanValResponse response) {

        String accountId = "account123";
        // Act
        DigitapAiPanValResponseV2 resultEvent = panNameVerificationService.getDigitapEvent(response, accountId, panNameVerificationRequest);

        // Assert
        assertNotNull(resultEvent, "The resultEvent should not be null");
        assertEquals("clientRef123", resultEvent.getClientRefNum());
        assertEquals("request123", resultEvent.getRequestId());
        assertEquals("200", resultEvent.getHttpResponseCode());
        assertEquals("103", resultEvent.getResultCode());
        assertEquals(accountId, resultEvent.getAccountId());
        assertEquals(panNameVerificationService.getAge("15/08/1990"), resultEvent.getDigitapAge());
        assertTrue(resultEvent.getResultNameMatch(), "Name match should be true");
        assertEquals("95", String.valueOf(resultEvent.getResultNameMatchScore()));

        // Verify encrypted values
        assertEquals(panNameVerificationService.encrypt("John"), resultEvent.getUserEnteredFirstName());
        assertEquals(panNameVerificationService.encrypt("Doe"), resultEvent.getUserEnteredLastName());
        assertEquals(panNameVerificationService.encrypt("John"), resultEvent.getDigitapFirstName());
        assertEquals(panNameVerificationService.encrypt("A"), resultEvent.getDigitapMiddleName());
        assertEquals(panNameVerificationService.encrypt("Doe"), resultEvent.getDigitapLastName());
        assertEquals(panNameVerificationService.encrypt("John A Doe"), resultEvent.getDigitapFullname());
    }

    @Test
    public void testGetDigitapEvent_WithoutResult(@JsonFile("requests/digitapRequest.json")PanNameVerificationRequest panNameVerificationRequest,
                                               @JsonFile("response/DigitapAiPanValResponse2.json")DigitapAiPanValResponse response) {

        String accountId = "account123";
        // Act
        DigitapAiPanValResponseV2 resultEvent = panNameVerificationService.getDigitapEvent(response, accountId, panNameVerificationRequest);

        // Assert
        assertNotNull(resultEvent, "The resultEvent should not be null");
        assertEquals("clientRef123", resultEvent.getClientRefNum());
        assertEquals("request123", resultEvent.getRequestId());
        assertEquals("200", resultEvent.getHttpResponseCode());
        assertEquals("103", resultEvent.getResultCode());
        assertEquals(accountId, resultEvent.getAccountId());
        assertNull(resultEvent.getDigitapAge(), "Digitap age should be null");
        assertEquals(panNameVerificationService.encrypt("John"), resultEvent.getUserEnteredFirstName());
        assertEquals(panNameVerificationService.encrypt("Doe"), resultEvent.getUserEnteredLastName());
        assertNull(resultEvent.getDigitapFirstName(), "Digitap first name should be null");
        assertNull(resultEvent.getDigitapMiddleName(), "Digitap middle name should be null");
        assertNull(resultEvent.getDigitapLastName(), "Digitap last name should be null");
        assertNull(resultEvent.getDigitapFullname(), "Digitap full name should be null");
        assertFalse(resultEvent.getResultNameMatch(), "Name match should be false");
        assertEquals("0", String.valueOf(resultEvent.getResultNameMatchScore()));
    }
}
