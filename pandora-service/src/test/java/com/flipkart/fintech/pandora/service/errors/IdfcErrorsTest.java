package com.flipkart.fintech.pandora.service.errors;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.flipkart.fintech.pandora.service.errors.IdfcErrors.DownloadCkycErrors;
import com.flipkart.fintech.pandora.service.errors.IdfcErrors.SearchCkycErrors;
import java.util.HashMap;
import org.junit.jupiter.api.Test;

public class IdfcErrorsTest {

  @Test
  void shouldRejectWhenCkycAcctTypeIsFour() {
    String errorMessage = "CKYCAccType value is 04. As per compliance guidelines, we can’t move ahead with this.";
    String actualError = DownloadCkycErrors.ACC_TYPE_FOUR.populateIfNeeded(new HashMap<>());
    assertEquals(errorMessage, actualError);
    assertEquals("REJECT", DownloadCkycErrors.ACC_TYPE_FOUR.getErrorType());
    assertEquals("IDFCERR004", DownloadCkycErrors.ACC_TYPE_FOUR.getErrorCode());

  }

  @Test
  void shouldRejectWhenKycNumberDoNotExists() {
    String errorMessage = "KYC Number ************ do not exists in system";
    HashMap<String, String> data = new HashMap<>();
    data.put("kycNumber", "************");
    String actualError = DownloadCkycErrors.KYC_NUMBER_DO_NOT_EXISTS.populateIfNeeded(data);
    assertEquals(errorMessage, actualError);
    assertEquals("REJECT", DownloadCkycErrors.KYC_NUMBER_DO_NOT_EXISTS.getErrorType());
  }

  @Test
  void shouldRejectWhenMultipleCkycIdsPresent() {
    String errorMessage = "Multiple CKYC IDs in Search Response";
    String actualError = SearchCkycErrors.MULTIPLE_CKYC_IDS.populateIfNeeded(new HashMap<>());
    assertEquals(errorMessage, actualError);
    assertEquals("REJECT", SearchCkycErrors.MULTIPLE_CKYC_IDS.getErrorType());
  }

}
