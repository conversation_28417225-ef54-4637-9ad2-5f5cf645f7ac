package com.flipkart.fintech.pandora.service.extensions;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.pandora.service.annotations.JsonFile;
import java.io.IOException;
import java.io.InputStream;
import org.junit.jupiter.api.extension.ExtensionContext;
import org.junit.jupiter.api.extension.ParameterContext;
import org.junit.jupiter.api.extension.ParameterResolver;

public class JsonFileParameterResolver implements ParameterResolver {

  @Override
  public boolean supportsParameter(ParameterContext parameterContext, ExtensionContext extensionContext) {
    return parameterContext.isAnnotated(JsonFile.class);
  }

  @Override
  public Object resolveParameter(ParameterContext parameterContext, ExtensionContext extensionContext) {
    JsonFile annotation = parameterContext.getParameter().getAnnotation(JsonFile.class);
    String path = annotation.value();
    Class<?> type = parameterContext.getParameter().getType();

    ObjectMapper objectMapper = new ObjectMapper();

    try (InputStream is = getClass().getClassLoader().getResourceAsStream(path)) {
      return objectMapper.readValue(is, type);
    } catch (IOException e) {
      throw new RuntimeException("Failed to read JSON file from the path + " + path, e);
    }
  }
}

