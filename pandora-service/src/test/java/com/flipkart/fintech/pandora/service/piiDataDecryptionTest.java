package com.flipkart.fintech.pandora.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.pandora.api.model.pl.request.PanVerificationRequest;
import com.flipkart.fintech.pandora.api.model.pl.request.PennyDropRequest;
import com.flipkart.fintech.pandora.service.core.decryption.FormDataDecryption;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;

import com.flipkart.fintech.profile.api.request.BureauRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;


public class piiDataDecryptionTest {
    private PanVerificationRequest panVerificationRequest;
    private PennyDropRequest pennyDropRequest;
    private BureauRequest bureauRequest;
    @BeforeEach
    public void setUp() throws Exception {
        InputStream inputStream = FormDataDecryption.class.getClassLoader().getResourceAsStream("PIIEncryptionData.json");
        panVerificationRequest = new ObjectMapper().readValue(inputStream, PanVerificationRequest.class);
        inputStream = FormDataDecryption.class.getClassLoader().getResourceAsStream("PennyDropDecryptionData.json");
        pennyDropRequest = new ObjectMapper().readValue(inputStream, PennyDropRequest.class);
        inputStream = FormDataDecryption.class.getClassLoader().getResourceAsStream("experianDecryptionData.json");
        bureauRequest = new ObjectMapper().readValue(inputStream, BureauRequest.class);
    }
    @Test
    public void decryptDataTest() {
        System.out.println("Before decryption:");
        System.out.println( panVerificationRequest.getPan());
        String decryptedData = decryptData("pan",panVerificationRequest.getPan());
        System.out.println("After decryption:");
        panVerificationRequest.setPan(decryptedData);
        System.out.println( panVerificationRequest.getPan());

    }
    @Test
    public void decryptExperianDataTest() {
        System.out.println("before encryption");
        System.out.println(bureauRequest.toString());
        Map<String,Object> encryptData  = new HashMap<>();
        encryptData.put("firstName",bureauRequest.getFirstName());
        encryptData.put("lastName",bureauRequest.getLastName());
        encryptData.put("pan",bureauRequest.getPan());
        encryptData.put("email",bureauRequest.getEmail());
        Map<String,Object> decryptData = new FormDataDecryption().decryptFormData(encryptData);
        bureauRequest.setLastName((String) decryptData.get("lastName"));
        bureauRequest.setFirstName((String) decryptData.get("firstName"));
        bureauRequest.setPan((String) decryptData.get("pan"));
        bureauRequest.setEmail((String) decryptData.get("email"));
        System.out.println("after decryption");
        System.out.println(bureauRequest.toString());
    }

    @Test
    public void decryptBankDetailsTest() {
        System.out.println("Before decryption:");
        System.out.println( pennyDropRequest.getIfscCode()+","+pennyDropRequest.getCustBankAccNo());
        String decryptDataIfsc = decryptData("ifscCode",pennyDropRequest.getIfscCode());
        String decryptedDataBankAcc = decryptData("custBankAccNo",pennyDropRequest.getCustBankAccNo());
        System.out.println("After decryption:");
        pennyDropRequest.setCustBankAccNo(decryptedDataBankAcc);
        pennyDropRequest.setIfscCode(decryptDataIfsc);
        System.out.println( pennyDropRequest.getIfscCode()+","+pennyDropRequest.getCustBankAccNo());


    }

    private String decryptData(String field,String data) {
        Map<String,Object> encryptData = new HashMap<>();
        encryptData.put(field, data);
        Map<String,Object> decryptData;
        decryptData = new FormDataDecryption().decryptFormData(encryptData);
        return (String) decryptData.get(field);
    }
}
