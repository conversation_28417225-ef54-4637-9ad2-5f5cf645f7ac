package com.flipkart.fintech.pandora.service.resources.pl;

import com.flipkart.fintech.pandora.api.model.pl.request.LoanDisbursedData;
import com.flipkart.fintech.pandora.service.core.pl.LoanDisbursedDataPublisher;
import com.flipkart.fintech.pandora.service.core.pl.LoanService;
import com.flipkart.fintech.pandora.service.core.pl.CustomerIdentificationService;
import com.flipkart.fintech.pandora.service.core.pl.PaymentService;
import com.flipkart.fintech.pandora.service.core.pl.AutoDisbursalService;
import com.flipkart.fintech.pandora.service.core.pl.EncryptionService;
import com.flipkart.fintech.pandora.service.resources.pl.LoanResource;
import org.junit.Before;
import org.junit.Test;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

public class LoanResourceTest {

    private LoanService loanService;
    private CustomerIdentificationService customerIdentificationService;
    private PaymentService paymentService;
    private AutoDisbursalService autoDisbursalService;
    private EncryptionService encryptionService;
    private LoanDisbursedDataPublisher loanDisbursedDataPublisher;
    private LoanResource loanResource;

    @Before
    public void setUp() {
        loanService = mock(LoanService.class);
        customerIdentificationService = mock(CustomerIdentificationService.class);
        paymentService = mock(PaymentService.class);
        autoDisbursalService = mock(AutoDisbursalService.class);
        encryptionService = mock(EncryptionService.class);
        loanDisbursedDataPublisher = mock(LoanDisbursedDataPublisher.class);

        loanResource = new LoanResource(
                loanService,
                customerIdentificationService,
                paymentService,
                autoDisbursalService,
                encryptionService,
                loanDisbursedDataPublisher
        );
    }

    @Test
    public void testGetUserOfferData() throws Exception {
        LoanDisbursedData data = mock(LoanDisbursedData.class);
        when(loanDisbursedDataPublisher.publishDisbursalData(data)).thenReturn("message-id-1");

        String result = loanResource.getUserOfferData(data);

        assertEquals("message-id-1", result);
        verify(loanDisbursedDataPublisher).publishDisbursalData(data);
    }
}