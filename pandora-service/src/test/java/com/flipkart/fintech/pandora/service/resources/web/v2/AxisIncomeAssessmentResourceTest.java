package com.flipkart.fintech.pandora.service.resources.web.v2;

import com.flipkart.fintech.ardour.client.IncomeApplicationClientImpl;
import com.flipkart.fintech.pandora.api.model.incomeassessment.request.GenerateTokenRequest;
import com.flipkart.fintech.pandora.api.model.incomeassessment.request.GetStatusRequest;
import com.flipkart.fintech.pandora.api.model.incomeassessment.request.InitiateApplicationRequest;
import com.flipkart.fintech.pandora.api.model.incomeassessment.response.GenerateTokenResponse;
import com.flipkart.fintech.pandora.api.model.incomeassessment.response.GetStatusResponse;
import com.flipkart.fintech.pandora.api.model.incomeassessment.response.InitiateApplicationResponse;
import com.flipkart.fintech.pandora.service.application.configuration.AxisCbcConfiguration;
import com.flipkart.fintech.pandora.service.core.incomeassessment.*;
import com.flipkart.fintech.pandora.service.external.models.incomeassessment.request.axis.AxisGenerateTokenRequest;
import com.flipkart.fintech.pandora.service.external.models.incomeassessment.request.axis.AxisGetStatusRequest;
import com.flipkart.fintech.pandora.service.external.models.incomeassessment.request.axis.AxisInitiateApplicationRequest;
import com.flipkart.fintech.pandora.service.external.models.incomeassessment.response.axis.AxisGenerateTokenResponse;
import com.flipkart.fintech.pandora.service.external.models.incomeassessment.response.axis.AxisGetStatusResponse;
import com.flipkart.fintech.pandora.service.external.models.incomeassessment.response.axis.AxisInitiateApplicationResponse;
import com.flipkart.fintech.pandora.service.hystrix.PandoraHystrixProperties;
import com.flipkart.fintech.pandora.service.resources.web.IncomeAssessmentResource;
import com.flipkart.fintech.pandora.service.utils.EncryptionUtil;
import com.flipkart.fintech.pandora.service.utils.PandoraContext;
import io.dropwizard.testing.junit.ResourceTestRule;
import org.junit.jupiter.api.BeforeEach;
import org.junit.ClassRule;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.runners.MockitoJUnitRunner;

import javax.ws.rs.client.Entity;
import javax.ws.rs.core.MultivaluedHashMap;
import javax.ws.rs.core.MultivaluedMap;
import java.util.HashMap;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class AxisIncomeAssessmentResourceTest {
    private static final PandoraHystrixProperties pandoraHystrixProperties = new PandoraHystrixProperties();
    private static IncomeAssessmentAdapter assessmentAdapter = mock(IncomeAssessmentAdapter.class);
    private static IncomeAssessmentClientService incomeAssessmentClientService = mock(IncomeAssessmentClientService.class);
    private String TEST_ACC_ID = "ACCTEST";
    private static IncomeApplicationClientImpl incomeApplicationClient = mock(IncomeApplicationClientImpl.class);
    private static EncryptionUtil encryptionUtil = mock(EncryptionUtil.class);
    private static AxisCbcConfiguration axisCbcConfiguration = mock(AxisCbcConfiguration.class);
    private static IncomeAssessmentService incomeAssessmentService =
            new IncomeAssessmentServiceImpl(assessmentAdapter, incomeAssessmentClientService, pandoraHystrixProperties, incomeApplicationClient, encryptionUtil, axisCbcConfiguration);
    @ClassRule
    public static final ResourceTestRule resources = ResourceTestRule.builder()
            .addResource(new IncomeAssessmentResource(incomeAssessmentService))
            .build();

    @BeforeEach
    public void setUp() {

        MultivaluedMap<String, String> headers = PandoraContext.getHeaders();
        if (null != headers) {
            headers.add("X-Perf-Test", "True");
        }
        else {
            headers = new MultivaluedHashMap<>();
            headers.add("X-Perf-Test", "True");
        }

        PandoraContext.setHeaders(headers);
        pandoraHystrixProperties.setCommandCircuitBreakerProperties(new HashMap<>());
        pandoraHystrixProperties.setCommandExecutionProperties(new HashMap<>());
        pandoraHystrixProperties.setThreadPoolProperties(new HashMap<>());
    }

    @Test
    public void inititateApplicationHappyCase() throws Exception {
        InitiateApplicationRequest applicationRequest = new InitiateApplicationRequest();
        applicationRequest.setAccountId(TEST_ACC_ID);
        applicationRequest.setIncomeAssessmentId("C1-10");

        AxisInitiateApplicationRequest axisInitiateApplicationRequest = new AxisInitiateApplicationRequest();

        AxisInitiateApplicationResponse axisInitiateApplicationResponse = new AxisInitiateApplicationResponse();
        InitiateApplicationResponse initiateApplicationResponse = new InitiateApplicationResponse();
        initiateApplicationResponse.setIncomeAssessmentId("C1-10");

        when(incomeAssessmentClientService.initiateApplication(axisInitiateApplicationRequest,
                IncomeAssessmentConfigurationKeys.INITIATE_APPLICATON.getKey())).thenReturn(axisInitiateApplicationResponse);

        when(assessmentAdapter.initiateApplicationRequestToAxisInitiateApplicationRequest(applicationRequest)).thenReturn(
                axisInitiateApplicationRequest);
        when(assessmentAdapter.axisInitiateApplicationResponseToInitiateApplicationResponse(
                axisInitiateApplicationResponse)).thenReturn(initiateApplicationResponse);

        InitiateApplicationResponse actual = resources.target("/1/income/initate-appplication")
                .request()
                .post(Entity.json(applicationRequest))
                .readEntity(InitiateApplicationResponse.class);

        assertThat(actual.getIncomeAssessmentId()).isEqualToIgnoringCase("C1-10");
    }

    @Test
    public void generateTokenHappyCase() throws Exception {
        GenerateTokenRequest generateTokenRequest = new GenerateTokenRequest();
        generateTokenRequest.setAccountId(TEST_ACC_ID);
        generateTokenRequest.setIncomeAssessmentId("C1-11");


        AxisGenerateTokenRequest axisGenerateTokenRequest = new AxisGenerateTokenRequest();

        AxisGenerateTokenResponse axisGenerateTokenResponse = new AxisGenerateTokenResponse();
        GenerateTokenResponse generateTokenResponse = new GenerateTokenResponse();
        generateTokenResponse.setToken("token");

        when(incomeAssessmentClientService.generateToken(axisGenerateTokenRequest,
                IncomeAssessmentConfigurationKeys.GENERATE_TOKEN.getKey())).thenReturn(axisGenerateTokenResponse);

        when(assessmentAdapter.generateTokenRequestToAxisGenerateTokenRequest(generateTokenRequest)).thenReturn(
                axisGenerateTokenRequest);
        when(assessmentAdapter.axisGenerateTokenResponseToGenerateTokenResponse(
                axisGenerateTokenResponse)).thenReturn(generateTokenResponse);

        GenerateTokenResponse actual = resources.target("/1/income/generate-token")
                .request()
                .post(Entity.json(generateTokenRequest))
                .readEntity(GenerateTokenResponse.class);

        assertThat(actual.getToken()).isEqualToIgnoringCase("token");
    }

    @Test
    public void getStatusHappyCase() throws Exception {
        GetStatusRequest getStatusRequest = new GetStatusRequest();
        getStatusRequest.setAccountId(TEST_ACC_ID);
        getStatusRequest.setIncomeAssessmentId("C1-12");


        AxisGetStatusRequest axisGetStatusRequest = new AxisGetStatusRequest();

        AxisGetStatusResponse axisGetStatusResponse = new AxisGetStatusResponse();
        GetStatusResponse getStatusResponse = new GetStatusResponse();
        getStatusResponse.setStatus("FAILED");

        when(incomeAssessmentClientService.getStatus(axisGetStatusRequest,
                IncomeAssessmentConfigurationKeys.GET_STATUS.getKey())).thenReturn(axisGetStatusResponse);

        when(assessmentAdapter.getStatusRequestToAxisGetStatusRequest(getStatusRequest)).thenReturn(
                axisGetStatusRequest);
        when(assessmentAdapter.axisGetStatusResponseToGetStatusResponse(
                axisGetStatusResponse)).thenReturn(getStatusResponse);

        GetStatusResponse actual = resources.target("/1/income/get-status")
                .request()
                .post(Entity.json(getStatusRequest))
                .readEntity(GetStatusResponse.class);

        assertThat(actual.getStatus()).isEqualToIgnoringCase("FAILED");
    }

}
