package com.flipkart.fintech.pandora.service.resources.web.v2;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.pandora.api.model.*;
import com.flipkart.fintech.pandora.api.model.common.EmploymentStatus;
import com.flipkart.fintech.pandora.api.model.common.Gender;
import com.flipkart.fintech.pandora.api.model.common.STATUS;
import com.flipkart.fintech.pandora.api.model.request.onboarding.*;
import com.flipkart.fintech.pandora.api.model.request.onboarding.OtpDetails;
import com.flipkart.fintech.pandora.api.model.request.onboarding.v2.*;
import com.flipkart.fintech.pandora.api.model.response.onboarding.v2.*;
import com.flipkart.fintech.pandora.service.application.configuration.AxisCbcApiConfigurations;
import com.flipkart.fintech.pandora.service.application.configuration.AxisCbcConfiguration;
import com.flipkart.fintech.pandora.service.application.configuration.CbcAxisTestConfig;
import com.flipkart.fintech.pandora.service.external.adapters.AxisAdapterUtil;
import com.flipkart.fintech.pandora.service.external.adapters.AxisUserOnboardingAdapter;
import com.flipkart.fintech.pandora.service.external.dataModels.axisUserOnboarding.CustDemographics;
import com.flipkart.fintech.pandora.service.external.dataModels.axisUserOnboarding.CustomerCardDetails;
import com.flipkart.fintech.pandora.service.external.dataModels.axisUserOnboarding.request.*;
import com.flipkart.fintech.pandora.service.external.dataModels.axisUserOnboarding.request.encrypted.*;
import com.flipkart.fintech.pandora.service.external.dataModels.axisUserOnboarding.response.*;
import com.flipkart.fintech.pandora.service.external.dataModels.axisUserOnboarding.response.encrypted.*;
import com.flipkart.fintech.pandora.service.utils.Constants;
import com.flipkart.fintech.pandora.service.utils.EncryptionUtil;
import com.flipkart.fintech.pandora.service.utils.PandoraContext;
import com.flipkart.kloud.config.DynamicBucket;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.junit.runners.BlockJUnit4ClassRunner;

import javax.ws.rs.BadRequestException;
import javax.ws.rs.core.MultivaluedHashMap;
import javax.ws.rs.core.MultivaluedMap;
import java.math.BigInteger;
import java.util.HashMap;

import static com.flipkart.fintech.pandora.service.utils.Constants.AxisCbc.VALIDATION_TOKEN;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 * @since 06/05/19.
 */
@RunWith(value = BlockJUnit4ClassRunner.class)
public class AxisUserOnboardingAdaterTest {
    private EncryptionUtil aesEncryptionUtil = mock(EncryptionUtil.class);
    private AxisCbcConfiguration axisCbcConfiguration = new AxisCbcConfiguration();
    AxisAdapterUtil axisAdapterUtil = new AxisAdapterUtil(axisCbcConfiguration);
    private DynamicBucket dynamicBucket = mock(DynamicBucket.class);

    @BeforeEach
    public void setUp() throws Exception {
        MultivaluedMap<String, String> headers = PandoraContext.getHeaders();
        if (null != headers) {
            headers.add("X-Perf-Test", "True");
        }
        else {
            headers = new MultivaluedHashMap<>();
            headers.add("X-Perf-Test", "True");
        }
        PandoraContext.setHeaders(headers);
        axisCbcConfiguration.setChannelId("FK");
        axisCbcConfiguration.setTestConfigsMap(new CbcAxisTestConfig());
        AxisCbcApiConfigurations checkCardEligibility = new AxisCbcApiConfigurations();
        checkCardEligibility.setApiPath("abc");
        checkCardEligibility.setServiceRequestId("123");
        checkCardEligibility.setServiceRequestVersion("1");
        axisCbcConfiguration.getAxisCbcApiConfigurationsMap()
                .put(AxisApiConfigurationKeys.CHECK_COHORT_ELIGIBILTIY.getKey(), checkCardEligibility);
        axisCbcConfiguration.getAxisCbcApiConfigurationsMap()
                .put(AxisApiConfigurationKeys.DEMO_CARD_CONSENT.getKey(), checkCardEligibility);
        axisCbcConfiguration.getAxisCbcApiConfigurationsMap()
                .put(AxisApiConfigurationKeys.APPLY_CARD_CONSENT.getKey(), checkCardEligibility);
        axisCbcConfiguration.getAxisCbcApiConfigurationsMap()
                .put(AxisApiConfigurationKeys.FETCH_CUSTOMER_DEMOGRAPHICS.getKey(), checkCardEligibility);
        axisCbcConfiguration.getAxisCbcApiConfigurationsMap()
                .put(AxisApiConfigurationKeys.PROCESS_NEW_CARD_CASE.getKey(), checkCardEligibility);
        axisCbcConfiguration.getAxisCbcApiConfigurationsMap()
                .put(AxisApiConfigurationKeys.CHECK_CASE_STATUS.getKey(), checkCardEligibility);
        axisCbcConfiguration.getAxisCbcApiConfigurationsMap()
                .put(AxisApiConfigurationKeys.CHECK_CASE_STATUS_MOBILE.getKey(), checkCardEligibility);
        axisCbcConfiguration.setEncryptionKey("53EACE72CD83D6B60754C2F3959168EA");

        axisCbcConfiguration.setEncryptAxisLogs(false);
    }

    @Test
    public void applyNowRequestToCheckCardEligibiltyRequestHappyCaseTest() throws Exception {
        ObjectMapper objectMapper = mock(ObjectMapper.class);
        AxisUserOnboardingAdapter
                AxisUserOnboardingAdapter = new AxisUserOnboardingAdapter(objectMapper, aesEncryptionUtil, axisCbcConfiguration, axisAdapterUtil, dynamicBucket);
        ApplyNowRequest applyNowRequest = new ApplyNowRequest();
        LeadInformation leadInformation = new LeadInformation();
        leadInformation.setEmailId("<EMAIL>");
        leadInformation.setMobileNo("9886677565");
        applyNowRequest.setLeadInformation(leadInformation);
        FingerprintInfo fingerprintInfo = new FingerprintInfo();
        fingerprintInfo.setDeviceId("238289asd234");
        CheckCohortEligibilityRequestBody request1 = new CheckCohortEligibilityRequestBody();
        request1.setDeviceId("238289asd234");
        request1.setMobileNumber("9886677565");
        applyNowRequest.setFingerprintInfo(fingerprintInfo);
        when(aesEncryptionUtil.encryptWithAesCS5(objectMapper.writeValueAsString(request1),
                axisCbcConfiguration.getEncryptionKey(), Constants.AxisCbc.AXIS_CBC_IV)).thenReturn("ABC123");
        CheckCohortEligibilityRequest request = AxisUserOnboardingAdapter.applyNowRequestToCheckCohortEligibilityRequest(applyNowRequest);
        assertThat(request.getCheckCohortEligibilityRequestBodyEncrypted()).isEqualToIgnoringCase("ABC123");

    }

    @Test
    public void applyNowRequestToCheckCardEligibiltyRequestErrorTest() throws Exception {
        ObjectMapper objectMapper = mock(ObjectMapper.class);
        AxisUserOnboardingAdapter AxisUserOnboardingAdapter = new AxisUserOnboardingAdapter(objectMapper, aesEncryptionUtil, axisCbcConfiguration, axisAdapterUtil, dynamicBucket);
        ApplyNowRequest applyNowRequest = new ApplyNowRequest();
        LeadInformation leadInformation = new LeadInformation();
        leadInformation.setEmailId("<EMAIL>");
        leadInformation.setMobileNo("9886677565");
        applyNowRequest.setLeadInformation(leadInformation);
        FingerprintInfo fingerprintInfo = new FingerprintInfo();
        fingerprintInfo.setDeviceId("238289asd234");
        CheckCohortEligibilityRequestBody request1 = new CheckCohortEligibilityRequestBody();
        request1.setDeviceId("238289asd234");
        request1.setMobileNumber("9886677565");
        applyNowRequest.setFingerprintInfo(fingerprintInfo);
        when(aesEncryptionUtil.encryptWithAesCS5(objectMapper.writeValueAsString(request1),
                axisCbcConfiguration.getEncryptionKey(), Constants.AxisCbc.AXIS_CBC_IV)).thenThrow(
                new RuntimeException("bad Exception"));
        assertThatThrownBy(() -> AxisUserOnboardingAdapter.applyNowRequestToCheckCohortEligibilityRequest(applyNowRequest)).isInstanceOf(
                BadRequestException.class)
                .hasMessageContaining("Failed to Serialize the CheckEligibilityRequest bad Exception");

    }

    @Test
    public void checkCardEligibiltyResponseToApplyNowResponseHappyCaseTest() throws Exception {
        ObjectMapper objectMapper = mock(ObjectMapper.class);
        AxisUserOnboardingAdapter AxisUserOnboardingAdapter = new AxisUserOnboardingAdapter(objectMapper, aesEncryptionUtil, axisCbcConfiguration, axisAdapterUtil, dynamicBucket);
        CheckCohortEligibilityResponse checkCohortEligibilityResponse = new CheckCohortEligibilityResponse();
        CheckCohortEligibilityResponseBody checkCohortEligibilityResponseBody = new CheckCohortEligibilityResponseBody();
        checkCohortEligibilityResponseBody.setApplicationReferenceId("12345");
        checkCohortEligibilityResponse.setCheckCohortEligibilityResponseBodyEncrypted("12345");
        when(aesEncryptionUtil.decryptWithAesCS5("12345", axisCbcConfiguration.getEncryptionKey())).thenReturn(
                "ABC123");
        when(objectMapper.readValue("ABC123", CheckCohortEligibilityResponseBody.class)).thenReturn(
                checkCohortEligibilityResponseBody);
        ApplyNowResponse applyNowResponse =
                AxisUserOnboardingAdapter.checkCohortEligibilityResponseToApplyNowResponse(checkCohortEligibilityResponse);
        assertThat(applyNowResponse.getLenderInfoSection().getLenderLeadReferenceNo()).isEqualToIgnoringCase("12345");
    }

    @Test
    public void checkCardEligibiltyResponseToApplyNowResponseTestErrorCase() throws Exception {
        ObjectMapper objectMapper = mock(ObjectMapper.class);
        AxisUserOnboardingAdapter AxisUserOnboardingAdapter = new AxisUserOnboardingAdapter(objectMapper, aesEncryptionUtil, axisCbcConfiguration, axisAdapterUtil, dynamicBucket);
        CheckCohortEligibilityResponse checkCohortEligibilityResponse = new CheckCohortEligibilityResponse();
        CheckCohortEligibilityResponseBody checkCohortEligibilityResponseBody = new CheckCohortEligibilityResponseBody();
        checkCohortEligibilityResponseBody.setApplicationReferenceId("12345");
        checkCohortEligibilityResponse.setCheckCohortEligibilityResponseBodyEncrypted("12345");
        doThrow(new RuntimeException("bad Exception")).when(aesEncryptionUtil)
                .decryptWithAesCS5("12345", axisCbcConfiguration.getEncryptionKey());
        when(objectMapper.readValue("ABC123", CheckCohortEligibilityResponseBody.class)).thenReturn(
                checkCohortEligibilityResponseBody);
        assertThatThrownBy(() -> AxisUserOnboardingAdapter.checkCohortEligibilityResponseToApplyNowResponse(
                checkCohortEligibilityResponse)).isInstanceOf(BadRequestException.class)
                .hasMessageContaining("Failed to Serialize the ApplyNowResponse bad Exception");
    }

    @Test
    public void generateOtpRequestToDemoConsentOTPGenerationRequestHappyCaseTest() throws Exception {
        ObjectMapper objectMapper = mock(ObjectMapper.class);
        AxisUserOnboardingAdapter AxisUserOnboardingAdapter = new AxisUserOnboardingAdapter(objectMapper, aesEncryptionUtil, axisCbcConfiguration, axisAdapterUtil, dynamicBucket);
        GenerateOtpRequest generateOtpRequest = new GenerateOtpRequest();
        LeadInformation leadInformation = new LeadInformation();
        leadInformation.setMobileNo("9886677565");
        generateOtpRequest.setLeadInformation(leadInformation);
        FingerprintInfo fingerprintInfo = new FingerprintInfo();
        fingerprintInfo.setDeviceId("238289asd234");
        generateOtpRequest.setFingerprintInfo(fingerprintInfo);
        generateOtpRequest.setOtpDetails(new OtpDetails());
        generateOtpRequest.getOtpDetails().setOtpReferenceId("*********");
        LenderInfoSection lenderInfoSection = new LenderInfoSection();
        lenderInfoSection.setLenderLeadReferenceNo("****************");
        HashMap<String, Object> lenderReferenceInfos = new HashMap<String, Object>() {{
            put("validationToken","12345");
        }};
        lenderInfoSection.setLenderReferenceInfos(lenderReferenceInfos);
        generateOtpRequest.setLenderInfoSection(lenderInfoSection);
        OTPGenerationRequest otpGenerationRequest = new OTPGenerationRequest();
        otpGenerationRequest.setApplicationReferenceId("****************");
        otpGenerationRequest.setDeviceId("238289asd234");
        otpGenerationRequest.setMobileNumber("9886677565");
        otpGenerationRequest.setOtpReferenceId("*********");
        when(aesEncryptionUtil.encryptWithAesCS5(objectMapper.writeValueAsString(otpGenerationRequest),
                axisCbcConfiguration.getEncryptionKey(), Constants.AxisCbc.AXIS_CBC_IV)).thenReturn("ABC123");
        DemoConsentOTPGenerationRequest request =
                AxisUserOnboardingAdapter.generateOtpRequestToDemoConsentOTPGenerationRequest(generateOtpRequest);
        assertThat(request.getDemoConsentOTPGenerationRequestBodyEncrypted()).isEqualToIgnoringCase("ABC123");
    }

    @Test
    public void generateOtpRequestToDemoConsentOTPGenerationRequestErrorCaseTest() throws Exception {
        ObjectMapper objectMapper = mock(ObjectMapper.class);
        AxisUserOnboardingAdapter AxisUserOnboardingAdapter = new AxisUserOnboardingAdapter(objectMapper, aesEncryptionUtil, axisCbcConfiguration, axisAdapterUtil, dynamicBucket);
        GenerateOtpRequest generateOtpRequest = new GenerateOtpRequest();
        LeadInformation leadInformation = new LeadInformation();
        leadInformation.setMobileNo("9886677565");
        generateOtpRequest.setLeadInformation(leadInformation);
        FingerprintInfo fingerprintInfo = new FingerprintInfo();
        fingerprintInfo.setDeviceId("238289asd234");
        generateOtpRequest.setFingerprintInfo(fingerprintInfo);
        generateOtpRequest.setOtpDetails(new OtpDetails());
        generateOtpRequest.getOtpDetails().setOtpReferenceId("*********");
        LenderInfoSection lenderInfoSection = new LenderInfoSection();
        lenderInfoSection.setLenderLeadReferenceNo("****************");
        HashMap<String, Object> lenderReferenceInfos = new HashMap<String, Object>() {{
            put("validationToken","12345");
        }};
        lenderInfoSection.setLenderReferenceInfos(lenderReferenceInfos);
        generateOtpRequest.setLenderInfoSection(lenderInfoSection);
        OTPGenerationRequest otpGenerationRequest = new OTPGenerationRequest();
        otpGenerationRequest.setApplicationReferenceId("****************");
        otpGenerationRequest.setDeviceId("238289asd234");
        otpGenerationRequest.setMobileNumber("9886677565");
        otpGenerationRequest.setOtpReferenceId("*********");
        when(aesEncryptionUtil.encryptWithAesCS5(objectMapper.writeValueAsString(otpGenerationRequest),
                axisCbcConfiguration.getEncryptionKey(), Constants.AxisCbc.AXIS_CBC_IV)).thenThrow(
                new RuntimeException("bad Exception"));
        assertThatThrownBy(
                () -> AxisUserOnboardingAdapter.generateOtpRequestToDemoConsentOTPGenerationRequest(generateOtpRequest)).isInstanceOf(
                BadRequestException.class)
                .hasMessageContaining("Failed to Serialize the DemoConsentOTPGenerationRequest bad Exception");
    }

    @Test
    public void demoConsentOTPGenerationResponseToOtpGenerationResponseHappyCaseTest() throws Exception {
        ObjectMapper objectMapper = mock(ObjectMapper.class);
        AxisUserOnboardingAdapter AxisUserOnboardingAdapter = new AxisUserOnboardingAdapter(objectMapper, aesEncryptionUtil, axisCbcConfiguration, axisAdapterUtil, dynamicBucket);
        DemoConsentOTPGenerationResponse demoConsentOTPGenerationResponse = new DemoConsentOTPGenerationResponse();
        demoConsentOTPGenerationResponse.setDemoConsentOTPGenerationResponseBodyEncrypted("12345");
        OtpGenerationResponse otpGenerationResponse = new OtpGenerationResponse();
        otpGenerationResponse.setIsOtpGenerated(true);
        demoConsentOTPGenerationResponse.setDemoConsentOTPGenerationResponseBodyEncrypted("12345");
        when(aesEncryptionUtil.decryptWithAesCS5("12345", axisCbcConfiguration.getEncryptionKey())).thenReturn(
                "ABC123");
        when(objectMapper.readValue("ABC123", OtpGenerationResponse.class)).thenReturn(otpGenerationResponse);
        GenerateOtpResponse generateOtpResponse =
                AxisUserOnboardingAdapter.demoConsentOTPGenerationResponseToOtpGenerationResponse(demoConsentOTPGenerationResponse);
        assertThat(generateOtpResponse.getOtpDetails().getIsOtpGenerated()).isEqualTo(true);
    }

    @Test
    public void demoConsentOTPGenerationResponseToOtpGenerationResponseErrorCaseTest() throws Exception {
        ObjectMapper objectMapper = mock(ObjectMapper.class);
        AxisUserOnboardingAdapter AxisUserOnboardingAdapter = new AxisUserOnboardingAdapter(objectMapper, aesEncryptionUtil, axisCbcConfiguration, axisAdapterUtil, dynamicBucket);
        DemoConsentOTPGenerationResponse demoConsentOTPGenerationResponse = new DemoConsentOTPGenerationResponse();
        demoConsentOTPGenerationResponse.setDemoConsentOTPGenerationResponseBodyEncrypted("12345");
        OtpGenerationResponse otpGenerationResponse = new OtpGenerationResponse();
        otpGenerationResponse.setIsOtpGenerated(true);
        demoConsentOTPGenerationResponse.setDemoConsentOTPGenerationResponseBodyEncrypted("12345");
        when(objectMapper.readValue("ABC123", OtpGenerationResponse.class)).thenReturn(otpGenerationResponse);
        doThrow(new RuntimeException("bad Exception")).when(aesEncryptionUtil)
                .decryptWithAesCS5("12345", axisCbcConfiguration.getEncryptionKey());
        assertThatThrownBy(() -> AxisUserOnboardingAdapter.demoConsentOTPGenerationResponseToOtpGenerationResponse(
                demoConsentOTPGenerationResponse)).isInstanceOf(BadRequestException.class)
                .hasMessageContaining("Failed to Serialize the DemoConsentOTPGenerationResponse bad Exception");
    }

    @Test
    public void getPersonalInfoRequestToFetchCustDemographicsRequestHappyTest() throws Exception {
        ObjectMapper objectMapper = mock(ObjectMapper.class);
        AxisUserOnboardingAdapter AxisUserOnboardingAdapter = new AxisUserOnboardingAdapter(objectMapper, aesEncryptionUtil, axisCbcConfiguration, axisAdapterUtil, dynamicBucket);
        GetPersonalInfoRequest getPersonalInfoRequest = new GetPersonalInfoRequest();
        LeadInformation leadInformation = new LeadInformation();
        leadInformation.setMobileNo("9886677565");
        getPersonalInfoRequest.setLeadInformation(leadInformation);
        FingerprintInfo fingerprintInfo = new FingerprintInfo();
        fingerprintInfo.setDeviceId("238289asd234");
        getPersonalInfoRequest.setFingerprintInfo(fingerprintInfo);
        getPersonalInfoRequest.setOtpDetails(new OtpDetails());
        getPersonalInfoRequest.getOtpDetails().setOtpReferenceId("*********");
        getPersonalInfoRequest.getOtpDetails().setOtp("123456");
        LenderInfoSection lenderInfoSection = new LenderInfoSection();
        lenderInfoSection.setLenderLeadReferenceNo("****************");
        HashMap<String, Object> lenderReferenceInfos = new HashMap<String, Object>() {{
            put("validationToken","12345");
        }};
        lenderInfoSection.setLenderReferenceInfos(lenderReferenceInfos);
        getPersonalInfoRequest.setLenderInfoSection(lenderInfoSection);
        FetchCustomerDemographicDetailsRequestBody request = new FetchCustomerDemographicDetailsRequestBody();
        request.setApplicationReferenceId("****************");
        request.setDeviceId("238289asd234");
        request.setMobileNumber("9886677565");
        request.setOtpReferenceId("*********");
        request.setOtp("123456");
        when(aesEncryptionUtil.encryptWithAesCS5(objectMapper.writeValueAsString(request),
                axisCbcConfiguration.getEncryptionKey(), Constants.AxisCbc.AXIS_CBC_IV)).thenReturn("ABC123");
        FetchCustDemographicsRequest actual =
                AxisUserOnboardingAdapter.getPersonalInfoRequestToFetchCustDemographicsRequest(getPersonalInfoRequest);
        assertThat(actual.getFetchCustDemographicsRequestBodyEncrypted()).isEqualToIgnoringCase("ABC123");

    }

    @Test
    public void getPersonalInfoRequestToFetchCustDemographicsRequestErrorTest() throws Exception {
        ObjectMapper objectMapper = mock(ObjectMapper.class);
        AxisUserOnboardingAdapter AxisUserOnboardingAdapter = new AxisUserOnboardingAdapter(objectMapper, aesEncryptionUtil, axisCbcConfiguration, axisAdapterUtil, dynamicBucket);
        GetPersonalInfoRequest getPersonalInfoRequest = new GetPersonalInfoRequest();
        LeadInformation leadInformation = new LeadInformation();
        leadInformation.setMobileNo("9886677565");
        getPersonalInfoRequest.setLeadInformation(leadInformation);
        FingerprintInfo fingerprintInfo = new FingerprintInfo();
        fingerprintInfo.setDeviceId("238289asd234");
        getPersonalInfoRequest.setFingerprintInfo(fingerprintInfo);
        getPersonalInfoRequest.setOtpDetails(new OtpDetails());
        getPersonalInfoRequest.getOtpDetails().setOtpReferenceId("*********");
        getPersonalInfoRequest.getOtpDetails().setOtp("123456");
        LenderInfoSection lenderInfoSection = new LenderInfoSection();
        lenderInfoSection.setLenderLeadReferenceNo("****************");
        HashMap<String, Object> lenderReferenceInfos = new HashMap<String, Object>() {{
            put("validationToken","12345");
        }};
        lenderInfoSection.setLenderReferenceInfos(lenderReferenceInfos);
        getPersonalInfoRequest.setLenderInfoSection(lenderInfoSection);
        FetchCustomerDemographicDetailsRequestBody request = new FetchCustomerDemographicDetailsRequestBody();
        request.setApplicationReferenceId("****************");
        request.setDeviceId("238289asd234");
        request.setMobileNumber("9886677565");
        request.setOtpReferenceId("*********");
        request.setOtp("123456");
        when(aesEncryptionUtil.encryptWithAesCS5(objectMapper.writeValueAsString(request),
                axisCbcConfiguration.getEncryptionKey(), Constants.AxisCbc.AXIS_CBC_IV)).thenThrow(
                new RuntimeException("bad Exception"));
        assertThatThrownBy(() -> AxisUserOnboardingAdapter.getPersonalInfoRequestToFetchCustDemographicsRequest(
                getPersonalInfoRequest)).isInstanceOf(BadRequestException.class)
                .hasMessageContaining("Failed to Serialize the GetPersonalInfoRequest bad Exception");

    }

    @Test
    public void fetchCustDemographicsResponseToGetPersonalInfoResponseHappyTest() throws Exception {
        ObjectMapper objectMapper = mock(ObjectMapper.class);
        AxisUserOnboardingAdapter AxisUserOnboardingAdapter = new AxisUserOnboardingAdapter(objectMapper, aesEncryptionUtil, axisCbcConfiguration, axisAdapterUtil, dynamicBucket);
        FetchCustDemographicsResponse fetchCustDemographicsResponse = new FetchCustDemographicsResponse();
        fetchCustDemographicsResponse.setFetchCustomerDemographicsResponseBodyEncrypted("12345");
        FetchCustomerDemographicDetailsResponseBody response = new FetchCustomerDemographicDetailsResponseBody();
        response.setValidationToken("12345");
        response.setCustDemographics(new CustDemographics());
        when(aesEncryptionUtil.decryptWithAesCS5("12345", axisCbcConfiguration.getEncryptionKey())).thenReturn(
                "ABC123");
        when(objectMapper.readValue("ABC123", FetchCustomerDemographicDetailsResponseBody.class)).thenReturn(response);
        GetPersonalInfoResponse actualResponse =
                AxisUserOnboardingAdapter.fetchCustDemographicsResponseToGetPersonalInfoResponse(fetchCustDemographicsResponse,
                        "1234578", "ABC123");
        assertThat(actualResponse.getLenderInfoSection().getLenderReferenceInfos().get("validationToken")).isEqualTo(
                "12345");
        assertThat(actualResponse.getLenderInfoSection().getLenderLeadReferenceNo()).isEqualTo("1234578");
    }

    @Test
    public void fetchCustDemographicsResponseToGetPersonalInfoResponseErrorTest() throws Exception {
        ObjectMapper objectMapper = mock(ObjectMapper.class);
        AxisUserOnboardingAdapter AxisUserOnboardingAdapter = new AxisUserOnboardingAdapter(objectMapper, aesEncryptionUtil, axisCbcConfiguration, axisAdapterUtil, dynamicBucket);
        FetchCustDemographicsResponse fetchCustDemographicsResponse = new FetchCustDemographicsResponse();
        fetchCustDemographicsResponse.setFetchCustomerDemographicsResponseBodyEncrypted("12345");
        FetchCustomerDemographicDetailsResponseBody response = new FetchCustomerDemographicDetailsResponseBody();
        response.setValidationToken("12345");
        response.setCustDemographics(new CustDemographics());
        doThrow(new RuntimeException("bad Exception")).when(aesEncryptionUtil)
                .decryptWithAesCS5("12345", axisCbcConfiguration.getEncryptionKey());
        when(objectMapper.readValue("ABC123", FetchCustomerDemographicDetailsResponseBody.class)).thenReturn(response);
        assertThatThrownBy(
                () -> AxisUserOnboardingAdapter.fetchCustDemographicsResponseToGetPersonalInfoResponse(fetchCustDemographicsResponse,
                        "12345", "ABC123")).isInstanceOf(BadRequestException.class)
                .hasMessageContaining("Failed to Serialize the FetchCustDemographicsResponse bad Exception");
    }

    @Test
    public void generateOtpRequestToApplyCardConsentOTPGenRequestHappyCaseTest() throws Exception {
        ObjectMapper objectMapper = mock(ObjectMapper.class);
        AxisUserOnboardingAdapter AxisUserOnboardingAdapter = new AxisUserOnboardingAdapter(objectMapper, aesEncryptionUtil, axisCbcConfiguration, axisAdapterUtil, dynamicBucket);
        GenerateOtpRequest generateOtpRequest = new GenerateOtpRequest();
        LeadInformation leadInformation = new LeadInformation();
        leadInformation.setMobileNo("9886677565");
        generateOtpRequest.setLeadInformation(leadInformation);
        FingerprintInfo fingerprintInfo = new FingerprintInfo();
        fingerprintInfo.setDeviceId("238289asd234");
        generateOtpRequest.setFingerprintInfo(fingerprintInfo);
        generateOtpRequest.setOtpDetails(new OtpDetails());
        generateOtpRequest.getOtpDetails().setOtpReferenceId("*********");
        LenderInfoSection lenderInfoSection = new LenderInfoSection();
        lenderInfoSection.setLenderLeadReferenceNo("****************");
        HashMap<String, Object> lenderReferenceInfos = new HashMap<String, Object>() {{
            put("validationToken","12345");
        }};
        lenderInfoSection.setLenderReferenceInfos(lenderReferenceInfos);
        generateOtpRequest.setLenderInfoSection(lenderInfoSection);
        OTPGenerationRequest otpGenerationRequest = new OTPGenerationRequest();
        otpGenerationRequest.setApplicationReferenceId("****************");
        otpGenerationRequest.setDeviceId("238289asd234");
        otpGenerationRequest.setMobileNumber("9886677565");
        otpGenerationRequest.setOtpReferenceId("*********");
        when(aesEncryptionUtil.encryptWithAesCS5(objectMapper.writeValueAsString(otpGenerationRequest),
                axisCbcConfiguration.getEncryptionKey(), Constants.AxisCbc.AXIS_CBC_IV)).thenReturn("ABC123");
        ApplyCardConsentOTPGenRequest request =
                AxisUserOnboardingAdapter.generateOtpRequestToApplyCardConsentOTPGenRequest(generateOtpRequest, AxisApiConfigurationKeys.APPLY_CARD_CONSENT.getKey());
        assertThat(request.getApplyCardConsentOTPGenRequestBodyEncrypted()).isEqualToIgnoringCase("ABC123");
    }

    @Test
    public void generateOtpRequestToApplyCardConsentOTPGenRequestErrorCaseTest() throws Exception {
        ObjectMapper objectMapper = mock(ObjectMapper.class);
        AxisUserOnboardingAdapter AxisUserOnboardingAdapter = new AxisUserOnboardingAdapter(objectMapper, aesEncryptionUtil, axisCbcConfiguration, axisAdapterUtil, dynamicBucket);
        GenerateOtpRequest generateOtpRequest = new GenerateOtpRequest();
        LeadInformation leadInformation = new LeadInformation();
        leadInformation.setMobileNo("9886677565");
        generateOtpRequest.setLeadInformation(leadInformation);
        FingerprintInfo fingerprintInfo = new FingerprintInfo();
        fingerprintInfo.setDeviceId("238289asd234");
        generateOtpRequest.setFingerprintInfo(fingerprintInfo);
        generateOtpRequest.setOtpDetails(new OtpDetails());
        generateOtpRequest.getOtpDetails().setOtpReferenceId("*********");
        LenderInfoSection lenderInfoSection = new LenderInfoSection();
        lenderInfoSection.setLenderLeadReferenceNo("****************");
        HashMap<String, Object> lenderReferenceInfos = new HashMap<String, Object>() {{
            put("validationToken","12345");
        }};
        lenderInfoSection.setLenderReferenceInfos(lenderReferenceInfos);
        generateOtpRequest.setLenderInfoSection(lenderInfoSection);
        OTPGenerationRequest otpGenerationRequest = new OTPGenerationRequest();
        otpGenerationRequest.setApplicationReferenceId("****************");
        otpGenerationRequest.setDeviceId("238289asd234");
        otpGenerationRequest.setMobileNumber("9886677565");
        otpGenerationRequest.setOtpReferenceId("*********");
        when(aesEncryptionUtil.encryptWithAesCS5(objectMapper.writeValueAsString(otpGenerationRequest),
                axisCbcConfiguration.getEncryptionKey(), Constants.AxisCbc.AXIS_CBC_IV)).thenThrow(
                new RuntimeException("bad Exception"));
        assertThatThrownBy(
                () -> AxisUserOnboardingAdapter.generateOtpRequestToApplyCardConsentOTPGenRequest(generateOtpRequest, AxisApiConfigurationKeys.APPLY_CARD_CONSENT.getKey())).isInstanceOf(
                BadRequestException.class)
                .hasMessageContaining("Failed to Serialize the ApplyCardConsentOTPGenRequest bad Exception");
    }

    @Test
    public void applyCardConsentOTPGenResponseToOtpGenerationResponseHappyCaseTest() throws Exception {
        ObjectMapper objectMapper = mock(ObjectMapper.class);
        AxisUserOnboardingAdapter AxisUserOnboardingAdapter = new AxisUserOnboardingAdapter(objectMapper, aesEncryptionUtil, axisCbcConfiguration, axisAdapterUtil, dynamicBucket);
        ApplyCardConsentOTPGenResponse applyCardConsentOTPGenResponse = new ApplyCardConsentOTPGenResponse();
        applyCardConsentOTPGenResponse.setApplyCardConsentOTPGenResponseBodyEncrypted("12345");
        OtpGenerationResponse otpGenerationResponse = new OtpGenerationResponse();
        otpGenerationResponse.setIsOtpGenerated(true);
        applyCardConsentOTPGenResponse.setApplyCardConsentOTPGenResponseBodyEncrypted("12345");
        when(aesEncryptionUtil.decryptWithAesCS5("12345", axisCbcConfiguration.getEncryptionKey())).thenReturn(
                "ABC123");
        when(objectMapper.readValue("ABC123", OtpGenerationResponse.class)).thenReturn(otpGenerationResponse);
        GenerateOtpResponse generateOtpResponse =
                AxisUserOnboardingAdapter.applyCardConsentOTPGenResponseToOtpGenerationResponse(applyCardConsentOTPGenResponse,"123454");
        assertThat(generateOtpResponse.getOtpDetails().getIsOtpGenerated()).isEqualTo(true);
    }

    @Test
    public void applyCardConsentOTPGenResponseToOtpGenerationResponseErrorCaseTest() throws Exception {
        ObjectMapper objectMapper = mock(ObjectMapper.class);
        AxisUserOnboardingAdapter AxisUserOnboardingAdapter = new AxisUserOnboardingAdapter(objectMapper, aesEncryptionUtil, axisCbcConfiguration, axisAdapterUtil, dynamicBucket);
        ApplyCardConsentOTPGenResponse applyCardConsentOTPGenResponse = new ApplyCardConsentOTPGenResponse();
        applyCardConsentOTPGenResponse.setApplyCardConsentOTPGenResponseBodyEncrypted("12345");
        OtpGenerationResponse otpGenerationResponse = new OtpGenerationResponse();
        otpGenerationResponse.setIsOtpGenerated(true);
        when(objectMapper.readValue("ABC123", OtpGenerationResponse.class)).thenReturn(otpGenerationResponse);
        doThrow(new RuntimeException("bad Exception")).when(aesEncryptionUtil)
                .decryptWithAesCS5("12345", axisCbcConfiguration.getEncryptionKey());
        assertThatThrownBy(
                () -> AxisUserOnboardingAdapter.applyCardConsentOTPGenResponseToOtpGenerationResponse(applyCardConsentOTPGenResponse,"213435"))
                .isInstanceOf(BadRequestException.class)
                .hasMessageContaining("Failed to Serialize the ApplyCardConsentOTPGenResponse bad Exception");
    }

    @Test
    public void creditAssessmentInfoRequestToProcessNewCardCaseRequestHappyTest() throws Exception {
        ObjectMapper objectMapper = new ObjectMapper();
        AxisUserOnboardingAdapter AxisUserOnboardingAdapter = new AxisUserOnboardingAdapter(objectMapper, aesEncryptionUtil, axisCbcConfiguration, axisAdapterUtil, dynamicBucket);
        CreditAssessmentInfoRequest creditAssessmentInfoRequest = new CreditAssessmentInfoRequest();
        FingerprintInfo fingerprintInfo = new FingerprintInfo();
        fingerprintInfo.setDeviceId("238289asd234");
        creditAssessmentInfoRequest.setFingerprintInfo(fingerprintInfo);
        creditAssessmentInfoRequest.setOtpDetails(new OtpDetails());
        creditAssessmentInfoRequest.getOtpDetails().setOtpReferenceId("*********");
        LenderInfoSection lenderInfoSection = new LenderInfoSection();
        lenderInfoSection.setLenderLeadReferenceNo("****************");
        HashMap<String, Object> lenderReferenceInfos = new HashMap<>();
        lenderReferenceInfos.put(VALIDATION_TOKEN, "12345");
        lenderInfoSection.setLenderReferenceInfos(lenderReferenceInfos);
        creditAssessmentInfoRequest.setLenderInfoSection(lenderInfoSection);
        creditAssessmentInfoRequest.setAutoDebitDetails(new AutoDebitDetails());
        creditAssessmentInfoRequest.setCustomerConsents(new CustomerConsents());
        creditAssessmentInfoRequest.setEducationDetails(new EducationDetails());
        EmploymentDetails employmentDetails = new EmploymentDetails();
        employmentDetails.setEmploymentStatus(EmploymentStatus.SELF_EMPLOYED);
        creditAssessmentInfoRequest.setEmploymentDetails(employmentDetails);
        PersonalInfoRequest personalInfoRequest = new PersonalInfoRequest();
        personalInfoRequest.setGender(Gender.M);
        personalInfoRequest.setMobileNumber("**********");
        creditAssessmentInfoRequest.setPersonalInfoRequest(personalInfoRequest);
        IncomeDetails incomeDetails = new BankStatement();
        incomeDetails.setIncome(BigInteger.TEN);
        creditAssessmentInfoRequest.setIncomeDetails(incomeDetails);
        creditAssessmentInfoRequest.setProductDetails(new ProductDetails());
        ProcessNewCardCaseRequest processNewCardCaseRequest = new ProcessNewCardCaseRequest();
        processNewCardCaseRequest.setProcessNewCardCaseRequestBodyEncrypted("ABC12345");
        ProcessNewCardCaseRequestBody processNewCardCaseRequestBody = new ProcessNewCardCaseRequestBody();
        processNewCardCaseRequestBody.setApplicationReferenceId("****************");
        processNewCardCaseRequestBody.setDeviceId("238289asd234");
        processNewCardCaseRequestBody.setOtpReferenceId("*********");
        CustomerCardDetails customerCardDetails = new CustomerCardDetails();
        customerCardDetails.setEmployementType("SELF-EMPLOYED");
        customerCardDetails.setSex("M");
        customerCardDetails.setCustomerConsents(new CustomerConsents());
        processNewCardCaseRequestBody.setCustomerCardDetails(customerCardDetails);
        processNewCardCaseRequestBody.setMobileNumber("91**********");
        when(aesEncryptionUtil.encryptWithAesCS5(objectMapper.writeValueAsString(processNewCardCaseRequestBody),
                axisCbcConfiguration.getEncryptionKey(), Constants.AxisCbc.AXIS_CBC_IV)).thenReturn("ABC123");
        ProcessNewCardCaseRequest request =
                AxisUserOnboardingAdapter.creditAssessmentInfoRequestToProcessNewCardCaseRequest(creditAssessmentInfoRequest);
        assertThat(request.getProcessNewCardCaseRequestBodyEncrypted()).isEqualToIgnoringCase("ABC123");

    }

    @Test
    public void creditAssessmentInfoRequestToProcessNewCardCaseRequestErrorTest() throws Exception {
        ObjectMapper objectMapper = new ObjectMapper();
        AxisUserOnboardingAdapter AxisUserOnboardingAdapter = new AxisUserOnboardingAdapter(objectMapper, aesEncryptionUtil, axisCbcConfiguration, axisAdapterUtil, dynamicBucket);
        CreditAssessmentInfoRequest creditAssessmentInfoRequest = new CreditAssessmentInfoRequest();
        FingerprintInfo fingerprintInfo = new FingerprintInfo();
        fingerprintInfo.setDeviceId("238289asd234");
        creditAssessmentInfoRequest.setFingerprintInfo(fingerprintInfo);
        creditAssessmentInfoRequest.setOtpDetails(new OtpDetails());
        creditAssessmentInfoRequest.getOtpDetails().setOtpReferenceId("*********");
        LenderInfoSection lenderInfoSection = new LenderInfoSection();
        lenderInfoSection.setLenderLeadReferenceNo("****************");
        HashMap<String, Object> lenderReferenceInfos = new HashMap<String, Object>() {{
            put("validationToken","12345");
        }};
        lenderInfoSection.setLenderReferenceInfos(lenderReferenceInfos);
        creditAssessmentInfoRequest.setLenderInfoSection(lenderInfoSection);
        creditAssessmentInfoRequest.setAutoDebitDetails(new AutoDebitDetails());
        creditAssessmentInfoRequest.setCustomerConsents(new CustomerConsents());
        creditAssessmentInfoRequest.setEducationDetails(new EducationDetails());
        EmploymentDetails employmentDetails = new EmploymentDetails();
        employmentDetails.setEmploymentStatus(EmploymentStatus.SELF_EMPLOYED);
        creditAssessmentInfoRequest.setEmploymentDetails(employmentDetails);
        PersonalInfoRequest personalInfoRequest = new PersonalInfoRequest();
        personalInfoRequest.setGender(Gender.M);
        personalInfoRequest.setMobileNumber("**********");
        creditAssessmentInfoRequest.setPersonalInfoRequest(personalInfoRequest);
        creditAssessmentInfoRequest.setProductDetails(new ProductDetails());
        ProcessNewCardCaseRequest processNewCardCaseRequest = new ProcessNewCardCaseRequest();
        processNewCardCaseRequest.setProcessNewCardCaseRequestBodyEncrypted("ABC12345");
        ProcessNewCardCaseRequestBody processNewCardCaseRequestBody = new ProcessNewCardCaseRequestBody();
        processNewCardCaseRequestBody.setApplicationReferenceId("****************");
        processNewCardCaseRequestBody.setDeviceId("238289asd234");
        processNewCardCaseRequestBody.setOtpReferenceId("*********");
        processNewCardCaseRequestBody.setMobileNumber("91**********");
        CustomerCardDetails customerCardDetails = new CustomerCardDetails();
        customerCardDetails.setEmployementType("SELF-EMPLOYED");
        customerCardDetails.setSex("M");
        customerCardDetails.setCustomerConsents(new CustomerConsents());
        processNewCardCaseRequestBody.setCustomerCardDetails(customerCardDetails);
        when(aesEncryptionUtil.encryptWithAesCS5(objectMapper.writeValueAsString(processNewCardCaseRequestBody),
                axisCbcConfiguration.getEncryptionKey(), Constants.AxisCbc.AXIS_CBC_IV)).thenThrow(
                new RuntimeException("bad Exception"));
        ;
        assertThatThrownBy(() -> AxisUserOnboardingAdapter.creditAssessmentInfoRequestToProcessNewCardCaseRequest(
                creditAssessmentInfoRequest)).isInstanceOf(BadRequestException.class)
                .hasMessageContaining("Failed to Serialize the CreditAssessmentInfoRequest bad Exception");
    }

    @Test
    public void processNewCardResponseToCreditAssessmentInfoResponseHappyTest() throws Exception {
        ObjectMapper objectMapper = mock(ObjectMapper.class);
        AxisUserOnboardingAdapter AxisUserOnboardingAdapter = new AxisUserOnboardingAdapter(objectMapper, aesEncryptionUtil, axisCbcConfiguration, axisAdapterUtil, dynamicBucket);
        ProcessNewCardCaseResponse processNewCardCaseResponse = new ProcessNewCardCaseResponse();
        ProcessNewCardCaseResponseBody processNewCardCaseResponseBody = new ProcessNewCardCaseResponseBody();
        processNewCardCaseResponseBody.setCaseReferenceId("*********");
        processNewCardCaseResponseBody.setIsCaseCreated(true);
        processNewCardCaseResponse.setProcessNewCardCaseReponseBodyEncrypted("12345");
        when(aesEncryptionUtil.decryptWithAesCS5("12345", axisCbcConfiguration.getEncryptionKey())).thenReturn(
                "ABC123");
        when(objectMapper.readValue("ABC123", ProcessNewCardCaseResponseBody.class)).thenReturn(
                processNewCardCaseResponseBody);
        CreditAssessmentInfoResponse creditAssessmentInfoResponse =
                AxisUserOnboardingAdapter.processNewCardResponseToCreditAssessmentInfoResponse(processNewCardCaseResponse, "123455");
        assertThat(creditAssessmentInfoResponse.getLenderInfoSection().getLenderLeadReferenceNo()).isEqualTo("123455");
        assertThat(creditAssessmentInfoResponse.getLenderInfoSection().getLenderApplicationReferenceNo()).isEqualTo(
                "*********");
    }

    @Test
    public void processNewCardResponseToCreditAssessmentInfoResponseErrorTest() throws Exception {
        ObjectMapper objectMapper = mock(ObjectMapper.class);
        AxisUserOnboardingAdapter AxisUserOnboardingAdapter = new AxisUserOnboardingAdapter(objectMapper, aesEncryptionUtil, axisCbcConfiguration, axisAdapterUtil, dynamicBucket);
        ProcessNewCardCaseResponse processNewCardCaseResponse = new ProcessNewCardCaseResponse();
        ProcessNewCardCaseResponseBody processNewCardCaseResponseBody = new ProcessNewCardCaseResponseBody();
        processNewCardCaseResponseBody.setCaseReferenceId("*********");
        processNewCardCaseResponse.setProcessNewCardCaseReponseBodyEncrypted("12345");
        doThrow(new RuntimeException("bad Exception")).when(aesEncryptionUtil)
                .decryptWithAesCS5("12345", axisCbcConfiguration.getEncryptionKey());
        when(objectMapper.readValue("ABC123", ProcessNewCardCaseResponseBody.class)).thenReturn(
                processNewCardCaseResponseBody);
        assertThatThrownBy(
                () -> AxisUserOnboardingAdapter.processNewCardResponseToCreditAssessmentInfoResponse(processNewCardCaseResponse,
                        "123455")).isInstanceOf(BadRequestException.class)
                .hasMessageContaining("Failed to Serialize the ProcessNewCardCaseResponse bad Exception");

    }

    @Test
    public void userAcceptanceRequestToNonEncryptedCheckCaseStatusRequestTest() {
//        ObjectMapper objectMapper = mock(ObjectMapper.class);
//        AxisUserOnboardingAdapter AxisUserOnboardingAdapter = new AxisUserOnboardingAdapter(objectMapper, aesEncryptionUtil, axisCbcConfiguration, axisAdapterUtil);
//        UserAcceptanceRequest userAcceptanceRequest = new UserAcceptanceRequest();
//        LenderInfoSection lenderInfoSection = new LenderInfoSection();
//        lenderInfoSection.setLenderLeadReferenceNo("****************");
//        lenderInfoSection.setLenderApplicationReferenceNo("C123456");
//        userAcceptanceRequest.setLenderInfoSection(lenderInfoSection);
//
//
//
//        CheckCaseStatusRequest nonEncryptedCheckCaseStatusRequest =
//                AxisUserOnboardingAdapter.userAcceptanceRequestToCheckCaseStatusRequest(userAcceptanceRequest);
//        assertThat(nonEncryptedCheckCaseStatusRequest.getCaseReferenceId()).isEqualTo("C123456");


    }

    @Test
    public void checkCaseStatusResponseToUserAcceptanceResponseTest() throws Exception {
        ObjectMapper objectMapper = mock(ObjectMapper.class);
        AxisUserOnboardingAdapter AxisUserOnboardingAdapter = new AxisUserOnboardingAdapter(objectMapper, aesEncryptionUtil, axisCbcConfiguration, axisAdapterUtil, dynamicBucket);
        CheckCaseStatusResponse checkCaseStatusResponse = new CheckCaseStatusResponse();
        NonEncryptedCheckCaseStatusResponse nonEncryptedCheckCaseStatusResponse =
                new NonEncryptedCheckCaseStatusResponse();
        nonEncryptedCheckCaseStatusResponse.setApplicationSerNo("12345");
        nonEncryptedCheckCaseStatusResponse.setIsCaseProcessed(true);
        nonEncryptedCheckCaseStatusResponse.setIsCardApproved(true);
        checkCaseStatusResponse.setCheckCaseStatusResponseBodyEncrypted("ABC12345");
        when(aesEncryptionUtil.decryptWithAesCS5("ABC12345", axisCbcConfiguration.getEncryptionKey())).thenReturn(
                "ABC123");
        when(objectMapper.readValue("ABC123", NonEncryptedCheckCaseStatusResponse.class)).thenReturn(
                nonEncryptedCheckCaseStatusResponse);
        UserAcceptanceResponse userAcceptanceResponse =
                AxisUserOnboardingAdapter.checkCaseStatusResponseToUserAcceptanceResponse(checkCaseStatusResponse, false);
        assertThat(userAcceptanceResponse.getPaymentInstruments().get(0).getPaymentInstrumentId()).isEqualTo("12345");
    }

    @Test
    public void checkCaseStatusResponseToUserAcceptanceResponseErrorTest() throws Exception {
        ObjectMapper objectMapper = mock(ObjectMapper.class);
        AxisUserOnboardingAdapter AxisUserOnboardingAdapter = new AxisUserOnboardingAdapter(objectMapper, aesEncryptionUtil, axisCbcConfiguration, axisAdapterUtil, dynamicBucket);
        CheckCaseStatusResponse checkCaseStatusResponse = new CheckCaseStatusResponse();
        NonEncryptedCheckCaseStatusResponse nonEncryptedCheckCaseStatusResponse =
                new NonEncryptedCheckCaseStatusResponse();
        nonEncryptedCheckCaseStatusResponse.setApplicationSerNo("12345");
        nonEncryptedCheckCaseStatusResponse.setIsCaseProcessed(true);
        checkCaseStatusResponse.setCheckCaseStatusResponseBodyEncrypted("ABC12345");
        doThrow(new RuntimeException("bad Exception")).when(aesEncryptionUtil)
                .decryptWithAesCS5("ABC12345", axisCbcConfiguration.getEncryptionKey());
        when(objectMapper.readValue("ABC123", NonEncryptedCheckCaseStatusResponse.class)).thenReturn(
                nonEncryptedCheckCaseStatusResponse);
        assertThatThrownBy(() -> AxisUserOnboardingAdapter.checkCaseStatusResponseToUserAcceptanceResponse(
                checkCaseStatusResponse, false)).isInstanceOf(BadRequestException.class)
                .hasMessageContaining("Failed to Serialize the CheckCaseStatusResponse bad Exception");
    }

    @Test
    public void checkCaseStatusWhenCardIsNotCreatedTest() throws Exception {
        ObjectMapper objectMapper = mock(ObjectMapper.class);
        AxisUserOnboardingAdapter AxisUserOnboardingAdapter = new AxisUserOnboardingAdapter(objectMapper, aesEncryptionUtil, axisCbcConfiguration, axisAdapterUtil, dynamicBucket);
        CheckCaseStatusResponse checkCaseStatusResponse = new CheckCaseStatusResponse();
        NonEncryptedCheckCaseStatusResponse nonEncryptedCheckCaseStatusResponse =
                new NonEncryptedCheckCaseStatusResponse();
        nonEncryptedCheckCaseStatusResponse.setApplicationSerNo("12345");
        nonEncryptedCheckCaseStatusResponse.setIsCaseProcessed(false);
        checkCaseStatusResponse.setCheckCaseStatusResponseBodyEncrypted("ABC12345");
        when(aesEncryptionUtil.decryptWithAesCS5("ABC12345", axisCbcConfiguration.getEncryptionKey())).thenReturn(
                "ABC123");
        when(objectMapper.readValue("ABC123", NonEncryptedCheckCaseStatusResponse.class)).thenReturn(
                nonEncryptedCheckCaseStatusResponse);
        UserAcceptanceResponse userAcceptanceResponse =
                AxisUserOnboardingAdapter.checkCaseStatusResponseToUserAcceptanceResponse(checkCaseStatusResponse, false);
        assertThat(userAcceptanceResponse.getBaseResponse().getStatus()).isEqualTo(STATUS.CASE_UNDER_PROCESS);
    }

    @Test
    public void creditAssessmentInfoRequestToCheckCaseStatusMobileRequest() throws Exception {
        ObjectMapper objectMapper = new ObjectMapper();
        AxisUserOnboardingAdapter AxisUserOnboardingAdapter = new AxisUserOnboardingAdapter(objectMapper, aesEncryptionUtil, axisCbcConfiguration, axisAdapterUtil, dynamicBucket);
        CreditAssessmentInfoRequest creditAssessmentInfoRequest = new CreditAssessmentInfoRequest();
        FingerprintInfo fingerprintInfo = new FingerprintInfo();
        fingerprintInfo.setDeviceId("238289asd234");
        creditAssessmentInfoRequest.setFingerprintInfo(fingerprintInfo);
        LenderInfoSection lenderInfoSection = new LenderInfoSection();
        lenderInfoSection.setLenderLeadReferenceNo("****************");
        HashMap<String, Object> lenderReferenceInfos = new HashMap<>();
        lenderInfoSection.setLenderReferenceInfos(lenderReferenceInfos);
        creditAssessmentInfoRequest.setLenderInfoSection(lenderInfoSection);
        PersonalInfoRequest personalInfoRequest = new PersonalInfoRequest();
        personalInfoRequest.setMobileNumber("**********");
        creditAssessmentInfoRequest.setPersonalInfoRequest(personalInfoRequest);
        CheckCaseStatusRequest checkCaseStatusMobileRequest = new CheckCaseStatusRequest();
        checkCaseStatusMobileRequest.setCheckCaseStatusRequestBodyEncrypted("ABC12345");
        CheckCaseStatusMobileRequestBody checkCaseStatusMobileRequestBody = new CheckCaseStatusMobileRequestBody();
        checkCaseStatusMobileRequestBody.setApplicationReferenceId("****************");
        checkCaseStatusMobileRequestBody.setDeviceId("238289asd234");
        checkCaseStatusMobileRequestBody.setMobileNumber("91**********");
        when(aesEncryptionUtil.encryptWithAesCS5(objectMapper.writeValueAsString(checkCaseStatusMobileRequestBody),
                axisCbcConfiguration.getEncryptionKey(), Constants.AxisCbc.AXIS_CBC_IV)).thenReturn("ABC123");
        CheckCaseStatusRequest request =
                AxisUserOnboardingAdapter.creditAssessmentInfoRequestToCheckCaseStatusRequest(creditAssessmentInfoRequest);
        assertThat(request.getCheckCaseStatusRequestBodyEncrypted()).isEqualToIgnoringCase("ABC123");
    }

    @Test
    public void CheckCaseStatusMobileResponseToCreditAssessmentInfoResponseHappyTest() throws Exception {
        ObjectMapper objectMapper = mock(ObjectMapper.class);
        AxisUserOnboardingAdapter AxisUserOnboardingAdapter = new AxisUserOnboardingAdapter(objectMapper, aesEncryptionUtil, axisCbcConfiguration, axisAdapterUtil, dynamicBucket);
        CheckCaseStatusResponse checkCaseStatusMobileResponse = new CheckCaseStatusResponse();
        CheckCaseStatusMobileResponseBody checkCaseStatusMobileResponseBody = new CheckCaseStatusMobileResponseBody();
        checkCaseStatusMobileResponseBody.setCaseReferenceId("*********");
        checkCaseStatusMobileResponseBody.setCaseProcessed(true);
        checkCaseStatusMobileResponse.setCheckCaseStatusResponseBodyEncrypted("12345");
        when(aesEncryptionUtil.decryptWithAesCS5("12345", axisCbcConfiguration.getEncryptionKey())).thenReturn(
                "ABC123");
        when(objectMapper.readValue("ABC123", CheckCaseStatusMobileResponseBody.class)).thenReturn(
                checkCaseStatusMobileResponseBody);
        CreditAssessmentInfoResponse creditAssessmentInfoResponse =
                AxisUserOnboardingAdapter.checkCaseStatusMobileToCreditAssessmentInfoResponse(checkCaseStatusMobileResponse, "123455");
        assertThat(creditAssessmentInfoResponse.getLenderInfoSection().getLenderLeadReferenceNo()).isEqualTo("123455");
        assertThat(creditAssessmentInfoResponse.getLenderInfoSection().getLenderApplicationReferenceNo()).isEqualTo(
                "*********");
    }

}
