package com.flipkart.fintech.pandora.service.resources.web.v2;

import com.flipkart.fintech.pandora.api.model.CardPaymentInstrument;
import com.flipkart.fintech.pandora.api.model.billing.CreditUsageInformation;
import com.flipkart.fintech.pandora.api.model.billing.request.CreditUsageRequest;
import com.flipkart.fintech.pandora.service.client.indiabulls.IndiaBullsBillingClient;
import com.flipkart.fintech.pandora.service.client.indiabulls.IndiaBullsBillingMockClient;
import com.flipkart.fintech.pandora.service.client.indiabulls.IndiaBullsClientConfig;
import com.flipkart.fintech.pandora.service.core.billing.BillingManagerFactory;
import com.flipkart.fintech.pandora.service.core.billing.v2.IndiaBullsBillingManager;
import com.flipkart.fintech.pandora.service.hystrix.PandoraHystrixProperties;
import com.flipkart.fintech.pandora.service.utils.PandoraContext;
import io.dropwizard.testing.junit.ResourceTestRule;
import org.junit.jupiter.api.AfterEach;
import org.junit.Assert;
import org.junit.jupiter.api.BeforeEach;
import org.junit.ClassRule;
import org.mockito.Mockito;

import javax.ws.rs.client.Entity;
import javax.ws.rs.core.MultivaluedHashMap;
import javax.ws.rs.core.MultivaluedMap;

import java.math.BigDecimal;

/**
 * <AUTHOR> H Adavi
 */
public class IndiaBullsBillingResourceTest {
	
	private static final BillingManagerFactory billingManagerFactory = Mockito.mock(BillingManagerFactory.class);
	
	@ClassRule
	public static final ResourceTestRule resources = ResourceTestRule.builder()
			.addResource(new BillingResource(billingManagerFactory))
			.build();
	
	@BeforeEach
	public void setUp() throws Exception {
		
		IndiaBullsBillingManager indiaBullsBillingManager;
		IndiaBullsBillingClient indiaBullsBillingClient;
		IndiaBullsBillingMockClient indiaBullsBillingMockClient;
		PandoraHystrixProperties pandoraHystrixProperties;
		IndiaBullsClientConfig indiaBullsClientConfig;
		
		indiaBullsClientConfig = new IndiaBullsClientConfig();
		indiaBullsBillingClient = new IndiaBullsBillingClient(indiaBullsClientConfig);
		
		indiaBullsBillingMockClient = new IndiaBullsBillingMockClient();
		
		pandoraHystrixProperties = new PandoraHystrixProperties();
		
		indiaBullsBillingManager = new IndiaBullsBillingManager(indiaBullsBillingClient,indiaBullsBillingMockClient,pandoraHystrixProperties);
		Mockito.when(billingManagerFactory.getBillingManager("india_bulls","efa")).thenReturn(indiaBullsBillingManager);
		
	}
	
	@AfterEach
	public void tearDown() throws Exception {
	}
	

	public void getCreditUsageInformaion() {
		
		MultivaluedMap<String, String> headers = PandoraContext.getHeaders();
		if(null != headers){
			headers.add("X-Perf-Test","True");
		} else {
			headers = new MultivaluedHashMap<>();
			headers.add("X-Perf-Test","True");
		}
		PandoraContext.setHeaders(headers);
		
		CardPaymentInstrument cardPaymentInstrument = new CardPaymentInstrument();
		cardPaymentInstrument.setCardNumber("");
		
		CreditUsageRequest creditUsageRequest = new CreditUsageRequest();
		creditUsageRequest.setPaymentInstrument(cardPaymentInstrument);
		
		CreditUsageInformation creditUsageInformation = new CreditUsageInformation();
		creditUsageInformation.setTotalCreditLimit(new BigDecimal(60000));
		creditUsageInformation.setTotalCreditUsage(new BigDecimal(40000));
		
		CreditUsageInformation actual = resources.target("/2/billing/creditUsage/india_bulls/efa").request().header("X-Perf-Test",
				"True").post(Entity.json(creditUsageRequest)).readEntity(CreditUsageInformation.class);
		
		Assert.assertEquals(actual,creditUsageInformation);
	}
	

	public void getCreditUsageDetails() {
	}

	public void getBillInformation() {
	}
}