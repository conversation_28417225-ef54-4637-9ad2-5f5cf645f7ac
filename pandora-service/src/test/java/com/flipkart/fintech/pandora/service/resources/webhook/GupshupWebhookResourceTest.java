package com.flipkart.fintech.pandora.service.resources.webhook;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.flipkart.fintech.pandora.service.core.events.GupshupEventManager;
import com.flipkart.fintech.pandora.service.client.config.GupshupWebhookConfig;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pandora.external.models.request.GupshupRequest;

import javax.ws.rs.core.Response;
@ExtendWith(MockitoExtension.class)
class GupshupWebhookResourceTest {
@Mock
    private GupshupEventManager gupshupEventManager;
    @Mock
    private GupshupWebhookConfig gupshupWebhookConfig;

    @InjectMocks
    private GupshupWebhookResource gupshupWebhookResource;


    @Test
    void sendIncomingMessageEventReturnsOkForValidApiKeyAndRequest() {
        when(gupshupWebhookConfig.getWebhookApiKey()).thenReturn("validApiKey");

        GupshupRequest request = new GupshupRequest();
        Response response = gupshupWebhookResource.sendIncomingMessageEvent("validApiKey", request);

        assertEquals(HttpStatus.SC_OK, response.getStatus());
        verify(gupshupEventManager, times(1)).handleIncomingMessageEvent(request);
    }

    @Test
    void sendIncomingMessageEventReturnsUnauthorizedForInvalidApiKey() {
        when(gupshupWebhookConfig.getWebhookApiKey()).thenReturn("validApiKey");

        GupshupRequest request = new GupshupRequest();
        Response response = gupshupWebhookResource.sendIncomingMessageEvent("invalidApiKey", request);

        assertEquals(HttpStatus.SC_UNAUTHORIZED, response.getStatus());
        verifyNoInteractions(gupshupEventManager);
    }

    @Test
    void sendIncomingMessageEventThrowsExceptionWhenEventManagerFails() {
        when(gupshupWebhookConfig.getWebhookApiKey()).thenReturn("validApiKey");
        doThrow(new RuntimeException("Event manager error")).when(gupshupEventManager).handleIncomingMessageEvent(any());

        GupshupRequest request = new GupshupRequest();
        assertThrows(RuntimeException.class, () -> gupshupWebhookResource.sendIncomingMessageEvent("validApiKey", request));
    }

    @Test
    void sendIncomingMessageEventReturnsUnauthorizedForNullApiKey() {
        GupshupRequest request = new GupshupRequest();
        Response response = gupshupWebhookResource.sendIncomingMessageEvent(null, request);

        assertEquals(HttpStatus.SC_UNAUTHORIZED, response.getStatus());
        verifyNoInteractions(gupshupEventManager);
    }
}
