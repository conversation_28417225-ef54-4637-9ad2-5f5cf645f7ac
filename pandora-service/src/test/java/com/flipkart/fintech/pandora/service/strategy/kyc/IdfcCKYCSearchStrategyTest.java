package com.flipkart.fintech.pandora.service.strategy.kyc;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.common.enums.Tenant;
import com.flipkart.fintech.filter.RequestContext;
import com.flipkart.fintech.filter.RequestContextThreadLocal;
import com.flipkart.fintech.pandora.api.model.request.ckyc.SearchCkycRequest;
import com.flipkart.fintech.pandora.service.client.pl.IdfcUtil;
import com.flipkart.fintech.pandora.service.client.pl.kyc.IdfcClientV2;
import com.flipkart.fintech.pandora.service.client.pl.response.ckyc.SearchCkycResponse;
import com.flipkart.fintech.pandora.service.client.auth.AccessTokenProvider;
import com.flipkart.fintech.pandora.service.hystrix.PandoraHystrixNameConstants;
import com.flipkart.fintech.pandora.service.hystrix.PandoraHystrixProperties;
import com.flipkart.kloud.config.DynamicBucket;
import com.flipkart.sensitive.configuration.SensitiveAnnotationConfiguration;
import com.flipkart.sensitive.core.EncryptionManager;
import com.flipkart.sensitive.core.SensitiveUtil;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.runners.MockitoJUnitRunner;

import java.security.NoSuchAlgorithmException;
import java.util.HashMap;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyString;

@RunWith(MockitoJUnitRunner.class)
class IdfcCKYCSearchStrategyTest {
    @Mock
    private IdfcClientV2 idfcClient;
    @Mock
    private IdfcUtil idfcUtil;
    private PandoraHystrixProperties pandoraHystrixProperties;
    @Mock
    private DynamicBucket dynamicBucket;
    @Mock
    private AccessTokenProvider accessTokenProvider;
    private IdfcCKYCSearchStrategy idfcCKYCSearchStrategy;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
        RequestContextThreadLocal.setRequestContext(new RequestContext(Tenant.FK_CONSUMER_CREDIT, null, null, null, false));
        pandoraHystrixProperties = new PandoraHystrixProperties();
        pandoraHystrixProperties.setCommandCircuitBreakerProperties(new HashMap<>());
        pandoraHystrixProperties.setCommandExecutionProperties(new HashMap<>());
        pandoraHystrixProperties.setThreadPoolProperties(new HashMap<>());
        objectMapper = new ObjectMapper();
        SensitiveAnnotationConfiguration sensitiveAnnotationConfiguration = new SensitiveAnnotationConfiguration(new HashMap<>());
        sensitiveAnnotationConfiguration.getKeyMap().put("kycKey", "03A1CE01ED4D5FA1ABBDCD728C0BE2A8");
        SensitiveUtil.setEncryptionManager(new EncryptionManager(sensitiveAnnotationConfiguration));
        idfcCKYCSearchStrategy = new IdfcCKYCSearchStrategy(idfcUtil, idfcClient, pandoraHystrixProperties, dynamicBucket, accessTokenProvider);
        Mockito.when(dynamicBucket.getInt(PandoraHystrixNameConstants.HYSTRIX_IDFC_SEARCH_CKYC_TIMEOUT)).thenReturn(30000);
    }

    @AfterEach
    void tearDown() {
    }

    @Test
    void execute() throws NoSuchAlgorithmException, JsonProcessingException {
        SearchCkycRequest searchCkycRequest = getSearchCkycRequest();
        Mockito.when(accessTokenProvider.getAccessToken(any())).thenReturn("access_token");
        Mockito.when(idfcUtil.generateIdfcRequestIdFromAccountId(any())).thenReturn("reqid");
        Mockito.when(idfcClient.searchCkyc(any(), anyString())).thenReturn(getSearchCkycResponse());
        com.flipkart.fintech.pandora.api.model.response.ckyc.SearchCkycResponse response = idfcCKYCSearchStrategy.execute(searchCkycRequest);
        assertEquals("CKYCSuccess", response.getTransactionStatus());
    }

    private SearchCkycRequest getSearchCkycRequest() {
        return SearchCkycRequest.builder()
                .accountId("test-acc")
                .panNumber("pan1")
                .build();
    }

    private SearchCkycResponse getSearchCkycResponse() throws JsonProcessingException {
        return objectMapper.readValue("{\"SearchCkycResponse\":{\"Detail\":[{\"CKYCID\":\"**************\",\"TransactionStatus\":\"CKYCSuccess\",\"TransactionRejectionDescription\":null}]}}", SearchCkycResponse.class);
    }
}