package com.flipkart.fintech.pandora.service.strategy.kyc;

import com.flipkart.fintech.common.enums.Tenant;
import com.flipkart.fintech.filter.RequestContext;
import com.flipkart.fintech.filter.RequestContextThreadLocal;
import com.flipkart.fintech.pandora.api.model.common.STATUS;
import com.flipkart.fintech.pandora.api.model.request.onboarding.PanNumberRequest;
import com.flipkart.fintech.pandora.api.model.response.onboarding.PanNumberResponse;
import com.flipkart.fintech.pandora.service.client.pl.IdfcUtil;
import com.flipkart.fintech.pandora.service.client.pl.kyc.IdfcClientV2;
import com.flipkart.fintech.pandora.service.client.pl.response.PanNumberVerificationResponse;
import com.flipkart.fintech.pandora.service.client.pl.response.PanUserInfo;
import com.flipkart.fintech.pandora.service.client.pl.response.PanVerificationResponse;
import com.flipkart.fintech.pandora.service.client.auth.AccessTokenProvider;
import com.flipkart.fintech.pandora.service.hystrix.PandoraHystrixNameConstants;
import com.flipkart.fintech.pandora.service.hystrix.PandoraHystrixProperties;
import com.flipkart.kloud.config.DynamicBucket;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.runners.MockitoJUnitRunner;

import java.security.NoSuchAlgorithmException;
import java.util.HashMap;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyString;

@RunWith(MockitoJUnitRunner.class)
class IdfcPanValidationStrategyTest {
    @Mock
    private IdfcClientV2 idfcClient;
    @Mock
    private IdfcUtil idfcUtil;
    private PandoraHystrixProperties pandoraHystrixProperties;
    @Mock
    private DynamicBucket dynamicBucket;
    @Mock
    private AccessTokenProvider accessTokenProvider;
    private IdfcPanValidationStrategy idfcPanValidationStrategy;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
        RequestContextThreadLocal.setRequestContext(new RequestContext(Tenant.FK_CONSUMER_CREDIT, null, null, null, false));
        pandoraHystrixProperties = new PandoraHystrixProperties();
        pandoraHystrixProperties.setCommandCircuitBreakerProperties(new HashMap<>());
        pandoraHystrixProperties.setCommandExecutionProperties(new HashMap<>());
        pandoraHystrixProperties.setThreadPoolProperties(new HashMap<>());
        idfcPanValidationStrategy = new IdfcPanValidationStrategy(idfcClient, idfcUtil, pandoraHystrixProperties, dynamicBucket, accessTokenProvider);
        Mockito.when(dynamicBucket.getInt(PandoraHystrixNameConstants.HYSTRIX_IDFC_PAN_VERIFICATION_TIMEOUT)).thenReturn(30000);
    }

    @AfterEach
    void tearDown() {
    }

    @Test
    void execute() throws NoSuchAlgorithmException {
        PanNumberRequest panNumberRequest = getPanNumberRequest();
        Mockito.when(accessTokenProvider.getAccessToken(any())).thenReturn("access_token");
        Mockito.when(idfcUtil.generateIdfcRequestIdFromAccountId(any())).thenReturn("reqid");
        Mockito.when(idfcClient.verifyPan(any(), anyString())).thenReturn(getPanVerificationResponse());
        PanNumberResponse panNumberResponse = idfcPanValidationStrategy.execute(panNumberRequest);
        assertEquals(STATUS.SUCCESS, panNumberResponse.getStatus());
        assertEquals("valid", panNumberResponse.getPanVerifiedStatus());
    }

    private PanNumberRequest getPanNumberRequest() {
        PanNumberRequest panNumberRequest = new PanNumberRequest();
        panNumberRequest.setPan("pantest");
        panNumberRequest.setAccId("test-acc");
        return panNumberRequest;
    }

    private PanNumberVerificationResponse getPanVerificationResponse() {
        PanNumberVerificationResponse panNumberVerificationResponse = new PanNumberVerificationResponse();
        PanVerificationResponse panVerificationResponse = new PanVerificationResponse();
        panVerificationResponse.setResponseId("res");
        panVerificationResponse.setStatus("s");
        PanUserInfo panUserInfo = new PanUserInfo();
        panUserInfo.setPnStatus("E");
        panUserInfo.setFirstName("krs");
        panVerificationResponse.setPanUserInfo(panUserInfo);
        panNumberVerificationResponse.setPanVerificationResponse(panVerificationResponse);
        return panNumberVerificationResponse;
    }
}