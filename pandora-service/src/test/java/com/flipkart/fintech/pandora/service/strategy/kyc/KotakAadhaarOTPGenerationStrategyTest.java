package com.flipkart.fintech.pandora.service.strategy.kyc;

import com.flipkart.fintech.pandora.api.model.common.STATUS;
import com.flipkart.fintech.pandora.api.model.request.EncryptionKeyData;
import com.flipkart.fintech.pandora.api.model.request.ekyc.EkycSendOtpRequest;
import com.flipkart.fintech.pandora.api.model.response.ekyc.EkycSendOtpResponse;
import com.flipkart.fintech.pandora.service.client.auth.AccessTokenProvider;
import com.flipkart.fintech.pandora.service.client.auth.Scope;
import com.flipkart.fintech.pandora.service.client.auth.KotakAuthenticationClient;
import com.flipkart.fintech.pandora.service.client.kotak.kyc.KotakKYCClient;
import com.flipkart.fintech.pandora.service.client.kotak.responses.AadhaarOTPGenerationResponse;
import com.flipkart.fintech.pandora.service.core.security.SecurityService;
import com.flipkart.fintech.pandora.service.hystrix.PandoraHystrixProperties;
import org.junit.Assert;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.runners.MockitoJUnitRunner;

import java.util.HashMap;

@RunWith(MockitoJUnitRunner.class)
public class KotakAadhaarOTPGenerationStrategyTest {

    @Mock
    private KotakKYCClient kotakKYCClient;
    @Mock
    private AccessTokenProvider accessTokenProvider;
    @Mock
    private SecurityService securityService;
    private AadhaarOTPGenerationStrategy aadhaarOTPGenerationStrategy;
    @Mock
    private KotakAuthenticationClient kotakAuthenticationClient;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        PandoraHystrixProperties pandoraHystrixProperties = new PandoraHystrixProperties();
        pandoraHystrixProperties.setCommandCircuitBreakerProperties(new HashMap<>());
        pandoraHystrixProperties.setCommandExecutionProperties(new HashMap<>());
        pandoraHystrixProperties.setThreadPoolProperties(new HashMap<>());
        Mockito.when(accessTokenProvider.getAccessToken(Mockito.any(Scope.class))).thenReturn("accessToken");
        Mockito.when(securityService.getDecryptedData(Mockito.any(), Mockito.any())).thenReturn("decryptedData");
        aadhaarOTPGenerationStrategy = new KotakAadhaarOTPGenerationStrategy(kotakKYCClient, pandoraHystrixProperties,
                securityService, kotakAuthenticationClient);
    }

    @Test
    public void testAadhaarOTPGenerationStrategySuccess() {
        EkycSendOtpRequest ekycSendOtpRequest = new EkycSendOtpRequest();
        ekycSendOtpRequest.setAadhaarNumber("testEncryptedAadhaarNumber");
        ekycSendOtpRequest.setExternalRefId("ACCTEST1234");
        ekycSendOtpRequest.setExternalRefKey("ACCTEST1234");
        ekycSendOtpRequest.setEncKeyData(new EncryptionKeyData());
        AadhaarOTPGenerationResponse aadhaarOTPGenerationResponse = new AadhaarOTPGenerationResponse();
        aadhaarOTPGenerationResponse.setStatus("Success");
        Mockito.when(kotakKYCClient.executeKYCFunction(Mockito.any(), Mockito.any(), Mockito.any(),
                Mockito.any(), Mockito.any())).thenReturn(aadhaarOTPGenerationResponse);
        Mockito.when(kotakAuthenticationClient.generateAccessToken()).thenReturn("testToken");
        EkycSendOtpResponse ekycSendOtpResponse = aadhaarOTPGenerationStrategy.execute(ekycSendOtpRequest);
        Assert.assertNotNull(ekycSendOtpResponse);
        Assert.assertEquals(STATUS.SUCCESS, ekycSendOtpResponse.getStatus());
    }
}
