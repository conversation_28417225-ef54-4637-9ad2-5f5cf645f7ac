package com.flipkart.fintech.pandora.service.strategy.kyc;

import com.flipkart.fintech.pandora.api.model.request.ckyc.SearchCkycRequest;
import com.flipkart.fintech.pandora.api.model.response.ckyc.SearchCkycResponse;
import com.flipkart.fintech.pandora.service.client.auth.AccessTokenProvider;
import com.flipkart.fintech.pandora.service.client.auth.Scope;
import com.flipkart.fintech.pandora.service.client.auth.KotakAuthenticationClient;
import com.flipkart.fintech.pandora.service.client.kotak.kyc.KotakKYCClient;
import com.flipkart.fintech.pandora.service.client.kotak.responses.CKYCSearchResponse;
import com.flipkart.fintech.pandora.service.hystrix.PandoraHystrixProperties;
import org.junit.Assert;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.runners.MockitoJUnitRunner;

import java.util.HashMap;

@RunWith(MockitoJUnitRunner.class)
public class KotakCKYCSearchStrategyTest {

    @Mock
    private KotakKYCClient kotakKYCClient;
    @Mock
    private AccessTokenProvider accessTokenProvider;
    private CKYCSearchStrategy ckycSearchStrategy;
    @Mock
    private KotakAuthenticationClient kotakAuthenticationClient;

    @BeforeEach
    public void setUp()
    {
        MockitoAnnotations.initMocks(this);
        PandoraHystrixProperties pandoraHystrixProperties = new PandoraHystrixProperties();
        pandoraHystrixProperties.setCommandCircuitBreakerProperties(new HashMap<>());
        pandoraHystrixProperties.setCommandExecutionProperties(new HashMap<>());
        pandoraHystrixProperties.setThreadPoolProperties(new HashMap<>());
        Mockito.when(accessTokenProvider.getAccessToken(Mockito.any(Scope.class))).thenReturn("accessToken");
        ckycSearchStrategy = new KotakCKYCSearchStrategy(kotakKYCClient, pandoraHystrixProperties, kotakAuthenticationClient);
    }

    @Test
    public void testCKYCSearchStrategySuccess()
    {
        CKYCSearchResponse ckycSearchResponse = new CKYCSearchResponse();
        ckycSearchResponse.setStatus("Success");
        SearchCkycRequest searchCkycRequest = new SearchCkycRequest();
        searchCkycRequest.setAccountId("ACCTEST12345");
        Mockito.when(kotakKYCClient.executeKYCFunction(Mockito.any(),
                Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(ckycSearchResponse);
        Mockito.when(kotakAuthenticationClient.generateAccessToken()).thenReturn("testToken");
        SearchCkycResponse searchCkycResponse = ckycSearchStrategy.execute(searchCkycRequest);
        Assert.assertNotNull(searchCkycResponse);
        Assert.assertEquals("SUCCESS", searchCkycResponse.getTransactionStatus());
    }
}
