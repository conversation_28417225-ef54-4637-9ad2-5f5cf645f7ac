package com.flipkart.fintech.pandora.service.strategy.kyc;

import com.flipkart.fintech.pandora.api.model.request.onboarding.PanNumberRequest;
import com.flipkart.fintech.pandora.api.model.response.onboarding.PanNumberResponse;
import com.flipkart.fintech.pandora.service.client.auth.AccessTokenProvider;
import com.flipkart.fintech.pandora.service.client.auth.KotakAuthenticationClient;
import com.flipkart.fintech.pandora.service.client.auth.Scope;
import com.flipkart.fintech.pandora.service.client.kotak.kyc.KotakKYCClient;
import com.flipkart.fintech.pandora.service.client.kotak.responses.PanValidationResponse;
import com.flipkart.fintech.pandora.service.hystrix.PandoraHystrixProperties;
import com.flipkart.sensitive.core.SensitiveUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class KotakPanValidationStrategyTest {

    @Mock
    private KotakKYCClient kotakKYCClient;
    @Mock
    private AccessTokenProvider accessTokenProvider;
    private PanValidationStrategy panValidationStrategy;
    @Mock
    private KotakAuthenticationClient kotakAuthenticationClient;

    @BeforeEach
    void setUp() {
        PandoraHystrixProperties pandoraHystrixProperties = new PandoraHystrixProperties();
        pandoraHystrixProperties.setCommandCircuitBreakerProperties(new HashMap<>());
        pandoraHystrixProperties.setCommandExecutionProperties(new HashMap<>());
        pandoraHystrixProperties.setThreadPoolProperties(new HashMap<>());
        lenient().when(accessTokenProvider.getAccessToken(any(Scope.class))).thenReturn("accessToken");
        panValidationStrategy = new KotakPanValidationStrategy(kotakKYCClient, pandoraHystrixProperties,
                kotakAuthenticationClient);
    }

    @Test
    void testKotakPanValidationStrategy()
    {
        PanValidationResponse panValidationResponse = new PanValidationResponse();
        panValidationResponse.setPanName("testPANName");
        panValidationResponse.setStatus("success");
        when(kotakKYCClient.executeKYCFunction(any(),
                any(), any(), any(), any())).thenReturn(panValidationResponse);
        when(kotakAuthenticationClient.generateAccessToken()).thenReturn("testToken");
        PanNumberRequest panNumberRequest = new PanNumberRequest();
        panNumberRequest.setAccId("ACCTEST1234");
        panNumberRequest.setPan("TESTPAN1234");
        try(MockedStatic<SensitiveUtil> mockedStatic = mockStatic(SensitiveUtil.class)) {
            mockedStatic.when(() -> SensitiveUtil.decryptSensitiveFields(any())).thenReturn(panNumberRequest);
            PanNumberResponse panNumberResponse = panValidationStrategy.execute(panNumberRequest);
            assertNotNull(panNumberResponse);
            assertEquals("testPANName", panNumberResponse.getPanVerifiedName());
        }
    }
}
