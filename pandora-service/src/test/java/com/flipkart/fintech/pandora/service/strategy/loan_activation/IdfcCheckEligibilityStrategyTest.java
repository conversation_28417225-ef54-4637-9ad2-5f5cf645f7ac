package com.flipkart.fintech.pandora.service.strategy.loan_activation;

import com.flipkart.fintech.common.enums.Tenant;
import com.flipkart.fintech.filter.RequestContext;
import com.flipkart.fintech.filter.RequestContextThreadLocal;
import com.flipkart.fintech.pandora.api.model.request.onboarding.CompressedUserRequest;
import com.flipkart.fintech.pandora.api.model.request.onboarding.UserRequest;
import com.flipkart.fintech.pandora.api.model.response.onboarding.UserResponse;
import com.flipkart.fintech.pandora.service.client.pl.LenderConstants;
import com.flipkart.fintech.pandora.service.client.pl.IdfcUtil;
import com.flipkart.fintech.pandora.service.client.pl.kyc.IdfcClientV2;
import com.flipkart.fintech.pandora.service.client.pl.requestV2.CheckApprovalStatusRequestV2;
import com.flipkart.fintech.pandora.service.client.pl.response.CheckApprovalStatusResponse;
import com.flipkart.fintech.pandora.service.client.auth.AccessTokenProvider;
import com.flipkart.fintech.pandora.service.hystrix.PandoraHystrixNameConstants;
import com.flipkart.fintech.pandora.service.hystrix.PandoraHystrixProperties;
import com.flipkart.fintech.pinaka.api.response.v3.FetchLenderIdentifierResponse;
import com.flipkart.fintech.pinaka.client.PinakaClientException;
import com.flipkart.fintech.pinaka.client.v3.PinakaClientV3;
import com.flipkart.kloud.config.DynamicBucket;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.runners.MockitoJUnitRunner;

import java.security.NoSuchAlgorithmException;
import java.util.HashMap;

import static org.junit.Assert.assertEquals;
import static org.mockito.Matchers.any;

@RunWith(MockitoJUnitRunner.class)
class IdfcCheckEligibilityStrategyTest {
    @Mock
    private IdfcClientV2 idfcClient;
    @Mock
    private IdfcUtil idfcUtil;
    private PandoraHystrixProperties pandoraHystrixProperties;
    @Mock
    private PinakaClientV3 pinakaClientV3;
    @Mock
    private DynamicBucket dynamicBucket;
    @Mock
    private AccessTokenProvider accessTokenProvider;
    private IdfcCheckEligibilityStrategy idfcCheckEligibilityStrategy;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
        RequestContextThreadLocal.setRequestContext(new RequestContext(Tenant.FK_CONSUMER_CREDIT, null, null, null, false));
        pandoraHystrixProperties = new PandoraHystrixProperties();
        pandoraHystrixProperties.setCommandCircuitBreakerProperties(new HashMap<>());
        pandoraHystrixProperties.setCommandExecutionProperties(new HashMap<>());
        pandoraHystrixProperties.setThreadPoolProperties(new HashMap<>());
        idfcCheckEligibilityStrategy = new IdfcCheckEligibilityStrategy(idfcClient, idfcUtil, pandoraHystrixProperties, pinakaClientV3, dynamicBucket, accessTokenProvider);
        Mockito.when(dynamicBucket.getInt(PandoraHystrixNameConstants.HYSTRIX_IDFC_CHECK_ELIGIBILITY_TIMEOUT)).thenReturn(30000);
    }

    @AfterEach
    void tearDown() {
    }

    @Test
    void execute() throws NoSuchAlgorithmException, PinakaClientException {
        UserRequest userRequest = getUserRequest("FK", 100000.50, "ABCD", "ACC");
        Mockito.when(accessTokenProvider.getAccessToken(any())).thenReturn("access_token");
        Mockito.when(idfcUtil.generateIdfcRequestIdFromAccountId("test_Account")).thenReturn("reqid");
        Mockito.when(dynamicBucket.getInt(PandoraHystrixNameConstants.HYSTRIX_IDFC_CHECK_ELIGIBILITY_TIMEOUT)).thenReturn(15000);
        Mockito.when(pinakaClientV3.fetchLenderIdentifierResponse(any(), any())).thenReturn(FetchLenderIdentifierResponse.builder().lenderReferenceNumber("123").referenceNumber("456").build());
        Mockito.when(idfcClient.checkApprovalStatus(getCheckApprovalRequest("reqid"), "access_token")).thenReturn(getCheckApprovalResponse());
        UserResponse userResponse = idfcCheckEligibilityStrategy.execute(userRequest);
        assertEquals(Boolean.TRUE, userResponse.isEligible());
    }

    private UserRequest getUserRequest(String businessCategory, Double loanAmount, String controlFlowToken, String externalReferenceId) {
        UserRequest userRequest = null;
        CompressedUserRequest compressedUserRequest;
        compressedUserRequest = new CompressedUserRequest();
        compressedUserRequest.setBusinessCategory(businessCategory);
        compressedUserRequest.setType(UserRequest.RequestType.COMPRESSED);
        compressedUserRequest.setLoanAmount(loanAmount);
        compressedUserRequest.setControlFlowToken(controlFlowToken);
        compressedUserRequest.setExternalReferenceId(externalReferenceId);
        return compressedUserRequest;
    }

    private CheckApprovalStatusRequestV2 getCheckApprovalRequest(String reqId) {
        CheckApprovalStatusRequestV2 checkApprovalStatusRequestV2 = new CheckApprovalStatusRequestV2();
        checkApprovalStatusRequestV2.setReqId(reqId);
        return checkApprovalStatusRequestV2;
    }

    private CheckApprovalStatusResponse getCheckApprovalResponse() {
        CheckApprovalStatusResponse checkApprovalStatusResponse = new CheckApprovalStatusResponse();
        checkApprovalStatusResponse.setStatus(LenderConstants.CHECK_APPROVAL_SUCCESS);
        return checkApprovalStatusResponse;
    }
}