package com.flipkart.fintech.pandora.service.strategy.loan_activation;

import com.flipkart.fintech.common.enums.Tenant;
import com.flipkart.fintech.filter.RequestContext;
import com.flipkart.fintech.filter.RequestContextThreadLocal;
import com.flipkart.fintech.pandora.api.model.common.STATUS;
import com.flipkart.fintech.pandora.api.model.request.onboarding.AccountActivationRequest;
import com.flipkart.fintech.pandora.api.model.response.onboarding.AccountActivationResponse;
import com.flipkart.fintech.pandora.service.application.configuration.PandoraConfiguration;
import com.flipkart.fintech.pandora.service.client.pl.IdfcUtil;
import com.flipkart.fintech.pandora.service.client.pl.kyc.IdfcClientV2;
import com.flipkart.fintech.pandora.service.client.pl.requestV2.CreateLoanRequestV2;
import com.flipkart.fintech.pandora.service.client.pl.response.CreateLoanResponse;
import com.flipkart.fintech.pandora.service.client.pl.response.IdfcCreateLoanResult;
import com.flipkart.fintech.pandora.service.client.auth.AccessTokenProvider;
import com.flipkart.fintech.pandora.service.hystrix.PandoraHystrixNameConstants;
import com.flipkart.fintech.pandora.service.hystrix.PandoraHystrixProperties;
import com.flipkart.kloud.config.DynamicBucket;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.security.NoSuchAlgorithmException;
import java.util.HashMap;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Matchers.any;

class IdfcLoanCreationStrategyTest {

    @Mock
    private IdfcClientV2 idfcClient;
    @Mock
    private IdfcUtil idfcUtil;
    private PandoraHystrixProperties pandoraHystrixProperties;
    @Mock
    private DynamicBucket dynamicBucket;
    @Mock
    private AccessTokenProvider accessTokenProvider;
    @Mock
    PandoraConfiguration pandoraConfiguration;
    private IdfcLoanCreationStrategy idfcLoanCreationStrategy;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
        RequestContextThreadLocal.setRequestContext(new RequestContext(Tenant.FK_CONSUMER_CREDIT, null, null, null, false));
        pandoraHystrixProperties = new PandoraHystrixProperties();
        pandoraHystrixProperties.setCommandCircuitBreakerProperties(new HashMap<>());
        pandoraHystrixProperties.setCommandExecutionProperties(new HashMap<>());
        pandoraHystrixProperties.setThreadPoolProperties(new HashMap<>());
        idfcLoanCreationStrategy = new IdfcLoanCreationStrategy(idfcClient, idfcUtil, pandoraHystrixProperties, pandoraConfiguration, dynamicBucket, accessTokenProvider);
        Mockito.when(dynamicBucket.getInt(PandoraHystrixNameConstants.HYSTRIX_IDFC_CREATE_LOAN_TIMEOUT)).thenReturn(30000);

    }

    @AfterEach
    void tearDown() {
    }

    @Test
    void execute() throws NoSuchAlgorithmException {
        AccountActivationRequest accountActivationRequest = getAccountActivationRequest();
        Mockito.when(accessTokenProvider.getAccessToken(any())).thenReturn("access_token");
        Mockito.when(idfcUtil.generateIdfcRequestIdFromAccountId(accountActivationRequest.getCustomerId())).thenReturn("reqid");
        Mockito.when(idfcClient.createLoan(getCreateLoanRequest(), "access_token")).thenReturn(getCreateLoanResponse());
        AccountActivationResponse accountActivationResponse = idfcLoanCreationStrategy.execute(accountActivationRequest);
        assertEquals(STATUS.SUCCESS, accountActivationResponse.getStatus());
    }

    private AccountActivationRequest getAccountActivationRequest() {
        AccountActivationRequest accountActivationRequest = new AccountActivationRequest();
        accountActivationRequest.setCustomerId("test_account");
        accountActivationRequest.setLoanAmount(10000.0);
        accountActivationRequest.setConsentTime("23/02/1997");
        accountActivationRequest.setLenderLoanAccountNumber("123");
        accountActivationRequest.setLenderCustomerRefernceNumber("456");
        return accountActivationRequest;
    }

    private CreateLoanRequestV2 getCreateLoanRequest() {
        CreateLoanRequestV2 createLoanRequestV2 = new CreateLoanRequestV2();
        createLoanRequestV2.setCrnNo(456L);
        createLoanRequestV2.setLoanId(123L);
        createLoanRequestV2.setCustomerConsents("23/02/1997");
        createLoanRequestV2.setSchemeId("");
        createLoanRequestV2.setLoanAmount("10000.0");
        createLoanRequestV2.setReqId("reqid");
        createLoanRequestV2.setSourceSystem("FLPKRT");
        return createLoanRequestV2;
    }

    private CreateLoanResponse getCreateLoanResponse() {
        CreateLoanResponse createLoanResponse = new CreateLoanResponse();
        IdfcCreateLoanResult idfcCreateLoanResult = new IdfcCreateLoanResult();
        idfcCreateLoanResult.setMessage("Successfully Received.");
        createLoanResponse.setIdfcCreateLoanResult(idfcCreateLoanResult);
        return createLoanResponse;
    }
}