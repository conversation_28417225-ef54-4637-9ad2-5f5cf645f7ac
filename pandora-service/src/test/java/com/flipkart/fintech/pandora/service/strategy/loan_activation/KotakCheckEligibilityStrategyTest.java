package com.flipkart.fintech.pandora.service.strategy.loan_activation;

import com.flipkart.fintech.pandora.api.model.common.STATUS;
import com.flipkart.fintech.pandora.api.model.request.onboarding.CompressedUserRequest;
import com.flipkart.fintech.pandora.api.model.response.onboarding.UserResponse;
import com.flipkart.fintech.pandora.service.client.auth.AccessTokenProvider;
import com.flipkart.fintech.pandora.service.client.auth.Scope;
import com.flipkart.fintech.pandora.service.client.auth.KotakAuthenticationClient;
import com.flipkart.fintech.pandora.service.client.kotak.loan_activation.KotakLoanActivationClient;
import com.flipkart.fintech.pandora.service.client.kotak.responses.CheckEligibilityResponse;
import com.flipkart.fintech.pandora.service.hystrix.PandoraHystrixProperties;
import org.junit.Assert;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.runners.MockitoJUnitRunner;

import java.util.HashMap;

@RunWith(MockitoJUnitRunner.class)
public class KotakCheckEligibilityStrategyTest {

    @Mock
    private KotakLoanActivationClient kotakLoanActivationClient;
    @Mock
    private AccessTokenProvider accessTokenProvider;
    private CheckEligibilityStrategy checkEligibilityStrategy;
    @Mock
    private KotakAuthenticationClient kotakAuthenticationClient;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        PandoraHystrixProperties pandoraHystrixProperties = new PandoraHystrixProperties();
        pandoraHystrixProperties.setCommandCircuitBreakerProperties(new HashMap<>());
        pandoraHystrixProperties.setCommandExecutionProperties(new HashMap<>());
        pandoraHystrixProperties.setThreadPoolProperties(new HashMap<>());
        Mockito.when(accessTokenProvider.getAccessToken(Mockito.any(Scope.class))).thenReturn("accessToken");
        checkEligibilityStrategy = new KotakCheckEligibilityStrategy(kotakLoanActivationClient, pandoraHystrixProperties,
                kotakAuthenticationClient);
    }

    @Test
    public void testCheckEligibilityStrategy() {
        CompressedUserRequest compressedUserRequest = new CompressedUserRequest();
        compressedUserRequest.setExternalReferenceId("ACCTEST1234");
        CheckEligibilityResponse checkEligibilityResponse = new CheckEligibilityResponse();
        checkEligibilityResponse.setStatus("Success");
        Mockito.when(kotakLoanActivationClient.executionLoanActivationFunction(Mockito.any(),
                Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(checkEligibilityResponse);
        Mockito.when(kotakAuthenticationClient.generateAccessToken()).thenReturn("testToken");
        UserResponse userResponse = checkEligibilityStrategy.execute(compressedUserRequest);
        Assert.assertNotNull(userResponse);
        Assert.assertEquals(STATUS.SUCCESS, userResponse.getStatus());
    }
}
