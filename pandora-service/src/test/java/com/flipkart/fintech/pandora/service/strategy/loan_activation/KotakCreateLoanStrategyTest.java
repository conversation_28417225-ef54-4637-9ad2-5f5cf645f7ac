package com.flipkart.fintech.pandora.service.strategy.loan_activation;

import com.flipkart.fintech.pandora.api.model.common.STATUS;
import com.flipkart.fintech.pandora.api.model.request.onboarding.AccountActivationRequest;
import com.flipkart.fintech.pandora.api.model.response.onboarding.AccountActivationResponse;
import com.flipkart.fintech.pandora.service.client.auth.AccessTokenProvider;
import com.flipkart.fintech.pandora.service.client.auth.Scope;
import com.flipkart.fintech.pandora.service.client.auth.KotakAuthenticationClient;
import com.flipkart.fintech.pandora.service.client.kotak.loan_activation.KotakLoanActivationClient;
import com.flipkart.fintech.pandora.service.client.kotak.responses.LoanCreationResponse;
import com.flipkart.fintech.pandora.service.hystrix.PandoraHystrixProperties;
import org.junit.Assert;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.runners.MockitoJUnitRunner;

import java.util.HashMap;

@RunWith(MockitoJUnitRunner.class)
public class KotakCreateLoanStrategyTest {
    @Mock
    private KotakLoanActivationClient kotakLoanActivationClient;
    @Mock
    private AccessTokenProvider accessTokenProvider;
    private LoanCreationStrategy loanCreationStrategy;
    @Mock
    private KotakAuthenticationClient kotakAuthenticationClient;

    @BeforeEach
    public void setUo() {
        MockitoAnnotations.initMocks(this);
        PandoraHystrixProperties pandoraHystrixProperties = new PandoraHystrixProperties();
        pandoraHystrixProperties.setCommandCircuitBreakerProperties(new HashMap<>());
        pandoraHystrixProperties.setCommandExecutionProperties(new HashMap<>());
        pandoraHystrixProperties.setThreadPoolProperties(new HashMap<>());
        Mockito.when(accessTokenProvider.getAccessToken(Mockito.any(Scope.class))).thenReturn("accessToken");
        loanCreationStrategy = new KotakLoanCreationStrategy(kotakLoanActivationClient, pandoraHystrixProperties,
                kotakAuthenticationClient);
    }

    @Test
    public void testCreateLoanStrategy() {
        AccountActivationRequest accountActivationRequest = new AccountActivationRequest();
        accountActivationRequest.setCustomerId("ACCTEST1234");
        accountActivationRequest.setLoanAmount(new Double(10000));
        LoanCreationResponse loanCreationResponse = new LoanCreationResponse();
        loanCreationResponse.setStatus("Success");
        Mockito.when(kotakLoanActivationClient.executionLoanActivationFunction(Mockito.any(),
                Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(loanCreationResponse);
        Mockito.when(kotakAuthenticationClient.generateAccessToken()).thenReturn("testToken");
        AccountActivationResponse accountActivationResponse = loanCreationStrategy.execute(accountActivationRequest);
        Assert.assertNotNull(accountActivationResponse);
        Assert.assertEquals(STATUS.SUCCESS, accountActivationResponse.getStatus());
    }
}
