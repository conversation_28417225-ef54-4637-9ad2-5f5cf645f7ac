package com.flipkart.fintech.pandora.service.strategy.loan_activation;

import com.flipkart.fintech.pandora.api.model.common.FinancialProvider;
import com.flipkart.fintech.pandora.api.model.common.STATUS;
import com.flipkart.fintech.pandora.api.model.request.onboarding.UnderwritingRequest;
import com.flipkart.fintech.pandora.api.model.response.onboarding.UnderwritingResponse;
import com.flipkart.fintech.pandora.service.client.auth.AccessTokenProvider;
import com.flipkart.fintech.pandora.service.client.auth.Scope;
import com.flipkart.fintech.pandora.service.client.auth.KotakAuthenticationClient;
import com.flipkart.fintech.pandora.service.client.kotak.loan_activation.KotakLoanActivationClient;
import com.flipkart.fintech.pandora.service.client.kotak.responses.BRECheckResponse;
import com.flipkart.fintech.pandora.service.hystrix.PandoraHystrixProperties;
import org.junit.Assert;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.runners.MockitoJUnitRunner;

import java.util.HashMap;

@RunWith(MockitoJUnitRunner.class)
public class KotakUnderwritingStrategyTest {
    @Mock
    private KotakLoanActivationClient kotakLoanActivationClient;
    @Mock
    private AccessTokenProvider accessTokenProvider;
    private UnderwritingStrategy underwritingStrategy;
    @Mock
    private KotakAuthenticationClient kotakAuthenticationClient;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        PandoraHystrixProperties pandoraHystrixProperties = new PandoraHystrixProperties();
        pandoraHystrixProperties.setCommandCircuitBreakerProperties(new HashMap<>());
        pandoraHystrixProperties.setCommandExecutionProperties(new HashMap<>());
        pandoraHystrixProperties.setThreadPoolProperties(new HashMap<>());
        Mockito.when(accessTokenProvider.getAccessToken(Mockito.any(Scope.class))).thenReturn("accessToken");
        underwritingStrategy = new KotakUnderwritingStrategy(kotakLoanActivationClient, pandoraHystrixProperties,
                kotakAuthenticationClient);
    }

    @Test
    public void testUnderwritingStrategy() {
        UnderwritingRequest underwritingRequest = new UnderwritingRequest();
        underwritingRequest.setAccountId("ACCTEST1234");
        underwritingRequest.setFinancialProvider(FinancialProvider.KOTAK);
        BRECheckResponse breCheckResponse = new BRECheckResponse();
        breCheckResponse.setStatus("Success");
        Mockito.when(kotakLoanActivationClient.executionLoanActivationFunction(Mockito.any(),
                Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(breCheckResponse);
        Mockito.when(kotakAuthenticationClient.generateAccessToken()).thenReturn("testToken");
        UnderwritingResponse underwritingResponse = underwritingStrategy.execute(underwritingRequest);
        Assert.assertNotNull(underwritingResponse);
        Assert.assertEquals(STATUS.SUCCESS, underwritingResponse.getStatus());
    }
}
