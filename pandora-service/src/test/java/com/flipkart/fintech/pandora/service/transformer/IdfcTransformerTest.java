package com.flipkart.fintech.pandora.service.transformer;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.affordability.model.response.AddressDetailResponse;
import com.flipkart.affordability.model.userservice.UserProfile;
import com.flipkart.fintech.pandora.api.model.pl.request.AutoDisbursalRequest;
import com.flipkart.fintech.pandora.api.model.pl.request.InitialOfferGenerationRequest;
import com.flipkart.fintech.pandora.api.model.pl.request.LoanOnboardingRequest;
import com.flipkart.fintech.pandora.api.model.pl.request.PennyDropRequest;
import com.flipkart.fintech.pandora.api.model.pl.request.StartLoanRequest;
import com.flipkart.fintech.pandora.api.model.pl.request.*;
import com.flipkart.fintech.pandora.api.model.pl.response.AutoDisbursalResponse;
import com.flipkart.fintech.pandora.api.model.pl.response.SearchCkycResponse;
import com.flipkart.fintech.pandora.api.model.pl.response.*;
import com.flipkart.fintech.pandora.api.model.pl.response.VkycStatus.VkycStatusEnum;
import com.flipkart.fintech.pandora.idfc.client.request.*;
import com.flipkart.fintech.pandora.idfc.client.response.InitialOfferGenerationResponse;
import com.flipkart.fintech.pandora.idfc.client.response.LoanOnboardingResponse;
import com.flipkart.fintech.pandora.idfc.client.response.PanVerificationResponse;
import com.flipkart.fintech.pandora.idfc.client.response.PennyDropResponse;
import com.flipkart.fintech.pandora.idfc.client.response.*;
import com.flipkart.fintech.pandora.service.annotations.JsonFile;
import com.flipkart.fintech.pandora.service.client.document.response.DocumentInteractionResponse;
import com.flipkart.fintech.pandora.service.client.pl.scheme.SchemeFinder;
import com.flipkart.fintech.pandora.service.core.document.DocumentService;
import com.flipkart.fintech.pandora.service.extensions.JsonFileParameterResolver;
import com.flipkart.fintech.user.data.client.UserDataClient;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.modelmapper.ModelMapper;

import java.io.FileNotFoundException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(JsonFileParameterResolver.class)
@ExtendWith(MockitoExtension.class)
class IdfcTransformerTest {

  private IdfcTransformer idfcTransformer;

  private ObjectMapper objectMapper;

  @Mock
  private UserDataClient userDataClient;

  @Mock
  private DocumentService documentService;

  @Mock
  private SchemeFinder schemeFinder;

  @BeforeEach
  void setUp() {
    String aadhaarEncryptionKey = "Idfc4321Uidai987";
    Map<String, VkycStatusEnum> vkycStatusMap = new HashMap<>();
    vkycStatusMap.put("Maker Call Initiated - Approved", VkycStatusEnum.APPROVED);
    schemeFinder = new SchemeFinder();
    idfcTransformer = new IdfcTransformer(new ModelMapper(), userDataClient, aadhaarEncryptionKey, documentService, vkycStatusMap, schemeFinder, "");
    objectMapper = new ObjectMapper();
  }

  @AfterEach
  void tearDown() {
    idfcTransformer = null;
  }

  private <T> T readJson(String jsonFilePath, Class<T> clazz) {
    try {
      InputStream is = getClass().getClassLoader().getResourceAsStream(jsonFilePath);
      if (is == null) {
        throw new FileNotFoundException("File not found: " + jsonFilePath);
      }

      return objectMapper.readValue(is, clazz);
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }

  @Test
  void shouldTransformVerifyEmandate(@JsonFile("emandateVerificationResponse.json")IdfcEmandateVerificationResponse idfcResponse) {
    EmandateVerificationResponse emandateVerificationResponse = idfcTransformer.transformEmandateVerificationResponse(idfcResponse);
    assertNotNull(emandateVerificationResponse);
    assertEquals("retry", emandateVerificationResponse.getEMandateStatus());
  }

  @Test
  void shouldTransformPanVerificationRequest() {
    PanVerificationRequest panVerificationRequest = new PanVerificationRequest();
    panVerificationRequest.setPan("**********");
    panVerificationRequest.setApplicationId("123456");

    PanNumberVerificationRequest panNumberVerificationRequest = idfcTransformer.transformPanRequest(panVerificationRequest);
    assertNotNull(panNumberVerificationRequest);
    assertEquals("**********", panNumberVerificationRequest.getPanNumber());
    assertEquals("123456", panNumberVerificationRequest.getReqId());
  }

  @Test
  void shouldTransformPanVerificationResponse() {
    PanVerificationResponse response = new PanVerificationResponse();
    response.setResponseId("908765");
    response.setStatus("SUCCESS");
    PanUserInfo panUserInfo = new PanUserInfo();
    panUserInfo.setPanNumber("**********");
    panUserInfo.setPanStatus("Existing and Valid PAN");
    panUserInfo.setPnStatus("E");
    response.setPanUserInfo(panUserInfo);
    com.flipkart.fintech.pandora.api.model.pl.response.PanVerificationResponse panVerificationResponse = idfcTransformer.transformPanResponse(response);
    assertNotNull(panVerificationResponse);
    assertEquals("Existing and Valid PAN", panVerificationResponse.getPanStatus());
    assertEquals("SUCCESS", panVerificationResponse.getStatus());
  }

  @Test
  void shouldTransformInitialOfferGenerationRequest(@JsonFile("initialOfferGenerationRequest.json") InitialOfferGenerationRequest initialOfferGenerationRequest) {
    UserProfile profile = new UserProfile();
    profile.setPrimaryPhone("+************");
    AddressDetailResponse addressDetails = new AddressDetailResponse();
    addressDetails.setAddressLine1("abc");
    addressDetails.setCountry("india");
    addressDetails.setPincode("123456");
    addressDetails.setCity("bangalore");
    addressDetails.setState("karnataka");
    com.flipkart.fintech.pandora.idfc.client.request.InitialOfferGenerationRequest initialOfferGenerationRequest1 = idfcTransformer.transformInitialOfferGenerationRequest(initialOfferGenerationRequest);
    assertNotNull(initialOfferGenerationRequest1);
    assertEquals("q2erww", initialOfferGenerationRequest1.getReqId());
    assertEquals("**********", initialOfferGenerationRequest1.getPanNumber());
    assertEquals("9189583407", initialOfferGenerationRequest1.getMobileNo());
    assertEquals("07-01-1993", initialOfferGenerationRequest1.getDateOfBirth());
  }

  @Test
  void shouldTransformInitialOfferGenerationResponse() {
    InitialOfferGenerationResponse initialOfferGenerationResponse = readJson("initialOfferGenerationResponse.json", InitialOfferGenerationResponse.class);
    com.flipkart.fintech.pandora.api.model.pl.response.InitialOfferGenerationResponse initialOfferGenerationResponse1 = idfcTransformer.transformInitialOfferGenerationRespnose(initialOfferGenerationResponse);
    assertNotNull(initialOfferGenerationResponse1);
    assertEquals("SUBMITTED", initialOfferGenerationResponse1.getStatus());
  }

  @Test
  void shouldTransformStatusCheckRequest() {
    StatusCheckRequest statusCheckRequest = readJson("statusCheckRequest.json", StatusCheckRequest.class);
    CheckApprovalStatusRequest checkApprovalStatusRequest = idfcTransformer.transformStatusCheckRequest(statusCheckRequest);
    assertNotNull(checkApprovalStatusRequest);
    assertEquals("abcdedf", checkApprovalStatusRequest.getReqId());
  }

  @Test
  void shouldTransformCheckApprovalStatusResponseForInitialOfferGeneration() {
    CheckApprovalStatusResponse checkApprovalStatusResponse = readJson("checkApprovalStatusResponse.json", CheckApprovalStatusResponse.class);
    InitialOfferGenerationStatusResponse transformedObject = (InitialOfferGenerationStatusResponse) idfcTransformer.transformStatusResponse(checkApprovalStatusResponse);
    assertNotNull(transformedObject);
    assertEquals("APPROVE", transformedObject.getStatus());
    assertEquals("Realtime", transformedObject.getCustomerEligibilityType());
    assertEquals("9900667181", transformedObject.getCrn());
    assertEquals("10380126", transformedObject.getLoanApplicationNo());
    assertEquals("SR132332", transformedObject.getReferenceId());
    assertEquals(2, transformedObject.getOffers().size());
    assertEquals("10.5", transformedObject.getOffers().get(0).getRoi());
  }

  @Test
  void shouldTransformCheckApprovalStatusResponseForVkycStatus(@JsonFile("vkycStatusResponse.json") CheckApprovalStatusResponse vkycStatusResponse) {
    VkycStatusCheckResponse transformedObject = (VkycStatusCheckResponse) idfcTransformer.transformStatusResponse(vkycStatusResponse);
    assertNotNull(transformedObject);
    assertEquals("APPROVE", transformedObject.getStatus());
    assertEquals("9907642218", transformedObject.getCrn());
    assertEquals("105326874", transformedObject.getLoanApplicationNo());
    assertEquals("1000787059", transformedObject.getReferenceId());
    assertNotNull(transformedObject.getVkycStatus());
    assertEquals("rejected", transformedObject.getVkycStatus().getStatus());
    assertEquals("VCIP link generated - Call not initiated", transformedObject.getVkycStatus().getMessage());
  }

  @Test
  void shouldTransformCkycSearchRequest() {
    com.flipkart.fintech.pandora.api.model.pl.request.SearchCkycRequest searchCkycRequest = readJson("searchCkycRequest.json", com.flipkart.fintech.pandora.api.model.pl.request.SearchCkycRequest.class);
    com.flipkart.fintech.pandora.idfc.client.request.SearchCkycRequest searchCkycRequest1 = idfcTransformer.transformCkycSearchRequest(searchCkycRequest);
    assertNotNull(searchCkycRequest1);
    assertEquals("C", searchCkycRequest1.getSearchCkycRequestBody().getDetails().get(0).getInputIdType());
  }

  @Test
  void shouldTransformSearchCkycResponse(@JsonFile("downloadCkycResponse.json") CkycDownloadResponse ckycDownloadResponse) {
    SearchCkycResponse searchCkycResponse = idfcTransformer.transformCkycResponse(ckycDownloadResponse);
    assertNotNull(searchCkycResponse);
    assertEquals("abcdefghijklmnopqrstuvwxyzabcdefghijklmnnopqrstuvwxyz", searchCkycResponse.getKyc().getPersonalDetails().get(0).getRecordIdentifier());
    assertEquals("Urban", searchCkycResponse.getKyc().getPersonalDetails().get(0).getCKycCorAddPinType());
    assertEquals("10071234567847", searchCkycResponse.getKyc().getPersonalDetails().get(0).getCKycNumber());
    assertEquals("M", searchCkycResponse.getKyc().getPersonalDetails().get(0).getCKycGender());
    assertEquals("14-Mar-2005", searchCkycResponse.getKyc().getPersonalDetails().get(0).getCkycdob());
  }

  @Test
  @Disabled
  void shouldTransformAadhaarGenerateOtpRequest() {
    com.flipkart.fintech.pandora.api.model.pl.request.AadhaarGenerateOtpRequest aadhaarGenerateOtpRequest = readJson("aadhaarGenerateOtpRequest.json", com.flipkart.fintech.pandora.api.model.pl.request.AadhaarGenerateOtpRequest.class);
    com.flipkart.fintech.pandora.idfc.client.request.AadhaarOtpGenerationRequest aadhaarGenerateOtpRequest1 = idfcTransformer.transformAadhaarGenerateOtpRequest(aadhaarGenerateOtpRequest);
    assertNotNull(aadhaarGenerateOtpRequest1);
    assertEquals("938475610", aadhaarGenerateOtpRequest1.getGenerateOTPRequest().getUid());
    assertEquals("qwerty", aadhaarGenerateOtpRequest1.getGenerateOTPRequest().getReqId());
  }

  @Test
  void shouldTransformAadhaarGenerateOtpResponse() {
    com.flipkart.fintech.pandora.idfc.client.response.AadhaarOtpGenerationResponse aadhaarOtpGenerationResponse = readJson("aadhaarGenerateOtpResponse.json", com.flipkart.fintech.pandora.idfc.client.response.AadhaarOtpGenerationResponse.class);
    com.flipkart.fintech.pandora.api.model.pl.response.AadhaarGenerateOtpResponse aadhaarGenerateOtpResponse = idfcTransformer.transformAadhaarGenerateOtpResponse(aadhaarOtpGenerationResponse);
    assertNotNull(aadhaarGenerateOtpResponse);
    assertEquals("OTP is generated", aadhaarGenerateOtpResponse.getStatus());
    assertEquals("202109121731152810784c", aadhaarGenerateOtpResponse.getTransactionId());
  }

  @Test
  @Disabled
  void shouldTransformAadhaarValidateOtpRequest() {
    com.flipkart.fintech.pandora.api.model.pl.request.AadhaarValidateOtpRequest aadhaarValidateOtpRequest = readJson("aadhaarValidateOtpRequest.json", com.flipkart.fintech.pandora.api.model.pl.request.AadhaarValidateOtpRequest.class);
    com.flipkart.fintech.pandora.idfc.client.request.AadhaarKycDetailsRequest aadhaarOtpValidationRequest = idfcTransformer.transformAadhaarValidateOtpRequest(aadhaarValidateOtpRequest);
    assertNotNull(aadhaarOtpValidationRequest);
    assertEquals("202109121731152810784c", aadhaarOtpValidationRequest.getGetKYCDetailServiceRequest().getOtpTxn());
  }

  @Test
  void shouldTransformAadhaarValidateOtpResponse(@JsonFile("aadhaarValidateOtpResponse.json") com.flipkart.fintech.pandora.idfc.client.response.AadhaarKycDetailsResponse aadhaarKycDetailsResponse) {
    com.flipkart.fintech.pandora.api.model.pl.response.AadhaarValidateOtpResponse aadhaarValidateOtpResponse = idfcTransformer.transformAadhaarValidateOtpResponse(aadhaarKycDetailsResponse);
    assertNotNull(aadhaarValidateOtpResponse);
    assertEquals("Kanpur Nagar", aadhaarValidateOtpResponse.getKyc().getAddress().getDist());
    assertEquals("Urban", aadhaarValidateOtpResponse.getKyc().getAddress().getPinType());
    assertEquals("1997-01-22", aadhaarValidateOtpResponse.getKyc().getDob());
    assertEquals("M", aadhaarValidateOtpResponse.getKyc().getGender());
  }

  @Test
  void shouldTransformStartLoanRequest() {
    StartLoanRequest startLoanRequest = readJson("startLoanRequest.json", StartLoanRequest.class);
    com.flipkart.fintech.pandora.idfc.client.request.StartLoanRequest startLoanRequest1 = idfcTransformer.transformStartLoanRequest(startLoanRequest);
    assertNotNull(startLoanRequest1);
    assertEquals(Double.valueOf("50000"), startLoanRequest1.getUnderWriting().getLoanAmount());
    assertEquals(Double.valueOf("570000"), startLoanRequest1.getUnderWriting().getEligibleLoanAmount());
    assertEquals("HYPERVERGE", startLoanRequest1.getComparePhoto().getSource());
    assertEquals("98", startLoanRequest1.getLivenessCheck().getLivenessScore());
  }

  @Test
  void shouldTransformStartLoanResponse() {
    StartLoanResponse startLoanResponse = readJson("startLoanResponse.json", StartLoanResponse.class);
    GenericStatusResponse genericStatusResponse = idfcTransformer.transformStartLoanResponse(startLoanResponse);
    assertNotNull(genericStatusResponse);
  }

  @Test
  void shouldTransformOfflineDataUploadRequest(@JsonFile("offlineDataUpload.json") OfflineDataUploadRequest offlineDataUploadRequest) {
    when(documentService.getDocument(any())).thenReturn(DocumentInteractionResponse.fromDocumentIdAndDocument("directory/documentId", "documentContent".getBytes()));
    OfflineUploadDataRequest offlineUploadDataRequest = idfcTransformer.transformOfflineDataUploadRequest(offlineDataUploadRequest);
    assertNotNull(offlineUploadDataRequest);
    assertEquals("ZG9jdW1lbnRDb250ZW50", offlineUploadDataRequest.getDocuments().get(0).getDocumentContent());
    assertEquals("KYC", offlineUploadDataRequest.getDocuments().get(0).getType());
    assertEquals("png", offlineUploadDataRequest.getDocuments().get(0).getFileExtension());
    assertEquals("736464", offlineUploadDataRequest.getLoanId());
  }

  @Test
  void shouldTransformOfflineDataUploadResponse() {
    OfflineDataUploadResponse offlineDataUploadResponse = readJson("offlineDataUploadResponse.json", OfflineDataUploadResponse.class);
    GenericStatusResponse genericStatusResponse = idfcTransformer.transformUploadDataResponse(offlineDataUploadResponse);
    assertNotNull(genericStatusResponse);
    assertEquals("success", genericStatusResponse.getStatus());
  }

  @Test
  void shouldTransformPositiveConfirmationRequest() {
    StatusCheckRequest statusCheckRequest = readJson("positiveConfirmationRequest.json", StatusCheckRequest.class);
    PositiveConfirmationStatusRequest newRequest = idfcTransformer.transformPositiveConfirmationRequest(statusCheckRequest);
    assertNotNull(newRequest);
    assertEquals("qwerty", newRequest.getReqId());
    assertEquals("SMONEY", newRequest.getSource());
  }

  @Test
  void shouldTransformPositiveConfirmationResponse() {
    PositiveConfirmationStatusResponse clientResponse = readJson("positiveConfirmationStatusResponse.json", PositiveConfirmationStatusResponse.class);
    PositiveConfirmationResponse positiveConfirmationResponse = idfcTransformer.transformPositiveConfirmationResponse(clientResponse);
    assertNotNull(positiveConfirmationResponse);
    assertEquals("INPROGRESS", positiveConfirmationResponse.getPositiveConfirmation());
  }

  @Test
  void shouldTransformPennyDropRequest() {
    PennyDropRequest pennyDropRequest = readJson("pennyDropRequest.json", PennyDropRequest.class);
    com.flipkart.fintech.pandora.idfc.client.request.PennyDropRequest pennyDropRequest1 = idfcTransformer.transformPennyDropRequest(pennyDropRequest);
    assertNotNull(pennyDropRequest1);
    assertEquals("qwerty", pennyDropRequest1.getReqId());
    assertEquals("SMONEY", pennyDropRequest1.getSource());
    assertEquals("**************", pennyDropRequest1.getCustBankAccNo());
  }

  @Test
  void shouldTransformPennyDropResponse() {
    PennyDropResponse response = readJson("pennyDropResponse.json", PennyDropResponse.class);
    com.flipkart.fintech.pandora.api.model.pl.response.PennyDropResponse pennyDropResponse = idfcTransformer.transformPennyDropResponse(response);
    assertNotNull(pennyDropResponse);
    assertEquals("************", pennyDropResponse.getTransactionId());
    assertEquals("S", pennyDropResponse.getStatus());
  }

  @Test
  void shouldTransformKfsOtpRequest(@JsonFile("kfsOtpRequest.json") KfsGenerateOtpRequest amsRequest) {
    UserProfile profile = new UserProfile();
    profile.setPrimaryPhone("+************");
    com.flipkart.fintech.pandora.idfc.client.request.KfsOtpGenerationRequest clientRequest = idfcTransformer.transformKfsOtpRequest(amsRequest);
    assertNotNull(clientRequest);
    assertEquals("JUSPAY_UAT", clientRequest.getCallId());
    assertEquals(8877901234L, clientRequest.getSendSms().getMobileNumber());
  }

  @Test
  void shouldTransformKfsOtpResponse() {
    com.flipkart.fintech.pandora.idfc.client.response.KfsOtpGenerationResponse clientResponse = readJson("kfsOtpResponse.json", com.flipkart.fintech.pandora.idfc.client.response.KfsOtpGenerationResponse.class);
    KfsGenerateOtpResponse amsResponse = idfcTransformer.transformKfsOtpResponse(clientResponse);
    assertNotNull(amsResponse);
    assertEquals("OK", amsResponse.getStatus());
  }

  @Test
  void shouldTransformKfsSubmitOtpRequest() {
    KfsSubmitOtpRequest amsRequest = readJson("kfsOtpSubmitRequest.json", com.flipkart.fintech.pandora.api.model.pl.request.KfsSubmitOtpRequest.class);
    KfsOtpValidationRequest clientRequest = idfcTransformer.transformKfsSubmitOtpRequest(amsRequest);
    assertNotNull(clientRequest);
    assertEquals("JUSPAY_UAT", clientRequest.getCallId());
    assertEquals("1234", clientRequest.getOtpValue());
  }

  @Test
  void shouldTransformKfsSubmitOtpResponse() {
    KfsOtpValidationResponse clientResponse = readJson("kfsSubmitOtpResponse.json", KfsOtpValidationResponse.class);
    KfsSubmitOtpResponse amsResponse = idfcTransformer.transformKfsSubmitOtpResponse(clientResponse);
    assertNotNull(amsResponse);
    assertEquals("SUCCESS", amsResponse.getStatus());
  }

  @Test
  void shouldTransformLoanOnboardingRequest(@JsonFile("loanOnboardingRequest.json") LoanOnboardingRequest amsRequest) {
    AddressDetailResponse addressDetails = new AddressDetailResponse();
    addressDetails.setAddressLine1("abc");
    addressDetails.setCountry("india");
    addressDetails.setPincode("123456");
    addressDetails.setCity("bangalore");
    addressDetails.setState("karnataka");
    UserProfile user = new UserProfile();
    user.setPrimaryPhone("+************");
    com.flipkart.fintech.pandora.idfc.client.request.LoanOnboardingRequest idfcRequest = idfcTransformer.transformLoanOnboardingRequest(amsRequest);
    assertNotNull(idfcRequest);
    assertEquals("Y", idfcRequest.getApplication().getSection().getCustomerConsents().getPanVerifyConsent());
    assertEquals("Y", idfcRequest.getApplication().getSection().getCustomerConsents().getBureauPullConsent());
    assertEquals("CO-EMI", idfcRequest.getApplication().getSection().getRequest().getRequestType());
    assertEquals("SMONEY", idfcRequest.getApplication().getSection().getRequest().getSourceSystem());
    assertEquals("R", idfcRequest.getApplication().getSection().getLoanDetails().getRateEMIFlag());
    assertEquals("1990-04-14", idfcRequest.getApplication().getSection().getCustomerDetails().get(0).getDateOfBirth());
    assertEquals("33", idfcRequest.getApplication().getSection().getCustomerDetails().get(0).getFieldAge());
    assertEquals(amsRequest.getLoanAmount(), idfcRequest.getApplication().getSection().getLoanDetails().getLoanAmount());
    assertEquals("2023-11-23T11:24:11.585", idfcRequest.getApplication().getSection().getCustomerConsents().getPanVerifyConsentTimestamp());
    assertEquals("Y", idfcRequest.getApplication().getSection().getCustomerConsents().getPanVerifyConsent());
  }

  @Test
  void shouldTransformLoanOnboardingResponse(@JsonFile("loanOnboardingResponse.json") LoanOnboardingResponse idfcResponse) {
    com.flipkart.fintech.pandora.api.model.pl.response.LoanOnboardingResponse amsResponse = idfcTransformer.transformLoanOnboardingResponse(idfcResponse);
    assertNotNull(amsResponse);
    assertEquals(idfcResponse.getResponse().getKfsDetails().get(0).getLoanAmt(), amsResponse.getResult().getResponse().getKfsDetails().get(0).getLoanAmt());
    assertEquals(idfcResponse.getResponse().getCharges().get(0).getChargeAmount(), amsResponse.getResult().getResponse().getKfsDetails().get(0).getProcessFee());
  }

  @Test
  void shouldTransformAutoDisbursalRequest() {
    AutoDisbursalRequest amsRequest = readJson("autoDisbursalRequest.json", AutoDisbursalRequest.class);
    com.flipkart.fintech.pandora.idfc.client.request.AutoDisbursalRequest idfcRequest = idfcTransformer.transformAutoDisbursalRequest(amsRequest);
    assertNotNull(idfcRequest);
    assertEquals("105326884", idfcRequest.getLoanId());
    assertNotNull(idfcRequest.getConsent());
    assertEquals("Y", idfcRequest.getConsent().getTncConsent());
    assertEquals("2023-11-24T08:23:50.276", idfcRequest.getConsent().getTncConsentTimestamp());
  }

  @Test
  void shouldTransformAutoDisbursalResponse(@JsonFile("autoDisbursalResponse.json") com.flipkart.fintech.pandora.idfc.client.response.AutoDisbursalResponse idfcResponse) {
    AutoDisbursalResponse amsResponse = idfcTransformer.transformAutoDisbursalResponse(idfcResponse);
    assertNotNull(amsResponse);
    assertEquals("SUCCESS", amsResponse.getMetadata().getStatus());
    assertEquals("12345", amsResponse.getResourceData().get(0).getEntityReqId());
    assertEquals("IDFC", amsResponse.getResourceData().get(0).getKfsDetails().get(0).getBankName());
    assertEquals("230", amsResponse.getResourceData().get(0).getKfsDetails().get(0).getProcessFee());
    assertEquals("20", amsResponse.getResourceData().get(0).getRepaySchedule().get(0).getInstallmentNumber());
    assertEquals("2023-09-08", amsResponse.getResourceData().get(0).getRepaySchedule().get(0).getDueDate());
  }

  @Test
  void shouldTransformIfscRequest() {
    IfscRequest amsRequest = readJson("ifscRequest.json", IfscRequest.class);
    IfscDetailsRequest idfcRequest = idfcTransformer.transformIfscRequest(amsRequest);
    assertNotNull(idfcRequest);
    assertEquals("HDFC0000687", idfcRequest.getIFSCCodeC());
    assertEquals("Bank_Detail__c", idfcRequest.getAttributes().getType());
  }

  @Test
  void shoudTransformIfscResponse() {
    IfscDetailsResponse clientResponse = readJson("ifscResponse.json", IfscDetailsResponse.class);
    IfscResponse amsResponse = idfcTransformer.transformIfscResponse(clientResponse);
    assertNotNull(amsResponse);
    assertEquals("5430", amsResponse.getAvailableModes().getDebitCard());
    assertNull(amsResponse.getAvailableModes().getNetBanking());
  }
}