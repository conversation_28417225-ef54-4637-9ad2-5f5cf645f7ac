package com.flipkart.fintech.pandora.service.transformer;

import com.flipkart.affordability.model.response.AddressDetailResponse;
import com.flipkart.fintech.user.data.models.AddressData;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.modelmapper.ModelMapper;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;

class UserDataTransformerTest {

    private UserDataTransformer userDataTransformer;

    @BeforeEach
    void setUp() {
        userDataTransformer = new UserDataTransformer(new ModelMapper());
    }

    @Test
    public void expectToMapAddressDataToAddressDetailResponse() {
        // given
        AddressData addressData = AddressData.builder()
            .country("India")
            .pincode("560103")
            .addressLine1("Address Line 1")
            .addressLine2("Address Line 2")
            .city("Bangalore")
            .phone("1234567890")
            .state("Karnataka")
            .name("Name")
            .landmark("Landmark")
            .locationTypeTag("Location Type Tag")
            .stateCode("KA")
            .build();

        // when
        AddressDetailResponse addressDetailResponse = userDataTransformer.transformAddressDetailResponse(addressData);

        // then
        assertThat(addressDetailResponse.getCountry()).isEqualTo("India");
        assertThat(addressDetailResponse.getPincode()).isEqualTo("560103");
        assertThat(addressDetailResponse.getAddressLine1()).isEqualTo("Address Line 1");
        assertThat(addressDetailResponse.getAddressLine2()).isEqualTo("Address Line 2");
        assertThat(addressDetailResponse.getCity()).isEqualTo("Bangalore");
        assertThat(addressDetailResponse.getPhone()).isEqualTo("1234567890");
        assertThat(addressDetailResponse.getState()).isEqualTo("Karnataka");
        assertThat(addressDetailResponse.getName()).isEqualTo("Name");
        assertThat(addressDetailResponse.getLandmark()).isEqualTo("Landmark");
        assertThat(addressDetailResponse.getLocationTypeTag()).isEqualTo("Location Type Tag");
        assertThat(addressDetailResponse.getStateCode()).isEqualTo("KA");
    }

}