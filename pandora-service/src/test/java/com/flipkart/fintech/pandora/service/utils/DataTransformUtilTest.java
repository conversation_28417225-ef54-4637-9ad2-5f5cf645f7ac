package com.flipkart.fintech.pandora.service.utils;

import com.flipkart.fintech.pandora.service.application.configuration.BucketingConfiguration;
import static org.junit.Assert.assertEquals;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by su<PERSON><PERSON><PERSON>.b on 19/07/18
 */
public class DataTransformUtilTest {

    BucketingConfiguration bucketingConfiguration;
    Map surrogates;

    @BeforeEach
    public void setUp() throws Exception {
        bucketingConfiguration = new BucketingConfiguration();

        bucketingConfiguration.setAccountVintage(Arrays.asList(90.0, 180.0, 360.0));
        bucketingConfiguration.setDaysSinceOrder(Arrays.asList(180.0));
        bucketingConfiguration.setOrdersFulfilled(Arrays.asList(3.0,5.0));
        bucketingConfiguration.setGmvTillDate(Arrays.asList(10000.0,20000.0));
        bucketingConfiguration.setPercentageReturned(Arrays.asList(25.0,33.0));
        bucketingConfiguration.setOrdersAtCommonAddress(Arrays.asList(2.0,3.0));
        bucketingConfiguration.setEnabled(true);

        surrogates = new HashMap();

    }

    @Test
    public  void testAccountVintage() {

        surrogates.put("account_vintage",0);
        Map bucketisedSurrogates = DataTransformUtil.bucketizeSurrogates( bucketingConfiguration, surrogates);
        assertEquals( 1, bucketisedSurrogates.get("account_vintage"));

        surrogates.put("account_vintage",80);
        bucketisedSurrogates = DataTransformUtil.bucketizeSurrogates( bucketingConfiguration, surrogates);
        assertEquals( 1, bucketisedSurrogates.get("account_vintage"));

        surrogates.put("account_vintage",89);
        bucketisedSurrogates = DataTransformUtil.bucketizeSurrogates( bucketingConfiguration, surrogates);
        assertEquals( 1, bucketisedSurrogates.get("account_vintage"));

        surrogates.put("account_vintage",90);
        bucketisedSurrogates = DataTransformUtil.bucketizeSurrogates( bucketingConfiguration, surrogates);
        assertEquals( 2, bucketisedSurrogates.get("account_vintage"));

        surrogates.put("account_vintage",100);
        bucketisedSurrogates = DataTransformUtil.bucketizeSurrogates( bucketingConfiguration, surrogates);
        assertEquals( 2, bucketisedSurrogates.get("account_vintage"));

        surrogates.put("account_vintage",180);
        bucketisedSurrogates = DataTransformUtil.bucketizeSurrogates( bucketingConfiguration, surrogates);
        assertEquals( 3, bucketisedSurrogates.get("account_vintage"));

        surrogates.put("account_vintage",200);
        bucketisedSurrogates = DataTransformUtil.bucketizeSurrogates( bucketingConfiguration, surrogates);
        assertEquals( 3, bucketisedSurrogates.get("account_vintage"));

        surrogates.put("account_vintage",360);
        bucketisedSurrogates = DataTransformUtil.bucketizeSurrogates( bucketingConfiguration, surrogates);
        assertEquals( 4, bucketisedSurrogates.get("account_vintage"));

        surrogates.put("account_vintage",400);
        bucketisedSurrogates = DataTransformUtil.bucketizeSurrogates( bucketingConfiguration, surrogates);
        assertEquals( 4, bucketisedSurrogates.get("account_vintage"));

        surrogates.put("account_vintage",1000);
        bucketisedSurrogates = DataTransformUtil.bucketizeSurrogates( bucketingConfiguration, surrogates);
        assertEquals( 4, bucketisedSurrogates.get("account_vintage"));

    }

    @Test
    public void testDaysSinceOrder() {

        surrogates.put("days_since_last_order", 0);
        Map bucketisedSurrogates = DataTransformUtil.bucketizeSurrogates(bucketingConfiguration, surrogates);
        assertEquals(1, bucketisedSurrogates.get("days_since_last_order"));

        surrogates.put("days_since_last_order", 20);
        bucketisedSurrogates = DataTransformUtil.bucketizeSurrogates(bucketingConfiguration, surrogates);
        assertEquals(1, bucketisedSurrogates.get("days_since_last_order"));

        surrogates.put("days_since_last_order", 180);
        bucketisedSurrogates = DataTransformUtil.bucketizeSurrogates(bucketingConfiguration, surrogates);
        assertEquals(2, bucketisedSurrogates.get("days_since_last_order"));

        surrogates.put("days_since_last_order", 181);
        bucketisedSurrogates = DataTransformUtil.bucketizeSurrogates(bucketingConfiguration, surrogates);
        assertEquals(2, bucketisedSurrogates.get("days_since_last_order"));

        surrogates.put("days_since_last_order", 200);
        bucketisedSurrogates = DataTransformUtil.bucketizeSurrogates(bucketingConfiguration, surrogates);
        assertEquals(2, bucketisedSurrogates.get("days_since_last_order"));
    }


    @Test
    public  void testOrdersFulfilled() {

        surrogates.put("no_of_orders_fulfilled",1);
        Map bucketisedSurrogates = DataTransformUtil.bucketizeSurrogates( bucketingConfiguration, surrogates);
        assertEquals( 1, bucketisedSurrogates.get("no_of_orders_fulfilled"));

        surrogates.put("no_of_orders_fulfilled",2);
        bucketisedSurrogates = DataTransformUtil.bucketizeSurrogates( bucketingConfiguration, surrogates);
        assertEquals( 1, bucketisedSurrogates.get("no_of_orders_fulfilled"));

        surrogates.put("no_of_orders_fulfilled",3);
        bucketisedSurrogates = DataTransformUtil.bucketizeSurrogates( bucketingConfiguration, surrogates);
        assertEquals( 2, bucketisedSurrogates.get("no_of_orders_fulfilled"));

        surrogates.put("no_of_orders_fulfilled",4);
        bucketisedSurrogates = DataTransformUtil.bucketizeSurrogates( bucketingConfiguration, surrogates);
        assertEquals( 2, bucketisedSurrogates.get("no_of_orders_fulfilled"));

        surrogates.put("no_of_orders_fulfilled",5);
        bucketisedSurrogates = DataTransformUtil.bucketizeSurrogates( bucketingConfiguration, surrogates);
        assertEquals( 3, bucketisedSurrogates.get("no_of_orders_fulfilled"));

        surrogates.put("no_of_orders_fulfilled",6);
        bucketisedSurrogates = DataTransformUtil.bucketizeSurrogates( bucketingConfiguration, surrogates);
        assertEquals( 3, bucketisedSurrogates.get("no_of_orders_fulfilled"));

        surrogates.put("no_of_orders_fulfilled",8);
        bucketisedSurrogates = DataTransformUtil.bucketizeSurrogates( bucketingConfiguration, surrogates);
        assertEquals( 3, bucketisedSurrogates.get("no_of_orders_fulfilled"));
    }


    @Test
    public void testGmvTillDate() {

        surrogates.put("gmv_till_date", Double.valueOf(0));
        Map bucketisedSurrogates = DataTransformUtil.bucketizeSurrogates(bucketingConfiguration, surrogates);
        assertEquals(1, bucketisedSurrogates.get("gmv_till_date"));

        surrogates.put("gmv_till_date", Double.valueOf(5000));
        bucketisedSurrogates = DataTransformUtil.bucketizeSurrogates(bucketingConfiguration, surrogates);
        assertEquals(1, bucketisedSurrogates.get("gmv_till_date"));

        surrogates.put("gmv_till_date", Double.valueOf(9999));
        bucketisedSurrogates = DataTransformUtil.bucketizeSurrogates(bucketingConfiguration, surrogates);
        assertEquals(1, bucketisedSurrogates.get("gmv_till_date"));

        surrogates.put("gmv_till_date", Double.valueOf(10000));
        bucketisedSurrogates = DataTransformUtil.bucketizeSurrogates(bucketingConfiguration, surrogates);
        assertEquals(2, bucketisedSurrogates.get("gmv_till_date"));

        surrogates.put("gmv_till_date", Double.valueOf(15000));
        bucketisedSurrogates = DataTransformUtil.bucketizeSurrogates(bucketingConfiguration, surrogates);
        assertEquals(2, bucketisedSurrogates.get("gmv_till_date"));

        surrogates.put("gmv_till_date", Double.valueOf(19999));
        bucketisedSurrogates = DataTransformUtil.bucketizeSurrogates(bucketingConfiguration, surrogates);
        assertEquals(2, bucketisedSurrogates.get("gmv_till_date"));

        surrogates.put("gmv_till_date", Double.valueOf(20000));
        bucketisedSurrogates = DataTransformUtil.bucketizeSurrogates(bucketingConfiguration, surrogates);
        assertEquals(3, bucketisedSurrogates.get("gmv_till_date"));

        surrogates.put("gmv_till_date", Double.valueOf(25000));
        bucketisedSurrogates = DataTransformUtil.bucketizeSurrogates(bucketingConfiguration, surrogates);
        assertEquals(3, bucketisedSurrogates.get("gmv_till_date"));

    }

    @Test
    public void testPercentageReturned() {

        surrogates.put("percentage_of_orders_returned_or_cancelled", Double.valueOf(0));
        Map bucketisedSurrogates = DataTransformUtil.bucketizeSurrogates(bucketingConfiguration, surrogates);
        assertEquals(1, bucketisedSurrogates.get("percentage_of_orders_returned_or_cancelled"));

        surrogates.put("percentage_of_orders_returned_or_cancelled", Double.valueOf(15.3));
        bucketisedSurrogates = DataTransformUtil.bucketizeSurrogates(bucketingConfiguration, surrogates);
        assertEquals(1, bucketisedSurrogates.get("percentage_of_orders_returned_or_cancelled"));

        surrogates.put("percentage_of_orders_returned_or_cancelled", Double.valueOf(24.99));
        bucketisedSurrogates = DataTransformUtil.bucketizeSurrogates(bucketingConfiguration, surrogates);
        assertEquals(1, bucketisedSurrogates.get("percentage_of_orders_returned_or_cancelled"));


        surrogates.put("percentage_of_orders_returned_or_cancelled", Double.valueOf(25.00));
        bucketisedSurrogates = DataTransformUtil.bucketizeSurrogates(bucketingConfiguration, surrogates);
        assertEquals(2, bucketisedSurrogates.get("percentage_of_orders_returned_or_cancelled"));

        surrogates.put("percentage_of_orders_returned_or_cancelled", Double.valueOf(30.99));
        bucketisedSurrogates = DataTransformUtil.bucketizeSurrogates(bucketingConfiguration, surrogates);
        assertEquals(2, bucketisedSurrogates.get("percentage_of_orders_returned_or_cancelled"));

        surrogates.put("percentage_of_orders_returned_or_cancelled", Double.valueOf(32.99));
        bucketisedSurrogates = DataTransformUtil.bucketizeSurrogates(bucketingConfiguration, surrogates);
        assertEquals(2, bucketisedSurrogates.get("percentage_of_orders_returned_or_cancelled"));

        surrogates.put("percentage_of_orders_returned_or_cancelled", Double.valueOf(33.00));
        bucketisedSurrogates = DataTransformUtil.bucketizeSurrogates(bucketingConfiguration, surrogates);
        assertEquals(3, bucketisedSurrogates.get("percentage_of_orders_returned_or_cancelled"));

        surrogates.put("percentage_of_orders_returned_or_cancelled", Double.valueOf(34.5));
        bucketisedSurrogates = DataTransformUtil.bucketizeSurrogates(bucketingConfiguration, surrogates);
        assertEquals(3, bucketisedSurrogates.get("percentage_of_orders_returned_or_cancelled"));

        surrogates.put("percentage_of_orders_returned_or_cancelled", Double.valueOf(58.99));
        bucketisedSurrogates = DataTransformUtil.bucketizeSurrogates(bucketingConfiguration, surrogates);
        assertEquals(3, bucketisedSurrogates.get("percentage_of_orders_returned_or_cancelled"));

    }

    @Test
    public void testOrdersAtCommonAddress() {

        surrogates.put("no_of_orders_cmn_addr_12_mnths", 0);
        Map bucketisedSurrogates = DataTransformUtil.bucketizeSurrogates(bucketingConfiguration, surrogates);
        assertEquals(1, bucketisedSurrogates.get("no_of_orders_cmn_addr_12_mnths"));

        surrogates.put("no_of_orders_cmn_addr_12_mnths", 1);
        bucketisedSurrogates = DataTransformUtil.bucketizeSurrogates(bucketingConfiguration, surrogates);
        assertEquals(1, bucketisedSurrogates.get("no_of_orders_cmn_addr_12_mnths"));

        surrogates.put("no_of_orders_cmn_addr_12_mnths", 2);
        bucketisedSurrogates = DataTransformUtil.bucketizeSurrogates(bucketingConfiguration, surrogates);
        assertEquals(2, bucketisedSurrogates.get("no_of_orders_cmn_addr_12_mnths"));

        surrogates.put("no_of_orders_cmn_addr_12_mnths", 3);
        bucketisedSurrogates = DataTransformUtil.bucketizeSurrogates(bucketingConfiguration, surrogates);
        assertEquals(3, bucketisedSurrogates.get("no_of_orders_cmn_addr_12_mnths"));

        surrogates.put("no_of_orders_cmn_addr_12_mnths", 4);
        bucketisedSurrogates = DataTransformUtil.bucketizeSurrogates(bucketingConfiguration, surrogates);
        assertEquals(3, bucketisedSurrogates.get("no_of_orders_cmn_addr_12_mnths"));

        surrogates.put("no_of_orders_cmn_addr_12_mnths", 5);
        bucketisedSurrogates = DataTransformUtil.bucketizeSurrogates(bucketingConfiguration, surrogates);
        assertEquals(3, bucketisedSurrogates.get("no_of_orders_cmn_addr_12_mnths"));
    }

    @Test
    public void testPrepaidOrders() {

        surrogates.put("number_of_prepaid_orders", 0);
        Map bucketisedSurrogates = DataTransformUtil.bucketizeSurrogates(bucketingConfiguration, surrogates);
        assertEquals(1, bucketisedSurrogates.get("number_of_prepaid_orders"));

        surrogates.put("number_of_prepaid_orders", 1);
        bucketisedSurrogates = DataTransformUtil.bucketizeSurrogates(bucketingConfiguration, surrogates);
        assertEquals(2, bucketisedSurrogates.get("number_of_prepaid_orders"));

        surrogates.put("number_of_prepaid_orders", 2);
        bucketisedSurrogates = DataTransformUtil.bucketizeSurrogates(bucketingConfiguration, surrogates);
        assertEquals(2, bucketisedSurrogates.get("number_of_prepaid_orders"));

    }

    @Test
    public void testHighestGmvInMonth() {

        surrogates.put("highest_gmv_in_a_month", Double.valueOf(1540.0));
        Map bucketisedSurrogates = DataTransformUtil.bucketizeSurrogates(bucketingConfiguration, surrogates);
        assertEquals(BigDecimal.valueOf(2000.0), bucketisedSurrogates.get("highest_gmv_in_a_month"));

        surrogates.put("highest_gmv_in_a_month", Double.valueOf(1980.0));
        bucketisedSurrogates = DataTransformUtil.bucketizeSurrogates(bucketingConfiguration, surrogates);
        assertEquals(BigDecimal.valueOf(2000.0), bucketisedSurrogates.get("highest_gmv_in_a_month"));

        surrogates.put("highest_gmv_in_a_month", Double.valueOf(2200.0));
        bucketisedSurrogates = DataTransformUtil.bucketizeSurrogates(bucketingConfiguration, surrogates);
        assertEquals(BigDecimal.valueOf(2000.0), bucketisedSurrogates.get("highest_gmv_in_a_month"));

        surrogates.put("highest_gmv_in_a_month", Double.valueOf(5500.0));
        bucketisedSurrogates = DataTransformUtil.bucketizeSurrogates(bucketingConfiguration, surrogates);
        assertEquals(BigDecimal.valueOf(6000.0), bucketisedSurrogates.get("highest_gmv_in_a_month"));

        surrogates.put("highest_gmv_in_a_month", Double.valueOf(5800.0));
        bucketisedSurrogates = DataTransformUtil.bucketizeSurrogates(bucketingConfiguration, surrogates);
        assertEquals(BigDecimal.valueOf(6000.0), bucketisedSurrogates.get("highest_gmv_in_a_month"));

        surrogates.put("highest_gmv_in_a_month", Double.valueOf(55800.0));
        bucketisedSurrogates = DataTransformUtil.bucketizeSurrogates(bucketingConfiguration, surrogates);
        assertEquals(BigDecimal.valueOf(56000.0), bucketisedSurrogates.get("highest_gmv_in_a_month"));

        surrogates.put("second_highest_gmv_in_a_month", Double.valueOf(185200.0));
        bucketisedSurrogates = DataTransformUtil.bucketizeSurrogates(bucketingConfiguration, surrogates);
        assertEquals(BigDecimal.valueOf(185000.0), bucketisedSurrogates.get("second_highest_gmv_in_a_month"));


        surrogates.put("second_highest_gmv_in_a_month", Double.valueOf(55200.0));
        bucketisedSurrogates = DataTransformUtil.bucketizeSurrogates(bucketingConfiguration, surrogates);
        assertEquals(BigDecimal.valueOf(55000.0), bucketisedSurrogates.get("second_highest_gmv_in_a_month"));

        surrogates.put("second_highest_gmv_in_a_month", Double.valueOf(900.0));
        bucketisedSurrogates = DataTransformUtil.bucketizeSurrogates(bucketingConfiguration, surrogates);
        assertEquals(BigDecimal.valueOf(1000.0), bucketisedSurrogates.get("second_highest_gmv_in_a_month"));

        surrogates.put("second_highest_gmv_in_a_month", Double.valueOf(200.0));
        bucketisedSurrogates = DataTransformUtil.bucketizeSurrogates(bucketingConfiguration, surrogates);
        assertEquals(BigDecimal.valueOf(0.0), bucketisedSurrogates.get("second_highest_gmv_in_a_month"));

    }

    @Test
    public void testFlag() {

        bucketingConfiguration.setEnabled(false);

        surrogates.put("account_vintage",200);
        Map bucketisedSurrogates = DataTransformUtil.bucketizeSurrogates( bucketingConfiguration, surrogates);
        assertEquals( 200, bucketisedSurrogates.get("account_vintage"));
    }
}
