package com.flipkart.fintech.pandora.service.utils;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class DateUtilTest {

    @Test
    void getFormattedDateTime() {
        String requestDate = "23/12/1999";
        String expectedDate = "1999-12-23";
        String responseDate = DateUtil.getFormattedDateTime(requestDate, DateUtil.DDMMYYYY_SLASH_DELIMETER_FORMATTER, DateUtil.YYYYMMDD_FORMATTER);
        System.out.println(responseDate);
        Assertions.assertEquals(expectedDate, responseDate);
    }
}