package com.flipkart.fintech.pandora.service.utils;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static com.flipkart.fintech.pandora.service.utils.EbcConstants.IDFC_NON_SHARED_IV;
import static org.junit.Assert.assertEquals;

public class EncryptionUtilTest {

    EncryptionUtil encryptionUtil;

    @BeforeEach
    public void setUp() {
        encryptionUtil = new EncryptionUtil();
    }

    @Test
    public void testEncryptWithAesCS5() throws Exception {
        String encryptedAccId = encryptionUtil.encryptWithAesCS5("ACC2DEDB3BC47054F818FAA8E680072E011R", "03B1CE01ED4D5FA1ABBDCD728C0BE3F2", IDFC_NON_SHARED_IV);
        assertEquals("jhI5nQdyb1+OEjmdB3JvXw0j+qDns1Q6PJx1PjTvV+9xpDHrjNjgDlolcACCvbczTvm4QtFLsqZmQlmTP/07Jw==", encryptedAccId);
    }

    @Test
    public void testDecryptWithAesCS5() throws Exception {
        String decryptedAccId = encryptionUtil.decryptWithAesCS5("jhI5nQdyb1+OEjmdB3JvXw0j+qDns1Q6PJx1PjTvV+9xpDHrjNjgDlolcACCvbczTvm4QtFLsqZmQlmTP/07Jw==", "03B1CE01ED4D5FA1ABBDCD728C0BE3F2");
        assertEquals(decryptedAccId, "ACC2DEDB3BC47054F818FAA8E680072E011R");
    }
}
