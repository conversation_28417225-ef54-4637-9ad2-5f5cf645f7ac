package com.flipkart.fintech.pandora.service.utils;

import org.junit.Assert;
import org.junit.Test;

public class IPAddressParserTest {

    @Test
    public void test() {
        Assert.assertEquals("*************", IPAddressParser.parseIpAddress("*************, *************").orElse(null));
        Assert.assertEquals("*************", IPAddressParser.parseIpAddress("*************,*************").orElse(null));
        Assert.assertEquals("*************", IPAddressParser.parseIpAddress(" *************, ************* ").orElse(null));
        Assert.assertEquals("*************", IPAddressParser.parseIpAddress(" *************, *************").orElse(null));
        Assert.assertEquals("*************", IPAddressParser.parseIpAddress("*************, *************").orElse(null));
        Assert.assertEquals("**************", IPAddressParser.parseIpAddress("*************, **************").orElse(null));
        Assert.assertEquals("*************", IPAddressParser.parseIpAddress("*************, *************").orElse(null));
        Assert.assertEquals("**************", IPAddressParser.parseIpAddress("**************").orElse(null));
        Assert.assertNull(IPAddressParser.parseIpAddress("127.0.0.1").orElse(null));
        Assert.assertNull(IPAddressParser.parseIpAddress("*************, *************").orElse(null));
        Assert.assertNull(IPAddressParser.parseIpAddress("*************, **************").orElse(null));
        Assert.assertNull(IPAddressParser.parseIpAddress("**************").orElse(null));
        Assert.assertEquals("2001:0db8:85a3:0000:0000:8a2e:0370:7334", IPAddressParser.parseIpAddress("2001:0db8:85a3:0000:0000:8a2e:0370:7334").orElse(null));
        Assert.assertNull(IPAddressParser.parseIpAddress("::1").orElse(null));
        Assert.assertEquals("***********", IPAddressParser.parseIpAddress("***********, **************,***********,************", "preprod").orElse(null));
        Assert.assertNull("***********", IPAddressParser.parseIpAddress("***********, **************,***********,************", "prod").orElse(null));
    }

}