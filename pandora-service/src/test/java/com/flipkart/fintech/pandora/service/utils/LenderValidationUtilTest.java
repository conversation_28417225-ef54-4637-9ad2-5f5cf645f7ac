package com.flipkart.fintech.pandora.service.utils;

import com.flipkart.fintech.pandora.service.exception.PandoraException;
import com.flipkart.kloud.config.DynamicBucket;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.when;


import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 27/04/21.
 */
public class LenderValidationUtilTest {

    LenderValidator lenderValidator;

    @Mock
    private DynamicBucket dynamicBucket;
    private List<String>  activeLenders = new ArrayList<>();

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        lenderValidator = new LenderValidator(dynamicBucket);
        activeLenders.add("IDFC");
        when(dynamicBucket.getStringArray("activeLenders")).thenReturn(activeLenders);
    }

    @Test
    public void isLenderValid() {
        lenderValidator.validateLender("IDFC");
    }

    @Test
    public void isLenderInvalid() {

        assertThrows(PandoraException.class, () -> lenderValidator.validateLender("XYZ"));
    }
}
