{"ekyc_approved_vkyc_in_progress_without_income": {"GetKycStatusResponse": {"SubHeader": {"requestUUID": "d71c3427-93b0-4b7d-9803-76da5c", "serviceRequestId": "AX.GEN.ICC.GET.KYC.STATUS", "serviceRequestVersion": "1.0", "channelId": "XXX"}, "GetKycStatusResponseBody": {"data": {"kyc": {"eKyc": {"lastUpdateTimestamp": {"epochMillis": 1231241241242}, "expirationTimestamp": {"epochMillis": 1231241241245}, "approvedStateContext": {}}, "vKyc": {"lastUpdateTimestamp": {"epochMillis": 1231241241242}, "expirationTimestamp": {"epochMillis": 1231241241245}, "inProgressStateContext": {}}, "offlineKyc": {"lastUpdateTimestamp": {"epochMillis": 1231241241242}, "notStartedStateContext": {}}, "state": "IN_PROGRESS"}}}}}, "ekyc_approved_vkyc_awaiting_approval_without_income": {"GetKycStatusResponse": {"SubHeader": {"requestUUID": "d71c3427-93b0-4b7d-9803-76da5c", "serviceRequestId": "AX.GEN.ICC.GET.KYC.STATUS", "serviceRequestVersion": "1.0", "channelId": "XXX"}, "GetKycStatusResponseBody": {"data": {"kyc": {"eKyc": {"lastUpdateTimestamp": {"epochMillis": 1631664000000}, "expirationTimestamp": {"epochMillis": 1631750400000}, "approvedStateContext": {}}, "vKyc": {"lastUpdateTimestamp": {"epochMillis": 1631664000000}, "expirationTimestamp": {"epochMillis": 1631750400000}, "awaitingApprovalStateContext": {}}, "offlineKyc": {"lastUpdateTimestamp": {"epochMillis": 1631664000000}, "notStartedStateContext": {}}, "state": "AWAITING_APPROVAL"}}}}}, "ekyc_approved_vkyc_rejected_offline_in_progress_without_income": {"GetKycStatusResponse": {"SubHeader": {"requestUUID": "d71c3427-93b0-4b7d-9803-76da5c", "serviceRequestId": "AX.GEN.ICC.GET.KYC.STATUS", "serviceRequestVersion": "1.0", "channelId": "XXX"}, "GetKycStatusResponseBody": {"data": {"kyc": {"eKyc": {"lastUpdateTimestamp": {"epochMillis": 1652160597967}, "expirationTimestamp": {"epochMillis": 1652187895961}, "approvedStateContext": {}}, "vKyc": {"lastUpdateTimestamp": {"epochMillis": 1652166063730}, "expirationTimestamp": {"epochMillis": 1652187895961}, "rejectedStateContext": {"reason": "NAME_MATCH_FAILURE"}}, "offlineKyc": {"lastUpdateTimestamp": {"epochMillis": 1652166063730}, "inProgressStateContext": {}}, "state": "IN_PROGRESS"}}}}}, "ekyc_approved_vkyc_rejected_without_offline": {"GetKycStatusResponse": {"SubHeader": {"requestUUID": "d71c3427-93b0-4b7d-9803-76da5c", "serviceRequestId": "AX.GEN.ICC.GET.KYC.STATUS", "serviceRequestVersion": "1.0", "channelId": "XXX"}, "GetKycStatusResponseBody": {"data": {"kyc": {"eKyc": {"lastUpdateTimestamp": {"epochMillis": 1652160597967}, "expirationTimestamp": {"epochMillis": 1652187895961}, "approvedStateContext": {}}, "vKyc": {"lastUpdateTimestamp": {"epochMillis": 1652166063730}, "expirationTimestamp": {"epochMillis": 1652187895961}, "rejectedStateContext": {"reason": "NAME_MATCH_FAILURE"}}, "state": "REJECTED"}}}}}, "ekyc_approved_vkyc_offline_rejected_without_income": {"GetKycStatusResponse": {"SubHeader": {"requestUUID": "d71c3427-93b0-4b7d-9803-76da5c", "serviceRequestId": "AX.GEN.ICC.GET.KYC.STATUS", "serviceRequestVersion": "1.0", "channelId": "XXX"}, "GetKycStatusResponseBody": {"data": {"kyc": {"eKyc": {"lastUpdateTimestamp": {"epochMillis": 1652160597967}, "expirationTimestamp": {"epochMillis": 1652187895961}, "approvedStateContext": {}}, "vKyc": {"lastUpdateTimestamp": {"epochMillis": 1652166063730}, "expirationTimestamp": {"epochMillis": 1652187895961}, "rejectedStateContext": {"reason": "NAME_MATCH_FAILURE"}}, "offlineKyc": {"lastUpdateTimestamp": {"epochMillis": 1652166063730}, "rejectedStateContext": {}}, "state": "REJECTED"}}}}}, "ekyc_vkyc_approved_income_in_progress": {"GetKycStatusResponse": {"SubHeader": {"requestUUID": "d71c3427-93b0-4b7d-9803-76da5c", "serviceRequestId": "AX.GEN.ICC.GET.KYC.STATUS", "serviceRequestVersion": "1.0", "channelId": "XXX"}, "GetKycStatusResponseBody": {"data": {"incomeVerification": {"onlineSubmission": {"lastUpdateTimestamp": {"epochMillis": 1631664000000}, "expirationTimestamp": {"epochMillis": 1631750400000}, "inProgressStateContext": {}}, "offlineSubmission": {"lastUpdateTimestamp": {"epochMillis": 1631664000000}, "notStartedStateContext": {}}, "state": "IN_PROGRESS"}, "kyc": {"eKyc": {"lastUpdateTimestamp": {"epochMillis": 1631664000000}, "expirationTimestamp": {"epochMillis": 1631750400000}, "approvedStateContext": {}}, "vKyc": {"lastUpdateTimestamp": {"epochMillis": 1631664000000}, "expirationTimestamp": {"epochMillis": 1631750400000}, "approvedStateContext": {}}, "offlineKyc": {"lastUpdateTimestamp": {"epochMillis": 1631664000000}, "notStartedStateContext": {}}, "state": "APPROVED"}}}}}, "ekyc_vkyc_approved_income_online_rejected_offline_in_progress": {"GetKycStatusResponse": {"SubHeader": {"requestUUID": "d71c3427-93b0-4b7d-9803-76da5c", "serviceRequestId": "AX.GEN.ICC.GET.KYC.STATUS", "serviceRequestVersion": "1.0", "channelId": "XXX"}, "GetKycStatusResponseBody": {"data": {"incomeVerification": {"onlineSubmission": {"lastUpdateTimestamp": {"epochMillis": 1631664000000}, "expirationTimestamp": {"epochMillis": 1631750400000}, "rejectedStateContext": {"reason": "POLICY_ERROR"}}, "offlineSubmission": {"lastUpdateTimestamp": {"epochMillis": 1631664000000}, "inProgressStateContext": {}}, "state": "IN_PROGRESS"}, "kyc": {"eKyc": {"lastUpdateTimestamp": {"epochMillis": 1631664000000}, "expirationTimestamp": {"epochMillis": 1631750400000}, "approvedStateContext": {}}, "vKyc": {"lastUpdateTimestamp": {"epochMillis": 1631664000000}, "expirationTimestamp": {"epochMillis": 1631750400000}, "approvedStateContext": {}}, "offlineKyc": {"lastUpdateTimestamp": {"epochMillis": 1631664000000}, "notStartedStateContext": {}}, "state": "APPROVED"}}}}}, "ekyc_vkyc_approved_income_online_approved": {"GetKycStatusResponse": {"SubHeader": {"requestUUID": "d71c3427-93b0-4b7d-9803-76da5c", "serviceRequestId": "AX.GEN.ICC.GET.KYC.STATUS", "serviceRequestVersion": "1.0", "channelId": "XXX"}, "GetKycStatusResponseBody": {"data": {"incomeVerification": {"onlineSubmission": {"lastUpdateTimestamp": {"epochMillis": 1631664000000}, "expirationTimestamp": {"epochMillis": 1631750400000}, "approvedStateContext": {}}, "offlineSubmission": {"lastUpdateTimestamp": {"epochMillis": 1631664000000}, "notStartedStateContext": {}}, "state": "APPROVED"}, "kyc": {"eKyc": {"lastUpdateTimestamp": {"epochMillis": 1631664000000}, "expirationTimestamp": {"epochMillis": 1631750400000}, "approvedStateContext": {}}, "vKyc": {"lastUpdateTimestamp": {"epochMillis": 1631664000000}, "expirationTimestamp": {"epochMillis": 1631750400000}, "approvedStateContext": {}}, "offlineKyc": {"lastUpdateTimestamp": {"epochMillis": 1631664000000}, "notStartedStateContext": {}}, "state": "APPROVED"}}}}}, "ekyc_vkyc_approved_income_not_started": {"GetKycStatusResponse": {"SubHeader": {"requestUUID": "d71c3427-93b0-4b7d-9803-76da5c", "serviceRequestId": "AX.GEN.ICC.GET.KYC.STATUS", "serviceRequestVersion": "1.0", "channelId": "XXX"}, "GetKycStatusResponseBody": {"data": {"incomeVerification": {"state": "NOT_STARTED"}, "kyc": {"eKyc": {"lastUpdateTimestamp": {"epochMillis": 1631664000000}, "expirationTimestamp": {"epochMillis": 1631750400000}, "approvedStateContext": {}}, "vKyc": {"lastUpdateTimestamp": {"epochMillis": 1631664000000}, "expirationTimestamp": {"epochMillis": 1631750400000}, "approvedStateContext": {}}, "offlineKyc": {"lastUpdateTimestamp": {"epochMillis": 1631664000000}, "notStartedStateContext": {}}, "state": "APPROVED"}}}}}, "ekyc_vkyc_approved_income_offline_in_progress": {"GetKycStatusResponse": {"SubHeader": {"requestUUID": "d71c3427-93b0-4b7d-9803-76da5c", "serviceRequestId": "AX.GEN.ICC.GET.KYC.STATUS", "serviceRequestVersion": "1.0", "channelId": "XXX"}, "GetKycStatusResponseBody": {"data": {"incomeVerification": {"offlineSubmission": {"lastUpdateTimestamp": {"epochMillis": 1631664000000}, "inProgressStateContext": {}}, "state": "IN_PROGRESS"}, "kyc": {"eKyc": {"lastUpdateTimestamp": {"epochMillis": 1631664000000}, "expirationTimestamp": {"epochMillis": 1631750400000}, "approvedStateContext": {}}, "vKyc": {"lastUpdateTimestamp": {"epochMillis": 1631664000000}, "expirationTimestamp": {"epochMillis": 1631750400000}, "approvedStateContext": {}}, "offlineKyc": {"lastUpdateTimestamp": {"epochMillis": 1631664000000}, "notStartedStateContext": {}}, "state": "APPROVED"}}}}}, "ekyc_approved_vkyc_not_started_in_progress_without_income": {"GetKycStatusResponse": {"SubHeader": {"requestUUID": "43c87765-520f-4e98-a1e8-fe8c95fccbfe", "serviceRequestId": "AX.GEN.ICC.GET.KYC.STATUS", "serviceRequestVersion": "1.0", "channelId": "FLIPKARTSM"}, "GetKycStatusResponseBody": {"data": {"kyc": {"vkyc": {"currentStage": "VKYC_INITIALISED", "lastUpdateTimestamp": {"epochMillis": 1747486680986}, "notStartedStateContext": {}, "expirationTimestamp": {"epochMillis": 1747659480952}}, "ekyc": {"currentStage": "EKYC_OTP_VERIFY_SUCCESS", "lastUpdateTimestamp": {"epochMillis": 1747486782374}, "approvedStateContext": {}, "expirationTimestamp": {"epochMillis": 1747659480952}}, "vKyc": {"currentStage": "VKYC_INITIALISED", "lastUpdateTimestamp": {"epochMillis": 1747486680986}, "notStartedStateContext": {}, "expirationTimestamp": {"epochMillis": 1747659480952}}, "state": "IN_PROGRESS", "offlineKyc": {"lastUpdateTimestamp": {"epochMillis": 1747486680986}, "notStartedStateContext": {}}, "eKyc": {"currentStage": "EKYC_OTP_VERIFY_SUCCESS", "lastUpdateTimestamp": {"epochMillis": 1747486782374}, "approvedStateContext": {}, "expirationTimestamp": {"epochMillis": 1747659480952}}}}}}}}