{"DownloadCkycResponse": {"RequestId": "akxyzabcefghijkmopqrstuv", "Details": {"TransactionId": "tran1", "TransactionStatus": "CKYCSuccess", "PositiveConfirmation": "Y", "CKYCPersonalDetail": [{"RecordIdentifier": "abcdefghijklmnopqrstuvwxyzabcdefghijklmnnopqrstuvwxyz", "ApplicationFormNo": "abcdefghijklmnopqrstuvwxyzabcdefghijklm", "BranchCode": "abcdefghijklmnopqrstuvwxyzabcdefghijklmnopqrstuvwxy", "CKYCConstiType": "01", "CKYCAccType": "01", "CKYCNumber": "10071234567847", "CKYCNamePrefix": "Mr", "CKYCFirstName": "<PERSON><PERSON><PERSON>", "CKYCMiddleName": "", "CKYCLastName": "<PERSON><PERSON><PERSON>", "CKYCFullName": "<PERSON><PERSON><PERSON><PERSON>", "CKYCMaidenNamePrefix": "", "CKYCMaidenFirstName": "", "CKYCMaidenMiddleName": "", "CKYCMaidenLastName": "", "CKYCMaidenFullName": "", "CKYCFatherNamePrefix": "Mr", "CKYCFatherFirstName": "Ram", "CKYCFatherMiddleName": "", "CKYCFatherLastName": "<PERSON><PERSON><PERSON>", "CKYCFatherFullName": "<PERSON><PERSON><PERSON>", "CKYCMotherNamePrefix": "Mrs", "CKYCMotherFirstName": "<PERSON><PERSON><PERSON>", "CKYCMotherMiddletName": "", "CKYCMotherLastName": "<PERSON><PERSON><PERSON>", "CKYCMotherFullName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CKYCGender": "M", "CKYCMaritalStatus": "01", "CKYCNationality": "IN", "CKYCOccupation": "B-01", "CKYCDOB": "14-Mar-2005", "CKYCResidentialStatus": "01", "CKYCTaxResidencyOutsideIndia": "02", "CKYCJurisdictionofRes": "", "CKYCTIN": "", "CKYCCountryOfBirth": "", "CKYCPlaceOfBirth": "", "CKYCPerAddType": "01", "CKYCPerAdd1": "hjng", "CKYCPerAdd2": "htg", "CKYCPerAdd3": "ghtyj", "CKYCPerAddCity": "Mumbai", "CKYCPerAddDistrict": "Mumbai", "CKYCPerAddState": "MH", "CKYCPerAddCountry": "IN", "CKYCPerAddPin": "400012", "CKYCPerAddPinType": "Rural", "CKYCPerAddPOA": "VoterId", "CKYCPerAddPOAOthers": "", "CKYCPerAddSameasCorAdd": "N", "CKYCCorAdd1": "jtyhjyj", "CKYCCorAdd2": "jyu", "CKYCCorAdd3": "rth", "CKYCCorAddCity": "Mumbai", "CKYCCorAddDistrict": "Mumbai", "CKYCCorAddState": "MH", "CKYCCorAddCountry": "IN", "CKYCCorAddPin": "400012", "CKYCCorAddPinType": "Urban", "CKYCPerAddSameAsJurAdd": "", "CKYCJurAdd1": "", "CKYCJurAdd2": "", "CKYCJurAdd3": "", "CKYCJurAddCity": "", "CKYCJurAddState": "", "CKYCJurAddCountry": "", "CKYCJurAddPin": "", "CKYCResTelSTD": "", "CKYCResTelNumber": "", "CKYCOffTelSTD": "", "CKYCOffTelNumber": "", "CKYCMobileISD": "", "CKYCMobileNumber": "", "CKYCFAXSTD": "0", "CKYCFaxNumber": "", "CKYCEmailAdd": "", "CKYCRemarks": "", "CKYCDateofDeclaration": "10-Dec-2018", "CKYCPlaceofDeclaration": "Mumbai", "CKYCKYCVerificationDate": "10-Dec-2018", "CKYCTypeofDocSubmitted": "01", "CKYCKYCVerificationName": "Harsa<PERSON>", "CKYCKYCVerificationDesg": "Manager", "CKYCKYCVerificationBranch": "Mumbai", "CKYCKYCVerificationEmpcode": "1", "CKYCNumberofIds": "1", "CKYCNumberofRelatedPersons": "1", "CKYCNumberofLocalAdds": "0", "CKYCNumberofImages": "2", "CKYCNameUpdated": "02", "CKYCPersonalorEntityDetailsUpdated": "02", "CKYCAddressDetailsUpdated": "02", "CKYCContactDetailsUpdated": "02", "CKYCRemarksUpdated": "02", "CKYCKYCVerificationUpdated": "", "CKYCIDDetailsUpdated": "02", "CKYCRelatedPersonsUpdated": "02", "CKYCImageDetailsUpdated": "02"}], "CKYCIDDetails": [{"CKYCIdentity": [{"CKYCIDSequence": "1", "CKYCIDType": "B", "CKYCIDNumber": "********", "CKYCIDExpiryDate": "", "CKYCIDProofSubmitted": "01", "CKYCIDVerificationStatus": "02"}]}], "CKYCRelatedPersonDetails": [{"CKYCRelatedPerson": {"CKYCRPSequence": "1", "CKYCRPRelation": "1", "CKYCRPCKYCNumber": "", "CKYCRPNamePrefix": "Mr", "CKYCRPFirstName": "<PERSON><PERSON><PERSON>", "CKYCRPMiddleName": "", "CKYCRPLastName": "<PERSON><PERSON><PERSON>", "CKYCRPPAN": "", "CKYCRPAadhar": "", "CKYCRPVoterId": "********", "CKYCRPNREGA": "", "CKYCRPPassportNumber": "", "CKYCRPPassportExpiryDate": "", "CKYCRPDrivingLicenceNumber": "", "CKYCRPDrivingLicenceExpiryDate": "", "CKYCRPProofOfIdName": "", "CKYCRPProofOfIdNumber": "", "CKYCRPSimplifiedIDType": "", "CKYCRPSimplifiedIDNumber": "", "CKYCRPDateofDeclaration": "10-Dec-2018", "CKYCRPPlaceofDeclaration": "Mumbai", "CKYCRPKYCVerificationDate": "10-Dec-2018", "CKYCRPTypeofDocSubmitted": "01", "CKYCRPKYCVerificationName": "Harsa<PERSON>", "CKYCRPKYCVerificationDesg": "Manager", "CKYCRPKYCVerificationBranch": "Mumbai", "CKYCRPKYCVerificationEmpcode": "1"}}], "CKYCImageDetails": [{"CKYCImage": [{"CKYCImageSequence": "1", "CKYCImageExtension": "jpg", "CKYCImageType": "Photograph", "CKYCImageGlobalorLocal": "1", "CKYCImageBranch": "", "CKYCImageData": "/9j/4AAQSkZJRgABAQAAAQABA2N4MhuAe/b319ajpaY6Z8HIiEOBcm9muTY0WY5doA +PwHsHr96j7R1BAO8JI9JWDGnRT9Jx/9k="}, {"CKYCImageSequence": "2", "CKYCImageExtension": "jpg", "CKYCImageType": "VoterId", "CKYCImageGlobalorLocal": "1", "CKYCImageBranch": "", "CKYCImageData": "/9j/4AAQSkZJRgABAQAAhuAY6Z8HIiEOBcm9muTY0WY5doA+PwHsHr96j7R1BAO8JI9JAQABAA tLe+bhT9mLVH6N4T/FWtYH/9k="}]}]}}}