{"lsp_application_id": "APP2506051556401596485687419849686841836", "lender": "MONEYVIEWOPENMKT", "pan": "YhpboRr+swRQIApBmMECQQ==", "dob": "dI2d8Ve+PkeiX6esrMcSBQ==", "organization": "Elphi inc", "income": "30000", "bonus_income": "0", "employment_type": "Salaried", "income_granularity": "MONTHLY", "income_mode": "ONLINE", "gender": "M", "email_id": "2bmx2onEBNLbmEHqLdD1LKXznLof43lIff/1pMfkSvg=", "phone_no": "", "first_name": "l6949qN2zNWahlozvs7c3Q==", "last_name": "oYXa39b5ssKM+GS6FqjK3Q==", "shipping_address_id": "CNTCTBFB49F54B42C4523A10ADD7DD", "state": "Wv0yipQso33yI4CdfQ0CUw==", "city": "O5ebEhfEgbB91DMCk4OvEA==", "pincode": "500042", "area": "4jh7LBGbVlzR1wiaJ2PI5Lm0GrvMkwrY1bmUenfvT4U=", "house_number": "U30NhMWjQhlEEDoMBeV8FiW5S2TrNF6c2NJQVjWmEBtWyjSkYGTnNDDPfW0jQNcChaV7JFYfqEvrKA/Ve7eftA==", "account_id": "ACC8EEDA325BFD44733BD23B5D089B05FB9U", "sm_user_id": "SMA2BA015DD271D409EAD5D658486526275", "lead_id": "", "offer_id": "", "cremo_score": "2", "cremo_version": "v10_2", "granular_cremo_score": "2.3", "consent_data": {"consentId": "001", "consentFor": "BUREAU_PULL", "userIP": "************, *************,*************", "deviceId": "cm19ln4i3255u136jefjyj2si-***************", "deviceInfo": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 FKUA/msite/0.0.1/msite/Mobile", "currentTimeStamp": "*************"}, "consent_meta_data_list": [{"userIP": "************, *************,*************", "deviceId": "cm19ln4i3255u136jefjyj2si-***************", "consentId": "003", "consentFor": "MFI", "deviceInfo": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 FKUA/msite/0.0.1/msite/Mobile", "consentType": "CHECKBOX", "currentTimeStamp": "*************"}, {"userIP": "************, *************,*************", "deviceId": "cm19ln4i3255u136jefjyj2si-***************", "consentId": "002", "consentFor": "NON_PEP", "deviceInfo": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 FKUA/msite/0.0.1/msite/Mobile", "consentType": "CHECKBOX", "currentTimeStamp": "*************"}, {"userIP": "************, *************,*************", "deviceId": "cm19ln4i3255u136jefjyj2si-***************", "consentId": "004", "consentFor": "CKYC", "deviceInfo": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 FKUA/msite/0.0.1/msite/Mobile", "consentType": "CHECKBOX", "currentTimeStamp": "*************"}], "merchant": "FLIPKART"}