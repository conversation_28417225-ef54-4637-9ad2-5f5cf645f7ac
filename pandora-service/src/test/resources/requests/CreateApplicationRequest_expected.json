{"lspApplicationId": "APP2506051556401596485687419849686841836", "lspId": "SuperMoney", "lspRedirectUrl": "https://dl.flipkart.com/sm-3p/pl/pages/journey?token=FHE1ltfc8H6Av3ctuCWRnthWMuWUiU5se5dWWmwgkXdzvBFyBcw57LTmtflQUMlyroE6oIlsAC9lhHAUWK5KYQ785LxsCLeVA0YpvzpQPvU%3D", "productType": "TERM_LOAN", "applicationType": "LOAN_ONBOARDING", "consents": [{"provided": true, "consentId": "001", "ip": "************", "deviceId": "cm19ln4i3255u136jefjyj2si-BR1726768938891", "deviceInfo": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 FKUA/msite/0.0.1/msite/Mobile", "ts": 1749118973891, "for": "BUREAU_PULL"}, {"provided": true, "consentId": "003", "ip": "************", "deviceId": "cm19ln4i3255u136jefjyj2si-BR1726768938891", "deviceInfo": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 FKUA/msite/0.0.1/msite/Mobile", "ts": 1749119194873, "for": "MFI"}, {"provided": true, "consentId": "002", "ip": "************", "deviceId": "cm19ln4i3255u136jefjyj2si-BR1726768938891", "deviceInfo": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 FKUA/msite/0.0.1/msite/Mobile", "ts": 1749119194873, "for": "NON_PEP"}, {"provided": true, "consentId": "004", "ip": "************", "deviceId": "cm19ln4i3255u136jefjyj2si-BR1726768938891", "deviceInfo": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 FKUA/msite/0.0.1/msite/Mobile", "ts": 1749119194873, "for": "CKYC"}], "userInfo": {"phoneNumber": "1234567890", "emailId": "<EMAIL>", "dob": "1996-08-08", "pan": "**********", "firstName": "KALAVAGUNTA", "lastName": "DURGARAO", "gender": "MALE", "addresses": [{"pincode": "500042", "city": "Hyderabad", "state": "TELANGANA", "country": "India", "qualifier": "CUSTOMER_FILLED", "line1": "Balanagar HAL gate 1 opposite Raghavendra mentions Telangana", "line2": "Swathi hotel landmark"}], "employmentDetails": {"type": "SALARIED", "organization": "Elphi inc", "income": 30000, "incomeGranularity": "MONTHLY", "incomeMode": "ONLINE"}, "additionalData": [{"score": "2.3", "dataType": "CREMO", "version": "v10_2"}]}, "merchant": "FLIPKART", "preApprovedOfferDetails": {"id": ""}}