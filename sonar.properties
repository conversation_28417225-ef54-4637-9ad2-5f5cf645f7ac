sonar.projectKey=com.flipkart.fintech:pandora
sonar.projectName=pandora
sonar.tests=./pandora-service/src/test,./pandora-api/src/test,./pandora-service-client/src/test
sonar.sources=./pandora-service/src/main,./pandora-api/src/main,./pandora-client/src/main,./pandora-service-client/src/main,./pandora-external-models/src/main,./user-data-client/src/main
sonar.sourceEncoding=UTF-8
sonar.verbose=true
sonar.coverage.jacoco.xmlReportPaths=./pandora-service/target/site/jacoco-ut/jacoco.xml,./pandora-service-client/target/site/jacoco-ut/jacoco.xml,./pandora-api/target/site/jacoco-ut/jacoco.xml,./pandora-client/target/site/jacoco-ut/jacoco.xml,./pandora-external-models/target/site/jacoco-ut/jacoco.xml,./user-data-client/target/site/jacoco-ut/jacoco.xml
sonar.exclusions=**/generated/**, **/test/**, **/test-classes/**, **/target/**, **/src/test/**, **/src/main/resources/**, **/src/test/resources/**, **/pandora-api/**, **/requests/**, **/responses/**, **/config/**
