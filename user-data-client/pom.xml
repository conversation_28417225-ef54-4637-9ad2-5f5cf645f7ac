<?xml version="1.0"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.flipkart.fintech</groupId>
        <artifactId>pandora</artifactId>
        <version>1.5.16-SM</version>
    </parent>
    <artifactId>user-data-client</artifactId>
    <name>user-data-client</name>
    <url>http://maven.apache.org</url>
    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.22</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.flipkart.affordability</groupId>
            <artifactId>affordability-service-clients</artifactId>
            <version>5.0.3-SM</version>
            <exclusions>
                <exclusion>
                        <groupId>org.apache.avro</groupId>
                        <artifactId>avro</artifactId>
                </exclusion>
                <exclusion>
                        <groupId>org.apache.avro</groupId>
                        <artifactId>avro-maven-plugin</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.0</version>
        </dependency>
        <dependency>
            <groupId>javax.ws.rs</groupId>
            <artifactId>javax.ws.rs-api</artifactId>
            <version>2.0</version>
        </dependency>
        <dependency>
            <groupId>javax.inject</groupId>
            <artifactId>javax.inject</artifactId>
            <version>1</version>
        </dependency>
        <dependency>
            <groupId>javax.validation</groupId>
            <artifactId>validation-api</artifactId>
            <version>1.1.0.Final</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <!-- Enable this plugin to publish the artifact for Java17 -->
<!--            <plugin>-->
<!--                <groupId>org.eclipse.transformer</groupId>-->
<!--                <artifactId>transformer-maven-plugin</artifactId>-->
<!--                <extensions>true</extensions>-->
<!--                <configuration>-->
<!--                    <rules>-->
<!--                        <jakartaDefaults>true</jakartaDefaults>-->
<!--                    </rules>-->
<!--                </configuration>-->
<!--                <executions>-->
<!--                    <execution>-->
<!--                        <id>default-jar</id>-->
<!--                        <goals>-->
<!--                            <goal>jar</goal>-->
<!--                        </goals>-->
<!--                        <configuration>-->
<!--                            <artifact>-->
<!--                                <groupId>${project.groupId}</groupId>-->
<!--                                <artifactId>${project.artifactId}</artifactId>-->
<!--                                <version>${project.version}</version>-->
<!--                            </artifact>-->
<!--                        </configuration>-->
<!--                    </execution>-->
<!--                </executions>-->
<!--            </plugin>-->

        </plugins>
    </build>
</project>
