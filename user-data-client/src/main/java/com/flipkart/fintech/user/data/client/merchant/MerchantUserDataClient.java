package com.flipkart.fintech.user.data.client.merchant;

import com.flipkart.fintech.user.data.models.AddressData;
import com.flipkart.fintech.user.data.models.UserData;
import com.flipkart.fintech.user.data.models.enums.PIIDataType;

public interface MerchantUserDataClient {
    UserData getUserData(String merchantUserId, String smUserId, PIIDataType piiDataType);

    AddressData getAddressData(String merchantUserId, String smUserId, String addressId, PIIDataType piiDataType);

    @Deprecated
    UserData getNonVerifiedUserData(String merchantUserId, String smUserId, PIIDataType piiDataType);
}
